﻿namespace Neuz.Core.Entity;

/// <summary>
/// 出库单明细
/// </summary>
[SugarTable(null, "出库单明细")]
[SugarIndex("index_{table}_SBSI", nameof(SrcBillKey), OrderByType.Asc, nameof(SrcBillId), OrderByType.Asc)]
[SugarIndex("index_{table}_SB", nameof(SrcBillNo), OrderByType.Asc)]
[SugarIndex("index_{table}_ST", nameof(SrcTaskId), OrderByType.Asc)]
public class StkOutStockEntry : EsEntryEntityBase
{
    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 拣货库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "拣货库区Id")]
    public long? SrcWhAreaId { get; set; }

    /// <summary>
    /// 拣货库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea SrcWhArea { get; set; }

    /// <summary>
    /// 拣货库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "拣货库位Id")]
    public long? SrcWhLocId { get; set; }

    /// <summary>
    /// 拣货库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc SrcWhLoc { get; set; }

    /// <summary>
    /// 发运库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "发运库区Id")]
    public long DestWhAreaId { get; set; }

    /// <summary>
    /// 发运库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea DestWhArea { get; set; }

    /// <summary>
    /// 发运库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "发运库位Id")]
    public long DestWhLocId { get; set; }

    /// <summary>
    /// 发运库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc DestWhLoc { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 退货数量
    /// </summary>
    [SugarColumn(ColumnDescription = "退货数量")]
    public decimal ReturnQty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer Container { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的毛重
    /// </remarks>
    [SugarColumn(ColumnDescription = "毛重")]
    public decimal GrossWeight { get; set; }

    /// <summary>
    /// 包装体积
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的包装体积
    /// </remarks>
    [SugarColumn(ColumnDescription = "包装体积")]
    public decimal PackingVolume { get; set; }

    /// <summary>
    /// 箱数
    /// </summary>
    /// <remarks>
    /// 默认计算规则：[Ceiling]向上取整(数量 / 物料的标准装箱量)
    /// </remarks>
    [SugarColumn(ColumnDescription = "箱数")]
    public int PackingQty { get; set; }

    /// <summary>
    /// 含税单价
    /// </summary>
    [SugarColumn(ColumnDescription = "含税单价")]
    public decimal Price { get; set; }

    ///// <summary>
    ///// 税率
    ///// </summary>
    //[SugarColumn(ColumnDescription = "税率")]
    //public decimal Tax { get; set; }

    /// <summary>
    /// 含税金额
    /// </summary>
    [SugarColumn(ColumnDescription = "含税金额")]
    public decimal TotalPrice { get; set; }

    ///// <summary>
    ///// 税额
    ///// </summary>
    //[SugarColumn(ColumnDescription = "税额")]
    //public decimal TotalTax { get; set; }

    ///// <summary>
    ///// 不含税单价
    ///// </summary>
    //[SugarColumn(ColumnDescription = "不含税单价")]
    //public decimal UnitPriceExcludeTax { get; set; }

    ///// <summary>
    ///// 不含税金额
    ///// </summary>
    //[SugarColumn(ColumnDescription = "不含税金额")]
    //public decimal TotalPriceExcludeTax { get; set; }

    /// <summary>
    /// 来源单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源单据标识", Length = 200)]
    public string? SrcBillKey { get; set; }

    /// <summary>
    /// 来源单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据编号", Length = 80)]
    public string? SrcBillNo { get; set; }

    /// <summary>
    /// 来源单据分录序号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录序号")]
    public int? SrcBillEntrySeq { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id")]
    public long? SrcBillId { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id")]
    public long? SrcBillEntryId { get; set; }

    /// <summary>
    /// 来源任务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源任务Id")]
    public long? SrcTaskId { get; set; }

    /// <summary>
    /// 来源任务明细Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源任务明细Id")]
    public long? SrcTaskEntryId { get; set; }

    /// <summary>
    /// 明细备注
    /// </summary>
    [SugarColumn(ColumnDescription = "明细备注", Length = 255)]
    public string? EntryMemo { get; set; }

    /// <summary>
    /// 辅助数量
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助数量")]
    public decimal AuxQty { get; set; }
    
    /// <summary>
    /// 辅助单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助单位Id")]
    public long? AuxUnitId { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxUnitId))]
    [CustomSerializeFields]
    public BdUnit AuxUnit { get; set; }

    /// <summary>
    /// 条码明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkOutStockBarcodeEntry.RelEntryId))]
    public List<StkOutStockBarcodeEntry> BarcodeEntries { get; set; }
}