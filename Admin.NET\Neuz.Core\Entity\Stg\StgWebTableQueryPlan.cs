﻿namespace Neuz.Core.Entity;

/// <summary>
/// Web端表格查询方案
/// </summary>
[SugarTable(null, "Web端表格查询方案")]
public class StgWebTableQueryPlan : EntityTenant
{
    /// <summary>
    /// 表格Id
    /// </summary>
    [SugarColumn(ColumnDescription = "表格Id", Length = 200)]
    public string TableId { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long UserId { get; set; }

    /// <summary>
    /// 方案名称
    /// </summary>
    [SugarColumn(ColumnDescription = "方案名称", Length = 200)]
    public string Name { get; set; }

    /// <summary>
    /// 方案Json
    /// </summary>
    [SugarColumn(ColumnDescription = "方案Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Json { get; set; }
}