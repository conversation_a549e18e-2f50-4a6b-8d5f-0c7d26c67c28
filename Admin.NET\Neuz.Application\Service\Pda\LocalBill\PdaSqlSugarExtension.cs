﻿using SqlSugar;
using System.Linq.Dynamic.Core;

namespace Neuz.Application.Pda.LocalBill;

public static class PdaSqlSugarExtension
{
    public static ISqlSugarClient AddPdaFilter<TS, TSE>(this ISqlSugarClient client, IServiceProvider serviceProvider, string warehouseId, string whAreaId, string ownerId)
    {
        if (IsExistProperty<TSE>(warehouseId))
        {
            client.AddWarehouseFilter<TS>(serviceProvider, DynamicExpressionParser.ParseLambda<TS, object>(new ParsingConfig(), true, warehouseId));
        }

        if (IsExistProperty<TSE>(whAreaId))
        {
            client.AddWhAreaFilter<TSE>(serviceProvider, DynamicExpressionParser.ParseLambda<TSE, object>(new ParsingConfig(), true, whAreaId));
        }

        if (IsExistProperty<TSE>(ownerId))
        {
            client.AddOwnerFilter<TSE>(serviceProvider, DynamicExpressionParser.ParseLambda<TSE, object>(new ParsingConfig(), true, ownerId));
        }

        return client;
    }

    private static bool IsExistProperty<T>(string propertyName)
    {
        var property = typeof(T).GetProperty("WhAreaId");
        return property != null;
    }
}