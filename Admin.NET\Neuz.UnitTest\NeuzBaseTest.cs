﻿using Admin.NET.Core;
using Furion;
using Neuz.Application;
using Neuz.Application.Model;
using Neuz.Core.Entity;
using Neuz.Core.Enum;
using Xunit;
using Xunit.Abstractions;
using Yitter.IdGenerator;

namespace Neuz.UnitTest;

public class NeuzBaseTest
{
    private readonly ITestOutputHelper _output;

    public NeuzBaseTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;
    }

    [Fact(DisplayName = "编码规则新增测试")]
    public void RuleInsertTest()
    {
        var service = App.GetService<SysCodeRuleService>();
        var rule = new SysCodeRule()
        {
            Number = "Rule1",
            Name = "规则1",
            EntityName = "111",
            Description = "hha",
            Entries = new List<SysCodeRuleEntry>
            {
                new()
                {
                    ElementType = ElementType.Const,
                    ConstValue = "22",
                },
                new()
                {
                    ElementType = ElementType.Const,
                    ConstValue = "33",
                }
            }
        };
        service.AddAsync(rule).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "编码规则删除测试")]
    public void RuleDeleteTest()
    {
        var rep = App.GetService<SqlSugarRepository<SysCodeRule>>();
        var service = App.GetService<SysCodeRuleService>();
        var entity = rep.GetFirst(u => u.Number == "Rule1");
        service.DeleteAsync(new IdsInput { Ids = new List<long> { entity.Id } }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "编码规则获取测试")]
    public void RuleGetTest()
    {
        var rep = App.GetService<SqlSugarRepository<SysCodeRule>>();
        var service = App.GetService<SysCodeRuleService>();
        var entity = rep.GetFirst(u => u.Number == "Rule1");
        var newEntity = service.GetAsync(new IdInput() { Id = entity.Id }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "编码规则更新测试")]
    public void RuleUpdateTest()
    {
        var rep = App.GetService<SqlSugarRepository<SysCodeRule>>();
        var service = App.GetService<SysCodeRuleService>();
        rep.AsTenant().BeginTran();
        var rule = new SysCodeRule()
        {
            Id = 12655212616901,
            Number = "Rule1",
            Name = "规则1",
            EntityName = "111",
            Description = "更新的",
            Entries = new List<SysCodeRuleEntry>
            {
                new()
                {
                    ElementType = ElementType.Const,
                    ConstValue = "新增的",
                },
                new()
                {
                    EntryId = 12655212644038,
                    Id = 12655212616901,
                    ElementType = ElementType.Const,
                    ConstValue = "33--更新",
                }
            }
        };
        //ConstValue="22"的里理论上要被删除
        service.UpdateAsync(rule).GetAwaiter().GetResult();
        rep.AsTenant().CommitTran();
    }

    [Fact(DisplayName = "编码规则获取报表数据测试")]
    public void RuleGetReportDataTest()
    {
        var service = App.GetService<SysCodeRuleService>();
        var data = service.GetReportData(new SysReportTemplate(), new StringIdsInput() { Ids = new List<string> { "12655212616901" } });
    }

    [Fact(DisplayName = "物料获取报表数据测试")]
    public void MaterialGetReportDataTest()
    {
        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        var data = service.GetReportData(new SysReportTemplate(), new StringIdsInput() { Ids = new List<string> { "12679242117957" } });
    }

    [Fact(DisplayName = "编码规则获取报表架构测试")]
    public void RuleGetReportSchemaTest()
    {
        var service = App.GetService<SysCodeRuleService>();
        var data = service.GetReportDataColAlias();
    }

    [Fact(DisplayName = "物料获取报表架构测试")]
    public void MaterialGetReportSchemaTest()
    {
        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        var data = service.GetReportDataColAlias();
    }

    [Fact(DisplayName = "物料、单位新增测试")]
    public void MaterialUnitInsertTest()
    {
        var unitService = App.GetService<BaseBdModelStdService<BdUnit>>();
        var unitId = unitService.AddAsync(new BdUnit()
        {
            Id = YitIdHelper.NextId(),
            Number = "G",
            Name = "克",
        }).GetAwaiter().GetResult();
        var materialService = App.GetService<BaseBdModelStdService<BdMaterial>>();
        materialService.AddAsync(new BdMaterial()
        {
            Number = "WL001",
            Name = "物料001",
            UnitId = unitId,
        }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "物料分组新增测试")]
    public void MaterialGroupInsertTest()
    {
        var groupService = App.GetService<BdDataGroupService>();
        var groupId = groupService.AddAsync(new BdDataGroup()
        {
            Id = YitIdHelper.NextId(),
            Number = "G01",
            Name = "分组01",
        }).GetAwaiter().GetResult();

        groupService.AddAsync(new BdDataGroup()
        {
            Id = YitIdHelper.NextId(),
            Number = "G0101",
            Name = "分组0101",
            Pid = 12685139778245,
        }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "物料分组删除测试")]
    public void MaterialGroupDeleteTest()
    {
        var groupService = App.GetService<BdDataGroupService>();
        var groupRep = App.GetService<SqlSugarRepository<BdDataGroup>>();
        groupRep.AsTenant().BeginTran();

        groupService.DeleteAsync(new IdsInput { Ids = new List<long> { 12685139778245 } }).GetAwaiter().GetResult();
        groupRep.AsTenant().CommitTran();
    }

    [Fact(DisplayName = "系统实体属性列表测试")]
    public void EntityPropertyListTest()
    {
        var service = App.GetService<SysEntityService>();
        var list = service.PropertyListAsync(new SysEntityPropertyInput
        {
            EntityName = "BdMaterial",
            ElementType = ElementType.BaseData,
        }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "系统实体基础资料属性列表测试")]
    public void EntityBaseDataPropertyListTest()
    {
        var service = App.GetService<SysEntityService>();
        var list = service.BaseDataPropertyListAsync(new SysEntityBaseDataPropertyInput()
        {
            EntityName = "BdMaterial",
            PropertyName = "Unit",
        }).GetAwaiter().GetResult();
    }

    [Fact(DisplayName = "查询测试")]
    public void QueryTest()
    {
        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        var list = service.CustomQueryPage(new CustomPageInput { PageSize = 20 }).GetAwaiter().GetResult();
    }
}