﻿namespace Neuz.Core.Enum;

/// <summary>
/// 库存预入库事务类型
/// </summary>
public enum StkInvPreInLogType
{
    /// <summary>
    /// 任务分配加预入库
    /// </summary>
    [Description("任务分配加预入库")]
    TaskAllocatePlus = 1,

    /// <summary>
    /// 拣货减预入库
    /// </summary>
    [Description("拣货减预入库")]
    PickMinus = 2,

    /// <summary>
    /// 调拨入减预入库
    /// </summary>
    [Description("调拨入减预入库")]
    TransferInMinus = 3,

    /// <summary>
    /// 手动关闭减锁定
    /// </summary>
    [Description("手动关闭减锁定")]
    ManualCloseMinus = 4,

    // 非标准流程，从1001开始
}