﻿using System.Collections.ObjectModel;
using System.Resources;
using Furion.Localization;
using Neuz.Application.Model;

namespace Neuz.Application;

/// <summary>
/// 条码档案模型服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysModelBar", Order = 100)]
public class SysModelBarService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 条码档案模型预设字典
    /// </summary>
    private readonly ReadOnlyDictionary<string, string> _modelBarPresets = new(new Dictionary<string, string>
    {
        ["BarBarcodeQuery"] = "条码查询.json",

        ["K3CloudBdMaterial"] = "K3Cloud物料.json",
        ["K3CloudBdStockStockLoc"] = "K3Cloud仓库仓位.json",
        ["K3CloudStkInventory"] = "K3Cloud即时库存.json",
        ["K3CloudPurPurchaseOrder"] = "K3Cloud采购订单.json",
        ["K3CloudPurReceiveBill"] = "K3Cloud收料通知单.json",
        ["K3CloudPrdMo"] = "K3Cloud生产订单.json",
        ["K3CloudPrdMoRpt"] = "K3Cloud生产汇报单.json",
        ["K3CloudPrdReturnMtrl"] = "K3Cloud生产退料单.json",
        ["K3CloudSalSaleOrder"] = "K3Cloud销售订单.json",
        ["K3CloudSalDeliveryNotice"] = "K3Cloud发货通知单.json",
        ["K3CloudSalReturnNotice"] = "K3Cloud退货通知单.json",

        ["K3WiseICItem"] = "K3Wise物料.json",
        ["K3WiseStock"] = "K3Wise仓库仓位.json",
        ["K3WiseInventory"] = "K3Wise即时库存.json",
        ["K3WiseBill1"] = "K3Wise外购入库.json",
        ["K3WiseBill551"] = "K3Wise任务单汇报_请检单.json",
        ["K3WiseBill71"] = "K3Wise采购订单.json",
        ["K3WiseBill72"] = "K3Wise收料通知_请检单.json",
        ["K3WiseBill73"] = "K3Wise退料通知单.json",
        ["K3WiseBill81"] = "K3Wise销售订单.json",
        ["K3WiseBill82"] = "K3Wise退货通知.json",
        ["K3WiseBill83"] = "K3Wise发货通知.json",
        ["K3WiseBill85"] = "K3Wise生产任务单.json",
        ["K3WiseBill1007105"] = "K3Wise委外订单.json",
    });

    /// <summary>
    /// 条码档案模型仓储
    /// </summary>
    private SqlSugarRepository<SysModelBar> ModelBarRep { get; }

    /// <summary>
    /// 条码档案模型历史仓储
    /// </summary>
    private SqlSugarRepository<SysModelBarHistory> ModelBarHistoryRep { get; }

    /// <summary>
    /// 条码制作模型管理服务
    /// </summary>
    private BarModelManageService BarModelManageService { get; }

    /// <summary>
    /// 日志组件
    /// </summary>
    private ILogger Logger { get; }

    /// <summary>
    /// 条码档案模型服务
    /// </summary>
    public SysModelBarService(IServiceProvider serviceProvider)
    {
        ModelBarRep = serviceProvider.GetService<SqlSugarRepository<SysModelBar>>();
        ModelBarHistoryRep = serviceProvider.GetService<SqlSugarRepository<SysModelBarHistory>>();
        BarModelManageService = serviceProvider.GetService<BarModelManageService>();
        Logger = serviceProvider.GetService<ILogger<SysModelBarService>>();
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    [HttpPost("page")]
    public async Task<SqlSugarPagedList<SysModelBarOutput>> Page(SysModelBarInput input)
    {
        var entities = await ModelBarRep.Context
            .Queryable(
                ModelBarRep.AsQueryable()
                    .WhereIF(!string.IsNullOrEmpty(input.ModelKey), u => u.ModelKey.Contains(input.ModelKey))
                    .WhereIF(!string.IsNullOrEmpty(input.ModelName), u => u.ModelName.Contains(input.ModelName))
                    .Select(u => new SysModelBarOutput(), true)
            )
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }

    /// <summary>
    /// 增加条码档案模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("add")]
    [UnitOfWork]
    public virtual async Task<long> Add(SysModelBar input)
    {
        var entity = await ModelBarRep.GetFirstAsync(u => u.ModelKey == input.ModelKey);
        if (entity != null)
            throw Oops.Bah(SysErrorCode.SysModelBar1001, input.ModelKey);

        // 校验 JSON 格式
        if (!JSON.IsValid(input.ModelContent)) throw Oops.Bah(SysErrorCode.SysModelBar1002, input.ModelKey);

        entity = input.Adapt<SysModelBar>();
        entity.Id = YitIdHelper.NextId();

        // 保存
        await ModelBarRep.InsertAsync(entity);

        return entity.Id;
    }

    /// <summary>
    /// 删除条码档案模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delete")]
    public Task<List<ExecResult>> Delete(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = ModelBarRep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            // 从 BarModelService 中移除模型
            BarModelManageService.DeleteModel(entity.ModelKey);

            // 从数据库中删除
            ModelBarRep.Delete(entity);

            return L.Text["ModelKey: {0} 删除成功", entity.ModelKey];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 更新条码档案模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("update")]
    [UnitOfWork]
    public async Task Update(SysModelBar input)
    {
        var entity = await ModelBarRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        // 内容有变化，插入历史
        if (entity.ModelKey != input.ModelKey || entity.ModelContent != input.ModelContent)
        {
            var history = new SysModelBarHistory
            {
                ModelKey = entity.ModelKey,
                ModelName = entity.ModelName,
                ModelContent = entity.ModelContent,
            };
            await ModelBarHistoryRep.InsertAsync(history);
        }

        // 处理 BarModelService 中的模型
        var model = JSON.Deserialize<BarModel>(input.ModelContent);
        if (entity.ModelKey != input.ModelKey)
        {
            BarModelManageService.DeleteModel(entity.ModelKey);
            BarModelManageService.AddModel(input.ModelKey, model);
        }
        else
        {
            BarModelManageService.UpdateModel(input.ModelKey, model);
        }

        // 保存更新
        entity.ModelKey = input.ModelKey;
        entity.ModelName = input.ModelName;
        entity.ModelContent = input.ModelContent;
        await ModelBarRep.UpdateAsync(entity);
    }

    /// <summary>
    /// 获取条码档案模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("get")]
    public async Task<SysModelBar> Get([FromQuery] IdInput input)
    {
        return await ModelBarRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// Id 集合操作执行
    /// </summary>
    /// <param name="ids">Id 集合</param>
    /// <param name="action">执行的操作，返回执行结果消息，失败消息通过异常抛出</param>
    /// <returns></returns>
    private List<ExecResult> ExecActions(ICollection<long> ids, Func<long, string> action)
    {
        var results = new List<ExecResult>();
        foreach (var id in ids)
        {
            try
            {
                var msg = action.Invoke(id);
                results.Add(new ExecResult { IsSuccess = true, Id = id, Message = msg });
            }
            catch (AppFriendlyException ex)
            {
                results.Add(new ExecResult { IsSuccess = false, Id = id, Message = ex.Message });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                results.Add(new ExecResult { IsSuccess = false, Id = id, Message = ex.Message });
            }
        }

        return results;
    }

    /// <summary>
    /// 复制
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("copy")]
    public async Task<long> Copy(IdInput input)
    {
        var entity = await ModelBarRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        entity.ModelKey = $"{entity.ModelKey} - 副本";
        entity.ModelName = $"{entity.ModelName} - 副本";
        entity.CreateTime = DateTime.Now;
        entity.CreateUserId = null;
        entity.CreateUserName = null;
        entity.UpdateTime = null;
        entity.UpdateUserId = null;
        entity.UpdateUserName = null;

        return await Add(entity);
    }

    /// <summary>
    /// 获取条码档案模型预设列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("getPresetsList")]
    public Task<List<SysModelBarPresetsOutput>> GetPresetsList()
    {
        var list = _modelBarPresets.Select(u => new SysModelBarPresetsOutput()
        {
            ModelKey = u.Key,
            ModelName = Path.GetFileNameWithoutExtension(u.Value), //去除扩展名
        }).ToList();

        return Task.FromResult(list);
    }

    /// <summary>
    /// 添加预设条码档案模型
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("addByPresets")]
    public async Task<long> AddByPresets(SysModelBarAddByPresetsInput input)
    {
        if (!_modelBarPresets.TryGetValue(input.ModelKey, out var name))
            throw Oops.Bah(SysErrorCode.SysModelBar1003, input.ModelKey);

        var assembly = Assembly.GetExecutingAssembly();
        // 资源名称，由程序集名称、资源目录路径和资源名称，用点“.”拼接
        var resourceName = $"{assembly.GetName().Name}.Service.Sys.SysModelBar.Presets.{name}";
        var modelContent = ReadEmbeddedResource(resourceName, assembly);

        var entity = new SysModelBar
        {
            ModelKey = input.ModelKey,
            ModelName = Path.GetFileNameWithoutExtension(name), //去除扩展名
            ModelContent = modelContent
        };

        return await Add(entity);
    }

    /// <summary>
    /// 读取嵌入资源
    /// </summary>
    /// <param name="resourceName">资源名称</param>
    /// <param name="assembly">资源所在程序集</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    private static string ReadEmbeddedResource(string resourceName, Assembly assembly)
    {
        using var stream = assembly.GetManifestResourceStream(resourceName);
        using var sr = new StreamReader(stream ?? throw new MissingManifestResourceException(L.Text["资源 {0} 找不到", resourceName]));

        var content = sr.ReadToEnd();
        return content;
    }
}