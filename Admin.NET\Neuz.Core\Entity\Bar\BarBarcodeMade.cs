﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案关联源单制作信息
/// </summary>
[SugarTable(null, "条码档案关联源单制作信息")]
[SugarIndex("index_{table}_FSSS", nameof(FuncKey), OrderByType.Asc, nameof(SourceBillKey), OrderByType.Asc, nameof(SourceBillId), OrderByType.Asc, nameof(SourceBillEntryId), OrderByType.Asc)]
public class BarBarcodeMade : EntityTenant
{
    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 100)]
    public string FuncKey { get; set; }

    /// <summary>
    /// 来源单据Key
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Key", Length = 50)]
    public string SourceBillKey { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id", Length = 50)]
    public string SourceBillId { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id", Length = 50)]
    public string SourceBillEntryId { get; set; }

    /// <summary>
    /// 已制作数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已制作数量")]
    public decimal MadeQty { get; set; }
}