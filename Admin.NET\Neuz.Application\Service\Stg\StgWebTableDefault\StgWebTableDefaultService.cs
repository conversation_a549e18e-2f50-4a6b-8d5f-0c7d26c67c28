﻿namespace Neuz.Application;

/// <summary>
/// Web端表格默认配置服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StgWebTableDefault", Order = 100)]
public class StgWebTableDefaultService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 仓储服务
    /// </summary>
    protected SqlSugarRepository<StgWebTableDefault> Rep { get; }

    /// <summary>
    /// Web端表格默认配置服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    public StgWebTableDefaultService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        Rep = App.GetRequiredService<SqlSugarRepository<StgWebTableDefault>>(serviceProvider);
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("saveConfig")]
    public async Task SaveConfig(StgWebTableDefaultInput input)
    {
        var serializerSettings = new JsonSerializerSettings(JSON.GetSerializerOptions<JsonSerializerSettings>())
        {
            NullValueHandling = NullValueHandling.Ignore
        };
        var json = JSON.Serialize(new StgWebTableDefaultOutput
        {
            ListColumns = input.ListColumns,
            SearchColumns = input.SearchColumns,
        }, serializerSettings);

        var hasChange = await Rep.AsUpdateable().SetColumns(u => new StgWebTableDefault { Json = json })
            .Where(u => u.TableId == input.TableId)
            .ExecuteCommandHasChangeAsync();

        if (hasChange) return;


        await Rep.InsertAsync(new StgWebTableDefault { TableId = input.TableId, Json = json });
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    /// <param name="input"></param>
    [NonAction]
    public async Task<StgWebTableDefaultOutput> GetConfig(StringIdInput input)
    {
        var stgWebTableDefault = await Rep.GetFirstAsync(r => r.TableId == input.Id);

        return stgWebTableDefault == null || string.IsNullOrEmpty(stgWebTableDefault.Json) ? null : JSON.Deserialize<StgWebTableDefaultOutput>(stgWebTableDefault.Json);
    }
}