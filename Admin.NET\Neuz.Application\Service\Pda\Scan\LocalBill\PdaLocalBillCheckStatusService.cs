﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill;

/// <summary>
/// 本地单据,检查状态服务
/// </summary>
public class PdaLocalBillCheckStatusService : ITransient
{
    /// <summary>
    /// 条码控制
    /// PDA收货:只允许初始、已出库状态
    /// PDA上架:只允许合格状态
    /// PDA下架、无源出库、其他出库:只允许已入库的条码进行出库下架
    /// PDA无源入库、其他入库:初始、已出库状态
    /// PDA调拨单:已入库状态
    /// </summary>
    private readonly Dictionary<string, List<BdBarcodeStatus>> CheckBarcodeKeys = new Dictionary<string, List<BdBarcodeStatus>>()
    {
        // 收货 - 初始, 已出库
        { "StkInNotice_StkReceive", [BdBarcodeStatus.Init, BdBarcodeStatus.Out] },
        // 上架 - 合格
        { "StkReceive_StkInStock", [BdBarcodeStatus.Qualified] },
        // 下架 - 已入库
        { "StkTask_StkOutStock", [BdBarcodeStatus.In] },
        // 无源出库 - 已入库
        { "_StkOutStock", [BdBarcodeStatus.In] },
        // 其它出库 - 已入库
        { "_StkAdjustmentOut", [BdBarcodeStatus.In] },
        // 无源入库 - 初始, 已出库
        { "_StkInStock", [BdBarcodeStatus.Init, BdBarcodeStatus.Out] },
        // 其他入库 - 初始, 已出库
        { "_StkAdjustmentIn", [BdBarcodeStatus.Init, BdBarcodeStatus.Out] },
        // 调拨单 - 已入库
        { "_StkTransfer", [BdBarcodeStatus.In] },
    };

    /// <summary>
    /// PDA收货:只能选/扫描暂存库区的库位
    /// PDA上架:只能选/扫描存储类型库区的库位PDA
    /// 其它入库:只能选/扫描存储类型库区的库位PDA
    /// 其它出库:只能选/扫描存储类型库区的库位PDA
    /// 下架:只能选/扫描存储类型库区的库位PDA
    /// 调拨:只能选/扫描存储类型/集货库区的库位
    /// </summary>
    private readonly Dictionary<string, List<BdWhAreaType>> CheckWhAreaTypeKeys = new Dictionary<string, List<BdWhAreaType>>()
    {
        // 收货 - 暂存 
        { "StkInNotice_StkReceive", [BdWhAreaType.Temp] },
        // 上架 - 存储
        { "StkReceive_StkInStock", [BdWhAreaType.Storage] },
        // 下架 - 存储
        { "StkTask_StkOutStock", [BdWhAreaType.Storage] },
        // 无源出库 - 存储
        { "_StkOutStock", [BdWhAreaType.Storage] },
        // 其它出库 - 存储
        { "_StkAdjustmentOut", [BdWhAreaType.Storage] },
        // 无源入库 - 存储
        { "_StkInStock", [BdWhAreaType.Storage] },
        // 其他入库 - 存储
        { "_StkAdjustmentIn", [BdWhAreaType.Storage] },
        // 调拨单 - 存储, 集货
        { "_StkTransfer", [BdWhAreaType.Storage, BdWhAreaType.Consolidation] },
    };

    private IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 缓存服务
    /// </summary>
    private PdaDataCacheService DataCacheService { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider"></param>
    public PdaLocalBillCheckStatusService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        DataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
    }

    /// <summary>
    /// 检查条码
    /// </summary>
    /// <returns></returns>
    public void CheckBarcodeStatus(long tranId, PdaLocalBillScanBarcodeArgs args, BdBarcode barcode)
    {
        if (!CheckBarcodeKeys.TryGetValue(args.Key, out List<BdBarcodeStatus> status)) return;
        // 如果不在列表, 抛错
        if (!status.Contains(barcode.Status))
        {
            var schemas = GetEnumOptionNames<BdBarcodeStatus>();
            // 返回枚举名称列表
            var limitEnumNames = schemas.Where(r => CheckBarcodeKeys[args.Key].Contains(r.Key)).Select(r => r.Value).ToList();
            var barcodeEnumName = schemas.FirstOrDefault(r => r.Key == barcode.Status);
            throw Oops.Bah(L.Text["只能扫描条码状态为: [{0}], 条码[{1}]状态为: [{2}]", string.Join(",", limitEnumNames), args.BarcodeString, barcodeEnumName.Value]);
        }
    }

    /// <summary>
    /// 检查库区库位
    /// </summary>
    /// <param name="key"></param>
    /// <param name="tranId"></param>
    /// <param name="stockInfo"></param>
    public void CheckWhAreaLocStatus(string key, long tranId, PdaLocalBillStockInfo stockInfo)
    {
        if (!CheckWhAreaTypeKeys.TryGetValue(key, out List<BdWhAreaType> status)) return;
        if (!status.Contains(stockInfo.WhAreaType))
        {
            var schemas = GetEnumOptionNames<BdWhAreaType>();
            // 返回枚举名称列表
            var limitEnumNames = schemas.Where(r => CheckWhAreaTypeKeys[key].Contains(r.Key)).Select(r => r.Value).ToList();
            var barcodeEnumName = schemas.FirstOrDefault(r => r.Key == stockInfo.WhAreaType);
            throw Oops.Bah(L.Text["只能选择库区类型为: [{0}], 扫描/选择类型: [{1}]", string.Join(",", limitEnumNames), barcodeEnumName.Value]);
        }
    }

    /// <summary>
    /// 返回库区库位Lookup过滤字符串
    /// </summary>
    /// <returns></returns>
    public string GetWhAreaLocStatusString(string key, long tranId)
    {
        if (!CheckWhAreaTypeKeys.TryGetValue(key, out List<BdWhAreaType> status)) return "";
        var schemas = GetEnumOptionCodes<BdWhAreaType>();
        var limitEnumCodes = schemas.Where(r => CheckWhAreaTypeKeys[key].Contains(r.Key)).Select(r => r.Value).ToList();
        return string.Join(",", limitEnumCodes);
    }

    /// <summary>
    /// 返回枚举列表
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Dictionary<T, string> GetEnumOptionNames<T>() where T : System.Enum
    {
        if (!typeof(T).IsEnum) throw Oops.Bah(BarErrorCode.BarModel1000, typeof(T).Name);

        var dictDataService = App.GetService<SysDictDataService>();
        var dataList = dictDataService.GetDataList(typeof(T).Name).GetAwaiter().GetResult();

        return dataList
            .Select(item =>
            {
                var multiLangName = MultiLangUtil.GetMultiLangValue(item.MultiLangName);
                return new KeyValuePair<T, string>((T)System.Enum.Parse(typeof(T), item.Code + ""), multiLangName ?? item.Label);
            })
            .ToDictionary();
    }

    /// <summary>
    /// 返回枚举列表
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Dictionary<T, string> GetEnumOptionCodes<T>() where T : System.Enum
    {
        if (!typeof(T).IsEnum) throw Oops.Bah(BarErrorCode.BarModel1000, typeof(T).Name);

        var dictDataService = App.GetService<SysDictDataService>();
        var dataList = dictDataService.GetDataList(typeof(T).Name).GetAwaiter().GetResult();

        return dataList
            .Select(item => new KeyValuePair<T, string>((T)System.Enum.Parse(typeof(T), item.Code + ""), item.Code))
            .ToDictionary();
    }
}