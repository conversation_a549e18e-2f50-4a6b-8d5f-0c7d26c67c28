using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.Link;

namespace Neuz.Application.Pda.Proxy.Dto;

/// <summary>
/// 保存单个模型入参
/// </summary>
public class PdaLocalBillConfigSaveInput
{
    public string Key { get; set; }
    
    /// <summary>
    /// Bill参数配置
    /// </summary>
    public PdaLocalBillModelParams BillParams { get; set; }
    
    /// <summary>
    /// Bill显示模型
    /// </summary>
    public PdaLocalBillSchemaBase BillSchema { get; set; }
    
    /// <summary>
    /// 单据转换映射
    /// </summary>
    public LocalBillLinkParamBase LinkParam { get; set; }
}