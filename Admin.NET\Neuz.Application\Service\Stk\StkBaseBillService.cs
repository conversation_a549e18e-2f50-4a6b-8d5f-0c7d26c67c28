﻿namespace Neuz.Application;

/// <summary>
/// 仓储单据服务基类
/// </summary>
/// <typeparam name="TEntity"></typeparam>
public abstract partial class StkBaseBillService<TEntity> : BaseBillService<TEntity> where TEntity : BillEntityBase, new()
{
    /// <summary>
    /// 库存服务
    /// </summary>
    protected StkInventoryService InventoryService { get; }

    /// <summary>
    /// 条码档案服务
    /// </summary>
    protected BdBarcodeService BarcodeService { get; }

    /// <summary>
    /// 辅助属性值服务
    /// </summary>
    protected BdAuxPropValueService AuxPropValueService { get; }

    protected StkBaseBillService(IServiceProvider serviceProvider, SqlSugarRepository<TEntity> rep) : base(serviceProvider, rep)
    {
        InventoryService = serviceProvider.GetService<StkInventoryService>();
        BarcodeService = serviceProvider.GetService<BdBarcodeService>();
        AuxPropValueService = serviceProvider.GetService<BdAuxPropValueService>();
    }

    protected override IQueryDefine GetQueryDefine()
    {
        return new StkBillQueryDefine();
    }

    /// <summary>
    /// 上游单据刷新状态
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    /// <param name="getSrcBillKeys">获取当前单据的所有来源单据标识回调方法</param>
    /// <param name="getSrcBillIds">获取当前单据指定来源单据标识的所有来源单据Id回调方法</param>
    /// <param name="getSrcTaskIds">获取当前单据所有来源任务Id回调方法</param>
    protected void SrcBillRefreshStatus(TEntity entity, Func<TEntity, List<string>> getSrcBillKeys,
        Func<TEntity, string, List<long>> getSrcBillIds, Func<TEntity, List<long>> getSrcTaskIds)
    {
        var srcBillKeys = getSrcBillKeys(entity);
        if (srcBillKeys.Count == 0)
            return;

        var stkInNoticeService = App.GetService<StkInNoticeService>(ServiceProvider);
        var stkReceiveService = App.GetService<StkReceiveService>(ServiceProvider);
        var stkInStockService = App.GetService<StkInStockService>(ServiceProvider);
        var stkOutNoticeService = App.GetService<StkOutNoticeService>(ServiceProvider);
        var stkOutStockService = App.GetService<StkOutStockService>(ServiceProvider);
        var stkTransferNoticeService = App.GetService<StkTransferNoticeService>(ServiceProvider);
        var stkTaskService = App.GetService<StkTaskService>(ServiceProvider);

        foreach (var srcBillKey in srcBillKeys)
        {
            // 上游单据Id集合
            var srcBillIds = getSrcBillIds(entity, srcBillKey);
            switch (srcBillKey)
            {
                case nameof(StkInNotice):
                    stkInNoticeService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkReceive):
                    stkReceiveService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkInStock):
                    stkInStockService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkOutNotice):
                    stkOutNoticeService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkOutStock):
                    stkOutStockService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkTransferNotice):
                    stkTransferNoticeService.RefreshStatus(srcBillIds);
                    break;
                case nameof(StkStockCount):
                    // 库存盘点没有 RefreshStatus 方法，不做处理
                    break;
                case nameof(StkAdjustment):
                    // 库存调整单没有 RefreshStatus 方法，不做处理
                    break;
                default:
                    throw new NotImplementedException($"{srcBillKey} 上游单据刷新状态未实现");
            }
        }

        // 来源任务Id集合
        var srcTaskIds = getSrcTaskIds(entity);
        if (srcTaskIds.Count > 0)
            stkTaskService.RefreshStatus(srcTaskIds);
    }

    protected override IImportModel GetImportModel()
    {
        return new StkDefaultImportModel();
    }

    protected override IExportModel GetExportModel()
    {
        return new StkDefaultExportModel();
    }
}