# Changelog

All notable changes to this project will be documented in this file. See [conventional commits](https://www.conventionalcommits.org/) for commit guidelines.

---
## [unreleased]

### Documentation

- **(CHANGELOG.md)** update - ([0bbfc89](https://github.com/yiyungent/PluginCore/commit/0bbfc8955b7f6338db2125c78ec250e9eeeadcce)) - github-actions[bot]
- **(CHANGELOG.md)** update - ([4f6b47b](https://github.com/yiyungent/PluginCore/commit/4f6b47b3f86bfce4a8f660166837a7322c568d78)) - github-actions[bot]

### Miscellaneous Chores

- **(cliff.toml)** add - ([5614ef0](https://github.com/yiyungent/PluginCore/commit/5614ef024d644349095e19a0016bb23d989b0c90)) - yiyun

---
## [PluginCore.IPlugins.AspNetCore-v0.1.1](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins.AspNetCore-v0.1.0..PluginCore.IPlugins.AspNetCore-v0.1.1) - 2023-12-30

### Features

- **(src/**/*.cs)** //  License: Apache-2.0 -> //  License: GNU LGPLv3 - ([57366d3](https://github.com/yiyungent/PluginCore/commit/57366d3e2afdb8e20e94851aa8a09f1ee61b6d7e)) - yiyun
- **(src/**/*.cs)** //  Project: https://moeci.com/PluginCore -> //  Project: https://yiyungent.github.io/PluginCore - ([7420480](https://github.com/yiyungent/PluginCore/commit/742048065978c1b8597fab3d52f011db4247fbda)) - yiyun

### Build

- **(plugincore.iplugins.aspnetcore.csproj)** 0.1.0 -> 0.1.1 - ([338e919](https://github.com/yiyungent/PluginCore/commit/338e919c74dcff4199b82c6046a62847cc68beb3)) - yiyun

---
## [PluginCore.IPlugins.AspNetCore-v0.1.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.AspNetCore-v1.0.2..PluginCore.IPlugins.AspNetCore-v0.1.0) - 2023-02-15

### Build

- **(plugincore.iplugins.aspnetcore.csproj)** `<Version>0.1.0</Version>` - ([162d304](https://github.com/yiyungent/PluginCore/commit/162d304f6b74941701b5b799228a3061ef4ea6c2)) - yiyun

---
## [PluginCore.AspNetCore-v1.0.2](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins.AspNetCore-v0.0.1..PluginCore.AspNetCore-v1.0.2) - 2022-04-19

### Style

- add: copyright: *.cs - ([9643dce](https://github.com/yiyungent/PluginCore/commit/9643dce112861a440d63306cb555accbed3d5111)) - yiyun

---
## [PluginCore.IPlugins.AspNetCore-v0.0.1](https://github.com/yiyungent/PluginCore/compare/PluginCore-v1.0.0..PluginCore.IPlugins.AspNetCore-v0.0.1) - 2022-04-16

### Refactoring

- 1.提取出 PluginCore.AspNetCore,PluginCore.IPlugins.AspNetCore 2.提取出更多接口,可自由替换 - ([fffd8d9](https://github.com/yiyungent/PluginCore/commit/fffd8d91c23fd6e4a4d09cbf91975beb3cf7acf0)) - yiyun

### Build

- **(plugincore.iplugins.aspnetcore.csproj)** <Version>0.0.1</Version> - ([b907263](https://github.com/yiyungent/PluginCore/commit/b9072639d894904add2faf46216e29f902ddf32b)) - yiyun

<!-- generated by git-cliff -->
