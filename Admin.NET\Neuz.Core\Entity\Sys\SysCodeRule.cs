﻿namespace Neuz.Core.Entity;

/// <summary>
/// 编码规则
/// </summary>
[SugarTable(null, "编码规则")]
public class SysCodeRule : BdEntityBase
{
    /// <summary>
    /// 实体名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实体名称", Length = 50)]
    public string EntityName { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(SysCodeRuleEntry.Id))]
    public List<SysCodeRuleEntry> Entries { get; set; }
}