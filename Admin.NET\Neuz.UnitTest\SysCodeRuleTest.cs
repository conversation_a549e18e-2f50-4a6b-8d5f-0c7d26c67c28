﻿using Admin.NET.Core;
using Furion;
using Furion.DependencyInjection;
using Neuz.Application;
using Neuz.Core.Entity;
using Neuz.Core.Enum;
using Xunit;
using Xunit.Abstractions;
using SqlSugarExtension = Neuz.Application.SqlSugarExtension;

namespace Neuz.UnitTest;

public class SysCodeRuleTest
{
    private readonly ITestOutputHelper _output;

    public SysCodeRuleTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;
    }

    [Fact(DisplayName = "常量_空对象_返回常量")]
    public void ConstNullObject_Const()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "test",
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var billNo = service.GenerateNoAsync(1, null).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            Assert.Equal("test", billNo);
        });
    }

    [Fact(DisplayName = "常量_文本字段_空对象_返回常量")]
    public void ConstTextNullObject_Const()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "test",
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.TextField,
                        ElementName = "Specification", //规格型号
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var billNo = service.GenerateNoAsync(1, null).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            Assert.Equal("test", billNo);
        });
    }

    [Fact(DisplayName = "常量_文本字段_非空对象_返回常量_补位文本字段")]
    public void ConstTextObject_ConstAddChar()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "test",
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.TextField,
                        ElementName = "Specification", //规格型号
                        Length = 4,
                        AddChar = "0",
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var bdMaterial = new BdMaterial { Specification = "广汽" };

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            Assert.Equal("test00广汽", billNo);
        });
    }

    [Fact(DisplayName = "数值字段格式化右侧截断_文本_非空对象_返回数值字段_替代符")]
    public void FormatNumberTextObject_NumberReplace()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.NumberField,
                        ElementName = "ExpPeriod", //保质期
                        Format = "{0:N0}",
                        CutStyle = true,
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "-",
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.NumberField,
                        ElementName = "ExpPeriod", //保质期
                        Format = "C2",
                        Length = 2,
                        CutStyle = true,
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.TextField,
                        ElementName = "Name", //名称
                        Length = 4,
                        AddChar = "0",
                        ReChar = "*",
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var bdMaterial = new BdMaterial { ExpPeriod = 18000, Name = null, };

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            Assert.Equal("18,000-¥1*", billNo);
        });
    }

    //[Fact(DisplayName = "枚举字段_非空对象_返回枚举字段")]
    //public void EnumObject_Enum()
    //{
    //    Scoped.Create((_, s) =>
    //    {
    //        var idInput = new IdInput { Id = 1 };
    //        var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
    //        //初始化测试数据
    //        var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
    //        if (codeRule != null)
    //            service.DeleteAsync(idInput).GetAwaiter().GetResult();

    //        codeRule = new SysCodeRule
    //        {
    //            Id = 1,
    //            EntityName = "PrdMo",
    //            Number = "PrdMoBillNoRule",
    //            Name = "生产订单单据编号规则",
    //            SysCodeRuleEntries = new List<SysCodeRuleEntry>
    //            {
    //                new()
    //                {
    //                    ElementType = ElementType.EnumField,
    //                    ElementName = "MoStatus", //业务状态
    //                    CodeElement = true,
    //                },
    //            }
    //        };
    //        service.AddAsync(codeRule).GetAwaiter().GetResult();

    //        var prdMo = new PrdMo { MoStatus = MoStatus.Close };

    //        var billNo = service.GenerateNoAsync(1, prdMo).GetAwaiter().GetResult();

    //        service.DeleteAsync(idInput).GetAwaiter().GetResult();

    //        Assert.Equal("结案", billNo);
    //    });
    //}

    [Fact(DisplayName = "基础资料属性空_非空对象_返回基础资料属性")]
    public void NullBaseDataObject_BaseDataProp()
    {
        Scoped.Create((_, s) =>
        {
            var rep2 = App.GetService<SqlSugarRepository<BdUnit>>(s.ServiceProvider);

            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            var material = rep2.GetFirst(u => u.Id == 2);
            if (material != null)
                rep2.Delete(material);

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.BaseData,
                        ElementName = "Unit", //单位
                        ElementProperty = "Number", //编码
                        CodeElement = true,
                    },
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var bdMaterial = new BdMaterial() { UnitId = 2, }; //Unit 对象为 null，生成编码规则的时候会去查找关联的 Unit 资料

            var bdUnit = new BdUnit() { Id = 2, Number = "g", Name = "克" };
            rep2.Insert(bdUnit);

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();
            rep2.Delete(bdUnit);

            Assert.Equal("g", billNo);
        });
    }

    [Fact(DisplayName = "基础资料属性非空_非空对象_返回基础资料属性")]
    public void BaseDataObject_BaseDataProp()
    {
        Scoped.Create((_, s) =>
        {
            var rep2 = App.GetService<SqlSugarRepository<BdUnit>>(s.ServiceProvider);

            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            var material = rep2.GetFirst(u => u.Id == 2);
            if (material != null)
                rep2.Delete(material);

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.BaseData,
                        ElementName = "Unit", //单位
                        ElementProperty = "Number", //编码
                        CodeElement = true,
                    },
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var bdUnit = new BdUnit() { Id = 2, Number = "g", Name = "克" };
            rep2.Insert(bdUnit);

            var bdMaterial = new BdMaterial() { UnitId = 2, Unit = bdUnit };

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();
            rep2.Delete(bdUnit);

            Assert.Equal("g", billNo);
        });
    }

    [Fact(DisplayName = "基础资料属性的属性为空_非空对象_返回空")]
    public void BaseDataNullPropObject_Empty()
    {
        Scoped.Create((_, s) =>
        {
            var rep2 = App.GetService<SqlSugarRepository<BdUnit>>(s.ServiceProvider);

            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            var material = rep2.GetFirst(u => u.Id == 2);
            if (material != null)
                rep2.Delete(material);

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.BaseData,
                        ElementName = "Unit", //单位
                        ElementProperty = "Description", //描述
                        CodeElement = true,
                    },
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var bdUnit = new BdUnit() { Id = 2, Number = "g", Name = "克" };
            rep2.Insert(bdUnit);

            var bdMaterial = new BdMaterial() { UnitId = 2, Unit = bdUnit };

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();
            rep2.Delete(bdUnit);

            Assert.Equal("", billNo);
        });
    }

    [Fact(DisplayName = "日期字段格式化_流水号_非空对象_返回日期字段_流水号")]
    public void FormatDateSerialObject_DateSerial()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 1 };
            var idsInput = new IdsInput { Ids = new List<long> { 1 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 1,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.DateTimeField,
                        ElementName = "CreateTime", //创建时间
                        Format = "{0:yyyyMMdd}",
                        CodeOnlyBy = true,
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "-",
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.Serial,
                        Length = 6,
                        Seed = 1,
                        Increment = 1,
                        AddChar = "0",
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var serialRep = App.GetService<SqlSugarRepository<SysCodeRuleSerial>>(s.ServiceProvider);
            var serialEntity = serialRep.GetFirst(u => u.CodeRuleId == 1);
            var bdMaterial = new BdMaterial { CreateTime = new DateTime(2022, 1, 26, 1, 2, 3) };

            var billNo = service.GenerateNoAsync(1, bdMaterial).GetAwaiter().GetResult();

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();
            Assert.Equal($"20220126-{((serialEntity?.CurNumber ?? 0) + 1).ToString().PadLeft(6, '0')}", billNo);
        });
    }

    [Fact(DisplayName = "日期字段格式化_流水号_非空对象_并发_返回日期字段_流水号")]
    public void FormatDateSerialObjectConcurrency_DateSerial()
    {
        Scoped.Create((_, s) =>
        {
            var idInput = new IdInput { Id = 12 };
            var idsInput = new IdsInput { Ids = new List<long> { 12 } };
            var service = App.GetService<SysCodeRuleService>(s.ServiceProvider);
            //初始化测试数据
            var codeRule = service.GetAsync(idInput).GetAwaiter().GetResult();
            if (codeRule != null)
                service.DeleteAsync(idsInput).GetAwaiter().GetResult();

            codeRule = new SysCodeRule
            {
                Id = 12,
                EntityName = "BdMaterial",
                Number = "BdMaterialNumberRule",
                Name = "物料编号规则",
                Entries = new List<SysCodeRuleEntry>
                {
                    new()
                    {
                        ElementType = ElementType.DateTimeField,
                        ElementName = "CreateTime", //创建时间
                        Format = "{0:yyyyMMdd}",
                        CodeOnlyBy = true,
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.Const,
                        ConstValue = "-",
                        CodeElement = true,
                    },
                    new()
                    {
                        ElementType = ElementType.Serial,
                        Length = 6,
                        Seed = 1,
                        Increment = 1,
                        AddChar = "0",
                        CodeElement = true,
                    }
                }
            };
            service.AddAsync(codeRule).GetAwaiter().GetResult();

            var serialRep = App.GetService<SqlSugarRepository<SysCodeRuleSerial>>(s.ServiceProvider);
            var serialEntity = serialRep.GetFirst(u => u.CodeRuleId == 12);
            var bdMaterial = new BdMaterial { CreateTime = new DateTime(2022, 1, 27, 1, 2, 3) };

            Parallel.For(0, 100, (i, loopState) =>
            {
                Scoped.Create((_, s2) =>
                {
                    // 重置 SqlSugar 上下文
                    SqlSugarExtension.ResetContext();
                    _output.WriteLine($"{i,4} - {DateTime.Now:yyyy-MM-dd HH:mm:ss} - 开始生成单据编号");
                    var service2 = App.GetService<SysCodeRuleService>(s2.ServiceProvider);
                    var billNo = service2.GenerateNoAsync(12, bdMaterial).GetAwaiter().GetResult();
                    _output.WriteLine($"{i,4} - {DateTime.Now:yyyy-MM-dd HH:mm:ss} - 生成单据编号：{billNo}");
                });
            });

            var newSerialEntity = serialRep.GetFirst(u => u.CodeRuleId == 12);

            service.DeleteAsync(idsInput).GetAwaiter().GetResult();
            Assert.Equal($"20220127-{((serialEntity?.CurNumber ?? 0) + 100).ToString().PadLeft(6, '0')}",
                $"20220127-{(newSerialEntity?.CurNumber ?? 1).ToString().PadLeft(6, '0')}");
        });
    }
}