﻿using Furion.Xunit;
using Xunit;
using Xunit.Abstractions;

// 配置启动类类型，第一个参数是 Startup 类完整限定名，第二个参数是当前项目程序集名称
[assembly: TestFramework("Neuz.UnitTest.TestProgram", "Neuz.UnitTest")]

namespace Neuz.UnitTest;

public class TestProgram : TestStartup
{
    public TestProgram(IMessageSink messageSink) : base(messageSink)
    {
        //设置环境为 UnitTest

        //早期使用 Inject.Create() 是没有创建主机，使用控制台的环境参数，新版 Furion 已移除 Inject.Create()
        //Environment.SetEnvironmentVariable("NETCORE_ENVIRONMENT", "UnitTest");

        //主机使用的环境参数，如：
        Environment.SetEnvironmentVariable("DOTNET_ENVIRONMENT", "UnitTest");

        //如果设置 ASPNETCORE_ENVIRONMENT，会替代 DOTNET_ENVIRONMENT 的设置
        //https://docs.microsoft.com/zh-cn/aspnet/core/fundamentals/environments?view=aspnetcore-6.0
        //Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "UnitTest");

        //创建一个不阻塞的服务，随机分配一个端口
        Serve.Run(RunOptions.DefaultSilence, Serve.IdleHost.Urls);
    }
}