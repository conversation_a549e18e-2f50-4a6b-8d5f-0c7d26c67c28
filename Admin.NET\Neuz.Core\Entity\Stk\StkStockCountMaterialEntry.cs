﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存盘点物料范围
/// </summary>
[SugarTable(null, "库存盘点物料范围")]
public class StkStockCountMaterialEntry : EntryEntityBase
{
    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }
}