<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <DocumentationFile></DocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Service\Sys\SysModelBar\Presets\**" />
    <EmbeddedResource Include="Service\Sys\SysModelBar\Presets\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Lib.Harmony" Version="2.3.5" />
    <PackageReference Include="Stimulsoft.Reports.Web.NetCore" Version="2025.1.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Neuz.Core\Neuz.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Model\Bar\Dto\" />
    <Folder Include="Service\Contract\" />
    <Folder Include="Service\Pda\Bill\Model\Bill\Lookup\" />
    <Folder Include="Service\Stg\StgWebTable\Dto\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Kingdee.CDP.WebApi.SDK">
      <HintPath>..\Assemblies\Kingdee.CDP.WebApi.SDK.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
