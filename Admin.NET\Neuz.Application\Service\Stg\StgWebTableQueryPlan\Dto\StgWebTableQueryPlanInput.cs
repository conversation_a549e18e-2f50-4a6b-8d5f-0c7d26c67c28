﻿namespace Neuz.Application;

/// <summary>
/// Web端表格查询方案新增输入参数
/// </summary>
public class StgWebTableQueryPlanAddInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 方案名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// Web端表格查询方案保存输入参数
/// </summary>
public class StgWebTableQueryPlanSaveInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 方案名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 方案Json
    /// </summary>
    public string? Json { get; set; }
}

/// <summary>
/// Web端表格查询方案删除输入参数
/// </summary>
public class StgWebTableQueryPlanDeleteInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 方案名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// Web端表格查询方案获取输入参数
/// </summary>
public class StgWebTableQueryPlanGetInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 方案名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// Web端表格查询方案重命名输入参数
/// </summary>
public class StgWebTableQueryPlanRenameInput : StgWebTableQueryPlanAddInput
{
    /// <summary>
    /// 原始方案名称
    /// </summary>
    public string OriginName { get; set; }
}