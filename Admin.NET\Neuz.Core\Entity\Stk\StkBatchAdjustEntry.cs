﻿namespace Neuz.Core.Entity;

/// <summary>
/// 批号调整单明细
/// </summary>
[SugarTable(null, "批号调整单明细")]
public class StkBatchAdjustEntry : EsEntryEntityBase
{
    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 原批号
    /// </summary>
    [SugarColumn(ColumnDescription = "原批号", Length = 100)]
    public string? SrcBatchNo { get; set; }

    /// <summary>
    /// 原批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"SrcBatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile SrcBatchFile { get; set; }

    /// <summary>
    /// 原生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "原生产日期")]
    public DateTime? SrcProduceDate { get; set; }

    /// <summary>
    /// 原有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "原有效期至")]
    public DateTime? SrcExpiryDate { get; set; }

    /// <summary>
    /// 目标批号
    /// </summary>
    [SugarColumn(ColumnDescription = "目标批号", Length = 100)]
    public string? DestBatchNo { get; set; }

    /// <summary>
    /// 目标批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"DestBatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile DestBatchFile { get; set; }

    /// <summary>
    /// 目标生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "目标生产日期")]
    public DateTime? DestProduceDate { get; set; }

    /// <summary>
    /// 目标有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "目标有效期至")]
    public DateTime? DestExpiryDate { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库位Id")]
    public long WhLocId { get; set; }

    /// <summary>
    /// 库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhLocId))]
    [CustomSerializeFields]
    public BdWhLoc WhLoc { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer Container { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 明细备注
    /// </summary>
    [SugarColumn(ColumnDescription = "明细备注", Length = 255)]
    public string? EntryMemo { get; set; }

    /// <summary>
    /// 条码明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkBatchAdjustBarcodeEntry.RelEntryId))]
    public List<StkBatchAdjustBarcodeEntry> BarcodeEntries { get; set; }
}