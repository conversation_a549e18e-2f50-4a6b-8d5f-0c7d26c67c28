﻿namespace Neuz.Core.Entity;

/// <summary>
/// 基础资料基类实体，包含外部系统关联字段
/// </summary>
public abstract class EsBdEntityBase : BdEntityBase
{
    /// <summary>
    /// 外部系统编码
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统编码", Length = 80)]
    public string? EsNumber { get; set; }
    /// <summary>
    /// 外部系统主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统主键Id", Length = 50)]
    public string? EsId { get; set; }
}