﻿namespace Neuz.Application;

/// <summary>
/// 库存变更信息
/// </summary>
public class StkInvChange
{
    /// <summary>
    /// 事务类型（当 <see cref="Qty"/> 不为 0 时，需要有值）
    /// </summary>
    public StkInvLogType? InvLogType { get; set; }

    /// <summary>
    /// 锁定事务类型（当 <see cref="LockQty"/> 不为 0 时，需要有值）
    /// </summary>
    public StkInvLockLogType? InvLockLogType { get; set; }

    /// <summary>
    /// 预入库事务类型（当 <see cref="PreInQty"/> 不为 0 时，需要有值）
    /// </summary>
    public StkInvPreInLogType? InvPreInLogType { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    public long MaterialId { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    /// <remarks>
    /// （增加正数，扣减负数）
    /// </remarks>
    public decimal Qty { get; set; }

    /// <summary>
    /// 锁定数量
    /// </summary>
    /// <remarks>
    /// （增加正数，扣减负数）
    /// </remarks>
    public decimal LockQty { get; set; }

    /// <summary>
    /// 预入库数量
    /// </summary>
    /// <remarks>
    /// （增加正数，扣减负数）
    /// </remarks>
    public decimal PreInQty { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    public long OwnerId { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    public long? AuxPropValueId { get; set; }

    ///// <summary>
    ///// 仓库Id
    ///// </summary>
    //public long WarehouseId { get; set; }

    ///// <summary>
    ///// 库区Id
    ///// </summary>
    //public long WhAreaId { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    public long WhLocId { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    public long? ContainerId { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    public long UnitId { get; set; }

    ///// <summary>
    ///// 来源库区Id
    ///// </summary>
    ///// <remarks>
    ///// 多用于调拨、移位等场景，记录当前库区库位的库存由哪个库区库位转移过来或者计划到哪个库区库位
    ///// </remarks>
    //public long? SourceWhAreaId { get; set; }

    /// <summary>
    /// 来源库位Id
    /// </summary>
    /// <remarks>
    /// 多用于调拨、移位等场景，记录当前库区库位的库存由哪个库区库位转移过来或者计划到哪个库区库位
    /// </remarks>
    public long? SourceWhLocId { get; set; }

    /// <summary>
    /// 关联单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    public string RelBillKey { get; set; }

    /// <summary>
    /// 关联单据编号
    /// </summary>
    public string RelBillNo { get; set; }

    /// <summary>
    /// 关联单据类型
    /// </summary>
    public string RelBillType { get; set; }

    /// <summary>
    /// 关联序号
    /// </summary>
    /// <remarks>
    /// （单据分录的序号）
    /// </remarks>
    public int? RelSeq { get; set; }

    /// <summary>
    /// 关联单据主键Id
    /// </summary>
    public long? RelBillId { get; set; }

    /// <summary>
    /// 关联单据分录主键Id
    /// </summary>
    public long? RelBillEntryId { get; set; }

    /// <summary>
    /// 关联源单单据编号
    /// </summary>
    public string RelSourceBillNo { get; set; }

    /// <summary>
    /// 库存主键Id
    /// </summary>
    /// <remarks>
    /// 如果指定库存主键Id，则优先使用此值进行查找库存行
    /// </remarks>
    public long? InventoryId { get; set; }

    /// <summary>
    /// 关联任务Id
    /// </summary>
    /// <remarks>
    /// 分配时产生的日志需要记录任务Id，用于任务的取消分配操作，其他情况的变更无需填写
    /// </remarks>
    public long? RelTaskId { get; set; }
}