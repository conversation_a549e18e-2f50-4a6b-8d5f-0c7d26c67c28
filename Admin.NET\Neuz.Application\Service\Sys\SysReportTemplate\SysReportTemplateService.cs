﻿namespace Neuz.Application;

/// <summary>
/// 报表模板服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysReportTemplate", Order = 100)]
public partial class SysReportTemplateService : NeuzBaseService<SysReportTemplate>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 编码规则服务
    /// </summary>
    protected SysCodeRuleService CodeRuleService { get; }

    /// <summary>
    /// 缓存服务
    /// </summary>
    protected SysCacheService CacheService { get; }

    /// <summary>
    /// 报表模板服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public SysReportTemplateService(IServiceProvider serviceProvider, SqlSugarRepository<SysReportTemplate> rep) : base(serviceProvider, rep)
    {
        CodeRuleService = serviceProvider.GetService<SysCodeRuleService>();
        CacheService = serviceProvider.GetService<SysCacheService>();
    }

    /// <summary>
    /// 分页关键字查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("pageByKeyword")]
    public async Task<SqlSugarPagedList<SysReportTemplateOutput>> PageByKeywordAsync(SysReportTemplateInput2 input)
    {
        var entities = await Rep.Context
            .Queryable(
                Rep.AsQueryable()
                    .WhereIF(!string.IsNullOrEmpty(input.FuncKey), u => u.FuncKey == input.FuncKey)
                    .WhereIF(!string.IsNullOrEmpty(input.Keyword), u => u.Number.Contains(input.Keyword) || u.Name.Contains(input.Keyword))
                    .Select(u => new SysReportTemplateOutput(), true)
            )
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }

    /// <inheritdoc />
    protected override void OnBeforeAdd(SysReportTemplate entity)
    {
        base.OnBeforeAdd(entity);

        if (string.IsNullOrWhiteSpace(entity.Number))
        {
            entity.Number = GenerateNumberAsync(entity).Result;
        }
        else
        {
            var checkEntity = Rep.GetFirst(u => u.Number == entity.Number);
            if (checkEntity != null)
                throw Oops.Bah(BaseErrorCode.BaseBd1002, entity.Number);
        }
    }

    /// <inheritdoc />
    [NonAction]
    public override Task UpdateAsync(SysReportTemplate input)
    {
        throw new NotSupportedException();
    }

    /// <summary>
    /// 重命名报表模板
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("rename")]
    public async Task RenameAsync(UpdateSysReportTemplateInput input)
    {
        var query = Rep.AsQueryable();
        var entity = await query.FirstAsync(o => o.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        input.Adapt(entity);

        await Rep.UpdateAsync(entity);
    }

    /// <summary>
    /// 更新报表模板 Json
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("updateReportJson")]
    public async Task UpdateReportJsonAsync(UpdateSysReportTemplateInput2 input)
    {
        var query = Rep.AsQueryable();
        var entity = await query.FirstAsync(o => o.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        input.Adapt(entity);

        await Rep.UpdateAsync(entity);
    }

    /// <summary>
    /// 生成编码
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    protected virtual Task<string> GenerateNumberAsync(SysReportTemplate entity)
    {
        return CodeRuleService.GenerateNoAsync($"{entity.GetType().Name}_Number", entity);
    }
}