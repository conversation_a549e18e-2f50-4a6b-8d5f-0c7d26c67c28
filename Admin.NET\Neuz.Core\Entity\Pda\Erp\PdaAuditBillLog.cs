﻿namespace Neuz.Core.Entity.Pda.Erp;

/// <summary>
/// Pda审核单据日志
/// </summary>
[SugarTable(null, "Pda审核单据日志")]
[SugarIndex("index_{table}_T", nameof(TranId), OrderByType.Asc)]
public class PdaAuditBillLog : EntityTenant
{
    /// <summary>
    /// Pda提交事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Pda审核事务Id")]
    public long TranId { get; set; }

    /// <summary>
    /// 目标单Key
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单Key", Length = 50)]
    public string? TargetKey { get; set; }

    /// <summary>
    /// 保存单据内码
    /// </summary>
    [SugarColumn(ColumnDescription = "审核单据内码", Length = 500)]
    public string? AuditBillId { get; set; }

    /// <summary>
    /// 返回结果
    /// </summary>
    [SugarColumn(ColumnDescription = "返回结果", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Result { get; set; }

    /// <summary>
    /// 附加信息1
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息1", Length = 255)]
    public string? Extra1 { get; set; }

    /// <summary>
    /// 附加信息2
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息2", Length = 255)]
    public string? Extra2 { get; set; }

    /// <summary>
    /// 附加信息3
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息3", Length = 255)]
    public string? Extra3 { get; set; }
}