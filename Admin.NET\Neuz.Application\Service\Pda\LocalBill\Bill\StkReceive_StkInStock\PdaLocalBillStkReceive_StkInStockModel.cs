﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill.StkInNotice_StkReceive;

/// <summary>
/// 收货单->入库单
/// </summary>
public class PdaLocalBillStkReceive_StkInStockModel : PdaLocalBillModel<StkReceive, StkInStock, StkReceiveEntry, StkInStockEntry>
{
    public PdaLocalBillStkReceive_StkInStockModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkReceive_StkInStock";

    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "supplierName",
                Caption = L.Text["供应商"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdSupplierModel",
                    LookupDataKey = "ScanHead.Supplier",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("SupplierId", "Id"),
                        new PdaLookupMapping("SupplierNumber", "Number"),
                        new PdaLookupMapping("SupplierName", "Name"),
                    }
                }
            }
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkReceive",
            SourceTitle = L.Text["收货单"],
            DestKey = "StkInStock",
            DestTitle = L.Text["入库单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            FirstStockType = FirstStockType.Select,
            IsOverSourceQty = true,
            IsMultiSource = false,
            SummaryOperationFields =
            [
                new PdaLocalBillSummaryOperationField { FieldName = "Material.Number", ScanFieldName = "MaterialNumber" },
                new PdaLocalBillSummaryOperationField { FieldName = "LastExecBillId", ScanFieldName = "SrcBillId" },
                new PdaLocalBillSummaryOperationField { FieldName = "LastExecBillEntryId", ScanFieldName = "SrcBillEntryId" },
            ]
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkReceive>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkReceiveEntry>(ServiceProvider, u => u.WhAreaId)
            .AddOwnerFilter<StkReceiveEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .WhereIF(linkParam.SourceBillTypes.Count > 0, t1 => linkParam.SourceBillTypes.Contains(t1.BillType))
                .Where(t1 => t1.BillNo.Contains(input.Keyword) && t1.DocumentStatus == DocumentStatus.Approve && t1.Status != StkReceiveStatus.Finish && t1.Id == SqlFunc
                    .Subqueryable<StkReceiveEntry>()
                    .Where(t2 => t2.Id == t1.Id && t2.EntryStatus != StkReceiveEntryStatus.Finish)
                    .GroupBy(t2 => t2.Id)
                    .Select(t2 => t2.Id))
                .Where(t1 => SqlFunc.Subqueryable<StkReceiveEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(t1 => t1.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = r.BillNo, SubTitle = r.Date.ToString("yyyy-MM-dd") }).ToArray();
        return result;
    }
}