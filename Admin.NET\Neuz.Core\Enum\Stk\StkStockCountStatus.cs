﻿namespace Neuz.Core.Enum;

/// <summary>
/// 盘点状态
/// </summary>
public enum StkStockCountStatus
{
    /// <summary>
    /// 未生成盘点明细
    /// </summary>
    [Description("未生成盘点明细"), Theme("info")]
    NoDetail = 0,

    /// <summary>
    /// 已生成盘点明细
    /// </summary>
    [Description("已生成盘点明细"), Theme("warning")]
    GeneratedDetail = 1,

    /// <summary>
    /// 盘点结束
    /// </summary>
    [Description("盘点结束"), Theme("success")]
    Finish = 2,
}