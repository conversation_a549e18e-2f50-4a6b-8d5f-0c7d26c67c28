﻿namespace Neuz.Core.Entity;

/// <summary>
/// 第三方系统登录身份表
/// </summary>
[SugarTable(null, "第三方系统登录身份表")]
[SysTable]
public class SysThirdAccess : EntityBase
{
    /// <summary>
    /// 应用标识
    /// </summary>
    [SugarColumn(ColumnDescription = "应用标识", Length = 128)]
    public string AppKey { get; set; }

    /// <summary>
    /// 应用密钥
    /// </summary>
    [SugarColumn(ColumnDescription = "应用密钥", Length = 256)]
    public string AppSecret { get; set; }

    /// <summary>
    /// 绑定租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "绑定租户Id")]
    public long BindTenantId { get; set; }

    /// <summary>
    /// 绑定租户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(BindTenantId))]
    public SysTenant BindTenant { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(SysThirdAccessEntry.Id))]
    public List<SysThirdAccessEntry> Entries { get; set; }
}