using Furion.Localization;
using Neuz.Application.Erp.Barcode.Dto;
using Neuz.Application.Erp.Barcode.K3Cloud;
using Neuz.Application.Erp.Barcode.K3Cloud.Dto;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;

namespace Neuz.Application.Pda.InventoryQuery.LocalBill;

/// <summary>
/// WMS即时库存查询
/// </summary>
public class PdaLocalBillInventoryQueryModel : PdaInventoryQueryModel
{
    public PdaLocalBillInventoryQueryModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        // 扫描条码
        var barcodeService = App.GetService<BdBarcodeService>(ServiceProvider);
        var bar = barcodeService.GetBarcodeAsync(barcode).Result;
        if (bar == null) throw Oops.Bah(L.Text["找不到条码[{0}]", barcode]);
        // 把条码信息,保存到查询记录
        var pdaData = GetPdaData(tranId);
        pdaData.SourceCells["MaterialId"] = bar.MaterialId;
        pdaData.SourceCells["MaterialNumber"] = bar.Material.Number;
        pdaData.SourceCells["MaterialName"] = bar.Material.Name;
        pdaData.SourceCells["StockId"] = bar.WhAreaId;
        pdaData.SourceCells["StockNumber"] = bar.WhArea.Number;
        pdaData.SourceCells["StockName"] = bar.WhArea.Name;
        pdaData.SourceCells["StockLocId"] = bar.WhLocId;
        pdaData.SourceCells["StockLocNumber"] = bar.WhLoc.Number;
        pdaData.SourceCells["StockLocName"] = bar.WhLoc.Name;
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    public override string Key { get; } = nameof(PdaLocalBillInventoryQueryModel);

    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    protected override async Task SourceSearch(long tranId, string valueKey)
    {
        var pdaData = GetPdaData(tranId);
        var inventoryService = App.GetService<StkInventoryService>(ServiceProvider);
        var data = await inventoryService.CustomQueryPage(new CustomPageInput
        {
            FieldNames =
            [
                "Id", "MaterialId", "MaterialNumber", "MaterialName", "MaterialSpecification", "BatchNo", "ProduceDate", "ExpiryDate",
                "WhAreaId", "WhAreaNumber", "WhAreaName", "WhLocId", "WhLocNumber", "WhLocName", "Qty",
                "UnitId", "UnitNumber", "UnitName"
            ],
            Conditions =
            [
                new QueryCondition { FieldName = "MaterialNumber", Op = DataQueryOp.Like, Value = pdaData.SourceCells["MaterialNumber"] },
                new QueryCondition { FieldName = "WhAreaNumber", Op = DataQueryOp.Like, Value = pdaData.SourceCells["StockNumber"] },
                new QueryCondition { FieldName = "WhLocNumber", Op = DataQueryOp.Like, Value = pdaData.SourceCells["StockLocNumber"] },
            ],
            Page = pdaData.SourcePage.Page,
            PageSize = pdaData.SourcePage.PageSize,
            Field = "MaterialNumber",
        });
        pdaData.SourcePage.Total = data.Total;
        pdaData.Sources = data.Items.Adapt<List<Dictionary<string, object>>>();
    }

    protected override PdaApiVanCell GetSourceRefreshShow(Dictionary<string, object> source)
    {
        var cell = new PdaApiVanCell
        {
            Id = source["Id"] + "",
            Title = $"[{source["MaterialNumber"]}]{source["MaterialName"]}",
            Label = $"{source["BatchNo"]}{(string.IsNullOrEmpty(source["ProduceDate"] + "") ? "" : "  " + Convert.ToDateTime(source["ProduceDate"]).ToString("yyyy-MM-dd"))}",
            Value = $"[{source["WhAreaNumber"]}]{source["WhAreaName"]} [{source["WhLocNumber"]}]{source["WhLocName"]}",
            SubLabel = $"{source["MaterialSpecification"]}",
            SubValue = $"{Convert.ToDecimal(source["Qty"]).ToString(PdaHelper.DecimalPrecision)} {source["UnitName"]}"
        };
        return cell;
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceCells.Properties.Clear();
        pdaShow.SourceCells = pdaData.SourceCells.Adapt<ExtensionObject>();
        // 显示物料
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["MaterialNumber"] + ""))
            pdaShow.SourceCells["Material"] = $"[{pdaShow.SourceCells["MaterialNumber"]}]{pdaShow.SourceCells["MaterialName"]}";
        // 显示仓库
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["StockNumber"] + ""))
            pdaShow.SourceCells["Stock"] =
                $"[{pdaShow.SourceCells["StockNumber"]}]{pdaShow.SourceCells["StockName"]} - [{pdaShow.SourceCells["StockLocNumber"]}]{pdaShow.SourceCells["StockLocName"]}";
        pdaShow.SourcePage = pdaData.SourcePage.Adapt<PdaApiPagination>();
        pdaShow.Sources.Clear();
        foreach (Dictionary<string, object> source in pdaData.Sources)
        {
            pdaShow.Sources.Add(GetSourceRefreshShow(new Dictionary<string, object>(source, StringComparer.OrdinalIgnoreCase)));
        }
    }

    class PdaSchema : IPdaInventorySchema
    {
        public string Title { get; set; } = L.Text["即时库存查询"];

        /// <summary>
        /// 源单列
        /// </summary>
        public List<PdaColumn> SourceCells { get; set; } = new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "material",
                Caption = L.Text["物料"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Material",
                Lookup = new PdaLookup
                {
                    LookupKey = "BdMaterial",
                    LookupDataKey = "sources.Material",
                    LookupMappings = new List<PdaLookupMapping>
                    {
                        new("MaterialId", "ID"),
                        new("MaterialNumber", "Number"),
                        new("MaterialName", "Name"),
                    }
                }
            },
            new PdaColumn
            {
                Fieldname = "stock",
                Caption = L.Text["仓库仓位"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Stock",
                Lookup = new PdaLookup
                {
                    LookupKey = "PdaLocalBdStockLocModel",
                    LookupDataKey = "sources.Stock",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new("StockId", "WhAreaId"),
                        new("StockNumber", "WhAreaNumber"),
                        new("StockName", "WhAreaName"),
                        new("StockLocId", "WhLocId"),
                        new("StockLocNumber", "WhLocNumber"),
                        new("StockLocName", "WhLocName"),
                    }
                }
            },
        };
    }
}