namespace Neuz.Core.Entity;

/// <summary>
/// pda用户模型配置
/// 基于用户用到的部分配置
/// </summary>
[SugarTable(null, "用户模型配置")]
[SugarIndex("index_{table}_M", nameof(ModelKey), OrderByType.Asc)]
public class SysModelPdaBarUc : EntityBase
{
    /// <summary>
    /// 模型Key
    /// </summary>
    [SugarColumn(ColumnDescription = "模型Key", Length = 100)]
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型内容
    /// </summary>
    [SugarColumn(ColumnDescription = "模型内容", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ModelContent { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long UserId { get; set; }

    /// <summary>
    /// 其它组合Key
    /// </summary>
    [SugarColumn(ColumnDescription = "其它组合Key", Length = 100)]
    public string? OtherKey { get; set; }
}