﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Pda.Bill.Model.Basic.Cloud;

namespace Neuz.Application.Pda.StockCount.Cloud.Basic;

/// <summary>
/// 物料盘点作业
/// </summary>
public class PdaCloudStockCountModel : PdaCloudBillModel
{
    /// <summary>
    /// 物料盘点作业
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="k3CloudInterface"></param>
    public PdaCloudStockCountModel(IServiceProvider serviceProvider, K3CloudInterface k3CloudInterface) : base(serviceProvider, k3CloudInterface)
    {
    }

    /// <inheritdoc />
    protected override string FormId => "STK_StockCountInput";

    /// <inheritdoc />
    protected override string OrgIdFieldKey => "FStockOrgId";

    /// <inheritdoc />
    public override List<string> HeadSelect { get; } = new List<string>
    {
        "FID AS SourceBillId",
        "FBillNo AS SourceBillNo",
        "FDate",
        "FStockOrgId AS OrgId",
        "FStockOrgId.FNumber AS OrgNumber",
        "FStockOrgId.FName AS OrgName",
    };

    /// <inheritdoc />
    public override List<string> DetailSelect { get; } = new List<string>
    {
        "FID AS SourceBillId",
        "FBillEntry_FEntryID AS SourceBillEntryId",
        "FBillNo AS SourceBillNo",
        "FStockId AS StockId",
        "FStockId.FNumber AS StockNumber",
        "FStockId.FName AS StockName",
        "FStockLocId AS StockLocId",
        "FStockLocId.FNumber AS StockLocNumber",
        "FStockLocId.FName AS StockLocName",
        "FMaterialId.FMasterId AS MaterialId",
        "FMaterialId.FNumber AS MaterialNumber",
        "FMaterialId.FName AS MaterialName",
        "FMaterialId.FSpecification AS MaterialSpec",
        "FLot.FNumber AS BatchNo",
        "FUnitID AS UnitId",
        "FUnitID.FNumber AS UnitNumber",
        "FUnitID.FName AS UnitName",
        "FAcctQty AS Qty",
        "FCountQty AS CheckQty",
        "FProduceDate AS ProduceDate",
        "FExpiryDate AS ExpiryDate",
        "FAuxpropId AS AuxPropId",
        "FAuxpropId.FNumber AS AuxPropNumber",
        "FAuxpropId.FName AS AuxPropName",
    };

    /// <inheritdoc />
    protected override List<QueryFilter> GetAdditionalQueryFilters(long tranId)
    {
        var list = base.GetAdditionalQueryFilters(tranId);
        list.Add(new QueryFilter("FDocumentStatus", QueryType.Equals, "A"/*创建*/, "FDocumentStatusGroup"));
        list.Add(new QueryFilter("FDocumentStatus", QueryType.Equals, "D"/*重新审核*/, "FDocumentStatusGroup"));

        return list;
    }
}