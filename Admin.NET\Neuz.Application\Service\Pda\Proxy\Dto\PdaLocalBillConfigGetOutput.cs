using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.Link;

namespace Neuz.Application.Pda.Proxy.Dto;

/// <summary>
/// 返回单个设置
/// </summary>
public class PdaLocalBillConfigGetOutput
{
    public string Key { get; set; }
    
    /// <summary>
    /// 功能点名称
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// Bill参数配置
    /// </summary>
    public PdaLocalBillModelParams BillParams { get; set; }
    
    /// <summary>
    /// Bill显示模型
    /// </summary>
    public IPdaLocalBillSchema BillSchema { get; set; }
    
    /// <summary>
    /// 单据转换映射
    /// </summary>
    public ILocalBillLinkParam LinkParam { get; set; }
}