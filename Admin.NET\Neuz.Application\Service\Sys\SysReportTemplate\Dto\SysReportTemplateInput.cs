﻿namespace Neuz.Application;

/// <summary>
/// 报表模板输入参数
/// </summary>
public class SysReportTemplateInput2 : BasePageInput
{
    /// <summary>
    /// 功能点Key
    /// </summary>
    public string FuncKey { get; set; }

    /// <summary>
    /// 关键字
    /// </summary>
    public string Keyword { get; set; }
}

/// <summary>
/// 更新报表模板输入参数
/// </summary>
public class UpdateSysReportTemplateInput
{
    /// <summary>
    /// Id主键
    /// </summary>
    [Required(ErrorMessage = "Id主键不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public string Name { get; set; }
}

/// <summary>
/// 更新报表模板输入参数2
/// </summary>
public class UpdateSysReportTemplateInput2
{
    /// <summary>
    /// Id主键
    /// </summary>
    [Required(ErrorMessage = "Id主键不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 报表模板 Json
    /// </summary>
    [Required(ErrorMessage = "报表模板 Json 不能为空")]
    public string Json { get; set; }
}

/// <summary>
/// 报表模板导出 Pdf 输入参数
/// </summary>
public class SysReportTemplateExportPdfInput
{
    /// <summary>
    /// 报表模板Id
    /// </summary>
    public long TemplateId { get; set; }

    /// <summary>
    /// 实现 <see cref="IReportData"/> 接口的类型名称（命名空间.类名, 程序集名）
    /// </summary>
    public string TypeofIReportData { get; set; }

    /// <summary>
    /// <see cref="string"/> 主键集合输入参数
    /// </summary>
    public List<string> Ids { get; set; }
}