﻿namespace Neuz.Core.Extension;

/// <summary>
/// 集合扩展操作
/// </summary>
public static class IEnumerableExtension
{
    /// <summary>
    /// 将数量分配到集合中并返回分配结果
    /// </summary>
    /// <param name="list">集合</param>
    /// <param name="getQty">获取集合项目中的数量</param>
    /// <param name="allocateQty">需要分配的数量</param>
    /// <param name="remainAllocateQty">剩余未分配的数量</param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static Dictionary<T, decimal> AllocateQtyToList<T>(this IEnumerable<T> list, Func<T, decimal> getQty, decimal allocateQty, out decimal remainAllocateQty)
    {
        var dic = new Dictionary<T, decimal>();

        remainAllocateQty = allocateQty;
        foreach (var item in list)
        {
            var qty = getQty(item);
            var handleQty = Math.Min(qty, remainAllocateQty);
            remainAllocateQty -= handleQty;
            dic.Add(item, handleQty);

            if (remainAllocateQty == 0)
                break;
        }

        return dic;
    }
}