﻿namespace Neuz.Core.Entity;

/// <summary>
/// 辅助属性
/// </summary>
[SugarTable(null, "辅助属性")]
public class BdAuxProp : EsBdEntityBase
{
    /// <summary>
    /// 辅助属性类别Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性类别Id")]
    public long AuxPropTypeId { get; set; }

    /// <summary>
    /// 辅助属性类别
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropTypeId))]
    [CustomSerializeFields]
    public BdAuxPropType AuxPropType { get; set; }
}