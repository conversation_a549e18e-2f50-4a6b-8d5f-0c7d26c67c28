﻿using Furion.Localization;

namespace Admin.NET.Core.Service;

/// <summary>
/// 系统授权校验相关服务
/// </summary>
public class SysLicenseCheckService : ITransient
{
    private readonly SysLicenseService _sysLicenseService;
    private readonly SysOnlineUserService _sysOnlineUserService;
    private readonly SqlSugarRepository<SysUser> _sysUserRep;

    public SysLicenseCheckService(SysLicenseService sysLicenseService, SysOnlineUserService sysOnlineUserService, SqlSugarRepository<SysUser> sysUserRep)
    {
        _sysLicenseService = sysLicenseService;
        _sysOnlineUserService = sysOnlineUserService;
        _sysUserRep = sysUserRep;
    }

    /// <summary>
    /// 在线用户数授权校验
    /// </summary>
    /// <param name="tenantId">租户Id</param>
    /// <param name="userId">用户Id</param>
    /// <returns></returns>
    public (bool, string) OnlineUserCheck(long tenantId, long userId)
    {
        var isValid = true;
        var errMsg = "";

        //租户在线数，不包含当前连接
        var tenantConnCount = _sysOnlineUserService.GetTenantConnCount(tenantId).GetAwaiter().GetResult();
        var licenseInfo = _sysLicenseService.GetVerificationLicenseInfo();
        if (licenseInfo.MaxConnectCount != 0 && tenantConnCount >= licenseInfo.MaxConnectCount)
        {
            //超级管理员和供应商不限制
            var curUser = _sysUserRep.GetFirst(u => u.Id == userId);
            if (curUser.AccountType != AccountTypeEnum.SuperAdmin&&curUser.AccountType!=AccountTypeEnum.Supplier)
            {
                isValid = false;
                errMsg = L.Text["已超出允许最大连接数，请联系系统管理员"];
            }
        }

        return (isValid, errMsg);
    }
}