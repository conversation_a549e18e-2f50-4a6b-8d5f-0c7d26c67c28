﻿using Furion.Localization;
using Magicodes.ExporterAndImporter.Excel;

namespace Neuz.Application;

/// <summary>
/// 缺料报表服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkMaterialShortageReport", Order = 100)]
public class StkMaterialShortageReportService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly SqlSugarRepository<StkOutNotice> _rep;

    /// <summary>
    /// 缺料报表服务
    /// </summary>
    public StkMaterialShortageReportService(IServiceProvider serviceProvider, SqlSugarRepository<StkOutNotice> rep)
    {
        _serviceProvider = serviceProvider;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("page")]
    public async Task<SqlSugarPagedList<StkMaterialShortageReportOutput>> PageAsync(StkMaterialShortageReportInput input)
    {
        var entities = await _rep.Context
            .AddWarehouseFilter<StkOutNotice>(_serviceProvider, u => u.WarehouseId) // 仓库权限
            .AddOwnerFilter<StkOutNoticeEntry>(_serviceProvider, u => u.OwnerId) // 货主权限
            .Queryable(
                _rep.AsQueryable()
                    .InnerJoin<StkOutNoticeEntry>((t1, t2) => t1.Id == t2.Id)
                    .InnerJoin<BdMaterial>((t1, t2, t3) => t2.MaterialId == t3.Id)
                    .LeftJoin<BdWarehouse>((t1, t2, t3, t4) => t1.WarehouseId == t4.Id)
                    .LeftJoin<BdOwner>((t1, t2, t3, t4, t5) => t2.OwnerId == t5.Id)
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber), (t1, t2, t3, t4, t5) => t3.Number.Contains(input.MaterialNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialName), (t1, t2, t3, t4, t5) => t3.Name.Contains(input.MaterialName))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber), (t1, t2, t3, t4, t5) => t4.Number.Contains(input.WarehouseNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseName), (t1, t2, t3, t4, t5) => t4.Name.Contains(input.WarehouseName))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerNumber), (t1, t2, t3, t4, t5) => t5.Number.Contains(input.OwnerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerName), (t1, t2, t3, t4, t5) => t5.Name.Contains(input.OwnerName))
                    .WhereIF(!string.IsNullOrEmpty(input.BatchNo), (t1, t2, t3, t4, t5) => t2.BatchNo.Contains(input.BatchNo))
                    .Where((t1, t2, t3, t4, t5) => (t1.AllocateStatus == StkOutNoticeAllocateStatus.Allocating || t1.AllocateStatus == StkOutNoticeAllocateStatus.AllocateFail) &&
                                                   t2.AllocateQty < t2.Qty)
                    .Select((t1, t2, t3, t4, t5) => new StkMaterialShortageReportOutput
                    {
                        Id = t1.Id,
                        BillNo = t1.BillNo,
                        BillType = t1.BillType,
                        WarehouseId = t4.Id,
                        WarehouseNumber = t4.Number,
                        WarehouseName = t4.Name,
                        OwnerId = t5.Id,
                        OwnerNumber = t5.Number,
                        OwnerName = t5.Name,
                        MaterialId = t3.Id,
                        MaterialNumber = t3.Number,
                        MaterialName = t3.Name,
                        BatchNo = t2.BatchNo,
                        ProduceDate = t2.ProduceDate,
                        ExpiryDate = t2.ExpiryDate,
                        Qty = t2.Qty,
                        AllocateQty = t2.AllocateQty,
                        OutQty = t2.OutQty,
                        RemainOutQty = t2.RemainOutQty,
                    })
            )
            .Distinct()
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);
        return entities;
    }

    /// <summary>
    /// 按查询条件导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("exportByQuery")]
    public async Task<FileContentResult> ExportByQuery(StkMaterialShortageReportInput input)
    {
        input.Page = 1;
        input.PageSize = 50000;
        var output = await PageAsync(input);
        var exporter = new ExcelExporter();
        byte[] bytes = await exporter.ExportAsByteArray(output.Items.ToList());
        var fileName = L.Text["缺料报表_{0}.xlsx", DateTimeOffset.Now.ToString("yyyyMMddHHmmss")];
        return new FileContentResult(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") { FileDownloadName = fileName };
    }
}