﻿using Magicodes.ExporterAndImporter.Core;
using static System.Int32;

namespace Neuz.Application;

/// <summary>
/// 物料有效期报表输入参数
/// </summary>
public class StkMaterialValidityReportInput : BasePageInput
{
    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 货主编码
    /// </summary>
    public string OwnerNumber { get; set; }

    /// <summary>
    /// 货主名称
    /// </summary>
    public string OwnerName { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    public string WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    public string WarehouseName { get; set; }
}