﻿namespace Neuz.Core.Enum;

/// <summary>
/// 库存锁定事务类型
/// </summary>
public enum StkInvLockLogType
{
    /// <summary>
    /// 任务分配加锁定
    /// </summary>
    [Description("任务分配加锁定")]
    TaskAllocatePlus = 1,

    /// <summary>
    /// 拣货减锁定
    /// </summary>
    [Description("拣货减锁定")]
    PickMinus = 2,

    /// <summary>
    /// 拣货加锁定
    /// </summary>
    [Description("拣货加锁定")]
    PickPlus = 3,

    /// <summary>
    /// 发运减锁定
    /// </summary>
    [Description("发运减锁定")]
    ShipMinus = 4,

    /// <summary>
    /// 调拨出减锁定
    /// </summary>
    [Description("调拨出减锁定")]
    TransferOutMinus = 5,

    /// <summary>
    /// 手动关闭减锁定
    /// </summary>
    [Description("手动关闭减锁定")]
    ManualCloseMinus = 6,

    // 非标准流程，从1001开始
}