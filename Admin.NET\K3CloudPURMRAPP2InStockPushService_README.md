# K3CloudPURMRAPP2InStockPushService 实现说明

## 概述

`K3CloudPURMRAPP2InStockPushService` 是一个新的K3Cloud推送服务，用于将入库单数据推送到K3Cloud的PUR_MRAPP2表单。该服务的核心特性是通过明细批次查询收货单，获取供应商和通知单外部编号信息，并根据供应商分组生成不同的ERP单据。

## 主要功能

### 1. 批次关联查询
- **输入**：入库单明细的物料ID和批次号
- **处理**：通过批次号和物料ID查询对应的收货单明细
- **输出**：获取收货单及其供应商信息

### 2. 供应商信息获取
- 从收货单主表获取供应商信息
- 从收货单明细获取通知单外部编号
- 将供应商信息附加到推送数据中

### 3. 数据增强
- 原始入库单数据 + 收货单供应商信息
- 支持通知单外部编号传递
- 保持原有的入库单业务逻辑

## 技术实现

### 核心类结构

```csharp
[Injection(Named = "K3Cloud:PurMrapp2InStock")]
public class K3CloudPURMRAPP2InStockPushService : K3CloudBasePushService<StkInStockPushData>
```

### 关键方法

#### 1. QueryLocalObject
```csharp
public override async Task<List<StkInStockPushData>> QueryLocalObject(...)
```
- 获取基础入库单数据
- 通过批次查询收货单信息
- 创建增强的推送数据

#### 2. QueryReceiveByBatch
```csharp
private async Task<ReceiveInfo> QueryReceiveByBatch(long materialId, string batchNo)
```
- 通过物料ID和批次号查询收货单明细
- 获取收货单主表及供应商信息
- 返回收货单信息封装

#### 3. AfterSetPushObject
```csharp
protected override async Task AfterSetPushObject(...)
```
- 设置供应商信息到推送对象
- 设置通知单外部编号
- 配置其他业务字段

## 数据流程

```
入库单明细 → 批次号+物料ID → 收货单明细 → 收货单主表 → 供应商信息
    ↓
推送数据增强 → K3Cloud PUR_MRAPP2 → 根据供应商分组的ERP单据
```

## 配置信息

- **服务注册名称**：`K3Cloud:PurMrapp2InStock`
- **目标表单ID**：`PUR_MRAPP2`
- **源单表单ID**：无（无源单推送）
- **转换规则ID**：无
- **支持的单据类型**：`SCRKD`（生产入库单）、`CGRKD`（采购入库单）

## 推送字段映射

### 主表字段
- `FSupplierId.FNumber`：供应商编码
- `F_PBGK_Supplier`：供应商名称
- `F_PBGK_NoticeEsBillNo`：通知单外部编号
- `FDate`：单据日期
- `F_PBGK_PdaUser`：创建用户
- `F_PBGK_BillType`：单据类型

### 明细字段
- `[FBillEntry].FMaterialId.FNumber`：物料编码
- `[FBillEntry].FQty`：数量
- `[FBillEntry].FUnitID.FNumber`：单位编码
- `[FBillEntry].FLot.FNumber`：批次号
- `[FBillEntry].F_PBGK_kc7Seq`：序号
- `[FBillEntry].FProduceDate`：生产日期
- `[FBillEntry].FExpiryDate`：有效期至
- `[FBillEntry]._SourceBillId_`：源单ID
- `[FBillEntry]._SourceBillEntryId_`：源单明细ID

## 使用场景

1. **生产入库单推送**：将生产入库的物料信息推送到K3Cloud，同时携带原始收货单的供应商信息
2. **采购入库单推送**：将采购入库的物料信息推送到K3Cloud，保持供应商追溯链
3. **供应商分组处理**：系统会自动根据供应商信息对推送数据进行分组，生成不同的ERP单据

## 注意事项

1. **批次匹配**：确保入库单明细的批次号能够在收货单明细中找到对应记录
2. **供应商信息**：如果找不到对应的收货单，供应商信息将为空
3. **数据完整性**：推送前会验证必要的字段信息
4. **错误处理**：如果批次查询失败，会继续处理但不包含供应商信息

## 扩展性

该服务设计具有良好的扩展性：
- 可以轻松添加更多的关联查询逻辑
- 支持自定义字段映射
- 可以扩展到其他类型的单据推送

## 测试建议

1. **单元测试**：测试批次查询逻辑
2. **集成测试**：测试完整的推送流程
3. **数据验证**：验证推送到K3Cloud的数据完整性
4. **性能测试**：测试大批量数据的推送性能
