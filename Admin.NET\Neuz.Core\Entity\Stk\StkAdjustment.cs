﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存调整单
/// </summary>
[SugarTable(null, "库存调整单")]
public class StkAdjustment : EsBillEntityBase
{
    /// <summary>
    /// 单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "单据日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "单据类型", Length = 80)]
    public string BillType { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 部门Id
    /// </summary>
    [SugarColumn(ColumnDescription = "部门Id")]
    public long? DepartmentId { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DepartmentId))]
    [CustomSerializeFields]
    public BdDepartment Department { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [SugarColumn(ColumnDescription = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 出库类型
    /// </summary>
    [SugarColumn(ColumnDescription = "出库类型", Length = 255)]
    public string? OutType { get; set; }

    /// <summary>
    /// 费用项目
    /// </summary>
    [SugarColumn(ColumnDescription = "费用项目", Length = 255)]
    public string? Expense { get; set; }

    /// <summary>
    /// 合同项目名称
    /// </summary>
    [SugarColumn(ColumnDescription = "合同项目名称", Length = 255)]
    public string? ContractProject { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnDescription = "项目名称", Length = 255)]
    public string? ProjectName { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "客户Id")]
    public long? CustomerId { get; set; }

    /// <summary>
    /// 客户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(CustomerId))]
    [CustomSerializeFields]
    public BdCustomer Customer { get; set; }

    /// <summary>
    /// 领料人Id
    /// </summary>
    [SugarColumn(ColumnDescription = "领料人Id")]
    public long? EmployeeId { get; set; }

    /// <summary>
    /// 领料人
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(EmployeeId))]
    [CustomSerializeFields]
    public BdEmployee Employee { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAdjustmentEntry.Id))]
    public List<StkAdjustmentEntry> Entries { get; set; }

    /// <summary>
    /// 资产出库类别
    /// </summary>
    [SugarColumn(ColumnDescription = "资产出库类别", Length = 255)]
    public string? AssetCategory { get; set; }
}