﻿namespace Neuz.Core.Enum;

/// <summary>
/// 条码状态
/// </summary>
public enum BdBarcodeStatus
{
    /// <summary>
    /// 初始
    /// </summary>
    [Description("初始"), Theme("info")]
    Init = 0,

    /// <summary>
    /// 作废
    /// </summary>
    [Description("作废"), Theme("danger")]
    Disuse = 1,

    /// <summary>
    /// 待检
    /// </summary>
    [Description("待检"), Theme("warning")]
    WaitInspect = 2,

    /// <summary>
    /// 合格
    /// </summary>
    [Description("合格"), Theme("success")]
    Qualified = 3,

    /// <summary>
    /// 不合格
    /// </summary>
    [Description("不合格"), Theme("danger")]
    UnQualified = 4,

    /// <summary>
    /// 已入库
    /// </summary>
    [Description("已入库"), Theme("primary")]
    In = 5,

    /// <summary>
    /// 已出库
    /// </summary>
    [Description("已出库"), Theme("info")]
    Out = 6,
}