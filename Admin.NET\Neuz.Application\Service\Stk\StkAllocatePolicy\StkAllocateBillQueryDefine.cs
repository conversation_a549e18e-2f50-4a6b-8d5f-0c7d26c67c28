﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 分配策略单据查询定义接口实现
/// </summary>
public class StkAllocateBillQueryDefine : DefaultQueryDefine
{
    /// <summary>
    /// 忽略的基础资料属性名称列表
    /// </summary>
    private readonly List<string> _ignoreBdPropertyNameList = new()
    {
        nameof(BdEntityBase.Id),
        nameof(BdEntityBase.CreateTime),
        nameof(BdEntityBase.CreateUserId),
        nameof(BdEntityBase.CreateUserName),
        nameof(BdEntityBase.IsDelete),
        nameof(BdEntityBase.TenantId),
        nameof(BdEntityBase.UpdateTime),
        nameof(BdEntityBase.UpdateUserId),
        nameof(BdEntityBase.UpdateUserName),
        nameof(BdEntityBase.IsForbid),
        nameof(BdEntityBase.ForbiddenTime),
        nameof(BdEntityBase.ForbiddenUserId),
        nameof(BdEntityBase.ForbiddenUserName),
        nameof(BdEntityBase.Description),
    };

    protected override List<EntityColumnInfo> GetQueryTableInfoEntityColumnInfos(ISqlSugarClient context, QueryTableInfo tableInfo)
    {
        var entityInfo = context.EntityMaintenance.GetEntityInfo(tableInfo.EntityType);

        // 根数据表，直接使用父类实现
        if(tableInfo.IsRootTable)
            return base.GetQueryTableInfoEntityColumnInfos(context, tableInfo);
        
        // 普通列（非导航列）
        var columnInfos = entityInfo.Columns.Where(u => u.Navigat == null).ToList();

        // 基础资料类型的实体
        if (tableInfo.EntityType.IsSubclassOf(typeof(BdEntityBase)))
        {
            // 返回的列信息，排除忽略的基础资料属性名称
            return columnInfos.Where(u => !_ignoreBdPropertyNameList.Contains(u.PropertyName)).ToList();
        }

        // 容器
        if (tableInfo.EntityType == typeof(BdContainer))
        {
            return columnInfos.Where(u => u.PropertyName == nameof(BdContainer.Number)).ToList();
        }

        return base.GetQueryTableInfoEntityColumnInfos(context, tableInfo);
    }
}