{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "SpecificationDocumentSettings": {
    "DocumentTitle": "Neuz 框架",
    "GroupOpenApiInfos": [
      {
        "Group": "Default",
        "Title": "Neuz",
        "Description": "管理系统",
        "Version": "1.0.0",
        "Order": 1000
      },
      {
        "Group": "All Groups",
        "Title": "所有接口",
        "Description": "管理系统",
        "Version": "1.0.0"
      }
    ],
    "DefaultGroupName": "Default", // 默认分组名
    "DocExpansionState": "List", // List、Full、None
    "EnableAllGroups": true,
    "LoginInfo": {
      "Enabled": true, // 是否开启Swagger登录
      "CheckUrl": "/api/swagger/checkUrl",
      "SubmitUrl": "/api/swagger/submitUrl"
    },
    "EnumToNumber": true // 枚举类型生成值类型
  }
}