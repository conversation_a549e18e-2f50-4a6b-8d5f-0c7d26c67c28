﻿namespace Neuz.Application.Pda.Proxy.Dto;

public class PdaApiSelectLookupDataInput
{
    /// <summary>
    /// Model的Key
    /// </summary>
    public string Key { get; set; }
    /// <summary>
    /// 事务Id
    /// </summary>
    public long TranId { get; set; }
    /// <summary>
    /// Lookup的Key,根据这个Key要Lookup什么内容
    /// </summary>
    public string LookupKey { get; set; }
    /// <summary>
    /// Lookup筛选的值或者的描的值
    /// </summary>
    public string ValueKey { get; set; }
}