﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储单据类型下级明细
/// </summary>
[SugarTable(null, "仓储单据类型下级明细")]
public class StkBillTypeNextEntry : EntryEntityBase
{
    /// <summary>
    /// 下级实体名称
    /// </summary>
    [SugarColumn(ColumnDescription = "下级实体名称", Length = 50)]
    public string NextEntityName { get; set; }

    /// <summary>
    /// 下级单据类型编码
    /// </summary>
    [SugarColumn(ColumnDescription = "下级单据类型编码", Length = 80)]
    public string NextBillTypeNumber { get; set; }
}