﻿using Neuz.Application.Pda.Enum;

namespace Neuz.Application.Service.Pda.SplitBarcode.Dto;

/// <summary>
/// PDA打印信息
/// </summary>
public class PdaPrintInfo
{
    /// <summary>
    /// 打印类型 蓝牙,服务器,打印服务
    /// </summary>
    public PdaPrintType PrintType { get; set; }

    /// <summary>
    /// 打印服务名称 打印服务类型用
    /// </summary>
    public string? PrintServiceName { get; set; }

    /// <summary>
    /// 打印机名字  服务器,打印服务 类型用
    /// </summary>
    public string? PrintName { get; set; }

    /// <summary>
    /// DPI 蓝牙打印类型用
    /// </summary>
    public int? Dpi { get; set; }

    /// <summary>
    /// 协议  CPCL / TSPL
    /// </summary>
    public string? Agreement { get; set; }

    /// <summary>
    /// 需要打印的模板 a - 拆分前条码,  b - 拆分后条码
    /// </summary>
    public List<string> PrintTemplates { get; set; }
}