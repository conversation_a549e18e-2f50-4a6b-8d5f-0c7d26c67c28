{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "t1.FBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t3.<PERSON><PERSON><PERSON><PERSON>", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t3.F<PERSON><PERSON>", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t1.FCheckDate", "title": "制单日期", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "t10.FNumber", "title": "生产车间编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t10.FName", "title": "生产车间名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "制单日期", "sortable": true, "format": "Date"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "<PERSON><PERSON><PERSON>", "title": "已制作数量"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillDate", "title": "单据日期", "sortable": true, "format": "Date"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Wise", "idFieldName": "t1.FInterID", "entryIdFieldName": "", "content": "SELECT\r\n    t1.FInterID AS _id,\r\n    t1.FTranType AS SourceBillKey,\r\n    t1.FInterID AS SourceBillId,\r\n    '0' AS SourceBillEntryId,\r\n    t1.FBillNo AS SourceBillNo,\r\n    t1.FCheckDate AS SourceBillDate,\r\n    t1.FItemID AS MaterialId,\r\n    t3.FNumber AS MaterialNumber,\r\n    t3.FName AS MaterialName,\r\n    t3.FShortNumber AS MaterialShortNumber,\r\n    t3.FModel AS MaterialSpec,\r\n    t1.FAuxQty AS Qty,\r\n    t1.FUnitID AS UnitId,\r\n    t4.FNumber AS UnitNumber,\r\n    t4.FName AS UnitName,\r\n    t1.FGMPBatchNo AS BatchNo,\r\n    t3.FBatchManager AS IsBatchManage,\r\n    t3.FIsSnManage AS IsSnManage,\r\n    t3.FISKFPeriod AS IsKfPeriod,\r\n    t3.FKFPeriod AS ExpPeriod,\r\n    t1.FWorkShop AS WorkShopId,\r\n    t10.FNumber AS WorkShopNumber,\r\n    t10.FName AS WorkShopName,\r\n    t1.FAuxPropID AS AuxPropId,\r\n    t13.FNumber AS AuxPropNumber,\r\n    t13.FName AS AuxPropName\r\n    -- t1.FSupplyID,\r\n    -- t1.FBillerID,\r\n    -- t1.FCheckerID,\r\n    -- t1.FConfirmerID,\r\n    -- t1.FConfirmDate,\r\n    -- t1.FConveyerID,\r\n    -- t1.FCommitDate,\r\n    -- t1.FBomInterID,\r\n    -- t1.FCommitQty,\r\n    -- t1.FPlanCommitDate,\r\n    -- t1.FPlanFinishDate,\r\n    -- t1.FPlanIssueDate,\r\n    -- t1.FProductionLineID,\r\n    -- t1.FRoutingID,\r\n    -- t1.FSourceEntryID,\r\n    -- t1.FStartDate,\r\n    -- t1.FFinishDate,\r\n    -- t1.FInHighLimit,\r\n    -- t1.FInLowLimit,\r\n    -- t1.FMrpClosed,\r\n    -- t1.FMRPLockFlag AS FMrpLockFlag,\r\n    -- t1.FMTONo AS FMtoNo,\r\n    -- t1.FQty,\r\n    -- t1.FQtyForItem,\r\n    -- t1.FQtyLost,\r\n    -- t1.FAuxQtyForItem,\r\n    -- t1.FAuxQtyLost,\r\n    -- t1.FQtyFinish,\r\n    -- t1.FQtyPass,\r\n    -- t1.FAuxQtyPass,\r\n    -- t1.FQtyScrap,\r\n    -- t1.FAuxQtyScrap,\r\n    -- t1.FAuxStockQty,\r\n    -- t1.FReleasedQty,\r\n    -- t1.FStockQty,\r\n    -- t1.FAuxInHighLimitQty,\r\n    -- t1.FAuxInLowLimitQty,\r\n    -- t1.FUnScheduledQty,\r\n    -- t1.FInHighLimitQty,\r\n    -- t1.FInLowLimitQty,\r\n    -- t1.FAuxCommitQty,\r\n    -- t1.FAuxQtyFinish,\r\n    -- t1.FReleasedAuxQty,\r\n    -- t1.FUnScheduledAuxQty,\r\n    -- t1.FClosed,\r\n    -- t1.FCloseDate,\r\n    -- t1.FStatus As FBillStatus,\r\n    -- t1.FCancellation,\r\n    -- t1.FCustID,\r\n    -- t2.FNumber As FProductionLineNumber,\r\n    -- t2.FName As FProductionLineName,\r\n    -- t3.FAlias As FItemAlias,\r\n    -- t3.FHelpCode As FItemHelpCode,\r\n    -- t3.FAuxClassID As FItemAuxClassID,\r\n    -- t3.FUnitGroupID As FItemUnitGroupID,\r\n    -- t4.FCoefficient As FUnitCoefficient,\r\n    -- t4.FUnitGroupID,\r\n    -- t5.FNumber As FCustNumber,\r\n    -- t5.FName As FCustName,\r\n    -- t6.FName As FConveyerName,\r\n    -- t7.FNumber As FSupplyNumber,\r\n    -- t7.FName As FSupplyName,\r\n    -- t8.FName As FCheckerName,\r\n    -- t9.FName As FBillerName,\r\n    -- t11.FName As FConfirmerName,\r\n    -- t12.FRoutingName,\r\n    -- t13.FItemClassID As FAuxPropClassID\r\n    --t3.FUnitPackageNumber, --Kis 没有此字段\r\nFROM\r\n    ICMO t1\r\n    LEFT JOIN t_Resource t2 ON (t2.FInterID = t1.FProductionLineID)\r\n    LEFT JOIN t_ICItem t3 ON (t3.FItemID = t1.FItemID)\r\n    LEFT JOIN t_MeasureUnit t4 ON (t4.FMeasureUnitID = t1.FUnitID)\r\n    LEFT JOIN t_Organization t5 ON (t5.FItemID = t1.FCustID)\r\n    LEFT JOIN t_User t6 ON (t6.FUserID = t1.FConveyerID)\r\n    LEFT JOIN t_Supplier t7 ON (t7.FItemID = t1.FSupplyID)\r\n    LEFT JOIN t_User t8 ON (t8.FUserID = t1.FCheckerID)\r\n    LEFT JOIN t_User t9 ON (t9.FUserID = t1.FBillerID)\r\n    LEFT JOIN t_Department t10 ON (t10.FItemID = t1.FWorkShop)\r\n    LEFT JOIN t_User t11 ON (t11.FUserID = t1.FConfirmerID)\r\n    LEFT JOIN t_Routing t12 ON (t12.FInterID = t1.FRoutingID)\r\n    LEFT JOIN t_AuxItem t13 ON (\r\n        t13.FItemID = t1.FAuxPropID\r\n        AND t13.FItemClassID = t3.FAuxClassID\r\n    )\r\nWHERE\r\n    (t1.FTranType = 85)\r\n    AND (t1.FStatus = 1)"}}