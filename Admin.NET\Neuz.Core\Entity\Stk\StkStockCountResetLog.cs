﻿namespace Neuz.Core.Entity.Stk;

/// <summary>
/// 盘点重置日志
/// </summary>
[SugarTable(null, "盘点重置日志")]
public class StkStockCountResetLog : EntityTenant
{
    /// <summary>
    /// 盘点表Id
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点表Id", Length = 200)]
    public string StockCountId { get; set; }

    /// <summary>
    /// 盘点表单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点表单据编号", Length = 200)]
    public string BillNo { get; set; }
}