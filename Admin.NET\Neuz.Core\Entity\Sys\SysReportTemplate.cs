﻿namespace Neuz.Core.Entity;

/// <summary>
/// 报表模板
/// </summary>
[SugarTable(null, "报表模板")]
[SugarIndex("index_{table}_F", nameof(FuncKey), OrderByType.Asc)]
public class SysReportTemplate : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 80)]
    public string Number { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 255)]
    public string Name { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 100)]
    public string? FuncKey { get; set; }

    /// <summary>
    /// 模板 Json
    /// </summary>
    [SugarColumn(ColumnDescription = "模板 Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Json { get; set; }
}