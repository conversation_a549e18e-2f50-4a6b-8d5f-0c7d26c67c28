﻿namespace Neuz.Core.Entity;

/// <summary>
/// Web端表格默认配置
/// </summary>
[SugarTable(null, "Web端表格默认配置")]
[SysTable]
public class StgWebTableDefault : EntityBaseId
{
    /// <summary>
    /// 表格Id
    /// </summary>
    [SugarColumn(ColumnDescription = "表格Id", Length = 200)]
    public string TableId { get; set; }

    /// <summary>
    /// 配置Json
    /// </summary>
    [SugarColumn(ColumnDescription = "配置Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Json { get; set; }
}