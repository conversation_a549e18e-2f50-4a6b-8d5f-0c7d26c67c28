﻿using Furion;
using Microsoft.AspNetCore.Http;
using Neuz.Application;
using Neuz.Application.Model;
using Neuz.Core.Entity;
using Xunit;

namespace Neuz.UnitTest;

public class ExcelImportExportTest
{
    [Fact(DisplayName = "物料导入模版生成测试")]
    public async Task BdMaterialImportTemplateGenerateTest()
    {
        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        var result = await service.ImportTemplate();
        var fileName = result.FileDownloadName;
        await using FileStream fs = new FileStream($@"d:\{fileName}", FileMode.Create);
        await fs.WriteAsync(result.FileContents);
        await fs.FlushAsync();
    }

    [Fact(DisplayName = "物料导入测试")]
    public async Task BdMaterialImportTest()
    {
        var fs = File.OpenRead("d:\\物料导入模版.xlsx");
        var file = new FormFile(fs, 0, fs.Length, "", "");

        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        await service.Import(file);
    }

    [Fact(DisplayName = "物料导出测试")]
    public async Task BdMaterialExportTest()
    {
        var service = App.GetService<BaseBdModelStdService<BdMaterial>>();
        var pageData = await service.CustomQueryPage(new CustomPageInput { FieldNames = ["Id"], Page = 1, PageSize = 20 });

        var ids = pageData.Items.Select(u => Convert.ToInt64(u["Id"])).Take(2).ToList();

        var exportResult = await service.Export(new IdsInput { Ids = ids });
        var fileName = exportResult.FileDownloadName;
        await using FileStream fs = new FileStream($@"d:\{fileName}", FileMode.Create);
        await fs.WriteAsync(exportResult.FileContents);
        await fs.FlushAsync();
    }

    [Fact(DisplayName = "编码规则导入模版生成测试")]
    public async Task SysCodeRuleImportTemplateGenerateTest()
    {
        var service = App.GetService<BaseBdModelStdService<SysCodeRule>>();
        var result = await service.ImportTemplate();
        var fileName = result.FileDownloadName;
        await using FileStream fs = new FileStream($@"d:\{fileName}", FileMode.Create);
        await fs.WriteAsync(result.FileContents);
        await fs.FlushAsync();
    }

    [Fact(DisplayName = "编码规则导入测试")]
    public async Task SysCodeRuleImportTest()
    {
        var fs = File.OpenRead("d:\\编码规则导入模版.xlsx");
        var file = new FormFile(fs, 0, fs.Length, "", "");

        var service = App.GetService<BaseBdModelStdService<SysCodeRule>>();
        await service.Import(file);
    }

    [Fact(DisplayName = "编码规则导出测试")]
    public async Task SysCodeRuleExportTest()
    {
        var service = App.GetService<BaseBdModelStdService<SysCodeRule>>();
        var pageData = await service.CustomQueryPage(new CustomPageInput { FieldNames = ["Id"], Page = 1, PageSize = 20 });

        var ids = pageData.Items.Select(u => Convert.ToInt64(u["Id"])).Take(2).ToList();

        var exportResult = await service.Export(new IdsInput { Ids = ids });
        var fileName = exportResult.FileDownloadName;
        await using FileStream fs = new FileStream($@"d:\{fileName}", FileMode.Create);
        await fs.WriteAsync(exportResult.FileContents);
        await fs.FlushAsync();
    }
}