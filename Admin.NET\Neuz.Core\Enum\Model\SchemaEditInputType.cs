﻿using Furion.DependencyInjection;

namespace Neuz.Core.Enum;

/// <summary>
/// 编辑页面输入框类型
/// </summary>
[SuppressSniffer]
public enum SchemaEditInputType
{
    /// <summary>
    /// 字符串输入框
    /// </summary>
    [Description("input")]
    Input,
    /// <summary>
    /// Lookup输入框
    /// </summary>
    [Description("lookup")]
    Lookup,
    /// <summary>
    /// 布尔类型输入框
    /// </summary>
    [Description("bool")]
    Bool,
    /// <summary>
    /// 日期输入框
    /// </summary>
    [Description("date")]
    Date,
    ///// <summary>
    ///// 下拉
    ///// </summary>
    [Description("select")]
    Select,
    ///// <summary>
    ///// 数字(未支持)
    ///// </summary>
    [Description("number")]
    Number,
    ///// <summary>
    ///// 多选下拉
    ///// </summary>
    [Description("multiSelect")]
    MultiSelect,
}