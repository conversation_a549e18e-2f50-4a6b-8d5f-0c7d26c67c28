﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;


namespace Neuz.Application.Pda.Proxy;

public class PdaDataCacheService : PdaCacheBaseService
{
    /// <summary>
    /// 
    /// </summary>
    protected ILogger<PdaCacheService> Logger => App.GetService<ILogger<PdaCacheService>>(_serviceProvider);

    /// <summary>
    /// 
    /// </summary>
    protected SysCacheService SysCacheService => App.GetService<SysCacheService>(_serviceProvider);

    /// <summary>
    /// 数据集合
    /// </summary>
    private static ConcurrentDictionary<long, IPdaData> PdaDatas = new ConcurrentDictionary<long, IPdaData>();

    /// <summary>
    /// 新增BillData
    /// </summary>
    /// <param name="modelKey"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public IPdaData CreateBillData(string modelKey, long userId)
    {
        //TODO: 现在如果有相同的键值，先删除旧的
        List<long> removeDatas = new List<long>();
        foreach (KeyValuePair<long, IPdaData> pair in PdaDatas)
        {
            if (pair.Value.ModelKey.Equals(modelKey) && pair.Value.UserId.Equals(userId))
            {
                removeDatas.Add(pair.Key);
            }
        }

        removeDatas.ForEach(r =>
        {
            Logger.LogWarning(L.Text["新建事务并删除事务Id:[{0}]", r]);
            if (PdaDatas.ContainsKey(r)) PdaDatas.Remove(r);
            SysCacheService.Remove($"pda_tranId_{r}");
        });
        var model = GetPdaModel(modelKey);
        IPdaData t = (IPdaData)Activator.CreateInstance(model.DataType);
        t.ModelKey = modelKey;
        t.UserId = userId;
        t.TranId = YitIdHelper.NextId();
        t.DataShow.TranId = t.TranId;
        t.DataShow.UserId = t.UserId;
        PdaDatas.TryAdd(t.TranId, t);
        return t;
    }

    /// <summary>
    /// 删除BillData
    /// </summary>
    /// <param name="tranId"></param>
    public void DelBillData(long tranId)
    {
        Logger.LogWarning(L.Text["新建事务并删除事务Id:[{0}]", tranId]);
        if (PdaDatas.ContainsKey(tranId))
        {
            Logger.LogWarning(L.Text["删除事务Id:[{0}]", tranId]);
            PdaDatas.Remove(tranId);
            SysCacheService.Remove($"pda_tranId_{tranId}");
        }
    }

    /// <summary>
    /// 获取BillData
    /// </summary>
    /// <param name="key"></param>
    /// <param name="tranId"></param>
    /// <returns></returns>
    public IPdaData GetBillData(string key, long tranId)
    {
        if (!PdaDatas.ContainsKey(tranId))
        {
            Logger.LogWarning(L.Text["没找到[{0}],找Redis还原", tranId]);
            var redisTran = SysCacheService.Get<byte[]>($"pda_tranId_{tranId}");
            if (redisTran == null) throw Oops.Bah(PdaErrorCode.Pda1004, tranId);
            var jsonStr = Encoding.UTF8.GetString(redisTran);
            var pdaModel = GetPdaModel(key);
            var billData = (IPdaData)JsonConvert.DeserializeObject(jsonStr, pdaModel.DataType);
            //var method = typeof(JsonConvert).GetMethod("DeserializeObject")?.MakeGenericMethod(new Type[] { pdaModel.DataType });
            //if (method == null) throw Oops.Bah("");
            //var billData = (IPdaData)method.Invoke(BindingFlags.Static | BindingFlags.Public, new object[] { jsonStr });
            PdaDatas[tranId] = billData;
        }

        return PdaDatas[tranId];
    }

    /// <summary>
    /// 获取BillData是否存在数据
    /// </summary>
    /// <param name="key"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public IPdaData GetBillDataForKey(string key, long userId)
    {
        var t = PdaDatas.Values.FirstOrDefault(f =>
            f.ModelKey.Equals(key) && f.UserId.Equals(userId) && !f.IsEmptyData());
        if (t == null)
        {
            var tranId = SysCacheService.Get<string>($"pda_billdata_{key}_{userId}");
            if (string.IsNullOrEmpty(tranId)) return default;
            var redisTran = SysCacheService.Get<byte[]>($"pda_tranId_{tranId}");
            if (redisTran == null) return default;
            var jsonStr = Encoding.UTF8.GetString(redisTran);
            var pdaModel = GetPdaModel(key);
            var redisBillData = (IPdaData)JsonConvert.DeserializeObject(jsonStr, pdaModel.DataType);

            //var method = typeof(JsonConvert).GetMethod("DeserializeObject")?.MakeGenericMethod(new Type[] { pdaModel.DataType });
            //if (method == null) throw Oops.Bah("");
            //var redisBillData = (IPdaData)method.Invoke(BindingFlags.Static | BindingFlags.Public, new object[] { jsonStr });

            if (redisBillData == null) return default;
            if (redisBillData.IsEmptyData()) return default;
            PdaDatas[Convert.ToInt64(tranId)] = redisBillData;
            return redisBillData;
        }

        return t;
    }

    /// <summary>
    /// 获取PDA BillModel
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public IPdaModel GetPdaModel(string key)
    {
        if (!PdaProxy.PdaModels.ContainsKey(key)) throw Oops.Bah(L.Text["没有找到PdaModel的Key[{0}]", key]);
        var model = _resolveNamed(PdaProxy.PdaModels[key].GetType().Name, default) as IPdaModel;
        var factory = App.GetService<PdaGetModelFactory>(_serviceProvider);
        model = factory.GetPdaModel(model);
        model.Initialization();
        return model;
    }

    /// <summary>
    /// 获取PDA BillModel
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public IPdaModelBill GetPdaModelBill(string key)
    {
        if (!PdaProxy.PdaModels.ContainsKey(key)) throw Oops.Bah(L.Text["没有找到PdaModel的Key[{0}]", key]);
        var model = _resolveNamed(PdaProxy.PdaModels[key].GetType().Name, default) as IPdaModelBill;
        var factory = App.GetService<PdaGetModelFactory>(_serviceProvider);
        model = (IPdaModelBill)factory.GetPdaModel(model);
        model.Initialization();
        return model;
    }

    /// <summary>
    /// 加载BillData
    /// </summary>
    /// <param name="pdaBillData"></param>
    /// <returns></returns>
    public IPdaData ReLoadBillData(IPdaData pdaBillData)
    {
        List<long> removeDatas = new List<long>();
        foreach (KeyValuePair<long, IPdaData> pair in PdaDatas)
        {
            if (pair.Value.ModelKey.Equals(pdaBillData.ModelKey) && pair.Value.UserId.Equals(pdaBillData.UserId))
            {
                removeDatas.Add(pair.Key);
            }
        }

        removeDatas.ForEach(r =>
        {
            Logger.LogWarning(L.Text["新建事务并删除事务Id:[{0}]", r]);
            if (PdaDatas.ContainsKey(r)) PdaDatas.Remove(r);
            SysCacheService.Remove($"pda_tranId_{r}");
        });
        PdaDatas.TryAdd(pdaBillData.TranId, pdaBillData);
        var billModel = GetPdaModel(pdaBillData.ModelKey);
        billModel.RefreshShow(pdaBillData.TranId);
        return pdaBillData;
    }

    /// <summary>
    /// 保存缓存
    /// </summary>
    /// <param name="key"></param>
    /// <param name="tranId"></param>
    public void SaveRedis(string key, long tranId)
    {
        var sysCacheService = App.GetService<SysCacheService>();
        var billData = GetBillData(key, tranId);
        var jsonStr = JsonConvert.SerializeObject(billData, new JsonSerializerSettings()
        {
            // 首字母小写(驼峰样式)
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            // 时间格式化
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            // 忽略循环引用
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            // 忽略空值
            NullValueHandling = NullValueHandling.Ignore
        });
        sysCacheService.Set($"pda_tranId_{tranId}", Encoding.UTF8.GetBytes(jsonStr), new TimeSpan(7, 0, 0, 0));
        sysCacheService.Set($"pda_billdata_{billData.ModelKey}_{billData.UserId}", tranId + "", new TimeSpan(7 * 2, 0, 0, 0));
    }

    public PdaDataCacheService(IServiceProvider serviceProvider, Func<string, ITransient, object> resolveNamed) : base(serviceProvider, resolveNamed)
    {
    }
}