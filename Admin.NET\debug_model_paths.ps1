# 调试模型路径脚本
Write-Host "=== OCR模型路径调试 ===" -ForegroundColor Green

# 获取当前目录
$currentDir = Get-Location
Write-Host "当前目录: $currentDir" -ForegroundColor Yellow

# 检查相对路径
$relativePath = "Plugins\Admin.NET.Plugin.PaddleOCR\OcrModel"
$fullPath = Join-Path $currentDir $relativePath
Write-Host "模型基础路径: $fullPath" -ForegroundColor Yellow
Write-Host "路径是否存在: $(Test-Path $fullPath)" -ForegroundColor $(if (Test-Path $fullPath) { "Green" } else { "Red" })

# 检查各个模型目录
$models = @("ch_PP-OCRv4", "ch_ppocr_server_v2.0", "en_PP-OCRv3", "ch_PP-OCRv4_server")

foreach ($model in $models) {
    Write-Host "`n--- 检查模型: $model ---" -ForegroundColor Cyan
    $modelPath = Join-Path $fullPath $model
    Write-Host "模型路径: $modelPath"
    Write-Host "目录存在: $(Test-Path $modelPath)" -ForegroundColor $(if (Test-Path $modelPath) { "Green" } else { "Red" })
    
    if (Test-Path $modelPath) {
        # 检查必需的子目录
        $subDirs = Get-ChildItem $modelPath -Directory
        Write-Host "子目录数量: $($subDirs.Count)"
        foreach ($subDir in $subDirs) {
            $hasModelFiles = (Get-ChildItem $subDir.FullName -Filter "*.pdmodel").Count -gt 0
            Write-Host "  - $($subDir.Name): $(if ($hasModelFiles) { '有模型文件' } else { '无模型文件' })" -ForegroundColor $(if ($hasModelFiles) { "Green" } else { "Red" })
        }
        
        # 检查keys文件
        $keysFiles = Get-ChildItem $modelPath -Filter "*.txt"
        Write-Host "Keys文件: $($keysFiles.Count) 个" -ForegroundColor $(if ($keysFiles.Count -gt 0) { "Green" } else { "Red" })
        foreach ($keyFile in $keysFiles) {
            Write-Host "  - $($keyFile.Name)"
        }
    }
}

Write-Host "`n=== 检查完成 ===" -ForegroundColor Green 