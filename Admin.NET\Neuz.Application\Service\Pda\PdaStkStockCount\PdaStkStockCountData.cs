﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.PdaStkStockCount.Dto;
using Neuz.Application.Pda.Proxy.Dto;

namespace Neuz.Application.Pda.PdaStkStockCount;

public class PdaStkStockCountData : IPdaData
{
    public string ModelKey { get; set; }
    public long TranId { get; set; }
    public long UserId { get; set; }
    public PdaShow DataShow { get; set; } = new PdaStkStockCountShow();
    public bool IsEmptyData()
    {
        return StkStockCountEntries.Count == 0;
    }

    /// <summary>
    /// 盘点单
    /// </summary>
    public StkStockCount StkStockCount { get; set; }
    /// <summary>
    /// 盘点明细
    /// </summary>
    public List<PdaStkStockCountEntry> StkStockCountEntries { get; set; } = new List<PdaStkStockCountEntry>();
    /// <summary>
    /// 仓库范围
    /// </summary>
    public List<StkStockCountWhAreaEntry> StkStockCountStockEntries { get; set; } = new List<StkStockCountWhAreaEntry>();
    /// <summary>
    /// 扫描的条码列表
    /// </summary>
    public List<PdaLocalBillBarcode> Barcodes { get; set; } = new List<PdaLocalBillBarcode>();

    /// <summary>
    /// 仓库信息
    /// </summary>
    public PdaLocalBillStockInfo StockInfo { get; set; }
    /// <summary>
    /// 明细分页
    /// </summary>
    public PdaApiPagination DetailPagination { get; set; } = new PdaApiPagination();
    /// <summary>
    /// 条码分页
    /// </summary>
    public PdaApiPagination BarcodePagination { get; set; } = new PdaApiPagination();

    /// <summary>
    /// 待确认是否允许跨仓库扫描的条码
    /// </summary>
    public List<WaitingConfirmBarcode> WaitingConfirmBarcodes { get; set; } = new List<WaitingConfirmBarcode>();
}

public class WaitingConfirmBarcode
{
    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    public BdBarcode Barcode { get; set; }
}