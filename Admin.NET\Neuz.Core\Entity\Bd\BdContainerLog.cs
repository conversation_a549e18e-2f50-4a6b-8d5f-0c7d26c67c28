﻿namespace Neuz.Core.Entity;

/// <summary>
/// 容器日志
/// </summary>
[SugarTable(null, "容器日志")]
public class BdContainerLog : EntityTenant
{
    /// <summary>
    /// 操作事务Id
    /// </summary>
    /// <remarks>
    /// （Pda事务Id）
    /// <para>以后有可能会多个箱一起装，暂且保留</para>
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作事务Id")]
    public long? OpTranId { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long ContainerId { get; set; }

    /// <summary>
    /// 容器编码
    /// </summary>
    [SugarColumn(ColumnDescription = "容器编码", Length = 200)]
    public string ContainerNumber { get; set; }

    /// <summary>
    /// 条码档案Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码档案Id")]
    public long? BarcodeId { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string? Barcode { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long? MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 日志类型
    /// </summary>
    [SugarColumn(ColumnDescription = "日志类型")]
    public BdContainerLogType LogType { get; set; }
}