﻿namespace Neuz.Core.Enum;

/// <summary>
/// 容器状态
/// </summary>
public enum BdContainerStatus
{
    /// <summary>
    /// 闲置
    /// </summary>
    [Description("闲置"), Theme("info")]
    Unused = 0,

    /// <summary>
    /// 已入库
    /// </summary>
    [Description("已入库"), Theme("primary")]
    In = 1,

    /// <summary>
    /// 已出库
    /// </summary>
    [Description("已出库"), Theme("info")]
    Out = 2,

    /// <summary>
    /// 作废
    /// </summary>
    [Description("作废"), Theme("danger")]
    Disuse = 3,
}