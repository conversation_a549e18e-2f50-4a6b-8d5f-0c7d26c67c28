﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案日志
/// </summary>
[SugarTable(null, "条码档案日志")]
[SugarIndex("index_{table}_B", nameof(Barcode), OrderByType.Asc)]
[SugarIndex("index_{table}_TB", nameof(TargetBillNo), OrderByType.Asc)]
public class BarBarcodeLog : EntityTenant
{
    /// <summary>
    /// 操作事务Id
    /// </summary>
    /// <remarks>
    /// （Pda事务Id）
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作事务Id")]
    public long OpTranId { get; set; }

    /// <summary>
    /// 条码档案Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码档案Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码档案
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(BarcodeId))]
    public BarBarcode BarcodeEntity { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 条码原数量
    /// </summary>
    [SugarColumn(ColumnDescription = "条码原数量")]
    public decimal OriginQty { get; set; }

    /// <summary>
    /// 操作数量
    /// </summary>
    /// <remarks>
    /// 表示操作的结果数量
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作数量")]
    public decimal OpQty { get; set; }

    /// <summary>
    /// 是否曾经入库（原值）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否曾经入库（原值）")]
    public bool IsOnceInStockOrigin { get; set; }

    /// <summary>
    /// 是否曾经入库（当前值）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否曾经入库（当前值）")]
    public bool IsOnceInStock { get; set; }

    /// <summary>
    /// 是否在库（原值）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否在库（原值）")]
    public bool IsInStockOrigin { get; set; }

    /// <summary>
    /// 是否在库（当前值）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否在库（当前值）")]
    public bool IsInStock { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    [SugarColumn(ColumnDescription = "操作类型")]
    public BarOpType OpType { get; set; }

    /// <summary>
    /// 目标单据Key
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单据Key", Length = 50)]
    public string? TargetBillKey { get; set; }

    /// <summary>
    /// 目标单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单据Id", Length = 500)]
    public string? TargetBillId { get; set; }

    /// <summary>
    /// 目标单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单据分录Id", Length = 500)]
    public string? TargetBillEntryId { get; set; }

    /// <summary>
    /// 目标单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单据编号", Length = 500)]
    public string? TargetBillNo { get; set; }

    /// <summary>
    /// 目标单据分录行号
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单据分录行号", Length = 500)]
    public string? TargetBillEntrySeq { get; set; }

    /// <summary>
    /// 来源仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓库Id", Length = 50)]
    public string? SourceStockId { get; set; }

    /// <summary>
    /// 来源仓库编码
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓库编码", Length = 50)]
    public string? SourceStockNumber { get; set; }

    /// <summary>
    /// 来源仓库名称
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓库名称", Length = 50)]
    public string? SourceStockName { get; set; }

    /// <summary>
    /// 来源仓位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓位Id", Length = 50)]
    public string? SourceStockLocId { get; set; }

    /// <summary>
    /// 来源仓位编码
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓位编码", Length = 50)]
    public string? SourceStockLocNumber { get; set; }

    /// <summary>
    /// 来源仓位名称
    /// </summary>
    [SugarColumn(ColumnDescription = "来源仓位名称", Length = 50)]
    public string? SourceStockLocName { get; set; }

    /// <summary>
    /// 目标仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓库Id", Length = 50)]
    public string? TargetStockId { get; set; }

    /// <summary>
    /// 目标仓库编码
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓库编码", Length = 50)]
    public string? TargetStockNumber { get; set; }

    /// <summary>
    /// 目标仓库名称
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓库名称", Length = 50)]
    public string? TargetStockName { get; set; }

    /// <summary>
    /// 目标仓位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓位Id", Length = 50)]
    public string? TargetStockLocId { get; set; }

    /// <summary>
    /// 目标仓位编码
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓位编码", Length = 50)]
    public string? TargetStockLocNumber { get; set; }

    /// <summary>
    /// 目标仓位名称
    /// </summary>
    [SugarColumn(ColumnDescription = "目标仓位名称", Length = 50)]
    public string? TargetStockLocName { get; set; }

    /// <summary>
    /// 操作功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "操作功能点Key", Length = 200)]
    public string OpFuncKey { get; set; }
}