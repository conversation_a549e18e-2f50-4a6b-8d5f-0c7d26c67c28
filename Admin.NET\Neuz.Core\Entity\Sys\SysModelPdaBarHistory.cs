namespace Neuz.Core.Entity;

/// <summary>
/// pda模型配置
/// </summary>
[SugarTable(null, "模型配置")]
[SugarIndex("index_{table}_M", nameof(ModelKey), OrderByType.Asc)]
public class SysModelPdaBarHistory : EntityBase
{
    /// <summary>
    /// 模型Key
    /// </summary>
    [SugarColumn(ColumnDescription = "模型Key", Length = 100)]
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "模型名称", Length = 100)]
    public string ModelName { get; set; }
    
    /// <summary>
    /// 模型内容
    /// </summary>
    [SugarColumn(ColumnDescription = "模型内容", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ModelContent { get; set; }
}