﻿namespace Admin.NET.Core;

public partial class SysUser
{
    #region Erp 相关

    /// <summary>
    /// Erp用户名
    /// </summary>
    [SugarColumn(ColumnDescription = "Erp用户名", Length = 50)]
    [MaxLength(50)]
    public string? ErpUserName { get; set; }

    /// <summary>
    /// Erp密码
    /// </summary>
    [SugarColumn(ColumnDescription = "Erp密码", Length = 50)]
    [MaxLength(50)]
    public string? ErpPassword { get; set; }

    #endregion

    /// <summary>
    /// 是否开启MFA验证
    /// </summary>
    [SugarColumn(ColumnDescription = "是否开启MFA验证",DefaultValue = "0")]
    public bool IsMfa { get; set; }

    /// <summary>
    /// MFA密钥
    /// </summary>
    [SugarColumn(ColumnDescription = "MFA密钥")]
    public string? MfaKey { get; set; }

    /// <summary>
    /// 系统是否开启MFA验证
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsSysMfa { get; set; }

    /// <summary>
    /// 绑定供应商编码
    /// </summary>
    [SugarColumn(ColumnDescription = "绑定供应商编码", Length = 50)]
    [MaxLength(50)]
    public string? BindSupplierNumber { get; set; }
}