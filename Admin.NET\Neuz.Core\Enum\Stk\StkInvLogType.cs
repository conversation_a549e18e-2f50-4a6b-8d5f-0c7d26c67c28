﻿namespace Neuz.Core.Enum;

/// <summary>
/// 库存事务类型
/// </summary>
public enum StkInvLogType
{
    /// <summary>
    /// 收货加库存
    /// </summary>
    [Description("收货加库存")]
    ReceivePlus = 1,

    /// <summary>
    /// 上架减库存
    /// </summary>
    [Description("上架减库存")]
    PutAwayMinus = 2,

    /// <summary>
    /// 上架加库存
    /// </summary>
    [Description("上架加库存")]
    PutAwayPlus = 3,

    /// <summary>
    /// 拣货减库存
    /// </summary>
    [Description("拣货减库存")]
    PickMinus = 4,

    /// <summary>
    /// 拣货加库存
    /// </summary>
    [Description("拣货加库存")]
    PickPlus = 5,

    /// <summary>
    /// 发运减库存
    /// </summary>
    [Description("发运减库存")]
    ShipMinus = 6,

    /// <summary>
    /// 调拨出减库存
    /// </summary>
    [Description("调拨出减库存")]
    TransferOutMinus = 7,

    /// <summary>
    /// 调拨入加库存
    /// </summary>
    [Description("调拨入加库存")]
    TransferInPlus = 8,

    /// <summary>
    /// 库存调整减库存
    /// </summary>
    [Description("库存调整减库存")]
    AdjustmentMinus = 9,

    /// <summary>
    /// 库存调整加库存
    /// </summary>
    [Description("库存调整加库存")]
    AdjustmentPlus = 10,

    /// <summary>
    /// 批号调整减库存
    /// </summary>
    [Description("批号调整减库存")]
    BatchAdjustMinus = 11,

    /// <summary>
    /// 批号调整加库存
    /// </summary>
    [Description("批号调整加库存")]
    BatchAdjustPlus = 12,

    // 非标准流程，从1001开始
}