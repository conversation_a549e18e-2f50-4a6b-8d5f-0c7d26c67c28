﻿using System.Security.Cryptography;

namespace Neuz.Application;

/// <summary>
/// 第三方系统登录授权服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysThirdAuth", Order = 100)]
public class SysThirdAuthService : IDynamicApiController
{
    /// <summary>
    /// 请求时间允许的偏差范围
    /// </summary>
    public TimeSpan AllowedDateDrift { get; set; } = TimeSpan.FromMinutes(5);

    private readonly SysThirdAccessService _thirdAccessService;
    private readonly SysAuthService _authService;
    private readonly SysLicenseCheckService _licenseCheckService;

    public SysThirdAuthService(SysThirdAccessService thirdAccessService,
        SysAuthService authService,
        SysLicenseCheckService licenseCheckService)
    {
        _thirdAccessService = thirdAccessService;
        _authService = authService;
        _licenseCheckService = licenseCheckService;
    }

    /// <summary>
    /// 第三方系统登录
    /// </summary>
    /// <param name="tld">第三方登录数据</param>
    /// <returns></returns>
    [HttpGet("thirdLogin")]
    [AllowAnonymous]
    public async Task<LoginOutput> ThirdLogin([FromQuery] string tld)
    {
        SysThirdLoginData thirdLoginData;
        try
        {
            // 转换16进制数据为 json 对象
            var jsonStr = Encoding.UTF8.GetString(Convert.FromHexString(tld));
            thirdLoginData = JSON.Deserialize<SysThirdLoginData>(jsonStr);
        }
        catch
        {
            throw Oops.Bah(SysErrorCode.SysThirdAuth1000);
        }

        // 验证请求数据是否在可接受的时间内
        // 根据时间戳转成时间格式(自动判断时间戳长度是秒还是以毫秒为单位)
        bool isMilliseconds = thirdLoginData.Timestamp > **********;
        var requestDate = isMilliseconds
            ? DateTimeOffset.FromUnixTimeMilliseconds(thirdLoginData.Timestamp).ToLocalTime().DateTime
            : DateTimeOffset.FromUnixTimeSeconds(thirdLoginData.Timestamp).ToLocalTime().DateTime;
        if (requestDate > DateTime.UtcNow.Add(AllowedDateDrift).ToLocalTime() || requestDate < DateTime.UtcNow.Subtract(AllowedDateDrift).ToLocalTime())
            throw Oops.Bah(SysErrorCode.SysThirdAuth1003);

        // 获取 appSecret
        var appSecret = await _thirdAccessService.GetAppSecret(thirdLoginData.AppKey);
        if (string.IsNullOrEmpty(appSecret))
            throw Oops.Bah(SysErrorCode.SysThirdAuth1001);

        // 校验签名
        var appSecretByte = Encoding.UTF8.GetBytes(appSecret);
        var messageForSign = GetMessageForSign(thirdLoginData);
        string serverSign = SignData(appSecretByte, messageForSign);

        if (serverSign != thirdLoginData.Sign)
            throw Oops.Bah(SysErrorCode.SysThirdAuth1002);

        // 账号是否存在
        var user = await _thirdAccessService.GetBindUser(thirdLoginData.AppKey, thirdLoginData.UserAccount);
        _ = user ?? throw Oops.Oh(ErrorCodeEnum.D0009);

        // 获取并验证账号
        user = await _authService.GetLoginUser(user.TenantId ?? 0, user.Account);

        // 授权校验
        var (isValid, errMsg) = _licenseCheckService.OnlineUserCheck(Convert.ToInt64(user.TenantId), user.Id);
        if (!isValid) throw Oops.Oh(errMsg);

        return await _authService.CreateToken(user);
    }

    /// <summary>
    /// 获取用于签名的消息
    /// </summary>
    /// <returns></returns>
    public static string GetMessageForSign(SysThirdLoginData thirdLoginData)
    {
        return $"{thirdLoginData.AppKey}&{thirdLoginData.UserAccount}&{thirdLoginData.Timestamp}";
    }

    /// <summary>
    /// 对数据进行签名
    /// </summary>
    /// <param name="secret"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    public static string SignData(byte[] secret, string data)
    {
        if (secret == null)
            throw new ArgumentNullException(nameof(secret));

        if (data == null)
            throw new ArgumentNullException(nameof(data));

        using HMAC hmac = new HMACSHA256();
        hmac.Key = secret;
        return Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(data)));
    }
}