﻿namespace Neuz.Application;

/// <summary>
/// 库存分配结果
/// </summary>
public class StkAllocateResult
{
    /// <summary>
    /// 单据主键Id
    /// </summary>
    public long BillId { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    public string BillNo { get; set; }

    /// <summary>
    /// 单据分录主键Id，如果没有则返回空
    /// </summary>
    public long? BillEntryId { get; set; }

    /// <summary>
    /// 单据分录序号，如果没有则返回空
    /// </summary>
    public int? BillEntrySeq { get; set; }

    /// <summary>
    /// 分配关联的锁定库存主键Id
    /// </summary>
    public long? LockInventoryId { get; set; }

    /// <summary>
    /// 分配关联的预入库库存主键Id
    /// </summary>
    public long? PreInInventoryId { get; set; }

    /// <summary>
    /// 分配数量
    /// </summary>
    /// <remarks>
    /// 锁定数量/预入库数量
    /// </remarks>
    public decimal AllocateQty { get; set; }

    /// <summary>
    /// 是否允许更换库位
    /// </summary>
    public bool IsAllowLocReplace { get; set; }

    /// <summary>
    /// 是否允许更换批号
    /// </summary>
    public bool IsAllowBatchNoReplace { get; set; }

    /// <summary>
    /// 使用的分配规则编码
    /// </summary>
    public string UsedRuleNumber { get; set; }

    /// <summary>
    /// 使用的分配规则名称
    /// </summary>
    public string UsedRuleName { get; set; }
}