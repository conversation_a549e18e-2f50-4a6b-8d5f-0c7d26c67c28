﻿using Furion.Localization;
using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Adapter.K3Cloud.ApiClient;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.StockCount.Data;
using QueryType = Neuz.Application.Pda.Enum.QueryType;

namespace Neuz.Application.Pda.StockCount.Cloud.Bill;

/// <summary>
/// 物料盘点作业
/// </summary>
public class PdaCloudStockCountBillModel : PdaStockCountModel
{
    private K3CloudInterface K3CloudInterface => App.GetService<K3CloudInterface>(_serviceProvider);

    /// <inheritdoc />
    public override string Key { get; set; } = "CloudStockCount";

    /// <inheritdoc />
    public override string ErpKey => "K3Cloud";

    /// <summary>
    /// 物料盘点作业
    /// </summary>
    /// <param name="serviceProvider"></param>
    public PdaCloudStockCountBillModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc />
    public override List<PdaLookupOutput> QueryLookupData(long tranId, string lookupKey, string lookupValue)
    {
        var key = lookupKey;
        if (lookupKey == "CloudStockCount")
            key = "STK_StockCountInput";
        if (lookupKey == "t_stock")
            key = "BD_StockStockLoc";
        return base.QueryLookupData(tranId, key, lookupValue);
    }

    /// <inheritdoc />
    public override bool SelectLookupData(long tranId, string key, string lookupKey, string valueKey, string lookupDataKey, QueryType queryType = QueryType.Id)
    {
        var lk = lookupKey;
        if (lookupKey == "CloudStockCount")
            lk = "STK_StockCountInput";
        if (lookupKey == "t_stock")
            lk = "BD_StockStockLoc";
        return base.SelectLookupData(tranId, key, lk, valueKey, lookupDataKey, queryType);
    }

    /// <inheritdoc />
    public override List<string> SummaryOperationFields => new()
    {
        "MaterialId",
        "UnitId",
        "BatchNo",
        "ProduceDate",
        "ExpiryDate",
        "ExpPeriod",
        "StockId",
        "StockLocId",
        "AuxPropId",
        "AuxPropNumber",
        "AuxPropName",
    };

    /// <inheritdoc />
    protected override Dictionary<long, PdaBillModelBase.PdaBarcodeInfo> GetSubmitWriteBackBillInfo(long tranId, PdaSubmitMessage pdaSubmitMessage,
        Dictionary<string, PdaSummaryInfo> summaryDetails)
    {
        var infos = new Dictionary<long, PdaBillModelBase.PdaBarcodeInfo>();
        var commitResults = (List<CommitResult>)pdaSubmitMessage["CommitResults"];
        var handleCommitResults = commitResults.Where(u => u.CommitResultEntries.Count > 0).ToList();

        foreach (var commitResult in handleCommitResults)
        {
            var billId = commitResult.TargetFId + "";
            var billNo = commitResult.TargetBillNo;
            foreach (var commitResultEntry in commitResult.CommitResultEntries)
            {
                var entryId = commitResultEntry.EntryId + "";
                var seq = commitResultEntry.Seq + "";
                var barcodeIds = string.IsNullOrWhiteSpace(commitResultEntry.RelateData) ? Array.Empty<string>() : commitResultEntry.RelateData.Split('|');

                foreach (var barcodeId in barcodeIds)
                {
                    PdaBarcode pdaBarcode = null;
                    foreach (var summaryDetail in summaryDetails)
                    {
                        pdaBarcode = summaryDetail.Value.PdaBarcodes.FirstOrDefault(u => u.Barcode.Id + "" == barcodeId);
                        if (pdaBarcode != null)
                            break;
                    }

                    if (pdaBarcode == null)
                    {
                        Logger.LogWarning(L.Text["条码 Id: {0} 在所有提交汇总信息中找不到", barcodeId]); //理论上不会出现找不到的情况
                        continue;
                    }

                    if (!infos.ContainsKey(pdaBarcode.Barcode.Id))
                    {
                        infos.Add(pdaBarcode.Barcode.Id, new PdaBillModelBase.PdaBarcodeInfo()
                        {
                            PdaBarcode = pdaBarcode,
                            TargetBillKey = ((PdaStockCountBillSchema)BillSchema).BillLink.DestKey,
                            TargetBillId = billId,
                            TargetBillEntryId = entryId,
                            TargetBillNo = billNo,
                            TargetBillEntrySeq = seq,
                        });
                    }
                    else
                    {
                        infos[pdaBarcode.Barcode.Id].PdaBarcode.CalcBarcode.ScanQty += pdaBarcode.CalcBarcode.ScanQty;
                    }
                }
            }
        }

        return infos;
    }

    /// <inheritdoc />
    public override PdaSubmitMessage SaveBill(long tranId, string billLinkSourceKey, string billLinkDestKey, int billLinkRob, DataTable submitTable)
    {
        var ids = submitTable.AsEnumerable().Where(r => !string.IsNullOrEmpty(r["FID"] + "")).Select(r => $"{r["FID"]}").Distinct().ToList();
        if (ids.Count <= 0) throw Oops.Bah(L.Text["没有明细行"]);
        if (ids.Count > 1) throw Oops.Bah(L.Text["盘点只能提交一个盘点单"]);
        var id = ids.First();

        var saveData = submitTable.ToDictionary();
        var sourceRows = new Dictionary<long, Dictionary<string, object>>(); //源单的行
        foreach (var row in saveData.AsEnumerable().Where(u => !string.IsNullOrEmpty(u["[FBillEntry].FEntryID"] + "")))
        {
            var entryId = Convert.ToInt64(row["[FBillEntry].FEntryID"]);
            sourceRows[entryId] = row;
        }

        var nonSourceRows = saveData.AsEnumerable().Where(u => string.IsNullOrEmpty(u["[FBillEntry].FEntryID"] + "")).ToList(); //源单外的行

        var client = K3CloudInterface.GetK3CloudClient();
        //查询物料盘点作业所有的行的数量
        var queryParam = new QueryBillParam
        {
            FormId = "STK_StockCountInput",
            FieldKeys = new List<string>
            {
                "FID",
                "FBillEntry_FEntryID AS FEntryID",
                "FCountQty",
                "FIsSystem",
            },
            Filters = new List<QueryFilter> { new("FID", Adapter.K3Cloud.QueryType.Equals, id) }
        };
        var queryResult = client.QueryBillData(queryParam).GetAwaiter().GetResult();

        //数据处理
        var newSaveData = new List<Dictionary<string, object>>();
        foreach (var record in queryResult)
        {
            var entryId = Convert.ToInt64(record["FEntryID"]);
            if (sourceRows.ContainsKey(entryId))
            {
                var countQty = Convert.ToDecimal(record["FCountQty"]);
                var sourceRow = sourceRows[entryId];
                sourceRow["[FBillEntry].FCountQty"] = Convert.ToDecimal(sourceRow["[FBillEntry].FCountQty"]) + countQty; //累加
                newSaveData.Add(sourceRow);
            }
            else
            {
                newSaveData.Add(new Dictionary<string, object>
                {
                    ["[FBillEntry].FEntryID"] = entryId,
                });
            }
        }

        newSaveData.AddRange(nonSourceRows);

        //保存
        var param = new SaveBillParam
        {
            TargetFormId = "STK_StockCountInput",
            Data = newSaveData,
        };
        var result = client.SaveBill(param, relateTranId: tranId).GetAwaiter().GetResult();
        var billNo = string.Join(",", result.CommitResults.Select(u => u.TargetBillNo).Where(u => !string.IsNullOrEmpty(u)).Distinct());
        var interId = string.Join(",", result.CommitResults.Select(u => u.TargetFId).Where(u => u != 0).Distinct());
        var message = string.Join(",", result.CommitResults.Where(u => u.FromActionName != "下推").Select(u => u.Message));

        if (string.IsNullOrEmpty(message))
            message = result.Message;

        var pdaSubmitMessage = new PdaSubmitMessage
        {
            ErrCode = result.IsSuccess ? 0 : -1,
            BillNo = billNo,
            InterId = interId,
            Message = message,
            ["CommitResults"] = result.CommitResults,
        };

        return pdaSubmitMessage;
    }

    /// <inheritdoc />
    public override PdaSubmitMessage ResetBill(string billId)
    {
        var client = K3CloudInterface.GetK3CloudClient();
        var queryParam = new QueryBillParam
        {
            FormId = "STK_StockCountInput",
            FieldKeys = new List<string>
            {
                "FID",
                "FBillNo",
                "FBillEntry_FEntryID AS FEntryID",
            },
            Filters = new List<QueryFilter>
            {
                new("FID", Adapter.K3Cloud.QueryType.Equals, billId),
                new("FIsSystem", Adapter.K3Cloud.QueryType.Equals, true), //是否盘点方案系统生成
            },
        };
        var result = client.QueryBillData(queryParam).GetAwaiter().GetResult();
        if (result == null || result.Count == 0)
            throw Oops.Bah(L.Text["物料盘点作业不存在"]);

        var billNo = result.First()["FBillNo"] + "";
        var saveParam = new SaveBillParam
        {
            TargetFormId = "STK_StockCountInput",
            Data = result.Select(u => new Dictionary<string, object>
            {
                ["FID"] = u["FID"],
                ["[FBillEntry].FEntryID"] = u["FEntryID"],
                ["[FBillEntry].FCountQty"] = 0,
            }).ToList()
        };
        var saveResult = client.SaveBill(saveParam).GetAwaiter().GetResult();

        var pdaSubmitMessage = new PdaSubmitMessage
        {
            ErrCode = saveResult.IsSuccess ? 0 : -1,
            BillNo = billNo,
            InterId = billId,
            Message = saveResult.IsSuccess ? L.Text["重置成功"] : L.Text["重置失败：{0}", saveResult.Message],
        };
        return pdaSubmitMessage;
    }

    /// <inheritdoc />
    public override void SetSubmitRowValue(PdaStockCountData stockCountData, DataRow newRow, PdaStockCountScanHead headData, PdaSummaryInfo summaryInfo)
    {
        newRow["FID"] = summaryInfo.Values.SourceBillId;
        newRow["[FBillEntry].FEntryID"] = summaryInfo.Values.SourceBillEntryId;
        newRow["[FBillEntry].FMaterialId.FNumber"] = summaryInfo.Values.MaterialNumber;
        newRow["[FBillEntry].FStockId.FNumber"] = summaryInfo.Values.StockNumber;
        newRow["[FBillEntry].FStockLocId.{stockLoc}.FNumber"] = summaryInfo.Values.StockLocId;
        newRow["[FBillEntry].FCountQty"] = summaryInfo.ScanQty;
        newRow["[FBillEntry].FLot.FNumber"] = summaryInfo.Values.BatchNo;
        newRow["[FBillEntry].FProduceDate"] = summaryInfo.Values.ProduceDate ?? (object)DBNull.Value;
        newRow["[FBillEntry].FExpiryDate"] = summaryInfo.Values.ExpiryDate ?? (object)DBNull.Value;
        newRow["[FBillEntry].FAuxPropId.{auxProp}.FNumber"] = summaryInfo.Values.AuxPropId;
        newRow["[FBillEntry].<FSerialSubEntity>.FSerialNo"] = GetToCloudSerialNo(summaryInfo);
        newRow["[FBillEntry]._RelateData_"] = GetRelateBarcodeIds(summaryInfo);
    }

    /// <inheritdoc />
    public override DataTable CreateSubmitTable()
    {
        DataTable submitTable = new DataTable();
        submitTable.Columns.AddRange(new[]
        {
            new DataColumn("FID"),
            new DataColumn("[FBillEntry].FEntryID"),
            new DataColumn("[FBillEntry].FMaterialId.FNumber"),
            new DataColumn("[FBillEntry].FStockId.FNumber"),
            new DataColumn("[FBillEntry].FStockLocId.{stockLoc}.FNumber"),
            new DataColumn("[FBillEntry].FCountQty", typeof(decimal)),
            new DataColumn("[FBillEntry].FLot.FNumber"),
            new DataColumn("[FBillEntry].FProduceDate", typeof(DateTime)),
            new DataColumn("[FBillEntry].FExpiryDate", typeof(DateTime)),
            new DataColumn("[FBillEntry].FAuxPropId.{auxProp}.FNumber"),
            new DataColumn("[FBillEntry].<FSerialSubEntity>.FSerialNo"),
            new DataColumn("[FBillEntry]._RelateData_"),
        });
        return submitTable;
    }

    /// <summary>
    /// 获取需要提交到金蝶序列号的条码，以“|^|”隔开
    /// </summary>
    /// <param name="summaryInfo"></param>
    /// <returns></returns>
    protected string GetToCloudSerialNo(PdaSummaryInfo summaryInfo)
    {
        return string.Join("|^|", summaryInfo.PdaBarcodes.Where(u => u.Barcode.IsSnManage).Select(u => u.Barcode.Barcode));
    }

    /// <summary>
    /// 获取提交汇总相关的条码Id，以“|”隔开
    /// </summary>
    /// <param name="summaryInfo"></param>
    /// <returns></returns>
    protected string GetRelateBarcodeIds(PdaSummaryInfo summaryInfo)
    {
        return string.Join("|", summaryInfo.PdaBarcodes.Select(u => u.Barcode.Id));
    }

    /// <inheritdoc />
    protected override bool GetIsCalcBarcodeFieldEmpty(string summaryBillField, object value)
    {
        var valueStr = value + "";
        return string.IsNullOrEmpty(valueStr) || (decimal.TryParse(valueStr, out var id) && id == 0);
    }
}