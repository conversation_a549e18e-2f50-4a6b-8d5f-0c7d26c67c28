﻿namespace Neuz.Core.Entity;

/// <summary>
/// 辅助属性值
/// </summary>
[SugarTable(null, "辅助属性值")]
[SugarIndex("index_{table}_N", nameof(Number), OrderByType.Asc)]
[SugarIndex("index_{table}_N2", nameof(Name), OrderByType.Asc)]
[SugarIndex("index_{table}_S", nameof(StorageValue), OrderByType.Asc)]
public class BdAuxPropValue : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 80)]
    public string Number { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 255)]
    public string Name { get; set; }

    /// <summary>
    /// 储存值(Json)
    /// </summary>
    [SugarColumn(ColumnDescription = "储存值(Json)", Length = 4000)]
    public string StorageValue { get; set; }
}