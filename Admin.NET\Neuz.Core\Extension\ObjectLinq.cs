﻿namespace Neuz.Core.Extension;

/// <summary>
/// object 调用 Linq
/// </summary>
public static class ObjectLinq
{
    /// <summary>
    /// 生成两个序列的差集
    /// <para>
    /// 此方法返回 firstList 在 secondList 中不存在的元素
    /// </para>
    /// </summary>
    /// <param name="firstList">第一个集合</param>
    /// <param name="secondList">第二个集合</param>
    /// <param name="genericType">集合的泛型类型</param>
    /// <param name="equalityComparer">相等比较器的对象</param>
    /// <returns></returns>
    public static object Except(object firstList, object secondList, Type genericType, object equalityComparer)
    {
        var method = typeof(Enumerable).GetMethods()
            .First(m => m.Name == nameof(Enumerable.Except) && m.IsGenericMethod && m.GetGenericArguments().Length == 1 && m.GetParameters().Length == 3)
            .MakeGenericMethod(genericType);
        return method.Invoke(null, new[] { firstList, secondList, equalityComparer });
    }

    /// <summary>
    /// 生成两个序列的交集
    /// <para>
    /// 此方法返回 firstList 在 secondList 中存在的元素
    /// </para>
    /// </summary>
    /// <param name="firstList">第一个集合</param>
    /// <param name="secondList">第二个集合</param>
    /// <param name="genericType">集合的泛型类型</param>
    /// <param name="equalityComparer">相等比较器的对象</param>
    /// <returns></returns>
    public static object Intersect(object firstList, object secondList, Type genericType, object equalityComparer)
    {
        var method = typeof(Enumerable).GetMethods()
            .First(m => m.Name == nameof(Enumerable.Intersect) && m.IsGenericMethod && m.GetGenericArguments().Length == 1 && m.GetParameters().Length == 3)
            .MakeGenericMethod(genericType);
        return method.Invoke(null, new[] { firstList, secondList, equalityComparer });
    }

    /// <summary>
    /// 按升序对序列的元素进行排序
    /// </summary>
    /// <param name="list">集合</param>
    /// <param name="genericType">集合的泛型类型</param>
    /// <param name="orderByPropName">排序属性名称</param>
    /// <param name="propType">属性类型</param>
    /// <returns></returns>
    public static object OrderBy(object list, Type genericType, string orderByPropName, Type propType)
    {
        var method = typeof(Enumerable).GetMethods()
            .First(m => m.Name == nameof(Enumerable.OrderBy) && m.IsGenericMethod && m.GetGenericArguments().Length == 2 && m.GetParameters().Length == 2)
            .MakeGenericMethod(genericType, propType);
        var uParamExp = Expression.Parameter(genericType, "u");//参数 u
        var seqExp = Expression.Property(uParamExp, orderByPropName);//如：u.Seq

        return method.Invoke(null, new[] { list, Expression.Lambda(seqExp, uParamExp).Compile() });
    }

    /// <summary>
    /// 从 <see cref="IEnumerable{T}"/> 创建一个 <see cref="List{T}"/>
    /// </summary>
    /// <param name="list">集合</param>
    /// <param name="genericType">集合的泛型类型</param>
    /// <returns></returns>
    public static object ToList(object list, Type genericType)
    {
        var method = typeof(Enumerable).GetMethod(nameof(Enumerable.ToList))!.MakeGenericMethod(genericType);
        return method.Invoke(null, new[] { list });
    }
}