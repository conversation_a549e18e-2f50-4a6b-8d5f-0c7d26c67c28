﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.PdaStkStockCount.Scan;

public class PdaStkStockCountStockOperation : PdaLocalBillScanBarcodeOperationBase
{
    public PdaStkStockCountStockOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        //230830 改成用仓位带出仓库仓位(确认仓位是唯一)
        var dataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = dataCacheService.GetBillData(args.Key, args.TranId) as PdaStkStockCountData;

        ////单据有组织，需要过滤组织下的仓库仓位
        //var orgId = billData.ScanHead?["orgId"]?.ToString();

        ////单据有仓库，需要过滤仓库下的仓位
        //var stockId = billData.ScanHead?["stockId"]?.ToString();

        //找到对应的仓库编码
        var rep = App.GetService<SqlSugarRepository<BdWhLoc>>(_serviceProvider);
        var locs = rep.AsQueryable()
            .LeftJoin<BdWhArea>((bl, bs) => bl.WhAreaId == bs.Id)
            .Where((bl, bs) => bl.Number == args.BarcodeString && bl.IsForbid == false).ToList();
        if (locs.Count > 1) throw Oops.Bah(L.Text["找到多个仓位编码: {0}", args.BarcodeString]);
        if (locs.Count < 1) return;
        var loc = locs[0];
        var stock = rep.Change<BdWhArea>().GetFirst(r => r.Id == loc.WhAreaId);
        if (stock == null) return;
        billData.StockInfo = new PdaLocalBillStockInfo
        {
            Properties = null,
            WhAreaId = stock.Id + "",
            WhAreaNumber = stock.Number,
            WhAreaName = stock.Name,
            WhLocId = loc.Id + "",
            WhLocNumber = loc.Number,
            WhLocName = loc.Name,
        };
        args.IsResult = true;
    }
}