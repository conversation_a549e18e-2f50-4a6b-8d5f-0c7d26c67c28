﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 仓储单据查询定义接口实现
/// </summary>
public class StkBillQueryDefine : DefaultQueryDefine
{
    protected override List<EntityColumnInfo> GetQueryTableInfoEntityColumnInfos(ISqlSugarClient context, QueryTableInfo tableInfo)
    {
        var entityInfo = context.EntityMaintenance.GetEntityInfo(tableInfo.EntityType);

        // 根数据表，直接使用父类实现
        if (tableInfo.IsRootTable)
            return base.GetQueryTableInfoEntityColumnInfos(context, tableInfo);

        // 普通列（非导航列）
        var columnInfos = entityInfo.Columns.Where(u => u.Navigat == null).ToList();

        // 物料
        if (tableInfo.EntityType == typeof(BdMaterial))
        {
            return columnInfos.Where(u => new[]
            {
                nameof(BdMaterial.Number),
                nameof(BdMaterial.Name),
                nameof(BdMaterial.Specification),
                nameof(BdMaterial.IsBatchManage),
                nameof(BdMaterial.IsKfPeriod),
                nameof(BdMaterial.ExpPeriod),
                nameof(BdMaterial.ExpUnit),
                nameof(BdMaterial.IsCheckReceive),
                nameof(BdMaterial.IsSnManage),
            }.Contains(u.PropertyName)).ToList();
        }

        // 辅助属性值
        if (tableInfo.EntityType == typeof(BdAuxPropValue))
        {
            return columnInfos.Where(u => new[]
            {
                nameof(BdAuxPropValue.Number),
                nameof(BdAuxPropValue.Name),
            }.Contains(u.PropertyName)).ToList();
        }

        // 容器
        if (tableInfo.EntityType == typeof(BdContainer))
        {
            return columnInfos.Where(u => u.PropertyName == nameof(BdContainer.Number)).ToList();
        }

        return base.GetQueryTableInfoEntityColumnInfos(context, tableInfo);
    }
}