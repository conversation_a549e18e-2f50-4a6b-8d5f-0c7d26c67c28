<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Admin.NET.Plugin.Pay.Alipay</name>
    </assembly>
    <members>
        <member name="M:Admin.NET.Plugin.Pay.Alipay.Middlewares.AlipaySayHelloMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            在 <see cref="!:PluginApplicationBuilder"/> Build 时, 将会 new Middleware(), 最终将所有 Middleware 包装为一个 <see cref="T:Microsoft.AspNetCore.Http.RequestDelegate"/>
            </summary>
            <param name="next"></param>
        </member>
        <member name="M:Admin.NET.Plugin.Pay.Alipay.Middlewares.AlipaySayHelloMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext,PluginCore.Interfaces.IPluginFinder)">
            <summary>
            
            </summary>
            <param name="httpContext"></param>
            <param name="pluginFinder">测试，是否运行时添加的Middleware，是否可以依赖注入</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Plugin.Pay.Alipay.Service.AlipayPluginCoreService">
            <summary>
            系统动态插件服务
            </summary>
        </member>
        <member name="M:Admin.NET.Plugin.Pay.Alipay.Service.AlipayPluginCoreService.Page">
            <summary>
            获取动态插件列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
    </members>
</doc>
