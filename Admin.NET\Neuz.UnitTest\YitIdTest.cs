﻿using Furion.DataEncryption;
using System.Buffers.Text;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using Xunit;
using Xunit.Abstractions;
using Yitter.IdGenerator;

namespace Neuz.UnitTest
{
    public class YitIdTest
    {
        private readonly ITestOutputHelper _output;

        public YitIdTest(ITestOutputHelper tempOutput)
        {
            _output = tempOutput;
        }

        [Fact]
        public void Id生成测试()
        {
            var id = YitIdHelper.NextId();
            _output.WriteLine($"生成 id: {id}");
        }

        [Fact]
        public void Test()
        {
            string key = Guid.NewGuid().ToString("N");

            var a = AESEncryption.Encrypt("AAABBcc中国人3#$%@$%^#", key);
            var c = Encrypt("AAABBcc中国人3#$%@$%^#", key);
            var d = AESEncryption.Decrypt(a, key);

            string key2 = "smkldospdosldaaa";

            var e = AESEncryption.Encrypt("AAABBcc中国人3#$%@$%^#", key);
            var f = Encrypt("AAABBcc中国人3#$%@$%^#", key);

            var g = AESEncryption.Decrypt(f, key);
        }

        public class MyClass
        {
            public string AA { get; set; }
            public TwoClass TT { get; set; }
        }

        public class TwoClass
        {
            public string BB { get; set; }
        }


        public static string Encrypt(string text, string skey)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(skey);
            using (Aes aes = Aes.Create())
            {
                using (ICryptoTransform encryptor = aes.CreateEncryptor(bytes, aes.IV))
                {
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        using (CryptoStream cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter streamWriter = new StreamWriter(cryptoStream))
                                streamWriter.Write(text);
                        }
                        byte[] iv = aes.IV;
                        //int num = iv.Length + (int)memoryStream.Length;
                        byte[] buffer = memoryStream.GetBuffer();
                        var i = buffer.ToList().IndexOf(0);
                        int num = iv.Length + i;
                        int bytesWritten = Base64.GetMaxEncodedToUtf8Length(num);
                        byte[] numArray = new byte[bytesWritten];
                        Unsafe.CopyBlock(ref numArray[0], ref iv[0], (uint)iv.Length);
                        //Unsafe.CopyBlock(ref numArray[iv.Length], ref buffer[0], (uint)memoryStream.Length
                        Unsafe.CopyBlock(ref numArray[iv.Length], ref buffer[0], (uint)i);
                        int utf8InPlace = (int)Base64.EncodeToUtf8InPlace((Span<byte>)numArray, num, out bytesWritten);
                        return Encoding.ASCII.GetString(numArray.AsSpan().Slice(0, bytesWritten - 0).ToArray());
                    }
                }
            }
        }

        /// <summary>解密</summary>
        /// <param name="hash">加密后字符串</param>
        /// <param name="skey">密钥</param>
        /// <returns></returns>
        public static string Decrypt(string hash, string skey)
        {
            byte[] numArray = Convert.FromBase64String(hash);
            byte[] rgbIV = new byte[16];
            byte[] buffer = new byte[numArray.Length - rgbIV.Length];
            Unsafe.CopyBlock(ref rgbIV[0], ref numArray[0], (uint)rgbIV.Length);
            Unsafe.CopyBlock(ref buffer[0], ref numArray[rgbIV.Length], (uint)(numArray.Length - rgbIV.Length));
            byte[] bytes = Encoding.UTF8.GetBytes(skey);
            using (Aes aes = Aes.Create())
            {
                using (ICryptoTransform decryptor = aes.CreateDecryptor(bytes, rgbIV))
                {
                    using (MemoryStream memoryStream = new MemoryStream(buffer))
                    {
                        using (CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader streamReader = new StreamReader((Stream)cryptoStream))
                                return streamReader.ReadToEnd();
                        }
                    }
                }
            }
        }
    }
}