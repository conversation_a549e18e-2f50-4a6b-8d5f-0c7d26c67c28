﻿using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Helper;

/// <summary>
/// 本地单据帮助类
/// </summary>
public class PdaLocalBillHelper
{
    public static object GetValue<T>(T t, string propertyName)
    {
        // 由再本地都是用对象，需要对propertyName解释
        var propertyNames = propertyName.Split('.');
        object value = t;
        for (int i = 0; i < propertyNames.Length; i++)
        {
            if (value == null) return null;
            value = GetPropertyValue(value, propertyNames[i]);

            // 如果是最后一个，则返回值
            if (i == propertyNames.Length - 1)
            {
                return value;
            }
        }

        return null;
    }

    /// <summary>
    /// 获取单列值
    /// </summary>
    /// <param name="o"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    private static object GetPropertyValue(object o, string propertyName)
    {
        var value = o;
        if (value is ExtensionObject exValue)
        {
            value = exValue[propertyName];
        }
        else
        {
            var property = value.GetType().GetProperty(propertyName);
            if (property == null) return null;
            value = property.GetValue(value);
        }

        return value;
    }
}