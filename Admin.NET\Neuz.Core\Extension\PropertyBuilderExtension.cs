﻿using System.Reflection.Emit;
using Furion.Localization;

namespace Neuz.Core.Extension;

/// <summary>
/// 属性构建器扩展
/// </summary>
public static class PropertyBuilderExtension
{
    /// <summary>
    /// 设置自定义特性
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="propertyBuilder"></param>
    /// <param name="constructorTypes"></param>
    /// <param name="constructorArgs"></param>
    /// <param name="attribute"></param>
    /// <exception cref="ArgumentException"></exception>
    public static void SetCustomAttribute<T>(this PropertyBuilder propertyBuilder, Type[] constructorTypes, object[] constructorArgs, T attribute)
    {
        var type = attribute.GetType();
        var attrCtorInfo = type.GetConstructor(constructorTypes);
        if (attrCtorInfo == null)
            throw new ArgumentException(L.Text["{0} 没有找到 {1} 的构造函数", type.Name, string.Join(", ", constructorTypes.Select(u => u.Name))], nameof(constructorTypes));

        //属性
        var properties = type.GetProperties().Where(u => u.CanWrite).ToList();
        var propertyValues = new List<object>();
        for (var i = properties.Count - 1; i >= 0; i--)
        {
            var property = properties[i];
            var propValue = property.GetValue(attribute);
            if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                //https://docs.microsoft.com/zh-cn/dotnet/api/system.reflection.emit.customattributebuilder.-ctor
                //不支持，CustomAttributeBuilder 的构造函数执行时会出错
                properties.RemoveAt(i);
            }
            else
            {
                propertyValues.Insert(0, propValue);
            }
        }

        //字段
        var fields = type.GetFields().ToList();
        var fieldValues = new List<object>();
        for (var i = fields.Count - 1; i >= 0; i--)
        {
            var field = fields[i];
            var fieldValue = field.GetValue(attribute);
            if (field.FieldType.IsGenericType && field.FieldType.GetGenericTypeDefinition() == typeof(Nullable<>) && fieldValue == null)
            {
                //https://docs.microsoft.com/zh-cn/dotnet/api/system.reflection.emit.customattributebuilder.-ctor
                //不支持，CustomAttributeBuilder 的构造函数执行时会出错
                fields.RemoveAt(i);
            }
            else
            {
                fieldValues.Insert(0, fieldValue);
            }
        }

        var attrBuilder = new CustomAttributeBuilder(attrCtorInfo, constructorArgs, properties.ToArray(), propertyValues.ToArray(),
            fields.ToArray(), fieldValues.ToArray());
        propertyBuilder.SetCustomAttribute(attrBuilder);
    }
}