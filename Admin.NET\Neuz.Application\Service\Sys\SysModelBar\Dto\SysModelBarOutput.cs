﻿namespace Neuz.Application;

/// <summary>
/// 条码档案模型输出参数
/// </summary>
public class SysModelBarOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 模型Key
    /// </summary>
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string UpdateUserName { get; set; }
}

/// <summary>
/// 条码档案模型预设列表输出参数
/// </summary>
public class SysModelBarPresetsOutput
{
    /// <summary>
    /// 模型Key
    /// </summary>
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }
}