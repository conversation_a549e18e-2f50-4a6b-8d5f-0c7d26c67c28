﻿namespace Neuz.Application;

/// <summary>
/// 系统实体属性输入参数
/// </summary>
public class SysEntityPropertyInput : BasePageInput
{
    /// <summary>
    /// 实体名称
    /// </summary>
    public string EntityName { get; set; }

    /// <summary>
    /// 元素类型
    /// </summary>
    public ElementType ElementType { get; set; }
}

/// <summary>
/// 系统实体基础资料属性输入参数
/// </summary>
public class SysEntityBaseDataPropertyInput : BasePageInput
{
    /// <summary>
    /// 实体名称
    /// </summary>
    public string EntityName { get; set; }

    /// <summary>
    /// 实体基础资料属性
    /// </summary>
    public string PropertyName { get; set; }
}
