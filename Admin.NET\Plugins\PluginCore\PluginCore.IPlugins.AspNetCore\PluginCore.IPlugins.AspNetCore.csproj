﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
		<PackageId>PluginCore.IPlugins.AspNetCore</PackageId>
		<Version>0.1.1</Version>
		<Company>yiyun</Company>
		<Authors>yiyun</Authors>
		<Description>PluginCore.AspNetCore Plugin Sdk</Description>
		<Copyright>Copyright (c) 2021-present yiyun</Copyright>
		<RepositoryUrl>https://github.com/yiyungent/PluginCore</RepositoryUrl>
		<PackageLicenseUrl>https://github.com/yiyungent/PluginCore/blob/main/LICENSE</PackageLicenseUrl>
		<PackageTags>PluginCore PluginCore.IPlugins.AspNetCore</PackageTags>
		<PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
	</PropertyGroup>

	<!-- 方便开发debug,与发布到nuget -->
	<ItemGroup Condition="'$(Configuration)' == 'Release'">
		<PackageReference Include="PluginCore.IPlugins" Version="0.9.1" />
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)' == 'Debug'">
		<ProjectReference Include="..\PluginCore.IPlugins\PluginCore.IPlugins.csproj" />
	</ItemGroup>

	<!-- 生成注释xml -->
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|netstandard2.0|AnyCPU'">
		<DocumentationFile>bin\Release\netstandard2.0\PluginCore.IPlugins.AspNetCore.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<Folder Include="Infrastructure\" />
		<Folder Include="Interfaces\" />
		<Folder Include="Models\" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
	</ItemGroup>

</Project>
