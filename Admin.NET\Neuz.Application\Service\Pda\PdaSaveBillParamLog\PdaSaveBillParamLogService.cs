﻿using Neuz.Application.Model;
using Neuz.Core.Entity.Pda.Erp;

namespace Neuz.Application;

/// <summary>
/// Pda保存单据日志服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "PdaSaveBillParamLog", Order = 100)]
public class PdaSaveBillParamLogService : NeuzBaseQueryService<PdaSaveBillParamLog>, IDynamicApiController
{
    /// <summary>
    /// Pda保存单据日志服务构造函数
    /// </summary>
    public PdaSaveBillParamLogService(IServiceProvider serviceProvider, SqlSugarRepository<PdaSaveBillParamLog> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "CreateTime", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "Param", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Result", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SourceKey", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "TargetKey", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "TargetBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CreateUserName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "TranId", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Input },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "TranId",
            "Param",
            "IsSuccess",
            "Result",
            "SourceKey",
            "TargetKey",
            "TargetBillNo",
            "Extra1",
            "Extra2",
            "Extra3",
            "CreateTime",
            "CreateUserName",
        ];
    }

    /// <summary>
    /// 获取Pda保存单据日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("get")]
    public async Task<PdaSaveBillParamLog> Get([FromQuery] IdInput input)
    {
        return await Rep.AsQueryable().Where(u => u.Id == input.Id).FirstAsync();
    }

    /// <summary>
    /// 清空日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("clear")]
    public void Clear()
    {
        Rep.AsSugarClient().DbMaintenance.TruncateTable<PdaSaveBillParamLog>();
    }
}