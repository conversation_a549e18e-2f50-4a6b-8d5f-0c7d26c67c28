﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.LocalBillDto;

namespace Neuz.Application.Pda.LocalBill;

/// <summary>
/// PDA本地单据接口
/// </summary>
public interface IPdaLocalBillModel
{
    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <param name="modifyQty"></param>
    /// <param name="properties"></param>
    void ScanBarcode(long tranId, BdBarcode barcode, decimal? modifyQty = null, Dictionary<string, object> properties = null);

    /// <summary>
    /// 扫描多条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcodes"></param>
    /// <param name="container"></param>
    void ScanBarcodes(long tranId, List<BdBarcode> barcodes, BdContainer container);

    /// <summary>
    /// 刷新数据
    /// </summary>
    /// <param name="tranId"></param>
    void RefreshShow(long tranId);

    /// <summary>
    /// 通过单号带源单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input);


    /// <summary>
    /// 获取单据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="isException"></param>
    /// <returns></returns>
    Task<dynamic> GetBill(PdaLocalBillLookSelectInput input, bool isException = true);

    /// <summary>
    /// 检查能匹配的数量
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="inPutPdaBarcode"></param>
    /// <param name="modifyQty"></param>
    /// <returns></returns>
    PdaLocalBillMatchingInfo CheckMatchingQty(long tranId, PdaLocalBillBarcode inPutPdaBarcode, decimal? modifyQty = null);

    /// <summary>
    /// 获取PDA传过来的仓库Id
    /// </summary>
    /// <param name="tranId"></param>
    /// <returns></returns>
    string GetWarehouseId(long tranId);

    /// <summary>
    /// 选择库区库位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task<dynamic> LocalBillWhAreaSelect(PdaLocalBillLookSelectInput input);

    /// <summary>
    /// 模型配置
    /// </summary>
    public PdaLocalBillModelConfig Config { get; set; }
}