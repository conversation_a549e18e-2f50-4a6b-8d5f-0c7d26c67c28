﻿namespace Neuz.Core.Entity;

/// <summary>
/// 部门
/// </summary>
[SugarTable(null, "部门")]
public class BdDepartment : EsBdEntityBase
{
    /// <summary>
    /// 部门
    /// </summary>
    [SugarColumn(ColumnDescription = "部门全称", Length = 100)]
    public string? FullName { get; set; }

    /// <summary>
    /// 上级部门Id
    /// </summary>
    [SugarColumn(ColumnDescription = "上级部门Id")]
    public long? ParentId { get; set; }

    /// <summary>
    /// 上级部门
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ParentId))]
    [CustomSerializeFields]
    public BdDepartment Parent { get; set; }
}