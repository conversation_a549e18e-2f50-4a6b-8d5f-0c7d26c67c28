﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Core.Entity.Pda.Erp;

namespace Neuz.Application.Pda.Proxy;

/// <summary>
/// PDA 缓存服务
/// </summary>
[ApiDescriptionSettings("PDA", Name = "PdaCacheService", Order = 300)]
public class PdaApiCacheService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly UserManager _userManager;
    private readonly PdaDataCacheService _pdaDataCacheService;
    private readonly SqlSugarRepository<PdaTempStoreBill> _storeBillRep;

    public PdaApiCacheService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _userManager = serviceProvider.GetService<UserManager>();
        _pdaDataCacheService = serviceProvider.GetService<PdaDataCacheService>();
        _storeBillRep = serviceProvider.GetService<SqlSugarRepository<PdaTempStoreBill>>();
    }
    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaModel")]
    public async Task<dynamic> GetPdaModel([FromQuery] string key)
    {
        var model = _pdaDataCacheService.GetPdaModel(key);
        return await Task.FromResult(model);
    }

    /// <summary>
    /// 创建新的单据数据
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("createBillData")]
    public async Task<dynamic> CreateBillData([FromQuery] string key)
    {
        var userId = _userManager.UserId;
        var billData = _pdaDataCacheService.CreateBillData(key, userId);
        var model = _pdaDataCacheService.GetPdaModel(key);
        model.BillDataInitialization(billData);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShow")]
    public async Task<dynamic> GetPdaBillDataShow([FromQuery] string key)
    {
        var userId = _userManager.UserId;
        var billData = _pdaDataCacheService.GetBillDataForKey(key, userId);
        return await Task.FromResult(billData?.DataShow);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShowForTranId")]
    public async Task<dynamic> GetPdaBillDataShowForTranId([FromQuery] long tranId, [FromQuery] string key)
    {
        var billData = _pdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = _pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.RefreshShow(tranId);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 删除单据模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delPdaBillData")]
    public Task DelPdaBillData([FromBody] PdaDelPdaBillDataInput input)
    {
        _pdaDataCacheService.DelBillData(input.TranId);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取默认Description
    /// </summary>
    /// <returns></returns>
    [HttpGet("billTempStoreDescription")]
    public string BillTempStoreDescription([FromQuery] long tranId, [FromQuery] string key)
    {
        var billData = _pdaDataCacheService.GetBillData(key, tranId);
        var description = string.IsNullOrEmpty(billData.ModelKey) ? $"{DateTime.Now:yyyyMMdd}_{tranId}" : $"{billData.ModelKey}_{tranId}";
        return description;
    }

    /// <summary>
    /// 新增单据暂存数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("newBillTempStore")]
    public async Task NewBillTempStore([FromBody] PdaNewBillTempStoreInput input)
    {
        var existTempStore = await _storeBillRep.GetFirstAsync(r => r.TranId.Equals(input.TranId));
        if (existTempStore != null)
        {
            var result = new PdaExtrasRestfulResult<PdaTempStoreBill>
            {
                Code = (int)PdaRestfulCode.P102,
                Message = null,
                Data = existTempStore,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);
        }

        await ConfirmNewBillTempStore(input);
    }

    /// <summary>
    /// 如果TranId在暂存存在,确认覆盖保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("confirmNewBillTempStore")]
    public async Task ConfirmNewBillTempStore([FromBody] PdaNewBillTempStoreInput input)
    {
        var exists = await _storeBillRep.GetListAsync(r => r.TranId.Equals(input.TranId));
        await _storeBillRep.DeleteAsync(exists);

        var billData = _pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var jsonStr = JsonConvert.SerializeObject(billData);
        var data = System.Text.Encoding.UTF8.GetBytes(jsonStr);
        var tempStoreBill = new PdaTempStoreBill
        {
            Id = YitIdHelper.NextId(),
            TranId = input.TranId,
            UserId = billData.UserId,
            FuncKey = billData.ModelKey,
            Description = input.Description,
            BillData = data
        };
        await _storeBillRep.InsertAsync(tempStoreBill);
    }

    /// <summary>
    /// 获取暂存列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("queryTempStore")]
    public async Task<dynamic> QueryTempStore([FromQuery] string funcKey, [FromQuery] long userId)
    {
        var result = await _storeBillRep.AsQueryable().Where(r => r.FuncKey.Equals(funcKey) && r.UserId.Equals(userId))
            .OrderBy(r => r.CreateTime)
            .Select(r => new
            {
                id = r.Id,
                title = r.CreateTime.ToString("yyyy-MM-dd"),
                label = r.Description,
                value = ""
            })
            .ToListAsync();
        return result;
    }

    /// <summary>
    /// 删除暂存数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("deleteTempStore")]
    public async Task DeleteTempStore([FromBody] TempStoreInput input)
    {
        var tempStore = await _storeBillRep.GetFirstAsync(r => r.Id.Equals(input.Id));
        if (tempStore == null) throw Oops.Bah(PdaErrorCode.PdaTempStore1002);
        await _storeBillRep.DeleteAsync(tempStore);
    }

    /// <summary>
    /// 读取单据暂存数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("loadBillTempStore")]
    public async Task<long> LoadBillTempStore([FromBody] TempStoreInput input)
    {
        var tempStore = await _storeBillRep.GetFirstAsync(r => r.Id.Equals(input.Id));
        if (tempStore == null) throw Oops.Bah(PdaErrorCode.PdaTempStore1002);

        var model = _pdaDataCacheService.GetPdaModel(tempStore.FuncKey);
        var jsonStr = Encoding.UTF8.GetString(tempStore.BillData);
        var billData = (IPdaData)JsonConvert.DeserializeObject(jsonStr, model.DataType);
        if (billData == null) throw Oops.Bah(PdaErrorCode.PdaTempStore1003);

        _pdaDataCacheService.ReLoadBillData(billData);
        return billData.TranId;
    }
}