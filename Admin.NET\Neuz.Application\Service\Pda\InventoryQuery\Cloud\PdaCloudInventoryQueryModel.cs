using Furion.Localization;
using Neuz.Application.Erp.Barcode.Dto;
using Neuz.Application.Erp.Barcode.K3Cloud;
using Neuz.Application.Erp.Barcode.K3Cloud.Dto;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;

namespace Neuz.Application.Pda.InventoryQuery.Cloud;

/// <summary>
/// CLOUD即时库存查询
/// </summary>
public class PdaCloudInventoryQueryModel : PdaInventoryQueryModel
{
    public PdaCloudInventoryQueryModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = nameof(PdaCloudInventoryQueryModel);

    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    protected override async Task SourceSearch(long tranId, string valueKey)
    {
        var pdaData = GetPdaData(tranId);
        var inventoryService = App.GetService<K3CloudStkInventoryService>(ServiceProvider);
        var data = await inventoryService.PageAsync(new K3CloudStkInventoryInput
        {
            Page = pdaData.SourcePage.Page,
            PageSize = pdaData.SourcePage.PageSize,
            Field = "FMaterialIdNumber",
            Order = "ASC",
            DescStr = null,
            ComboIds = new List<ErpComboId>(),
            MaterialIdNumber = pdaData.SourceCells["MaterialNumber"] + "",
            MaterialIdName = null,
            BatchNo = null,
            ProduceDateBegin = null,
            ProduceDateEnd = null,
            ExpiryDateBegin = null,
            ExpiryDateEnd = null,
            StockNumber = pdaData.SourceCells["StockNumber"] + "",
            StockName = null,
            StockLocNumber = pdaData.SourceCells["StockLocNumber"] + "",
            StockLocName = null,
            AuxPropNumber = null,
            AuxPropName = null,
            StockOrgIdNumber = pdaData.SourceCells["OrgNumber"] + "",
            StockOrgIdName = null
        });
        pdaData.SourcePage.Total = data.Total;
        pdaData.Sources = data.Items.Adapt<List<Dictionary<string, object>>>();
    }

    protected override PdaApiVanCell GetSourceRefreshShow(Dictionary<string, object> source)
    {
        var cell = new PdaApiVanCell
        {
            Id = source["Fid"] + "",
            Title = $"[{source["FMaterialIdNumber"]}]{source["FMaterialIdName"]}",
            Label = $"{source["FLot_Text"]}{(string.IsNullOrEmpty(source["FProduceDate"] + "") ? "" : "  " + Convert.ToDateTime(source["FProduceDate"]).ToString("yyyy-MM-dd"))}",
            Value = $"[{source["FStockIdNumber"]}]{source["FStockIdName"]} [{source["FStockLocIdNumber"]}]{source["FStockLocIdName"]}",
            SubLabel = $"{source["FMaterialIdSpecification"]}",
            SubValue = $"{Convert.ToDecimal(source["FQty"]).ToString(PdaHelper.DecimalPrecision)} {source["FStockUnitIdName"]}"
        };
        return cell;
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceCells.Properties.Clear();
        pdaShow.SourceCells = pdaData.SourceCells.Adapt<ExtensionObject>();
        // 显示组织
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["OrgNumber"] + ""))
            pdaShow.SourceCells["Org"] = $"[{pdaShow.SourceCells["OrgNumber"]}]{pdaShow.SourceCells["OrgName"]}";
        // 显示物料
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["MaterialNumber"] + ""))
            pdaShow.SourceCells["Material"] = $"[{pdaShow.SourceCells["MaterialNumber"]}]{pdaShow.SourceCells["MaterialName"]}";
        // 显示仓库
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["StockNumber"] + ""))
            pdaShow.SourceCells["Stock"] = $"[{pdaShow.SourceCells["StockNumber"]}]{pdaShow.SourceCells["StockName"]} - [{pdaShow.SourceCells["StockLocNumber"]}]{pdaShow.SourceCells["StockLocName"]}";
        pdaShow.SourcePage = pdaData.SourcePage.Adapt<PdaApiPagination>();
        pdaShow.Sources.Clear();
        foreach (Dictionary<string, object> source in pdaData.Sources)
        {
            pdaShow.Sources.Add(GetSourceRefreshShow(new Dictionary<string, object>(source, StringComparer.OrdinalIgnoreCase)));
        }
    }

    class PdaSchema : IPdaInventorySchema
    {
        public string Title { get; set; } = L.Text["即时库存查询"];

        /// <summary>
        /// 源单列
        /// </summary>
        public List<PdaColumn> SourceCells { get; set; } = new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "org",
                Caption = L.Text["组织"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.org",
                Lookup = new PdaLookup
                {
                    LookupKey = "ORG_Organizations",
                    LookupDataKey = "sources.org",
                    LookupMappings = new List<PdaLookupMapping>
                    {
                        new("OrgId", "FID"),
                        new("OrgNumber", "FNumber"),
                        new("OrgName", "FName"),
                    }
                }
            },
            new PdaColumn
            {
                Fieldname = "material",
                Caption = L.Text["物料"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Material",
                Lookup = new PdaLookup
                {
                    LookupKey = "InventoryQuery_BD_MATERIAL",
                    LookupDataKey = "sources.Material",
                    LookupMappings = new List<PdaLookupMapping>
                    {
                        new("MaterialId", "FID"),
                        new("MaterialNumber", "FNumber"),
                        new("MaterialName", "FName"),
                    }
                }
            },
            new PdaColumn
            {
                Fieldname = "stock",
                Caption = L.Text["仓库仓位"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Stock",
                Lookup = new PdaLookup
                {
                    LookupKey = "InventoryQuery_BD_StockStockLoc",
                    LookupDataKey = "sources.Stock",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new("StockId", "StockId"),
                        new("StockNumber", "StockNumber"),
                        new("StockName", "StockName"),
                        new("StockLocId", "StockLocId"),
                        new("StockLocNumber", "StockLocNumber"),
                        new("StockLocName", "StockLocName"),
                    }
                }
            },
        };
    }
}