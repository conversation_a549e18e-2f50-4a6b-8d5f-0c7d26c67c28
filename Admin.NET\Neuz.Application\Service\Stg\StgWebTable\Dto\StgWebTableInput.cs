﻿namespace Neuz.Application;

/// <summary>
/// Web端表格配置输入参数
/// </summary>
public class StgWebTableInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 配置Json
    /// </summary>
    public string? Json { get; set; }
}

/// <summary>
/// Web端表格搜索配置输入参数
/// </summary>
public class StgWebTableSearchInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 搜索配置Json
    /// </summary>
    public string? SearchJson { get; set; }
}