﻿namespace Neuz.Application;

/// <summary>
/// 日志文件服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysFileLog", Order = 100)]
public class SysFileLogService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysUser> _sysUserRep;
    private readonly string _logDir;

    public SysFileLogService(SqlSugarRepository<SysUser> sysUserRep)
    {
        _sysUserRep = sysUserRep;
        var baseLogDir = App.GetConfig<string>("Logging:File:FileName", true);
        baseLogDir = baseLogDir.LastIndexOf('/') > -1 ? baseLogDir[..baseLogDir.LastIndexOf('/')] : "";
        _logDir = Path.Combine(App.WebHostEnvironment.ContentRootPath, baseLogDir);
    }

    /// <summary>
    /// 获取日志文件列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("getList")]
    public List<FileLogOutput> GetList()
    {
        try
        {
            if (!Directory.Exists(_logDir))
                Directory.CreateDirectory(_logDir);
            var fileList = Directory.GetFiles(_logDir);

            var outputList = new List<FileLogOutput>();
            foreach (var item in fileList)
            {
                var info = new FileInfo(item);
                outputList.Add(new FileLogOutput
                {
                    FileName = info.Name,
                    Size = info.Length,
                    CreateTime = info.CreationTime
                });
            }

            outputList = outputList.OrderByDescending(u => u.CreateTime).ToList();

            return outputList;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="fileNames"></param>
    [HttpPost("delete")]
    public void Delete(List<string> fileNames)
    {
        foreach (var fileName in fileNames)
        {
            var path = Path.Combine(_logDir, fileName);
            try
            {
                File.Delete(path);
            }
            catch
            {
                // ignored
            }
        }
    }

    /// <summary>
    /// 下载
    /// </summary>
    /// <param name="fileName"></param>
    /// <returns></returns>
    [HttpGet("download")]
    [AllowAnonymous]
    [NonUnify]
    public IActionResult Download(string fileName)
    {
        var path = Path.Combine(_logDir, fileName);
        var fs = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
        return new FileStreamResult(fs, "application/octet-stream") { FileDownloadName = fileName };
    }
}