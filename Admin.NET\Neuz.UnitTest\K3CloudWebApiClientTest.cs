﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Adapter.K3Cloud.ApiClient;
using Neuz.Application.Adapter.K3Cloud.ApiClient.Param;
using Newtonsoft.Json;
using Xunit;
using Xunit.Abstractions;

namespace Neuz.UnitTest;

public class K3CloudWebApiClientTest
{
    private readonly ITestOutputHelper _output;

    private const string URL = "http://171.115.221.107:10008/k3cloud/";
    private const string ACCOUNT_NUMBER = "001";
    private const string USER_NAME = "demo";
    private const string PASSWORD = "********";

    private readonly LoginInfo _loginInfo = new() { Url = URL, AccountNumber = ACCOUNT_NUMBER, UserName = USER_NAME, Password = PASSWORD };

    public K3CloudWebApiClientTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;

    }

    [Fact]
    public void GetBusinessInfoTest()
    {
        var client = K3CloudWebApiClientManager.GetClient(_loginInfo);
        var result = client.QueryBusinessInfo(new QueryBusinessInfoParam { FormId = "PUR_PurchaseOrder" });
        _output.WriteLine(JsonConvert.SerializeObject(result));
    }

    [Fact]
    public void QueryTest()
    {
        var client = K3CloudWebApiClientManager.GetClient(_loginInfo);
        var result = client.ExecuteBillQuery(new QueryParam
        {
            FormId = "STK_InStock",
            FieldKeys = new List<string> { "FMaterialId.FNumber as aa", "FDate", "FRealQty", "FGiveAway" },
            TopRowCount = 10
        });
    }

    [Fact]
    public void ViewTest()
    {
        var client = K3CloudWebApiClientManager.GetClient(_loginInfo);
        var result = client.View("STK_InStock", new ViewParam
        {
            Number = "CGRK00044"
        });
    }

    [Fact]
    public void SwitchOrgTest()
    {
        var client = K3CloudWebApiClientManager.GetClient(_loginInfo);
        var result = client.SwitchOrg(new SwitchOrgParam { OrgNumber = "51000" });
    }

    [Fact]
    public void GetFieldAliasNameTest()
    {
        /*
         * 输入 -> 输出
         * FNumber -> FNumber
         * FNumber  FNumber1 -> FNumber1
         * FMaterialId.FName     FMtlName -> FMtlName
         * FMaterialId As     FMtlName2 -> FMtlName2
         * FMaterialId as     FMtlName3 -> FMtlName3
         * FMaterialId.FName as     FMtlName4 -> FMtlName4
         * FMaterialId . FName.FRemark as     FMtlName5 -> FMtlName5（3级的引用实际上金蝶接口不支持）
         * FMaterialId . FName. FRemark -> FMaterialIdFNameFRemark（3级的引用实际上金蝶接口不支持）
         * FMaterialId . FName -> FMaterialIdFName
         */
        Assert.Equal("FNumber", K3CloudWebApiClient.GetFieldAliasName("FNumber"));
        Assert.Equal("FNumber1", K3CloudWebApiClient.GetFieldAliasName("FNumber  FNumber1"));
        Assert.Equal("FMtlName", K3CloudWebApiClient.GetFieldAliasName("FMaterialId.FName     FMtlName"));
        Assert.Equal("FMtlName2", K3CloudWebApiClient.GetFieldAliasName("FMaterialId As     FMtlName2"));
        Assert.Equal("FMtlName3", K3CloudWebApiClient.GetFieldAliasName("FMaterialId as     FMtlName3"));
        Assert.Equal("FMtlName4", K3CloudWebApiClient.GetFieldAliasName("FMaterialId.FName as     FMtlName4"));
        Assert.Equal("FMtlName5", K3CloudWebApiClient.GetFieldAliasName("FMaterialId . FName.FRemark as     FMtlName5"));
        Assert.Equal("FMaterialIdFNameFRemark", K3CloudWebApiClient.GetFieldAliasName("FMaterialId . FName. FRemark"));
        Assert.Equal("FMaterialIdFName", K3CloudWebApiClient.GetFieldAliasName("FMaterialId . FName"));
    }

    [Fact]
    public void CommitResultTest()
    {
        var resultJson =
            @"{""Result"":{""ResponseStatus"":{""ErrorCode"":500,""IsSuccess"":false,""Errors"":[{""FieldName"":""AbstractInteractionResult"",""Message"":""{\""TiTle\"":\""更新库存出现异常情况，更新库存不成功！\"",\""Detail\"":\""{\\\""_Type_\\\"":{\\\""Name\\\"":\\\""MinusCheckResult\\\""},\\\""ErrType\\\"":2,\\\""Entry\\\"":[{\\\""_Type_\\\"":{\\\""Name\\\"":\\\""Entry\\\""},\\\""ErrMessage\\\"":\\\""出现负库存，库存更新不成功。\\\"",\\\""MaterialNumber\\\"":\\\""H3.A.083.026\\\"",\\\""MaterialName\\\"":\\\""改性硅烷密封剂(3.A.083.026)\\\"",\\\""StockName\\\"":\\\""区域1-配件 Area 1 components\\\"",\\\""StockLocName\\\"":\\\""\\\"",\\\""UnitName\\\"":\\\""支/PCS\\\"",\\\""Qty\\\"":\\\""-1.0000\\\"",\\\""SecUnitName\\\"":\\\""\\\"",\\\""SecQty\\\"":\\\""\\\"",\\\""LotText\\\"":\\\""\\\"",\\\""AuxPropName\\\"":\\\""\\\"",\\\""BOMNumber\\\"":\\\""\\\"",\\\""StockStatusName\\\"":\\\""可用\\\"",\\\""OwnerTypeName\\\"":\\\""业务组织\\\"",\\\""OwnerName\\\"":\\\""wms-test\\\"",\\\""KeeperTypeName\\\"":\\\""业务组织\\\"",\\\""KeeperName\\\"":\\\""wms-test\\\"",\\\""BillFormId\\\"":\\\""PRD_PickMtrl\\\"",\\\""EntityKey\\\"":\\\""FEntity\\\"",\\\""BillId\\\"":108323,\\\""EntryId\\\"":140239,\\\""StockOrgId\\\"":1,\\\""MtoNo\\\"":\\\""\\\"",\\\""ProjectNo\\\"":\\\""\\\"",\\\""Specification\\\"":\\\""玻璃胶\\\"",\\\""EntrySeq\\\"":1,\\\""BillNo\\\"":\\\""SOUT00000017\\\"",\\\""BaseUnitName\\\"":\\\""支/PCS\\\"",\\\""BaseQty\\\"":\\\""-1.0000\\\""}]}\""}"",""DIndex"":0}],""SuccessEntitys"":[],""SuccessMessages"":[],""MsgCode"":11}}}";

        var commitResult = CommitResult.FromReturnJson("审核", "生产领料单", resultJson, "");
    }
}