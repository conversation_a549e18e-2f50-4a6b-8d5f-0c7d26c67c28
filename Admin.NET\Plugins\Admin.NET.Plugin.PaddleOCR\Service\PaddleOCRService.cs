// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Furion.DependencyInjection;
using Microsoft.AspNetCore.Http;
using PaddleOCRSharp;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Plugin.PaddleOCR.Service;

/// <summary>
/// PaddleOCR 图像识别服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Description = "PaddleOCR 图像识别")]
public class PaddleOCRService : IDynamicApiController, ISingleton
{
    private readonly Dictionary<string, PaddleOCREngine> _engines;

    public PaddleOCRService()
    {
        _engines = new Dictionary<string, PaddleOCREngine>();
        InitializeEngines();
    }

    /// <summary>
    /// 初始化所有OCR引擎
    /// </summary>
    private void InitializeEngines()
    {
        Console.WriteLine($"[OCR Debug] ===== 开始初始化OCR引擎 =====");

        // 修复路径构建逻辑：需要从插件目录回退到主应用程序目录
        string currentDir = AppContext.BaseDirectory;
        Console.WriteLine($"[OCR Debug] Current AppContext.BaseDirectory: {currentDir}");

        // 如果当前目录包含 Plugins 路径，需要回退到主应用程序目录
        string baseDir = currentDir;
        if (currentDir.Contains("Plugins"))
        {
            // 从插件目录回退到主应用程序目录
            var parts = currentDir.Split(Path.DirectorySeparatorChar, StringSplitOptions.RemoveEmptyEntries);
            var mainAppIndex = -1;
            for (int i = 0; i < parts.Length; i++)
            {
                if (parts[i] == "Plugins")
                {
                    mainAppIndex = i;
                    break;
                }
            }

            if (mainAppIndex > 0)
            {
                baseDir = Path.Combine(parts.Take(mainAppIndex).ToArray());
                // 在Windows下需要添加驱动器号
                if (!baseDir.Contains(":"))
                {
                    baseDir = currentDir.Substring(0, currentDir.IndexOf("Plugins"));
                }
            }
        }

        string baseModelPath = Path.Combine(baseDir, "OcrModel");

        // 添加调试信息
        Console.WriteLine($"[OCR Debug] Original BaseDirectory: {currentDir}");
        Console.WriteLine($"[OCR Debug] Corrected BaseDirectory: {baseDir}");
        Console.WriteLine($"[OCR Debug] Final baseModelPath: {baseModelPath}");
        Console.WriteLine($"[OCR Debug] baseModelPath exists: {Directory.Exists(baseModelPath)}");

        // 1. 中英文模型V4 (轻量版)
        Console.WriteLine($"[OCR Debug] ===== 处理 ch_PP-OCRv4 模型 =====");
        var config1 = new OCRModelConfig();
        string modelPathV4 = Path.Combine(baseModelPath, "ch_PP-OCRv4");
        config1.det_infer = Path.Combine(modelPathV4, "ch_PP-OCRv4_det_infer");
        config1.cls_infer = Path.Combine(modelPathV4, "ch_ppocr_mobile_v2.0_cls_infer");
        config1.rec_infer = Path.Combine(modelPathV4, "ch_PP-OCRv4_rec_infer");
        config1.keys = Path.Combine(modelPathV4, "ppocr_keys.txt");

        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 det_infer path: {config1.det_infer}");
        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 det_infer exists: {Directory.Exists(config1.det_infer)}");
        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 cls_infer exists: {Directory.Exists(config1.cls_infer)}");
        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 rec_infer exists: {Directory.Exists(config1.rec_infer)}");
        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 keys exists: {File.Exists(config1.keys)}");

        if (Directory.Exists(config1.det_infer))
        {
            Console.WriteLine($"[OCR Debug] 开始创建 ch_PP-OCRv4 引擎...");
            try
            {
                _engines["ch_PP-OCRv4"] = new PaddleOCREngine(config1, new OCRParameter());
                Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 engine created successfully");
                Console.WriteLine($"[OCR Debug] 当前 _engines 数量: {_engines.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OCR Debug] Failed to create ch_PP-OCRv4 engine: {ex.Message}");
                Console.WriteLine($"[OCR Debug] Exception details: {ex}");
            }
        }
        else
        {
            Console.WriteLine($"[OCR Debug] ch_PP-OCRv4 det_infer 目录不存在，跳过创建引擎");
        }

        // 2. 服务器中英文模型v2
        Console.WriteLine($"[OCR Debug] ===== 处理 ch_ppocr_server_v2.0 模型 =====");
        var config2 = new OCRModelConfig();
        string modelPathV2 = Path.Combine(baseModelPath, "ch_ppocr_server_v2.0");
        config2.det_infer = Path.Combine(modelPathV2, "ch_ppocr_server_v2.0_det_infer");
        config2.cls_infer = Path.Combine(modelPathV2, "ch_ppocr_mobile_v2.0_cls_infer");
        config2.rec_infer = Path.Combine(modelPathV2, "ch_ppocr_server_v2.0_rec_infer");
        config2.keys = Path.Combine(modelPathV2, "ppocr_keys.txt");

        Console.WriteLine($"[OCR Debug] ch_ppocr_server_v2.0 det_infer exists: {Directory.Exists(config2.det_infer)}");

        if (Directory.Exists(config2.det_infer))
        {
            try
            {
                _engines["ch_ppocr_server_v2.0"] = new PaddleOCREngine(config2, new OCRParameter());
                Console.WriteLine($"[OCR Debug] ch_ppocr_server_v2.0 engine created successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OCR Debug] Failed to create ch_ppocr_server_v2.0 engine: {ex.Message}");
            }
        }

        // 3. 英文和数字模型v3
        Console.WriteLine($"[OCR Debug] ===== 处理 en_PP-OCRv3 模型 =====");
        var config3 = new OCRModelConfig();
        string modelPathEnV3 = Path.Combine(baseModelPath, "en_PP-OCRv3");
        config3.det_infer = Path.Combine(modelPathEnV3, "en_PP-OCRv3_det_infer");
        config3.cls_infer = Path.Combine(modelPathEnV3, "ch_ppocr_mobile_v2.0_cls_infer");
        config3.rec_infer = Path.Combine(modelPathEnV3, "en_PP-OCRv3_rec_infer");
        config3.keys = Path.Combine(modelPathEnV3, "en_dict.txt");

        Console.WriteLine($"[OCR Debug] en_PP-OCRv3 det_infer exists: {Directory.Exists(config3.det_infer)}");

        if (Directory.Exists(config3.det_infer))
        {
            try
            {
                _engines["en_PP-OCRv3"] = new PaddleOCREngine(config3, new OCRParameter());
                Console.WriteLine($"[OCR Debug] en_PP-OCRv3 engine created successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OCR Debug] Failed to create en_PP-OCRv3 engine: {ex.Message}");
            }
        }

        // 4. 服务器中英文模型V4
        Console.WriteLine($"[OCR Debug] ===== 处理 ch_PP-OCRv4_server 模型 =====");
        var config4 = new OCRModelConfig();
        string modelPathServerV4 = Path.Combine(baseModelPath, "ch_PP-OCRv4_server");
        config4.det_infer = Path.Combine(modelPathServerV4, "ch_PP-OCRv4_det_server_infer");
        config4.cls_infer = Path.Combine(modelPathServerV4, "ch_ppocr_mobile_v2.0_cls_infer");
        config4.rec_infer = Path.Combine(modelPathServerV4, "ch_PP-OCRv4_rec_server_infer");
        config4.keys = Path.Combine(modelPathServerV4, "ppocr_keys.txt");

        Console.WriteLine($"[OCR Debug] ch_PP-OCRv4_server det_infer exists: {Directory.Exists(config4.det_infer)}");

        if (Directory.Exists(config4.det_infer))
        {
            try
            {
                _engines["ch_PP-OCRv4_server"] = new PaddleOCREngine(config4, new OCRParameter());
                Console.WriteLine($"[OCR Debug] ch_PP-OCRv4_server engine created successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OCR Debug] Failed to create ch_PP-OCRv4_server engine: {ex.Message}");
            }
        }

        Console.WriteLine($"[OCR Debug] ===== 初始化完成 =====");
        Console.WriteLine($"[OCR Debug] Total engines loaded: {_engines.Count}");
        foreach (var engine in _engines.Keys)
        {
            Console.WriteLine($"[OCR Debug] Loaded engine: {engine}");
        }

        Console.WriteLine($"[OCR Debug] ===== OCR引擎初始化结束 =====");
    }

    /// <summary>
    /// 获取可用的OCR模型列表 📋
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("获取可用模型列表")]
    public async Task<dynamic> GetAvailableModels()
    {
        var models = _engines.Keys.Select(key => new
        {
            ModelName = key,
            Description = GetModelDescription(key)
        }).ToList();

        return await Task.FromResult(new
        {
            AvailableModels = models,
            DefaultModel = _engines.Keys.FirstOrDefault() ?? "无可用模型"
        });
    }

    /// <summary>
    /// 获取模型描述
    /// </summary>
    private string GetModelDescription(string modelName)
    {
        return modelName switch
        {
            "ch_PP-OCRv4" => "中英文模型V4 - 轻量版，适合一般场景",
            "ch_ppocr_server_v2.0" => "服务器中英文模型v2 - 高精度服务器版本",
            "en_PP-OCRv3" => "英文和数字模型v3 - 专门针对英文和数字优化",
            "ch_PP-OCRv4_server" => "服务器中英文模型V4 - 最新高精度服务器版本",
            _ => "未知模型"
        };
    }

    /// <summary>
    /// 识别身份证 🔖
    /// </summary>
    /// <param name="file">图片文件</param>
    /// <param name="modelName">模型名称，可选：ch_PP-OCRv4, ch_ppocr_server_v2.0, en_PP-OCRv3, ch_PP-OCRv4_server</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("识别身份证")]
    public async Task<dynamic> IDCardOCR([Required] IFormFile file, string modelName = "")
    {
        // 选择OCR引擎
        var engine = GetOCREngine(modelName);
        if (engine == null)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"指定的模型 '{modelName}' 不可用，可用模型：{string.Join(", ", _engines.Keys)}",
                AvailableModels = _engines.Keys.ToList()
            });
        }

        using var memoryStream = new MemoryStream();
        await file.CopyToAsync(memoryStream);
        var ocrRes = engine.DetectText(memoryStream.ToArray());

        List<TextBlock> textBlocks = ocrRes.TextBlocks;
        var cardName = TextBlockUtil.ReadIdCardName(textBlocks);
        var cardNo = TextBlockUtil.ReadIdCardNo(textBlocks);
        var cardAddress = TextBlockUtil.ReadIdCardAddress(textBlocks);

        return await Task.FromResult(new
        {
            Success = true,
            UsedModel = modelName.IsNullOrEmpty() ? _engines.Keys.First() : modelName,
            ModelDescription = GetModelDescription(modelName.IsNullOrEmpty() ? _engines.Keys.First() : modelName),
            CardName = cardName,
            CardNo = cardNo,
            CardAddress = cardAddress,
            AllTextBlocks = textBlocks.Select(t => new { t.Text, t.Score }).ToList()
        });
    }

    /// <summary>
    /// 通用文字识别 📝
    /// </summary>
    /// <param name="file">图片文件</param>
    /// <param name="modelName">模型名称</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("通用文字识别")]
    public async Task<dynamic> GeneralOCR([Required] IFormFile file, string modelName = "")
    {
        var engine = GetOCREngine(modelName);
        if (engine == null)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"指定的模型 '{modelName}' 不可用，可用模型：{string.Join(", ", _engines.Keys)}",
                AvailableModels = _engines.Keys.ToList()
            });
        }

        using var memoryStream = new MemoryStream();
        await file.CopyToAsync(memoryStream);
        var ocrRes = engine.DetectText(memoryStream.ToArray());

        return await Task.FromResult(new
        {
            Success = true,
            UsedModel = modelName.IsNullOrEmpty() ? _engines.Keys.First() : modelName,
            ModelDescription = GetModelDescription(modelName.IsNullOrEmpty() ? _engines.Keys.First() : modelName),
            TextBlocks = ocrRes.TextBlocks.Select(t => new
            {
                Text = t.Text,
                Score = t.Score,
                BoundingBox = new { t.BoxPoints }
            }).ToList(),
            FullText = string.Join("\n", ocrRes.TextBlocks.Select(t => t.Text))
        });
    }

    /// <summary>
    /// 获取OCR引擎
    /// </summary>
    private PaddleOCREngine GetOCREngine(string modelName)
    {
        if (string.IsNullOrEmpty(modelName))
        {
            return _engines.Values.FirstOrDefault();
        }

        return _engines.TryGetValue(modelName, out var engine) ? engine : null;
    }
}