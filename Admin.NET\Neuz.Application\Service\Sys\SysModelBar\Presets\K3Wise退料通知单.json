{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "t4.FBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t3.<PERSON><PERSON><PERSON><PERSON>", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t3.F<PERSON><PERSON>", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t4.FDate", "title": "单据日期", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "t8.<PERSON><PERSON><PERSON><PERSON>", "title": "供应商编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t8.<PERSON><PERSON><PERSON>", "title": "供应商名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t1.FSourceBillNo", "title": "源单编号", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "日期", "sortable": true, "format": "Date"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "FSourceBillNo", "title": "源单编码"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "<PERSON><PERSON><PERSON>", "title": "已制作数量"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "SupplierNumber", "title": "供应商编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "SupplierName", "title": "供应商名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillDate", "title": "单据日期", "sortable": true, "format": "Date"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Wise", "idFieldName": "t1.FInterID", "entryIdFieldName": "t1.FEntryID", "content": "SELECT\r\n    t1.FInterID AS _id,\r\n    t1.FEntryID AS _entryId,\r\n    t4.FTranType AS SourceBillKey,\r\n    t1.FInterID AS SourceBillId,\r\n    t1.FEntryID AS SourceBillEntryId,\r\n    t4.FBillNo AS SourceBillNo,\r\n    t4.FDate AS SourceBillDate,\r\n    t1.FItemID AS MaterialId,\r\n    t3.FNumber AS MaterialNumber,\r\n    t3.FName AS MaterialName,\r\n    t3.FShortNumber AS MaterialShortNumber,\r\n    t3.FModel AS MaterialSpec,\r\n    t1.FAuxQty AS Qty,\r\n    t1.FUnitID AS UnitId,\r\n    t13.FNumber AS UnitNumber,\r\n    t13.FName AS UnitName,\r\n    t1.FBatchNo AS BatchNo,\r\n    t3.FBatchManager AS IsBatchManage,\r\n    t3.FIsSnManage AS IsSnManage,\r\n    t3.FISKFPeriod AS IsKfPeriod,\r\n    t1.FKFPeriod AS ExpPeriod,\r\n    t1.FKFDate AS ProduceDate,\r\n    t1.FPeriodDate AS ExpiryDate,\r\n    t4.FSupplyID AS SupplierId,\r\n    t8.FNumber AS SupplierNumber,\r\n    t8.FName AS SupplierName,\r\n    t1.FStockID AS StockId,\r\n    t12.FNumber AS StockNumber,\r\n    t12.FName AS StockName,\r\n    t1.FDCSPID AS StockLocId,\r\n    t2.FNumber AS StockLocNumber,\r\n    t2.FName AS StockLocName,\r\n    t1.FAuxPropID AS AuxPropId,\r\n    t14.FNumber AS AuxPropNumber,\r\n    t14.FName AS AuxPropName,\r\n    t1.FSourceBillNo\r\n    -- t1.FMapNumber,\r\n    -- t1.FMapName,\r\n    -- t1.FQty,\r\n    -- t1.FSecCoefficient,\r\n    -- t1.FNote,\r\n    -- t1.FCommitQty,\r\n    -- t1.FSecCommitQty,\r\n    -- t1.FSecQty,\r\n    -- t1.FSourceInterId,\r\n    -- t1.FSourceEntryID,\r\n    -- t1.FSourceTranType,\r\n    -- t1.FAuxCommitQty,\r\n    -- t3.FAlias AS FItemAlias,\r\n    -- t3.FHelpCode AS FItemHelpCode,\r\n    -- t3.FKFPeriod AS FItemKfPeriod,\r\n    -- t3.FAuxClassID AS FItemAuxClassID,\r\n    -- t3.FUnitGroupID AS FItemUnitGroupID,\r\n    -- t4.FExplanation,\r\n    -- t4.FDeptID,\r\n    -- t4.FEmpID,\r\n    -- t4.FFManagerID,\r\n    -- t4.FRelateBrID,\r\n    -- t4.FBillerID,\r\n    -- t4.FCheckerID,\r\n    -- t4.FCheckDate,\r\n    -- t4.FStatus AS FBillStatus,\r\n    -- t4.FCancellation,\r\n    -- t5.FName AS FCheckerName,\r\n    -- t6.FNumber AS FRelateBrNumber,\r\n    -- t6.FName AS FRelateBrName,\r\n    -- t7.FNumber AS FFManagerNumber,\r\n    -- t7.FName AS FFManagerName,\r\n    -- t9.FNumber AS FEmpNumber,\r\n    -- t9.FName AS FEmpName,\r\n    -- t10.FNumber AS FDeptNumber,\r\n    -- t10.FName AS FDeptName,\r\n    -- t11.FName AS FBillerName,\r\n    -- t13.FCoefficient AS FUnitCoefficient,\r\n    -- t13.FUnitGroupID,\r\n    -- t14.FItemClassID AS FAuxPropClassID\r\nFROM\r\n    POInStockEntry t1\r\n    LEFT JOIN t_StockPlace t2 ON (t2.FSPID = t1.FDCSPID)\r\n    LEFT JOIN t_ICItem t3 ON (t3.FItemID = t1.FItemID)\r\n    LEFT JOIN POInStock t4 ON (t4.FInterID = t1.FInterID)\r\n    LEFT JOIN t_User t5 ON (t5.FUserID = t4.FCheckerID)\r\n    LEFT JOIN t_SonCompany t6 ON (t6.FItemID = t4.FRelateBrID)\r\n    LEFT JOIN t_Emp t7 ON (t7.FItemID = t4.FFManagerID)\r\n    LEFT JOIN t_Supplier t8 ON (t8.FItemID = t4.FSupplyID)\r\n    LEFT JOIN t_Emp t9 ON (t9.FItemID = t4.FEmpID)\r\n    LEFT JOIN t_Department t10 ON (t10.FItemID = t4.FDeptID)\r\n    LEFT JOIN t_User t11 ON (t11.FUserID = t4.FBillerID)\r\n    LEFT JOIN t_Stock t12 ON (t12.FItemID = t1.FStockID)\r\n    LEFT JOIN t_MeasureUnit t13 ON (t13.FMeasureUnitID = t1.FUnitID)\r\n    LEFT JOIN t_AuxItem t14 ON (\r\n        t14.FItemID = t1.FAuxPropID\r\n        AND t14.FItemClassID = t3.FAuxClassID\r\n    )\r\nWHERE\r\n    (t4.FTranType = 73)\r\n    AND (t4.FStatus = 1)"}}