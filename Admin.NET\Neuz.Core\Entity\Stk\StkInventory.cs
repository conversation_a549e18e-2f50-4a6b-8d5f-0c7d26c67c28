﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库位库存
/// </summary>
[SugarTable(null, "库位库存")]
public class StkInventory : EntityTenant
{
    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库位Id")]
    public long WhLocId { get; set; }

    /// <summary>
    /// 库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhLocId))]
    [CustomSerializeFields]
    public BdWhLoc WhLoc { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer Container { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    [SugarColumn(ColumnDescription = "库存数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 锁定数量
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定数量")]
    public decimal LockQty { get; set; }

    /// <summary>
    /// 可用数量
    /// </summary>
    /// <remarks>
    /// 可用数量=库存数量-锁定数量
    /// </remarks>
    [SugarColumn(ColumnDescription = "可用数量")]
    public decimal AvailableQty { get; set; }

    /// <summary>
    /// 预入库数量
    /// </summary>
    [SugarColumn(ColumnDescription = "预入库数量")]
    public decimal PreInQty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }
}