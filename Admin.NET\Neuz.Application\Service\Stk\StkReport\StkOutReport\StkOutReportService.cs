﻿using Furion.Localization;
using Magicodes.ExporterAndImporter.Excel;

namespace Neuz.Application;

/// <summary>
/// 出库报表服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkOutReport", Order = 100)]
public class StkOutReportService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly SqlSugarRepository<StkInventoryLog> _rep;

    /// <summary>
    /// 出库报表服务
    /// </summary>
    public StkOutReportService(IServiceProvider serviceProvider, SqlSugarRepository<StkInventoryLog> rep)
    {
        _serviceProvider = serviceProvider;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("page")]
    public async Task<SqlSugarPagedList<StkOutReportOutput>> PageAsync(StkOutReportInput input)
    {
        var entities = await _rep.Context
            .AddWarehouseFilter<StkInventoryLog>(_serviceProvider, u => u.WarehouseId) // 仓库权限
            .AddWhAreaFilter<StkInventoryLog>(_serviceProvider, u => u.WhAreaId) // 库区权限
            .AddOwnerFilter<StkInventoryLog>(_serviceProvider, u => u.OwnerId) // 货主权限
            .Queryable(
                _rep.AsQueryable()
                    .InnerJoin<BdMaterial>((t1, t2) => t1.MaterialId == t2.Id)
                    .LeftJoin<BdWarehouse>((t1, t2, t3) => t1.WarehouseId == t3.Id)
                    .LeftJoin<BdOwner>((t1, t2, t3, t4) => t1.OwnerId == t4.Id)
                    .LeftJoin<BdWhArea>((t1, t2, t3, t4, t5) => t1.WhAreaId == t5.Id)
                    .LeftJoin<BdWhLoc>((t1, t2, t3, t4, t5, t6) => t1.WhLocId == t6.Id)
                    .LeftJoin<BdUnit>((t1, t2, t3, t4, t5, t6, t7) => t1.UnitId == t7.Id)
                    .LeftJoin<BdAuxPropValue>((t1, t2, t3, t4, t5, t6, t7, t8) => t1.AuxPropValueId == t8.Id)
                    .LeftJoin<BdContainer>((t1, t2, t3, t4, t5, t6, t7, t8, t9) => t1.ContainerId == t9.Id)
                    .Where((t1, t2, t3, t4, t5, t6, t7, t8, t9) =>
                        (t1.InvLogType == StkInvLogType.PutAwayMinus || t1.InvLogType == StkInvLogType.PickMinus || t1.InvLogType == StkInvLogType.ShipMinus ||
                         t1.InvLogType == StkInvLogType.AdjustmentMinus || t1.InvLogType == StkInvLogType.BatchAdjustMinus) && t5.WhAreaType == BdWhAreaType.Storage)
                    .WhereIF(!string.IsNullOrEmpty(input.AuxPropValueNumber), (t1, t2, t3, t4, t5, t6, t7, t8, t9) => t8.Number.Contains(input.AuxPropValueNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.AuxPropValueName), (t1, t2, t3, t4, t5, t6, t7, t8, t9) => t8.Name.Contains(input.AuxPropValueName))
                    .Select((t1, t2, t3, t4, t5, t6, t7, t8, t9) => new
                    {
                        t1.Id,
                        t1.InvLogType,
                        MaterialId = t2.Id,
                        MaterialNumber = t2.Number,
                        MaterialName = t2.Name,
                        t1.BatchNo,
                        t1.ProduceDate,
                        t1.ExpiryDate,
                        t1.Qty,
                        UnitId = t7.Id,
                        UnitNumber = t7.Number,
                        UnitName = t7.Name,
                        OwnerId = t4.Id,
                        OwnerNumber = t4.Number,
                        OwnerName = t4.Name,
                        AuxPropValueId = t8.Id,
                        AuxPropValueNumber = t8.Number,
                        AuxPropValueName = t8.Name,
                        WarehouseId = t3.Id,
                        WarehouseNumber = t3.Number,
                        WarehouseName = t3.Name,
                        WhAreaId = t5.Id,
                        WhAreaNumber = t5.Number,
                        WhAreaName = t5.Name,
                        WhLocId = t6.Id,
                        WhLocNumber = t6.Number,
                        WhLocName = t6.Name,
                        ContainerId = t9.Id,
                        ContainerNumber = t9.Number,
                        t1.RelBillId,
                        t1.RelBillNo,
                        t1.RelBillType,
                        t1.RelBillKey,
                        t1.RelBillEntryId,
                        t1.RelSeq,
                        t1.CreateTime,
                        t1.CreateUserName,
                        t1.GroupName,
                    })
                    .MergeTable()
                    .LeftJoin<StkOutStock>((t, ts1) => t.RelBillId == ts1.Id)
                    .LeftJoin<BdCustomer>((t, ts1, bdc1) => ts1.CustomerId == bdc1.Id)
                    .LeftJoin<StkInStock>((t, ts1, bdc1, ti2) => t.RelBillId == ti2.Id)
                    .LeftJoin<BdCustomer>((t, ts1, bdc1, ti2, bdc2) => ti2.SupplierId == bdc2.Id)
                    .LeftJoin<StkAdjustment>((t, ts1, bdc1, ti2, bdc2, ta3) => t.RelBillId == ta3.Id)
                    .WhereIF(!string.IsNullOrEmpty(input.BillNo), (t, ts1, bdc1, ti2, bdc2, ta3) => t.RelBillNo.Contains(input.BillNo))
                    .WhereIF(!string.IsNullOrEmpty(input.BillType), (t, ts1, bdc1, ti2, bdc2, ta3) => t.RelBillType.Contains(input.BillType))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.MaterialNumber.Contains(input.MaterialNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialName), (t, ts1, bdc1, ti2, bdc2, ta3) => t.MaterialName.Contains(input.MaterialName))
                    .WhereIF(!string.IsNullOrEmpty(input.BatchNo), (t, ts1, bdc1, ti2, bdc2, ta3) => t.BatchNo.Contains(input.BatchNo))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WarehouseNumber.Contains(input.WarehouseNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseName), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WarehouseName.Contains(input.WarehouseName))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.OwnerNumber.Contains(input.OwnerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerName), (t, ts1, bdc1, ti2, bdc2, ta3) => t.OwnerName.Contains(input.OwnerName))
                    .WhereIF(!string.IsNullOrEmpty(input.WhAreaNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WhAreaNumber.Contains(input.WhAreaNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WhAreaName), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WhAreaName.Contains(input.WhAreaName))
                    .WhereIF(!string.IsNullOrEmpty(input.WhLocNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WhLocNumber.Contains(input.WhLocNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WhLocName), (t, ts1, bdc1, ti2, bdc2, ta3) => t.WhLocName.Contains(input.WhLocName))
                    .WhereIF(input.ProduceDate != null, (t, ts1, bdc1, ti2, bdc2, ta3) => t.ProduceDate == input.ProduceDate)
                    .WhereIF(!string.IsNullOrEmpty(input.ContainerNumber), (t, ts1, bdc1, ti2, bdc2, ta3) => t.ContainerNumber.Contains(input.ContainerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.EsBillNo),
                        (t, ts1, bdc1, ti2, bdc2, ta3) => (ts1.EsBillNo.Contains(input.EsBillNo) || ti2.EsBillNo.Contains(input.EsBillNo) || ta3.EsBillNo.Contains(input.EsBillNo)))
                    .WhereIF(input.BillDateBegin != null,
                        (t, ts1, bdc1, ti2, bdc2, ta3) => (ts1.Date >= input.BillDateBegin || ti2.Date >= input.BillDateBegin || ta3.Date >= input.BillDateBegin))
                    .WhereIF(input.BillDateEnd != null,
                        (t, ts1, bdc1, ti2, bdc2, ta3) => (ts1.Date <= input.BillDateEnd || ti2.Date <= input.BillDateEnd || ta3.Date <= input.BillDateEnd))
                    .WhereIF(!string.IsNullOrEmpty(input.CustomerNumber),
                        (t, ts1, bdc1, ti2, bdc2, ta3) => (bdc1.Number.Contains(input.CustomerNumber) || bdc2.Number.Contains(input.CustomerNumber)))
                    .WhereIF(!string.IsNullOrEmpty(input.CustomerName),
                        (t, ts1, bdc1, ti2, bdc2, ta3) => (bdc1.Name.Contains(input.CustomerName) || bdc2.Name.Contains(input.CustomerName)))
                    .WhereIF(input.PickingDateBegin != null, (t, ts1, bdc1, ti2, bdc2, ta3) => t.CreateTime >= input.PickingDateBegin && t.InvLogType == StkInvLogType.PickMinus)
                    .WhereIF(input.PickingDateEnd != null, (t, ts1, bdc1, ti2, bdc2, ta3) => t.CreateTime <= input.PickingDateEnd && t.InvLogType == StkInvLogType.PickMinus)
                    .WhereIF(input.ShippingDateBegin != null, (t, ts1, bdc1, ti2, bdc2, ta3) => t.CreateTime >= input.ShippingDateBegin && t.InvLogType == StkInvLogType.ShipMinus)
                    .WhereIF(input.ShippingDateEnd != null, (t, ts1, bdc1, ti2, bdc2, ta3) => t.CreateTime <= input.ShippingDateEnd && t.InvLogType == StkInvLogType.ShipMinus)
                    .Select((t, ts1, bdc1, ti2, bdc2, ta3) => new StkOutReportOutput
                    {
                        Id = t.Id,
                        BillNo = t.RelBillNo,
                        BillType = t.RelBillType,
                        WarehouseId = t.WarehouseId,
                        WarehouseNumber = t.WarehouseNumber,
                        WarehouseName = t.WarehouseName,
                        OwnerId = t.OwnerId,
                        OwnerNumber = t.OwnerNumber,
                        OwnerName = t.OwnerName,
                        Seq = (int)t.RelSeq,
                        MaterialId = t.MaterialId,
                        MaterialNumber = t.MaterialNumber,
                        MaterialName = t.MaterialName,
                        UnitId = t.Id,
                        UnitNumber = t.UnitNumber,
                        UnitName = t.UnitName,
                        BatchNo = t.BatchNo,
                        ProduceDate = t.ProduceDate,
                        ExpiryDate = t.ExpiryDate,
                        Qty = t.Qty,
                        WhAreaId = t.WhAreaId,
                        WhAreaNumber = t.WhAreaNumber,
                        WhAreaName = t.WhAreaName,
                        WhLocId = t.WhLocId,
                        WhLocNumber = t.WhLocNumber,
                        WhLocName = t.WhLocName,
                        ContainerId = t.ContainerId,
                        ContainerNumber = t.ContainerNumber,
                        AuxPropValueId = t.AuxPropValueId,
                        AuxPropValueNumber = t.AuxPropValueNumber,
                        AuxPropValueName = t.AuxPropValueName,
                        EsBillNo = t.RelBillKey == "StkOutStock" ? ts1.EsBillNo : t.RelBillKey == "StkInStock" ? ti2.EsBillNo : t.RelBillKey == "StkAdjustment" ? ta3.EsBillNo : "",
                        BillDate = t.RelBillKey == "StkOutStock" ? ts1.Date : t.RelBillKey == "StkInStock" ? ti2.Date : ta3.Date,
                        PushFlag = t.RelBillKey == "StkOutStock" ? ts1.PushFlag : t.RelBillKey == "StkInStock" ? ti2.PushFlag : ta3.PushFlag,
                        CustomerId = t.RelBillKey == "StkOutStock" ? bdc1.Id : t.RelBillKey == "StkInStock" ? bdc2.Id : 0,
                        CustomerNumber = t.RelBillKey == "StkOutStock" ? bdc1.Number : t.RelBillKey == "StkInStock" ? bdc2.Number : "",
                        CustomerName = t.RelBillKey == "StkOutStock" ? bdc1.Name : t.RelBillKey == "StkInStock" ? bdc2.Name : "",
                        ShippingDate = t.InvLogType == StkInvLogType.ShipMinus ? t.CreateTime : null,
                        PickingDate = t.InvLogType == StkInvLogType.PickMinus ? t.CreateTime : null
                    })
            )
            .Distinct()
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);
        return entities;
    }

    /// <summary>
    /// 按查询条件导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("exportByQuery")]
    public async Task<FileContentResult> ExportByQuery(StkOutReportInput input)
    {
        input.Page = 1;
        input.PageSize = 50000;
        var output = await PageAsync(input);
        var exporter = new ExcelExporter();
        byte[] bytes = await exporter.ExportAsByteArray(output.Items.ToList());
        var fileName = L.Text["出库报表_{0}.xlsx", DateTimeOffset.Now.ToString("yyyyMMddHHmmss")];
        return new FileContentResult(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") { FileDownloadName = fileName };
    }
}