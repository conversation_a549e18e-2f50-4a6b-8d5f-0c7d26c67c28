﻿namespace Neuz.Core.Enum;

/// <summary>
/// 库区类型
/// </summary>
public enum BdWhAreaType
{
    /// <summary>
    /// 存储区
    /// </summary>
    [Description("存储区"), Theme("primary")]
    Storage = 0,

    /// <summary>
    /// 暂存区
    /// </summary>
    [Description("暂存区"), Theme("warning")]
    Temp = 1,

    /// <summary>
    /// 集货区
    /// </summary>
    [Description("集货区"), Theme("info")]
    Consolidation = 2,
}