﻿namespace Neuz.Core.SeedData;

/// <summary>
/// Neuz 系统菜单种子数据
/// </summary>
[IgnoreUpdateSeed]
public class BarSysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu { Id = 1350000020000, Pid = 0, Title = "Wise", Path = "/k3wise", Name = "k3wise", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2200 },
            new SysMenu { Id = 1350000021000, Pid = 1350000020000, Title = "物料条码打印", Path = "/k3wise/basic", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000021001, Pid = 1350000021000, Title = "物料", Path = "/bar/barModel/K3WiseICItem/物料", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000021002, Pid = 1350000021000, Title = "即时库存", Path = "/bar/barModel/K3WiseInventory/即时库存", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000021003, Pid = 1350000021000, Title = "仓库仓位", Path = "/bar/barModel/K3WiseStock/仓库仓位", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },

            new SysMenu { Id = 1350000022000, Pid = 1350000020000, Title = "采购条码打印", Path = "/k3wise/pur", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000022002, Pid = 1350000022000, Title = "采购订单", Path = "/bar/barModel/K3WiseBill71/采购订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000022001, Pid = 1350000022000, Title = "收料通知单", Path = "/bar/barModel/K3WiseBill72/收料通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000022003, Pid = 1350000022000, Title = "外购入库单", Path = "/bar/barModel/K3WiseBill1/外购入库单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000022004, Pid = 1350000022000, Title = "退料通知单", Path = "/bar/barModel/K3WiseBill73/退料通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000022005, Pid = 1350000022000, Title = "委外订单", Path = "/bar/barModel/K3WiseBill1007105/委外订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000023000, Pid = 1350000020000, Title = "销售条码打印", Path = "/k3wise/sal", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000023001, Pid = 1350000023000, Title = "销售订单", Path = "/bar/barModel/K3WiseBill81/销售订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000023002, Pid = 1350000023000, Title = "发货通知单", Path = "/bar/barModel/K3WiseBill83/发货通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000023003, Pid = 1350000023000, Title = "退货通知单", Path = "/bar/barModel/K3WiseBill82/退货通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },

            new SysMenu { Id = 1350000024000, Pid = 1350000020000, Title = "生产条码打印", Path = "/k3wise/prd", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000024001, Pid = 1350000024000, Title = "生产任务单", Path = "/bar/barModel/K3WiseBill85/生产任务单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000024002, Pid = 1350000024000, Title = "任务单汇报", Path = "/bar/barModel/K3WiseBill551/任务单汇报", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000030000, Pid = 0, Title = "云星空", Path = "/k3cloud", Name = "k3cloud", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2300 },
            new SysMenu { Id = 1350000031000, Pid = 1350000030000, Title = "物料条码打印", Path = "/k3cloud/basic", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000031001, Pid = 1350000031000, Title = "物料", Path = "/bar/barModel/K3CloudBdMaterial/物料", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000031002, Pid = 1350000031000, Title = "即时库存", Path = "/bar/barModel/K3CloudStkInventory/即时库存", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000031003, Pid = 1350000031000, Title = "仓库仓位", Path = "/bar/barModel/K3CloudBdStockStockLoc/仓库仓位", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },

            new SysMenu { Id = 1350000032000, Pid = 1350000030000, Title = "采购条码打印", Path = "/k3cloud/pur", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000032001, Pid = 1350000032000, Title = "收料通知单", Path = "/bar/barModel/K3CloudPurReceiveBill/收料通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000032002, Pid = 1350000032000, Title = "采购订单", Path = "/bar/barModel/K3CloudPurPurchaseOrder/采购订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000033000, Pid = 1350000030000, Title = "销售条码打印", Path = "/k3cloud/sal", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000033001, Pid = 1350000033000, Title = "销售订单", Path = "/bar/barModel/K3CloudSalSaleOrder/销售订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000033002, Pid = 1350000033000, Title = "发货通知单", Path = "/bar/barModel/K3CloudSalDeliveryNotice/发货通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000033003, Pid = 1350000033000, Title = "退货通知单", Path = "/bar/barModel/K3CloudSalReturnNotice/退货通知单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000034000, Pid = 1350000030000, Title = "生产条码打印", Path = "/k3cloud/prd", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000034001, Pid = 1350000034000, Title = "生产订单", Path = "/bar/barModel/K3CloudPrdMo/生产订单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000034002, Pid = 1350000034000, Title = "生产汇报单", Path = "/bar/barModel/K3CloudPrdMoRpt/生产汇报单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000034003, Pid = 1350000034000, Title = "生产退料单", Path = "/bar/barModel/K3CloudPrdReturnMtrl/生产退料单", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000040000, Pid = 0, Title = "条码管理", Path = "/bar", Name = "bar", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2400 },
            new SysMenu { Id = 1350000041000, Pid = 1350000040000, Title = "条码查询", Path = "/bar/barBarcodeQuery", Name = "barBarcodeQuery", Component = "/business/bar/barBarcodeQuery/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000042000, Pid = 1350000040000, Title = "条码日志", Path = "/bar/barBarcodeLogQuery", Name = "barBarcodeLogQuery", Component = "/business/bar/barBarcodeLogQuery/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000043000, Pid = 1350000040000, Title = "装箱", Path = "/bar/barPackage", Name = "barPackage", Component = "/business/bar/barPackage/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000044000, Pid = 1350000040000, Title = "条码调整", Path = "/bar/barAdjust", Name = "barAdjust", Component = "/business/bar/barAdjust/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },

            new SysMenu { Id = 1350000045000, Pid = 1350000040000, Title = "装箱日志", Path = "/bar/barPackageLogQuery", Name = "barPackageLogQuery", Component = "/business/bar/barPackageLogQuery/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },

            new SysMenu { Id = 1350000046000, Pid = 1350000040000, Title = "条码制作模型", Path = "/sys/sysModelBar", Name = "sysModelBar", Component = "/business/sys/sysModelBar/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 9000 },
        };
    }
}