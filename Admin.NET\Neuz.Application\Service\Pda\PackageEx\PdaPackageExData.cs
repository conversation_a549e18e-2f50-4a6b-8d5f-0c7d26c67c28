﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.PackageEx.Dto;

namespace Neuz.Application.Pda.PackageEx;

/// <summary>
/// 装箱数据(为旧版装箱扩展)
/// </summary>
public class PdaPackageExData : IPdaData
{
    public string ModelKey { get; set; }
    public long TranId { get; set; }
    public long UserId { get; set; }
    public PdaShow DataShow { get; set; } = new PdaPackageExShow();
    public bool IsEmptyData()
    {
        return true;
    }

    /// <summary>
    /// 扫描的条码
    /// </summary>
    public List<BarBarcode> Barcodes { get; set; } = new List<BarBarcode>();

    /// <summary>
    /// 装/拆箱
    /// </summary>
    public PdaPackageExStatus Status { get; set; } = PdaPackageExStatus.In;

    /// <summary>
    /// 是否创建新箱
    /// </summary>
    public bool IsNew { get; set; } = false;

    /// <summary>
    /// 箱
    /// </summary>
    public BarPackage Package { get; set; }

    /// <summary>
    /// 是否已提交
    /// </summary>
    public bool IsSubmit { get; set; }

    // 源单数据
    public Dictionary<string, string> BillCells { get; } = new Dictionary<string, string>();
}