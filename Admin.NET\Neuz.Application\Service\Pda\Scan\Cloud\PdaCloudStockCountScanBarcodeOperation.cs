﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.Scan.Cloud;

public class PdaCloudStockCountScanBarcodeOperation : PdaScanBarcodeOperationBase
{
    public PdaCloudStockCountScanBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>();
        var billData = (PdaStockCountData)pdaCacheService.GetBillData(args.Key, args.TranId);
        var pdaModel = (PdaStockCountModel)pdaCacheService.GetPdaModel(billData.ModelKey);
        if (billData.ScanDetails.Count <= 0) throw Oops.Bah(L.Text["请先选择盘点方案"]);
        var barcodeService = App.GetService<BarBarcodeService>(_serviceProvider);
        var barcode = barcodeService.GetBarcodeAsync(args.BarcodeString).Result;
        if (barcode == null) return;
        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        args.Barcodes.Add(barcode);
        if (!args.Properties.ContainsKey("FirstStockType")) throw Oops.Bah(PdaErrorCode.Pda1031);
        pdaModel.ScanBarcode(args.TranId, args.Key, barcode, args.Properties["FirstStockType"] + "");
        args.IsResult = true;
    }
}