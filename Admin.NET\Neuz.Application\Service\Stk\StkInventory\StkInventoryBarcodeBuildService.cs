﻿namespace Neuz.Application;

/// <summary>
/// WMS库位库存条码档案构建服务
/// </summary>
// Injection 名称格式：BdBarcodeFuncKey:{FuncKey}，其中 FuncKey 为 前端指定的 modelKey（注意大小写）
[Injection(Named = "BdBarcodeFuncKey:stkInventory")]
public class StkInventoryBarcodeBuildService : BdBaseBarcodeBuildService, ITransient
{
    public StkInventoryBarcodeBuildService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override List<BdBarcode> CreateNewBarcodes(string modelKey, List<ComboId> comboIds)
    {
        var list = new List<BdBarcode>();

        var entities = BarcodeCodeRuleRep.Context.Queryable<StkInventory>()
            .IncludeNavCol()
            .Where(u => comboIds.Select(v => v.Id).Contains(u.Id))
            .ToList();

        foreach (var entity in entities)
        {
            var barcode = new BdBarcode
            {
                FuncKey = modelKey,
                SrcBillKey = null,
                SrcBillNo = null,
                SrcBillType = null,
                SrcBillEntrySeq = null,
                SrcBillId = null,
                SrcBillEntryId = null,
                Status = BdBarcodeStatus.In, // 已入库
                MaterialId = entity.MaterialId,
                Material = entity.Material,
                BatchNo = entity.BatchNo,
                BatchFile = null,
                ProduceDate = entity.ProduceDate,
                ExpiryDate = entity.ExpiryDate,
                Qty = entity.Qty,
                UnitId = entity.UnitId,
                Unit = entity.Unit,
                ContainerId = null, // TODO: 是否需要填入容器？还是说需要进行一次容器绑定操作
                Container = null, // TODO: 是否需要填入容器？还是说需要进行一次容器绑定操作
                OwnerId = entity.OwnerId,
                Owner = entity.Owner,
                AuxPropValueId = entity.AuxPropValueId,
                AuxPropValue = entity.AuxPropValue,
                WarehouseId = entity.WarehouseId,
                Warehouse = entity.Warehouse,
                WhAreaId = entity.WhAreaId,
                WhArea = entity.WhArea,
                WhLocId = entity.WhLocId,
                WhLoc = entity.WhLoc,
            };
            list.Add(barcode);
        }

        return list;
    }

    public override void ReCalcMadeQty(string modelKey, List<ComboId> comboIds)
    {
        // 不实现已制作条码数量计算
    }

    public override List<(ComboId, decimal)> GetCanMakeQty(string modelKey, List<ComboId> comboIds)
    {
        return new List<(ComboId, decimal)>();
    }
}