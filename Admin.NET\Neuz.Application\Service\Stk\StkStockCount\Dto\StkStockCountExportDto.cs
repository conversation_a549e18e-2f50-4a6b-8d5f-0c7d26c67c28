﻿using Magicodes.ExporterAndImporter.Core;

namespace Neuz.Application;

/// <summary>
/// 库存盘点导出结构
/// </summary>
public class StkStockCountExportDto
{
    /// <summary>
    /// 单据编号
    /// </summary>
    [ExporterHeader(DisplayName = "单据编号")]
    public string BillNo { get; set; }

    /// <summary>
    /// 盘点名称
    /// </summary>
    [ExporterHeader(DisplayName = "盘点名称")]
    public string Name { get; set; }

    /// <summary>
    /// 单据状态
    /// </summary>
    [ExporterHeader(DisplayName = "单据状态")]
    public DocumentStatus DocumentStatus { get; set; }

    /// <summary>
    /// 盘点状态
    /// </summary>
    [ExporterHeader(DisplayName = "盘点状态")]
    public StkStockCountStatus StockCountStatus { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    [ExporterHeader(DisplayName = "仓库编码")]
    public string WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    [ExporterHeader(DisplayName = "仓库名称")]
    public string WarehouseName { get; set; }

    /// <summary>
    /// 外部系统单据编号
    /// </summary>
    [ExporterHeader(DisplayName = "外部系统单据编号")]
    public string EsBillNo { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [ExporterHeader(DisplayName = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    [ExporterHeader(DisplayName = "审核时间", Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? ApproveTime { get; set; }

    /// <summary>
    /// 审核人姓名
    /// </summary>
    [ExporterHeader(DisplayName = "审核人姓名")]
    public string ApproveUserName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [ExporterHeader(DisplayName = "创建时间", Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    [ExporterHeader(DisplayName = "创建者姓名")]
    public string? CreateUserName { get; set; }
}

/// <summary>
/// 库存盘点库区明细导出结构
/// </summary>
public class StkStockCountWhAreaEntryExportDto
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExporterHeader(DisplayName = "序号")]
    public int Seq { get; set; }

    /// <summary>
    /// 库区编码
    /// </summary>
    [ExporterHeader(DisplayName = "库区编码")]
    public string WhAreaNumber { get; set; }

    /// <summary>
    /// 库区名称
    /// </summary>
    [ExporterHeader(DisplayName = "库区名称")]
    public string WhAreaName { get; set; }
}

/// <summary>
/// 库存盘点物料明细导出结构
/// </summary>
public class StkStockCountMaterialEntryExportDto
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExporterHeader(DisplayName = "序号")]
    public int Seq { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [ExporterHeader(DisplayName = "物料编码")]
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [ExporterHeader(DisplayName = "物料名称")]
    public string MaterialName { get; set; }
}

/// <summary>
/// 库存盘点明细导出结构
/// </summary>
public class StkStockCountEntryExportDto
{
    /// <summary>
    /// 序号
    /// </summary>
    [ExporterHeader(DisplayName = "序号")]
    public int Seq { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [ExporterHeader(DisplayName = "物料编码")]
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [ExporterHeader(DisplayName = "物料名称")]
    public string MaterialName { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [ExporterHeader(DisplayName = "批号")]
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [ExporterHeader(DisplayName = "生产日期", Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [ExporterHeader(DisplayName = "有效期至", Format = "yyyy-MM-dd HH:mm:ss", Width = 20)]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 库区编码
    /// </summary>
    [ExporterHeader(DisplayName = "库区编码")]
    public string WhAreaNumber { get; set; }

    /// <summary>
    /// 库区名称
    /// </summary>
    [ExporterHeader(DisplayName = "库区名称")]
    public string WhAreaName { get; set; }

    /// <summary>
    /// 库位编码
    /// </summary>
    [ExporterHeader(DisplayName = "库位编码")]
    public string WhLocNumber { get; set; }

    /// <summary>
    /// 库位名称
    /// </summary>
    [ExporterHeader(DisplayName = "库位名称")]
    public string WhLocName { get; set; }

    /// <summary>
    /// 容器编码
    /// </summary>
    [ExporterHeader(DisplayName = "容器编码")]
    public string ContainerNumber { get; set; }

    /// <summary>
    /// 账存数量
    /// </summary>
    [ExporterHeader(DisplayName = "账存数量")]
    public decimal AcctQty { get; set; }

    /// <summary>
    /// 盘点数量
    /// </summary>
    [ExporterHeader(DisplayName = "盘点数量")]
    public decimal CountQty { get; set; }

    /// <summary>
    /// 盘盈数量
    /// </summary>
    [ExporterHeader(DisplayName = "盘盈数量")]
    public decimal GainQty { get; set; }

    /// <summary>
    /// 盘亏数量
    /// </summary>
    [ExporterHeader(DisplayName = "盘亏数量")]
    public decimal LossQty { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    [ExporterHeader(DisplayName = "单位编码")]
    public string UnitNumber { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [ExporterHeader(DisplayName = "单位名称")]
    public string UnitName { get; set; }

    /// <summary>
    /// 货主编码
    /// </summary>
    [ExporterHeader(DisplayName = "货主编码")]
    public string OwnerNumber { get; set; }

    /// <summary>
    /// 货主名称
    /// </summary>
    [ExporterHeader(DisplayName = "货主名称")]
    public string OwnerName { get; set; }

    /// <summary>
    /// 辅助属性值编码
    /// </summary>
    [ExporterHeader(DisplayName = "辅助属性值编码")]
    public string AuxPropValueNumber { get; set; }

    /// <summary>
    /// 辅助属性值名称
    /// </summary>
    [ExporterHeader(DisplayName = "辅助属性值名称")]
    public string AuxPropValueName { get; set; }

    /// <summary>
    /// 是否通过系统生成
    /// </summary>
    [ExporterHeader(DisplayName = "是否通过系统生成")]
    public bool IsSystem { get; set; }

    /// <summary>
    /// 明细备注
    /// </summary>
    [ExporterHeader(DisplayName = "明细备注")]
    public string EntryMemo { get; set; }
}