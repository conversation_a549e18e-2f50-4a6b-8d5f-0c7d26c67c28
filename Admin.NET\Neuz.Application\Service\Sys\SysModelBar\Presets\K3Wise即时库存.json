{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "库存量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "sortable": true}, {"fieldName": "StockName", "title": "仓库名称", "sortable": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "sortable": true}, {"fieldName": "StockLocName", "title": "仓位名称", "sortable": true}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Wise", "idFieldName": "_id", "entryIdFieldName": "", "content": "SELECT\r\n    *\r\nFROM\r\n    (\r\n        SELECT\r\n            CAST(t1.FItemID AS VARCHAR(50)) + '|' + t1.FBatchNo + '|' + CAST(t1.FStockID AS VARCHAR(50)) + '|' + CAST(t1.FStockPlaceID AS VARCHAR(50)) + '|' + CAST(t1.FK<PERSON>eriod AS VARCHAR(50)) + '|' + t1.FKFDate + '|' + CAST(t1.FAuxPropID AS VARCHAR(50)) + '|' + t1.FMTONo AS _id,\r\n            t1.FItemID AS MaterialId,\r\n            t2.FNumber AS MaterialNumber,\r\n            t2.FName AS MaterialName,\r\n            t2.FShortNumber AS MaterialShortNumber,\r\n            t2.FModel AS MaterialSpec,\r\n            t1.FBatchNo AS BatchNo,\r\n            t1.FQty AS Qty,\r\n            t2.FUnitID AS UnitId,\r\n            t3.FNumber AS UnitNumber,\r\n            t3.FName AS UnitName,\r\n            t2.FBatchManager AS IsBatchManage,\r\n            t2.FIsSnManage AS IsSnManage,\r\n            t2.FISKFPeriod AS IsKfPeriod,\r\n            t1.FKFPeriod AS ExpPeriod,\r\n            t1.FKFDate AS ProduceDate,\r\n            t1.FAuxPropID AS AuxPropId,\r\n            t6.FNumber AS AuxPropNumber,\r\n            t6.FName AS AuxPropName,\r\n            t1.FStockID AS StockId,\r\n            t5.FNumber AS StockNumber,\r\n            t5.FName AS StockName,\r\n            t1.FStockPlaceID AS StockLocId,\r\n            t4.FNumber AS StockLocNumber,\r\n            t4.FName AS StockLocName,\r\n            1 AS IsInStock -- 在库\r\n            -- t1.FBal,\r\n            -- t1.FMTONo AS FMtoNo,\r\n            -- t1.FQtyLock,\r\n            -- t1.FSecQty,\r\n            -- t2.FKFPeriod AS FItemKfPeriod,\r\n            -- t2.FAuxClassID AS FItemAuxClassID,\r\n            -- t2.FUnitGroupID AS FItemUnitGroupID,\r\n            -- t3.FCoefficient AS FUnitCoefficient,\r\n            -- t3.FUnitGroupID,\r\n            -- t6.FItemClassID AS FAuxPropClassID,\r\n        FROM\r\n            ICInventory t1\r\n            LEFT JOIN t_ICItem t2 ON (t2.FItemID = t1.FItemID)\r\n            LEFT JOIN t_MeasureUnit t3 ON (t3.FMeasureUnitID = t2.FUnitID)\r\n            LEFT JOIN t_StockPlace t4 ON (t4.FSPID = t1.FStockPlaceID)\r\n            LEFT JOIN t_Stock t5 ON (t5.FItemID = t1.FStockID)\r\n            LEFT JOIN t_AuxItem t6 ON (\r\n                t6.FItemID = t1.FAuxPropID\r\n                AND t6.FItemClassID = t2.FAuxClassID\r\n            )\r\n        WHERE\r\n            (t1.FQty > 0)\r\n    ) AS table1\r\nWHERE\r\n    1 = 1"}}