﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存盘点库区范围
/// </summary>
[SugarTable(null, "库存盘点库区范围")]
public class StkStockCountWhAreaEntry : EntryEntityBase
{
    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }
}