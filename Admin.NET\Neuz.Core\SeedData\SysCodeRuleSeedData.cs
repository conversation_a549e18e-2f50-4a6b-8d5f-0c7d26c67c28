﻿namespace Neuz.Core.SeedData;

/// <summary>
/// 编码规则种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysCodeRuleSeedData : ISqlSugarEntitySeedData<SysCodeRule>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysCodeRule> HasData()
    {
        return new[]
        {
            new SysCodeRule { Id = 1400000001000, Number = "SysReportTemplate_Number", Name = "报表模版编码", EntityName = "SysReportTemplate", TenantId = 1300000000001, CreateUserId = 142307070910551, CreateUserName = "superAdmin", CreateTime = DateTime.Parse("2022-02-02 00:00:00") },
            new SysCodeRule { Id = 1400000002000, Number = "DemoBarcode", Name = "演示条码规则", EntityName = "BarBarcode", TenantId = 1300000000001, CreateUserId = 142307070910551, CreateUserName = "superAdmin", CreateTime = DateTime.Parse("2022-02-02 00:00:00") },
        };
    }
}

/// <summary>
/// 编码规则明细种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysCodeRuleEntrySeedData : ISqlSugarEntitySeedData<SysCodeRuleEntry>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysCodeRuleEntry> HasData()
    {
        return new[]
        {
            new SysCodeRuleEntry { EntryId = 1400000001001, Id = 1400000001000, Seq = 1, ElementType = ElementType.Const, ConstValue = "RPT", CutStyle = true, AddStyle = true, CodeElement = true },
            new SysCodeRuleEntry { EntryId = 1400000001002, Id = 1400000001000, Seq = 2, ElementType = ElementType.Serial, Length = 5, AddChar = "0", Seed = 1, Increment = 1, CutStyle = true, CodeElement = true },
            new SysCodeRuleEntry { EntryId = 1400000002001, Id = 1400000002000, Seq = 1, ElementType = ElementType.Const, ConstValue = "Demo", CutStyle = true, AddStyle = true, CodeElement = true },
            new SysCodeRuleEntry { EntryId = 1400000002002, Id = 1400000002000, Seq = 2, ElementType = ElementType.Serial, Length = 6, AddChar = "0", Seed = 1, Increment = 1, CutStyle = true, CodeElement = true },
        };
    }
}