using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.Proxy.Dto;

namespace Neuz.Application.Pda.Proxy;

public partial class PdaLocalBillConfigService
{
    /// <summary>
    /// 获取模型列表
    /// </summary>
    /// <returns></returns>
    [HttpPost("page")]
    public Task<SqlSugarPagedList<PdaLocalBillConfigPageOutput>> Page(PdaLocalBillConfigPageInput input)
    {
        var models = GetLocalBillModels();
        var output = new List<PdaLocalBillConfigPageOutput>();
        foreach (KeyValuePair<string, PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>> pdaLocalBillModelBillBase in models)
        {
            var link = pdaLocalBillModelBillBase.Value.Config.BillSchema.BillLink;
            output.Add(new PdaLocalBillConfigPageOutput
            {
                Key = pdaLocalBillModelBillBase.Key,
                Name = $"{link.SourceTitle}_{link.DestTitle}"
            });
        }

        if (!string.IsNullOrEmpty(input.Key)) output = output.Where(r => r.Key.Contains(input.Key)).ToList();
        if (!string.IsNullOrEmpty(input.Name)) output = output.Where(r => r.Name.Contains(input.Name)).ToList();
        
        var paged = output.ToPagedList(input.Page, input.PageSize);
        return Task.FromResult(paged);
    }
}