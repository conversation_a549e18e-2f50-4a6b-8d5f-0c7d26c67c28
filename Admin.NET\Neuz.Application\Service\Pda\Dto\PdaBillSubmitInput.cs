﻿using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Dto;

/// <summary>
/// 
/// </summary>
public class PdaBillSubmitInput
{
    /// <summary>
    /// 
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 是否确认提交(比如校验库存不足,是否前端确认需要提交)
    /// </summary>
    public bool IsRepeat { get; set; }

    /// <summary>
    /// 提交扩展内容
    /// </summary>
    public ExtensionObject Properties { get; set; }
}