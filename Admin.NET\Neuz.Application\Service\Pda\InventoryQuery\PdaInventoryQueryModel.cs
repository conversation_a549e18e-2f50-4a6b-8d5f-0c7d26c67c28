﻿using Furion.Localization;
using Neuz.Application.Erp.Barcode.K3Wise;
using Neuz.Application.Erp.Barcode.K3Wise.Dto;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Application.Erp.Barcode.Dto;
using Neuz.Application.Pda.Bill.Model.Basic.Cloud;
using Neuz.Application.Pda.Helper;

namespace Neuz.Application.Pda.InventoryQuery;

public abstract class PdaInventoryQueryModel : PdaModelBillBase<PdaInventoryQueryShow, PdaInventoryQueryData>
{
    protected PdaCacheService PdaCacheService => App.GetService<PdaCacheService>(ServiceProvider);

    protected PdaInventoryQueryModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public abstract override string Key { get; }
    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    public override void Initialization()
    {
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceCells.Properties.Clear();
        pdaShow.SourceCells = pdaData.SourceCells.Adapt<ExtensionObject>();
        // 显示物料
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["MaterialNumber"] + ""))
            pdaShow.SourceCells["Material"] = $"[{pdaShow.SourceCells["MaterialNumber"]}]{pdaShow.SourceCells["MaterialName"]}";
        // 显示仓库
        if (!string.IsNullOrEmpty(pdaShow.SourceCells["StockNumber"] + ""))
            pdaShow.SourceCells["Stock"] = $"[{pdaShow.SourceCells["StockNumber"]}]{pdaShow.SourceCells["StockName"]} - [{pdaShow.SourceCells["StockLocNumber"]}]{pdaShow.SourceCells["StockLocName"]}";
        pdaShow.SourcePage = pdaData.SourcePage.Adapt<PdaApiPagination>();
        pdaShow.Sources.Clear();
        foreach (Dictionary<string, object> source in pdaData.Sources)
        {
            pdaShow.Sources.Add(GetSourceRefreshShow(new Dictionary<string, object>(source, StringComparer.OrdinalIgnoreCase)));
        }
    }

    protected virtual PdaApiVanCell GetSourceRefreshShow(Dictionary<string, object> source)
    {
        var cell = new PdaApiVanCell
        {
            Id = source["Fid"] + "",
            Title = $"[{source["FItemNumber"]}]{source["FItemName"]}",
            Label = $"{source["FBatchNo"]}{(string.IsNullOrEmpty(source["FKfDate"] + "") ? "" : "  " + Convert.ToDateTime(source["FKfDate"]).ToString("yyyy-MM-dd"))}",
            Value = $"[{source["FStockNumber"]}]{source["FStockName"]} [{source["FSpNumber"]}]{source["FSpName"]}",
            SubLabel = $"{source["FItemModel"]}",
            SubValue = $"{Convert.ToDecimal(source["FQty"]).ToString(PdaHelper.DecimalPrecision)} {source["FUnitName"]}"
        };
        return cell;
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
        ((PdaInventoryQueryData)pdaData).SourcePage.Page = 1;
        ((PdaInventoryQueryData)pdaData).SourcePage.PageSize = PageSize;
        ((PdaInventoryQueryData)pdaData).SourcePage.Total = 0;
    }

    /// <summary>
    /// 查询每页大小
    /// </summary>
    protected virtual int PageSize { get; set; } = 50;

    // 查询lookup
    private Task<List<PdaLookupOutput>> LookupQuerySources(long tranId, string lookupKey, string lookupValue)
    {
        // 获取Lookup的指定Cell
        var cell = ((IPdaInventorySchema)BillSchema).SourceCells.FirstOrDefault(r => r.Type == "lookup" && r.Lookup?.LookupDataKey == lookupKey);
        if (cell == null) throw Oops.Bah(L.Text["没有找到指定Lookup[{0}]", lookupKey]);
        var model = PdaCacheService.GetPdaBasicModel(cell.Lookup.LookupKey);
        if (model is IPdaInventoryQueryBasicLookupModel m)
        {
            m.ModelKey = Key;
        }

        if (model is IPdaBasicLookupModel basicModel)
        {
            return Task.FromResult(basicModel.GetLookupOutput(tranId, basicModel.QueryBasicData(tranId, lookupValue)));
        }

        throw Oops.Bah(L.Text["Lookup[{0}]不是基础资料类型", lookupKey]);
    }

    private Task SelectLookupDataSources(long tranId, string lookupKey, string valueKey)
    {
        // 获取Lookup的指定Cell
        var cell = ((IPdaInventorySchema)BillSchema).SourceCells.FirstOrDefault(r => r.Type == "lookup" && r.Lookup?.LookupDataKey == lookupKey);
        if (cell == null) throw Oops.Bah(L.Text["没有找到指定Lookup[{0}]", lookupKey]);
        var model = PdaCacheService.GetPdaBasicModel(cell.Lookup.LookupKey);
        if (model is IPdaInventoryQueryBasicLookupModel m)
        {
            m.ModelKey = Key;
        }

        if (model is IPdaBasicLookupModel basicModel)
        {
            DataTable lookupData = basicModel.GetBasicData(tranId, valueKey);
            foreach (PdaLookupMapping mapping in cell.Lookup.LookupMappings)
            {
                var pdaData = GetPdaData(tranId);
                string valueStr = "";
                if (lookupData.Rows.Count > 0)
                {
                    var value = lookupData.Rows[0][mapping.SourceFieldName];
                    if (value is DateTime || value is DateTimeOffset)
                    {
                        valueStr = Convert.ToDateTime(value).ToString("yyyy-MM-dd");
                    }
                    else if (value is decimal)
                    {
                        valueStr = Convert.ToDecimal(value).ToString(PdaHelper.DecimalPrecision);
                    }
                    else
                    {
                        valueStr = value + "";
                    }
                }


                pdaData.SourceCells[mapping.DestFieldName] = valueStr;
            }
        }

        return Task.CompletedTask;
    }

    protected virtual async Task SelectLookupDataSourceSearch(long tranId, string valueKey)
    {
        var pdaData = GetPdaData(tranId);
        pdaData.SourcePage.Page = 1;
        await SourceSearch(tranId, valueKey);
    }

    protected virtual async Task SourceSearch(long tranId, string valueKey)
    {
        var pdaData = GetPdaData(tranId);
        var inventoryService = App.GetService<K3WiseIcInventoryService>(ServiceProvider);
        var data = await inventoryService.PageAsync(new K3WiseIcInventoryInput
        {
            Page = pdaData.SourcePage.Page,
            PageSize = pdaData.SourcePage.PageSize,
            Field = "FItemNumber",
            Order = "ASC",
            DescStr = null,
            ComboIds = new List<ErpComboId>(),
            ItemNumber = pdaData.SourceCells["MaterialNumber"] + "",
            ItemName = null,
            ItemModel = null,
            BatchNo = null,
            StockNumber = pdaData.SourceCells["StockNumber"] + "",
            StockName = null
        });
        pdaData.SourcePage.Total = data.Total;
        pdaData.Sources = data.Items.Adapt<List<Dictionary<string, object>>>();
    }

    private async Task SelectLookupDataSourcePageSearch(long tranId, string valueKey)
    {
        var pdaData = GetPdaData(tranId);
        pdaData.SourcePage.Page = Convert.ToInt32(valueKey);
        await SourceSearch(tranId, valueKey);
    }

    class PdaSchema : IPdaInventorySchema
    {
        public string Title { get; set; } = L.Text["即时库存查询"];

        /// <summary>
        /// 源单列
        /// </summary>
        public List<PdaColumn> SourceCells { get; set; } = new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "material",
                Caption = L.Text["物料"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Material",
                Lookup = new PdaLookup
                {
                    LookupKey = "t_ICItem",
                    LookupDataKey = "sources.Material",
                    LookupMappings = new List<PdaLookupMapping>
                    {
                        new("MaterialId", "FItemId"),
                        new("MaterialNumber", "FItemNumber"),
                        new("MaterialName", "FItemName"),
                    }
                }
            },
            new PdaColumn
            {
                Fieldname = "stock",
                Caption = L.Text["仓库仓位"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "sources.Stock",
                Lookup = new PdaLookup
                {
                    LookupKey = "t_stock",
                    LookupDataKey = "sources.Stock",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new("StockId", "StockId"),
                        new("StockNumber", "StockNumber"),
                        new("StockName", "StockName"),
                        new("StockLocId", "StockLocId"),
                        new("StockLocNumber", "StockLocNumber"),
                        new("StockLocName", "StockLocName"),
                    }
                }
            },
        };
    }

    #region 未实现的api

    public override Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        // 扫描条码
        var barcodeService = App.GetService<BarBarcodeService>(ServiceProvider);
        var bar = barcodeService.GetBarcodeAsync(barcode).Result;
        if (bar == null) throw Oops.Bah(L.Text["找不到条码[{0}]", barcode]);
        // 把条码信息,保存到查询记录
        var pdaData = GetPdaData(tranId);
        pdaData.SourceCells["MaterialId"] = bar.MaterialId;
        pdaData.SourceCells["MaterialNumber"] = bar.MaterialNumber;
        pdaData.SourceCells["MaterialName"] = bar.MaterialName;
        pdaData.SourceCells["StockId"] = bar.StockId;
        pdaData.SourceCells["StockNumber"] = bar.StockNumber;
        pdaData.SourceCells["StockName"] = bar.StockName;
        pdaData.SourceCells["StockLocId"] = bar.StockLocId;
        pdaData.SourceCells["StockLocNumber"] = bar.StockLocNumber;
        pdaData.SourceCells["StockLocName"] = bar.StockLocName;
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    public override Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        var splits = lookupKey.Split(".");
        switch (splits[0])
        {
            case "sources":
                return LookupQuerySources(tranId, lookupKey, lookupValue);
            default:
                throw new NotImplementedException();
        }
    }

    public override async Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        var splits = lookupKey.Split(".");
        switch (splits[0])
        {
            case "sources":
                await SelectLookupDataSources(tranId, lookupKey, valueKey);
                break;
            case "sourceSearch":
                await SelectLookupDataSourceSearch(tranId, valueKey);
                break;
            case "sourcePageSearch":
                await SelectLookupDataSourcePageSearch(tranId, valueKey);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        var splits = fieldKey.Split(".");
        if (splits.Length == 2)
        {
            switch (splits[0])
            {
                case "sources":
                    await SelectFieldDataSource(tranId, fieldKey, fieldValue);
                    break;
            }
        }

        RefreshShow(tranId);
    }

    /// <summary>
    /// 设置值
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldKey"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    private Task SelectFieldDataSource(long tranId, string fieldKey, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        var splits = fieldKey.Split(".");
        // 如果lookup列
        var cell = ((IPdaInventorySchema)BillSchema).SourceCells.Find(r => r.FieldDataKey == fieldKey && r.Type == "lookup");
        if (cell != null)
        {
            // 如果cell不为空,清空所有值
            pdaData.SourceCells[splits[1]] = fieldValue;
            cell.Lookup.LookupMappings.ForEach(r => { pdaData.SourceCells[r.DestFieldName] = fieldValue; });
        }
        else
        {
            pdaData.SourceCells[splits[1]] = fieldValue;
        }

        return Task.CompletedTask;
    }

    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        throw new NotImplementedException();
    }

    public override Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        throw new NotImplementedException();
    }

    #endregion
}