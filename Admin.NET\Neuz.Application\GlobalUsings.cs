﻿global using Admin.NET.Core;
global using Admin.NET.Core.Service;
global using Furion;
global using Furion.DatabaseAccessor;
global using Furion.DataEncryption;
global using Furion.DataValidation;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.EventBus;
global using Furion.FriendlyException;
global using Furion.HttpRemote;
global using Furion.JsonSerialization;
global using Furion.UnifyResult;
global using Mapster;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.Caching.Distributed;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Neuz.Application.ErrorCodes;
global using Neuz.Core;
global using Neuz.Core.Entity;
global using Neuz.Core.Enum;
global using Neuz.Core.EqualityComparers;
global using Neuz.Core.Extension;
global using Newtonsoft.Json;
global using Newtonsoft.Json.Linq;
global using Newtonsoft.Json.Serialization;
//global using SqlSugar; //与System.Reflection.IntrospectionExtensions.GetTypeInfo() 冲突
global using System.Collections;
global using System.Collections.Concurrent;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using System.Data;
global using System.Diagnostics;
global using System.Dynamic;
global using System.Linq.Expressions;
global using System.Net;
global using System.Reflection;
global using System.Text;
global using System.Text.RegularExpressions;
global using Yitter.IdGenerator;
