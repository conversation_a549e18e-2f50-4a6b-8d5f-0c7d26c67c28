﻿using Microsoft.AspNetCore.Builder;
using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Proxy;

/// <summary>
/// Pda代理
/// </summary>
[SuppressSniffer]
public static class PdaProxy
{
    /// <summary>
    /// Basic列表
    /// </summary>
    public static IDictionary<string, IPdaLookupModel> BasicModels = new Dictionary<string, IPdaLookupModel>();
    /// <summary>
    /// Bill列表
    /// </summary>
    public static IDictionary<string, IPdaBillModel> BillModels = new Dictionary<string, IPdaBillModel>();
    /// <summary>
    /// Model列表
    /// </summary>
    public static IDictionary<string, IPdaModel> PdaModels = new Dictionary<string, IPdaModel>();
    /// <summary>
    /// 扫描操作列表
    /// </summary>
    public static IDictionary<string, IPdaScanBarcodeOperation> ScanBarcodeOperations = new Dictionary<string, IPdaScanBarcodeOperation>();
    /// <summary>
    /// Pda代理
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UsePdaProxy(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();

        var pdaModels = scope.ServiceProvider.GetServices(typeof(IPdaModel));
        foreach (IPdaModel pdaModel in pdaModels)
        {
            if (pdaModel == null) continue;
            PdaModels.Add(pdaModel.Key, pdaModel);
        }

        var pdaBasicModels = scope.ServiceProvider.GetServices(typeof(IPdaLookupModel));
        foreach (IPdaLookupModel pdaBasicModel in pdaBasicModels)
        {
            if (pdaBasicModel == null) continue;
            BasicModels.Add(pdaBasicModel.LookupKey, pdaBasicModel);
        }

        var pdaBillModels = scope.ServiceProvider.GetServices(typeof(IPdaBillModel));
        foreach (IPdaBillModel pdaBillModel in pdaBillModels)
        {
            if (pdaBillModel == null) continue;
            BillModels.Add(pdaBillModel.Key, pdaBillModel);
        }

        var scanBarcodeOperations = scope.ServiceProvider.GetServices(typeof(IPdaScanBarcodeOperation));
        foreach (IPdaScanBarcodeOperation operation in scanBarcodeOperations)
        {
            if (operation == null) continue;
            ScanBarcodeOperations.Add(operation.Key, operation);
        }
        return app;
    }
}