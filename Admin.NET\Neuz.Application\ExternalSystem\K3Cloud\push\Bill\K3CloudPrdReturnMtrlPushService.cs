using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.ExternalSystem.Dto;
using SqlSugar;

namespace Neuz.Application.ExternalSystem.K3Cloud.push.Bill;


/// <summary>
/// 生产领料单->生产退料单
/// </summary>
/// <remarks>
/// 金蝶FormId: PRD_PickMtrl
/// </remarks>
[Injection(Named = "K3Cloud:PrdReturnMtrl")]

public class K3CloudPrdReturnMtrlPushService : K3CloudBasePushService<StkInStockPushData>
{

    public K3CloudPrdReturnMtrlPushService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider) : base(scopeFactory, serviceProvider)
    {
    }

    protected override string RuleId => "PRD_PICKMTRL2RETURNMTRL";

    protected override string SourceFormId => "PRD_PickMtrl";

    protected override BasePushHandle<StkInStockPushData> GetPushQuery()
    {
        return new InnerPushQuery(ServiceProvider, Rep.Context);
    }

    /// <summary>
    /// 推送查询
    /// </summary>
    private class InnerPushQuery : StkInStockPushHandle
    {
        /// <summary>
        /// K3Cloud 接口
        /// </summary>
        protected K3CloudInterface K3CloudInterface { get; }


        public InnerPushQuery(IServiceProvider serviceProvider, ISqlSugarClient context) : base(serviceProvider, context)
        {
            K3CloudInterface = serviceProvider.GetService<K3CloudInterface>();
        }

        protected override List<string> QueryBillTypes => new() { "SCTLRK" };

        public override async Task UpdateFlag(List<StkInStockPushData> localObjects, EsSyncPushResult esSyncPushResult, bool isByLastPushTime)
        {
            await base.UpdateFlag(localObjects, esSyncPushResult, isByLastPushTime);

            if (!esSyncPushResult.IsSuccess) return;

            // 项目需求，提交成功后，重新查询一次生成的单据，获取收料通知单明细进行回写
            var firstLocalObject = localObjects.First();

            var client = K3CloudInterface.GetK3CloudClient();
            var billList = await client.QueryBillData(new QueryBillParam
            {
                FormId = "PRD_ReturnMtrl",
                FieldKeys = new List<string>
                {
                    "FID",
                    "FBillNo",
                    "FEntity_FEntryID AS FEntryID",
                    "FEntity_FSeq AS FEntrySeq",
                },
                Filters = new List<QueryFilter> { new("FBillNo", QueryType.Equals, esSyncPushResult.TargetBillNo) }
            });
            // 入库单
            var stkInStock = await Context.Queryable<StkInStock>().Includes(u => u.Entries).FirstAsync(u => u.Id == firstLocalObject.Bill.Id);
            stkInStock.EsId = esSyncPushResult.TargetId;
            stkInStock.EsBillNo = esSyncPushResult.TargetBillNo;
            // 保存入库单变更
            await Context.Updateable(stkInStock).ExecuteCommandAsync();

            foreach (var entry in stkInStock.Entries)
            {
                var record = billList.FirstOrDefault(u => Convert.ToInt32(u["FEntrySeq"]) == entry.Seq);
                if (record == null)
                    continue;

                entry.EsEntryId = record["FEntryID"] + "";
            }
            // 保存入库单明细变更
            await Context.Updateable(stkInStock.Entries).ExecuteCommandAsync();
        }
    }
}
