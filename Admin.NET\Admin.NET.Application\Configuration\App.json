{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "Urls": "http://*:5005", // 配置默认端口
  //"https_port": 44325,

  //使用下面的配置，会有提示 Overriding address(es) 'http://localhost:5005'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
  //"Kestrel": {
  //  "Endpoints": {
  //    "Http": {
  //      "Url": "http://*:5005" // 配置默认端口
  //    },
  //    //"HttpsInlineCertAndKeyFile": {
  //    //  "Url": "https://*:5006",
  //    //  "Certificate": {
  //    //    "Path": "D:/cert/cert.pem",
  //    //    "KeyPath": "D:/cert/key.pem"
  //    //  }
  //    //}
  //  }
  //},

  "AllowedHosts": "*",

  "AppSettings": {
    "InjectSpecificationDocument": true, // 生产环境是否开启Swagger
    "ExternalAssemblies": [ "plugins" ] // 插件目录
  },
  "DynamicApiControllerSettings": {
    //"DefaultRoutePrefix": "api", // 默认路由前缀
    "CamelCaseSeparator": "", // 驼峰命名分隔符
    "SplitCamelCase": false, // 切割骆驼(驼峰)/帕斯卡命名
    "LowercaseRoute": false, // 小写路由格式
    "AsLowerCamelCase": true, // 小驼峰命名（首字母小写）
    "KeepVerb": false, // 保留动作方法请求谓词
    "KeepName": false // 保持原有名称不处理
  },
  "FriendlyExceptionSettings": {
    //"DefaultErrorMessage": "系统异常，请联系管理员",
    "ThrowBah": true, // 是否将 Oops.Oh 默认抛出为业务异常
    "LogError": true // 是否输出异常日志
  },
  "LocalizationSettings": {
    "SupportedCultures": [ "zh-CN", "en", "en-ZW" ], // 语言列表
    "DefaultCulture": "zh-CN", // 默认语言
    "DateTimeFormatCulture": "zh-CN" // 固定时间区域为特定时区（多语言）
  },
  "CorsAccessorSettings": {
    // "WithOrigins": ["https://gitee.com"],
    "WithExposedHeaders": [ "Content-Disposition", "X-Pagination", "access-token", "x-access-token" ], // 如果前端不代理且是axios请求
    "SignalRSupport": true // 启用 SignalR 跨域支持
  },
  "SnowId": {
    "WorkerId": 1, // 机器码 全局唯一
    "WorkerIdBitLength": 6, // 机器码位长 默认值6，取值范围 [1, 19]
    "SeqBitLength": 6, // 序列数位长 默认值6，取值范围 [3, 21]（建议不小于4，值越大性能越高、Id位数也更长）
    "WorkerPrefix": "adminnet_" // 缓存前缀
  },
  "Cryptogram": {
    "StrongPassword": false, // 是否开启密码强度验证
    "PasswordStrengthValidation": "(?=^.{6,16}$)(?=.*\\d)(?=.*\\W+)(?=.*[A-Z])(?=.*[a-z])(?!.*\\n).*$", // 密码强度验证正则表达式，必须须包含大小写字母、数字和特殊字符的组合，长度在6-16之间
    "PasswordStrengthValidationMsg": "密码必须包含大小写字母、数字和特殊字符的组合，长度在6-16之间", // 密码强度验证消息提示
    "CryptoType": "SM2", // 密码加密算法：MD5、SM2、SM4
    "PublicKey": "0484C7466D950E120E5ECE5DD85D0C90EAA85081A3A2BD7C57AE6DC822EFCCBD66620C67B0103FC8DD280E36C3B282977B722AAEC3C56518EDCEBAFB72C5A05312", // 公钥
    "PrivateKey": "8EDB615B1D48B8BE188FC0F18EC08A41DF50EA731FA28BF409E6552809E3A111" // 私钥
  },
  "JobCtrl": {
    "DisableLoadJob": false // 是否禁用 Job 加载
  }
}