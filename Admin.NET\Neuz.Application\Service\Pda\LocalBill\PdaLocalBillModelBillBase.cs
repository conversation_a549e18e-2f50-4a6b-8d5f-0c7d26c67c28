﻿using Furion.Localization;
using Neuz.Application.Model;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Scan.LocalBill;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;

namespace Neuz.Application.Pda.LocalBill;

public abstract class PdaLocalBillModelBillBase<TS, TD> : PdaModelBillBase<TS, TD> where TS : PdaLocalBillShow where TD : PdaLocalBillData
{
    protected PdaLocalBillModelBillBase(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Initialization()
    {
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
    }

    /// <summary>
    /// 模型配置
    /// </summary>
    public abstract PdaLocalBillModelConfig Config { get; set; }

    public abstract Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input);

    /// <summary>
    /// 获取单据头信息列表 (查询源单)
    /// </summary>
    /// <returns></returns>
    public abstract Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input);

    /// <summary>
    /// 获取单据信息
    /// </summary>
    /// <returns></returns>
    public abstract Task<dynamic> GetBill(PdaLocalBillLookSelectInput input, bool isException = true);

    /// <summary>
    /// LocalBill Lookup查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public virtual async Task<dynamic> LocalBillLookupQuery(PdaLocalBillPageInput input)
    {
        if (input.IsLocalBill)
        {
            return await LookupBillQuery(input);
        }

        return await LookupBdQuery(input);
    }

    /// <summary>
    /// 查询基础资料
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    protected virtual async Task<dynamic> LookupBdQuery(PdaLocalBillPageInput input)
    {
        var bmService = App.GetService<BdModelController>(ServiceProvider);

        BeforeLookupBdQuery(input.TranId, input);

        var lookup = GetLookup(GetPdaData(input.TranId), input.LookupDataKey);
        // 添加Lookup扩展条件
        if (lookup is { Properties: not null })
        {
            foreach (KeyValuePair<string, object> pair in lookup.Properties)
            {
                input.Properties.Add(pair.Key, pair.Value);
            }
        }

        var jObject = JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(input));
        var outPut = await bmService.LookupQueryAsync(input.LookupKey, jObject);
        Type sqlSugarPagedListType = typeof(SqlSugarPagedList<>);
        if (outPut != null && outPut.GetType().IsGenericType &&
            sqlSugarPagedListType.IsAssignableFrom(outPut.GetType().GetGenericTypeDefinition()))
        {
            // 处理逻辑
            var dyOutPut = JsonConvert.DeserializeObject<SqlSugarPagedList<dynamic>>(JsonConvert.SerializeObject(outPut));
            var result = JsonConvert.DeserializeObject<SqlSugarPagedList<PdaLookupOutput>>(JsonConvert.SerializeObject(dyOutPut));
            var os = new List<PdaLookupOutput>();
            foreach (var lp in dyOutPut.Items)
            {
                os.Add(new PdaLookupOutput()
                {
                    Key = lp.Id + "",
                    Title = lp.Number,
                    SubTitle = lp.Name
                });
            }

            result.Items = os.ToArray();
            return result;
        }

        return outPut;
    }

    /// <summary>
    /// 查基础物料前处理
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="input"></param>
    protected virtual void BeforeLookupBdQuery(long tranId, PdaLocalBillPageInput input)
    {
        // 库区需要过滤只显示暂存区
        if (input.LookupDataKey == "StockInfo")
        {
            var pdaLocalBillCheckStatusService = App.GetService<PdaLocalBillCheckStatusService>(ServiceProvider);
            var codes = pdaLocalBillCheckStatusService.GetWhAreaLocStatusString(Key, tranId);
            input.Properties["WhAreaType"] = codes;
        }
    }

    /// <summary>
    /// 查询单据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    protected virtual async Task<dynamic> LookupBillQuery(PdaLocalBillPageInput input)
    {
        // var link = new LocalBillLinkService(ServiceProvider, $"{Config.BillSchema.BillLink.SourceKey}_{Config.BillSchema.BillLink.DestKey}");
        return await QueryBillList(input);
    }

    /// <summary>
    /// LocalBill Lookup查询后选择
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public virtual async Task<dynamic> LocalBillLookupSelect(PdaLocalBillLookSelectInput input)
    {
        if (input.IsLocalBill)
        {
            return await LocalBillBillSelect(input);
        }

        if (input.LookupDataKey == "StockInfo")
        {
            return await LocalBillWhAreaSelect(input);
        }

        return await LocalBillBdSelect(input);
    }

    /// <summary>
    /// 先择库区库位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public virtual async Task<dynamic> LocalBillWhAreaSelect(PdaLocalBillLookSelectInput input)
    {
        var keys = input.Id.Split("_");
        if (keys.Length != 2) throw Oops.Bah(L.Text["Id[{0}]格式错误", input.Id]);
        var whAreaId = Convert.ToInt64(keys[0]);
        var whLocId = Convert.ToInt64(keys[1]);
        var whAreaLoc = await Rep.Change<BdWhArea>().AsQueryable()
            .InnerJoin<BdWhLoc>((t1, t2) => t1.Id == t2.WhAreaId)
            .Where((t1, t2) => t1.Id == whAreaId && t2.Id == whLocId)
            .Select((t1, t2) => new PdaLocalBillStockInfo
            {
                WhAreaId = t1.Id + "",
                WhAreaNumber = t1.Number,
                WhAreaName = t1.Name,
                WhLocId = t2.Id + "",
                WhLocNumber = t2.Number,
                WhLocName = t2.Name,
                WhAreaType = t1.WhAreaType
            })
            .FirstAsync();
        if (whAreaLoc == null) throw Oops.Bah(L.Text["没有找到库位[{0}_{1}]", whAreaId, whLocId]);
        // 检查仓库仓位是否合法(比如只能上存储区)
        var pdaLocalBillCheckStatusService = App.GetService<PdaLocalBillCheckStatusService>(ServiceProvider);
        pdaLocalBillCheckStatusService.CheckWhAreaLocStatus(input.Key, input.TranId, whAreaLoc);
        var pdaData = GetPdaData(input.TranId);
        pdaData.StockInfo = whAreaLoc;
        RefreshShow(input.TranId);
        return pdaData;
    }

    /// <summary>
    /// 获取基础资料信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    protected virtual async Task<dynamic> LocalBillBillSelectGet(PdaLocalBillLookSelectInput input)
    {
        var bmService = App.GetService<BdModelController>(ServiceProvider);
        var baseModel = bmService.GetModel(input.LookupKey);
        if (baseModel == null) throw Oops.Bah(L.Text["没有找到BaseModel[{0}]", input.LookupKey]);
        var outPut = await baseModel.GetAsync(input.Adapt<IdInput>());
        return outPut;
    }

    /// <summary>
    /// Lookup获取基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    protected virtual async Task<dynamic> LocalBillBdSelect(PdaLocalBillLookSelectInput input)
    {
        var outPut = await LocalBillBillSelectGet(input);

        var pdaData = GetPdaData(input.TranId);
        // 回写
        // 查找LookupDataKey对应的Col
        // 暂时只支持一层回写
        var keys = input.LookupDataKey.Split(".");
        if (keys.Length != 2) throw Oops.Bah(L.Text["LookupDataKey[{0}]格式错误", input.LookupDataKey]);

        var objName = keys[0];
        var objPropertyInfo = pdaData.GetType().GetProperty(objName);
        if (objPropertyInfo == null) throw Oops.Bah(L.Text["没有找到属性类型[{0}]", objName]);
        // 获取PdaData实体
        var propertyName = keys[1];
        var obj = objPropertyInfo.GetValue(pdaData);
        if (obj == null) throw Oops.Bah(L.Text["没有找到属性[{0}]", objName]);
        // 判断input.DetailIndex 是否为空,如果是空,就不是列表方式
        if (input.DetailIndex == null)
        {
            SetObjectValue(obj, outPut, propertyName);
        }
        else
        {
            if (!IsEnumerable(obj.GetType())) return pdaData;
            var itemPropertyInfo = obj.GetType().GetProperty("Item");
            if (itemPropertyInfo == null) return pdaData;
            object item = itemPropertyInfo.GetValue(obj, new object[] { input.DetailIndex });
            SetObjectValue(item, outPut, propertyName);
        }

        // 根据mapping回写
        var lookup = GetLookup(pdaData, input.LookupDataKey);
        if (lookup != null)
        {
            foreach (PdaLookupMapping mapping in lookup.LookupMappings)
            {
                // 获取返回的值
                var sourcePropertyInfo = outPut.GetType().GetProperty(mapping.SourceFieldName);
                if (sourcePropertyInfo == null) continue;
                var sourceValue = sourcePropertyInfo.GetValue(outPut);
                var ms = mapping.DestFieldName.Split(".");

                if (ms.Length == 1)
                {
                    // 如果只有一层,就是保存到当前
                    // 写值
                    SetObjectValue(obj, sourceValue, mapping.DestFieldName);
                }
                else
                {
                    // 如果有二层(暂时只支持2层)
                    var writePropertyInfo = pdaData.GetType().GetProperty(ms[0]);
                    if (writePropertyInfo == null) continue;
                    var writeValue = writePropertyInfo.GetValue(pdaData);
                    SetObjectValue(writeValue, sourceValue, ms[1]);
                }
            }
        }

        AfterLocalBillBdSelect(input, outPut, lookup);

        RefreshShow(input.TranId);
        return pdaData;
    }

    /// <summary>
    /// Lookup获取单据数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    protected virtual async Task<dynamic> LocalBillBillSelect(PdaLocalBillLookSelectInput input)
    {
        var pdaData = GetPdaData(input.TranId);
        await GetBill(input);
        RefreshShow(input.TranId);
        return pdaData;
    }

    /// <summary>
    /// LocalBill string,number,select,date等,值改变
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public virtual Task<dynamic> LocalBillSelectFieldData(PdaLocalBillSelectDataInput input)
    {
        var pdaData = GetPdaData(input.TranId);
        // 暂时只支持一层回写
        var keys = (input.FieldKey + "").Split(".");
        if (keys.Length != 2) return Task.FromResult<dynamic>(pdaData);
        var objName = keys[0];
        var objPropertyInfo = pdaData.GetType().GetProperty(objName);
        if (objPropertyInfo == null) return Task.FromResult<dynamic>(pdaData);
        // 获取PdaData实体
        var propertyName = keys[1];
        var obj = objPropertyInfo.GetValue(pdaData);
        if (obj == null) return Task.FromResult<dynamic>(pdaData);

        // 判断input.DetailIndex 是否为空,如果是空,就不是列表方式
        if (input.DetailIndex == null)
        {
            SetObjectValue(obj, input.FieldValue, propertyName);
        }
        else
        {
            if (!IsEnumerable(obj.GetType())) return Task.FromResult<dynamic>(pdaData);
            var itemPropertyInfo = obj.GetType().GetProperty("Item");
            if (itemPropertyInfo == null) return Task.FromResult<dynamic>(pdaData);
            object item = itemPropertyInfo.GetValue(obj, new object[] { input.DetailIndex });
            SetObjectValue(item, input.FieldValue, propertyName);
        }

        AfterLocalBillSelectFieldData(input);

        RefreshShow(input.TranId);
        return Task.FromResult<dynamic>(pdaData);
    }

    /// <summary>
    /// LocalBill List列表新增列
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public virtual Task<dynamic> LocalBillListAdd(PdaLocalBillListAddInput input)
    {
        var pdaData = GetPdaData(input.TranId);
        // 暂时只支持一层回写
        var keys = (input.ListKey + "").Split(".");
        if (keys.Length != 2) return Task.FromResult<dynamic>(pdaData);
        var objName = keys[0];
        var objPropertyInfo = pdaData.GetType().GetProperty(objName);
        if (objPropertyInfo == null) return Task.FromResult<dynamic>(pdaData);
        // 获取PdaData实体
        var obj = objPropertyInfo.GetValue(pdaData);
        if (obj == null) return Task.FromResult<dynamic>(pdaData);
        if (!IsEnumerable(obj.GetType())) return Task.FromResult<dynamic>(pdaData);
        if (obj.GetType().IsGenericType)
        {
            var paramType = obj.GetType().GetGenericArguments()[0];
            var addMethodInfo = obj.GetType().GetMethod("Add", BindingFlags.Instance | BindingFlags.Public);
            if (addMethodInfo == null) return Task.FromResult<dynamic>(pdaData);
            addMethodInfo.Invoke(obj, new[] { Activator.CreateInstance(paramType) });
        }

        RefreshShow(input.TranId);
        return Task.FromResult<dynamic>(pdaData);
    }

    /// <summary>
    /// LocalBill List列表删除列
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public virtual Task<dynamic> LocalBillListDelete(PdaLocalBillListDeleteInput input)
    {
        var pdaData = GetPdaData(input.TranId);
        if (input.DetailIndex == null) return Task.FromResult<dynamic>(pdaData);
        // 暂时只支持一层回写
        var keys = (input.ListKey + "").Split(".");
        if (keys.Length != 2) return Task.FromResult<dynamic>(pdaData);
        var objName = keys[0];
        var objPropertyInfo = pdaData.GetType().GetProperty(objName);
        if (objPropertyInfo == null) return Task.FromResult<dynamic>(pdaData);
        // 获取PdaData实体
        var obj = objPropertyInfo.GetValue(pdaData);
        if (obj == null) return Task.FromResult<dynamic>(pdaData);
        if (!IsEnumerable(obj.GetType())) return Task.FromResult<dynamic>(pdaData);
        if (obj.GetType().IsGenericType)
        {
            var deleteMethodInfo = obj.GetType().GetMethod("RemoveAt", BindingFlags.Instance | BindingFlags.Public);
            if (deleteMethodInfo == null) return Task.FromResult<dynamic>(pdaData);
            deleteMethodInfo.Invoke(obj, new object[] { input.DetailIndex });
        }

        RefreshShow(input.TranId);
        return Task.FromResult<dynamic>(pdaData);
    }

    protected virtual void AfterLocalBillSelectFieldData(PdaLocalBillSelectDataInput input)
    {
    }

    protected virtual void AfterLocalBillBdSelect(PdaLocalBillLookSelectInput input, dynamic outPut, PdaLocalBillLookup lookup)
    {
    }

    /// <summary>
    /// 获取Mappings
    /// </summary>
    /// <param name="pdaData"></param>
    /// <param name="lookupDataKey"></param>
    /// <returns></returns>
    private PdaLocalBillLookup GetLookup(TD pdaData, string lookupDataKey)
    {
        var mappings = new List<PdaLookupMapping>();
        // 暂时只支持一层回写
        var keys = lookupDataKey.Split(".");
        var key = GetDataToSchemaPropertyKey(keys[0]);
        var propertyInfo = Config.BillSchema.GetType().GetProperty(key);
        if (propertyInfo == null) return null;
        var elColumns = propertyInfo.GetValue(Config.BillSchema) as List<PdaLocalBillColumn>;
        if (elColumns == null) return null;
        var col = elColumns.FirstOrDefault(r =>
            r.Lookup != null && r.Lookup.LookupDataKey.Split(".").Length > 1 && r.Lookup.LookupDataKey.Split(".")[1].Equals(keys[1], StringComparison.OrdinalIgnoreCase));
        if (col == null) return null;
        if (col.Type != "lookup" || col.Lookup == null) return null;
        if (col.Lookup.LookupMappings == null) return null;
        return col.Lookup;
    }

    /// <summary>
    /// 从BillData的属性名获取Schema的属性名
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public virtual string GetDataToSchemaPropertyKey(string key)
    {
        switch (key)
        {
            case "ScanHead":
                return "DestHead";
        }

        return key;
    }

    /// <summary>
    /// 设置对象属性值
    /// </summary>
    /// <param name="obj"></param>
    /// <param name="value"></param>
    /// <param name="propertyName"></param>
    public void SetObjectValue(object obj, object value, string propertyName)
    {
        if ((obj.GetType().IsGenericType && obj.GetType().GetGenericTypeDefinition() == typeof(Dictionary<,>)))
        {
            var types = obj.GetType().GetGenericArguments();
            if (types.Length == 2 && types[0] == typeof(string))
            {
                // 因为这里判断不了目标的类型,这里只能判断值是否 "true" / "false" 判断是否布尔值
                if ($"{value}" == "true" || $"{value}" == "false")
                {
                    value = Convert.ToBoolean(value);
                }

                var itemPropertyInfo = obj.GetType().GetProperty("Item");
                if (itemPropertyInfo == null) return;
                itemPropertyInfo.SetValue(obj, value, new object[] { propertyName });
            }
        }
        else if (obj.GetType().IsSubclassOf(typeof(ExtensionObject)))
        {
            if ($"{value}" == "true" || $"{value}" == "false")
            {
                value = Convert.ToBoolean(value);
            }

            if (obj is ExtensionObject exObj)
            {
                exObj[propertyName] = value;
            }
        }
        else
        {
            var propertyInfo = obj.GetType().GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.Public);
            if (propertyInfo == null) return;
            SetPropertyValue(propertyInfo, obj, value);
        }
    }

    /// <summary>
    /// 设置属性值
    /// </summary>
    /// <param name="propertyInfo"></param>
    /// <param name="obj"></param>
    /// <param name="value"></param>
    public void SetPropertyValue(PropertyInfo propertyInfo, object obj, object value)
    {
        // 这里需要判断类型
        // 如果是字符串
        if (propertyInfo.PropertyType == typeof(string))
        {
            propertyInfo.SetValue(obj, value);
        }
        else if (propertyInfo.PropertyType == typeof(int) || propertyInfo.PropertyType == typeof(int?))
        {
            var isCheck = int.TryParse(value + "", out int iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(int?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成int出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(long) || propertyInfo.PropertyType == typeof(long?))
        {
            var isCheck = long.TryParse(value + "", out long iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(long?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成long出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(Int16) || propertyInfo.PropertyType == typeof(Int16?))
        {
            var isCheck = Int16.TryParse(value + "", out Int16 iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(Int16?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成int16出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(decimal) || propertyInfo.PropertyType == typeof(decimal?))
        {
            var isCheck = decimal.TryParse(value + "", out decimal iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(decimal?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成decimal出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(bool) || propertyInfo.PropertyType == typeof(bool?))
        {
            var isCheck = bool.TryParse(value + "", out bool iv);
            if (!isCheck) throw Oops.Bah(L.Text["[{0}]值[{1}]转成bool出错", propertyInfo.Name, value]);
            propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(DateTime) || propertyInfo.PropertyType == typeof(DateTime?))
        {
            var isCheck = DateTime.TryParse(value + "", out DateTime iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(DateTime?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成DateTime出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        else if (propertyInfo.PropertyType == typeof(DateTimeOffset) || propertyInfo.PropertyType == typeof(DateTimeOffset?))
        {
            var isCheck = DateTimeOffset.TryParse(value + "", out DateTimeOffset iv);
            if (!isCheck)
                if (propertyInfo.PropertyType == typeof(DateTimeOffset?))
                    propertyInfo.SetValue(obj, null);
                else throw Oops.Bah(L.Text["[{0}]值[{1}]转成DateTime出错", propertyInfo.Name, value]);
            else propertyInfo.SetValue(obj, iv);
        }
        // 如果是枚举
        else if (propertyInfo.PropertyType.IsEnum)
        {
            var isCheck = System.Enum.TryParse(propertyInfo.PropertyType, value + "", out object iv);
            if (!isCheck) throw Oops.Bah(L.Text["[{0}]值[{1}]转成枚举出错", propertyInfo.Name, value]);
            propertyInfo.SetValue(obj, iv);
        }
        // 如果是引用类型
        else if (propertyInfo.PropertyType.IsClass)
        {
            object iv = value;
            if (value is string)
                iv = JsonConvert.DeserializeObject(value + "", propertyInfo.PropertyType);
            propertyInfo.SetValue(obj, iv);
        }
        else
        {
            propertyInfo.SetValue(obj, value);
        }
    }

    /// <summary>
    /// 判断类型是否为列表类型
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static bool IsEnumerable(Type type)
    {
        if (type.IsArray)
        {
            return true;
        }

        if (typeof(IEnumerable).IsAssignableFrom(type))
        {
            return true;
        }

        foreach (var it in type.GetInterfaces())
            if (it.IsGenericType && typeof(IEnumerable<>) == it.GetGenericTypeDefinition())
                return true;
        return false;
    }

    /// <summary>
    /// 检查lb-grid必填项
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="columns"></param>
    /// <param name="values"></param>
    /// <returns></returns>
    public void CheckLbGridRequired<T>(long tranId, List<PdaLocalBillColumn> columns, List<T> values)
    {
        var errors = new List<string>();
        var index = 0;
        foreach (var value in values)
        {
            index++;
            foreach (PdaLocalBillColumn column in columns)
            {
                // 只有只读才处理
                if (!column.IsRequired) continue;

                // Lookup是实体类
                if (column.Type == "lookup" && column.Lookup != null)
                {
                    var keys = column.Lookup.LookupDataKey.Split('.');
                    var lookupDataKey = keys[^1];
                    var property = value.GetType().GetProperty(lookupDataKey, BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.Public);
                    if (property == null) continue;
                    var obj = property.GetValue(value);
                    if (IsNullObj(property.PropertyType, obj)) errors.Add(L.Text["数据[{0}]行的[{1}]必填项为空", index, column.Caption]);
                }
                else
                {
                    var keys = column.FieldDataKey.Split('.');
                    var lookupDataKey = keys[^1];
                    var property = value.GetType().GetProperty(lookupDataKey, BindingFlags.IgnoreCase | BindingFlags.Instance | BindingFlags.Public);
                    if (property == null) continue;
                    var obj = property.GetValue(value);
                    if (IsNullObj(property.PropertyType, obj)) errors.Add(L.Text["数据[{0}]的[{1}]必填项为空", index, column.Caption]);
                }
            }
        }

        if (errors.Count > 0) throw Oops.Bah(string.Join("\r\n", errors));
    }

    /// <summary>
    ///  判断对象是否为空
    /// </summary>
    /// <param name="type"></param>
    /// <param name="obj"></param>
    /// <returns></returns>
    private bool IsNullObj(Type type, object obj)
    {
        if (obj == null) return true;
        if (type == typeof(string))
        {
            return string.IsNullOrEmpty(obj + "");
        }

        if (type == typeof(int))
        {
            return Convert.ToInt32(obj) == 0;
        }

        if (type == typeof(long))
        {
            return Convert.ToInt64(obj) == 0;
        }

        if (type == typeof(Int16))
        {
            return Convert.ToInt16(obj) == 0;
        }

        if (type == typeof(decimal))
        {
            return Convert.ToDecimal(obj) == 0;
        }

        return false;
    }

    public override Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        PdaLocalBillScanBarcodeArgs args = new PdaLocalBillScanBarcodeArgs()
        {
            TranId = tranId,
            Key = Key,
            BarcodeString = barcode,
            Barcodes = new List<BdBarcode>(),
            IsResult = false,
            IsRepeat = isRepeat,
            Ext = ext
        };
        foreach (var scanBarcodeOperation in Config.BillParams.ScanBarcodeOperations)
        {
            if (args.IsResult) break;
            scanBarcodeOperation.Operation(args);
        }

        if (!args.IsResult) throw Oops.Bah(PdaErrorCode.Pda1013, barcode);
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    public override Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        throw new NotImplementedException();
    }

    public override Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        throw new NotImplementedException();
    }

    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        throw new NotImplementedException();
    }

    public override Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 从Config 中获取数据,不能在这里获取数据
    /// </summary>
    public override IPdaSchema BillSchema { get; }

    /// <summary>
    /// 判断空值
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public bool IsEmptyValue(object value)
    {
        if (value == null) return true;
        if (string.IsNullOrEmpty(value + "")) return true;
        var type = value.GetType();
        var isNum = type == typeof(int);
        isNum |= type == typeof(decimal);
        isNum |= type == typeof(double);
        isNum |= type == typeof(short);
        isNum |= type == typeof(long);
        if (!isNum) return false;

        if (decimal.Parse(value + "") == 0)
            return true;
        return false;
    }
}