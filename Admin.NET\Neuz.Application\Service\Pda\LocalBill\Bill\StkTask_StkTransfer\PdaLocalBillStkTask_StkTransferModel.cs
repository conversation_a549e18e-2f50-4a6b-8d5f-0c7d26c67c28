﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill.StkTask_StkTransfer;

/// <summary>
/// 任务单->调拨单
/// </summary>
public class PdaLocalBillStkTask_StkTransferModel : PdaLocalBillModel<StkTask, StkTransfer, StkTaskEntry, StkTransferEntry>
{
    public PdaLocalBillStkTask_StkTransferModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkTask_StkTransfer";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destWhAreaLoc",
                Caption = L.Text["调入库区库位"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdWhAreaLocModel",
                    LookupDataKey = "ScanHead.DestWhAreaLoc",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestWhAreaId", "WhAreaId"),
                        new PdaLookupMapping("DestWhAreaNumber", "WhAreaNumber"),
                        new PdaLookupMapping("DestWhAreaName", "WhAreaName"),
                        new PdaLookupMapping("DestWhLocId", "WhLocId"),
                        new PdaLookupMapping("DestWhLocNumber", "WhLocNumber"),
                        new PdaLookupMapping("DestWhLocName", "WhLocName"),
                        new PdaLookupMapping("DestWhAreaLoc", "WhAreaLoc"),
                    }
                }
            }
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkTask",
            SourceTitle = L.Text["任务单"],
            DestKey = "StkTransfer",
            DestTitle = L.Text["调拨单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            SummaryBillFields = new List<PdaLocalBillSummaryBillField>
            {
                new() { FieldName = "MaterialId" },
                new() { FieldName = "MaterialNumber" },
                new() { FieldName = "MaterialName" },
                new() { FieldName = "UnitId" },
                new() { FieldName = "UnitNumber" },
                new() { FieldName = "UnitName" },
                new() { FieldName = "BatchNo" },
                new() { FieldName = "ProduceDate" },
                new() { FieldName = "ExpPeriod" },
                new() { FieldName = "ExpUnit" },
                new() { FieldName = "ExpiryDate" },
                new() { FieldName = "SrcWhAreaId" },
                new() { FieldName = "SrcWhAreaNumber" },
                new() { FieldName = "SrcWhAreaName" },
                new() { FieldName = "SrcWhLocId" },
                new() { FieldName = "SrcWhLocNumber" },
                new() { FieldName = "SrcWhLocName" },
                new() { FieldName = "DestWhAreaId" },
                new() { FieldName = "DestWhAreaNumber" },
                new() { FieldName = "DestWhAreaName" },
                new() { FieldName = "DestWhLocId" },
                new() { FieldName = "DestWhLocNumber" },
                new() { FieldName = "DestWhLocName" },
                new() { FieldName = "SrcBillId" },
                new() { FieldName = "SourceBillEntryId" },
                new() { FieldName = "SrcBillKey" },
                new() { FieldName = "AuxUnitId" },
                new() { FieldName = "AuxPropValueId" },
            }
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkTask>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkTaskEntry>(ServiceProvider, u => u.SrcWhAreaId)
            .AddOwnerFilter<StkTaskEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .LeftJoin<StkInNotice>((t1, t2) => t1.SrcBillId == t2.Id)
                .Where(t1 => linkParam.SourceBillTypes.Contains(t1.SrcBillType) && t1.DocumentStatus == DocumentStatus.Approve && t1.Status != StkTaskStatus.Finish &&
                             (t1.BillNo.Contains(input.Keyword) || t1.SrcBillNo.Contains(input.Keyword) || t1.SrcBillId == SqlFunc.Subqueryable<StkInNoticeEntry>()
                                 .LeftJoin<BdMaterial>((m1, m2) => m1.MaterialId == m2.Id)
                                 .Where((m1, m2) => m1.Id == t1.Id && m1.EntryStatus != StkInNoticeEntryStatus.Finish)
                                 .WhereIF(!string.IsNullOrEmpty(input.Keyword), (m1, m2) => m2.Number.Contains(input.Keyword))
                                 .GroupBy((m1, m2) => m1.Id)
                                 .Select((m1, m2) => m1.Id)))
                .Where(t1 => SqlFunc.Subqueryable<StkTaskEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(r => r.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = $"{r.BillNo}[{r.SrcBillKey}]", SubTitle = r.Date.ToString("yyyy-MM-dd") }).ToArray();
        return result;
    }

    public override void RefreshShow(long tranId)
    {
        base.RefreshShow(tranId);
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => $"{r.SrcBillNo} - {r["EsSrcBillNo"]}").ToList();
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override async Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        // 获取Link
        // 按配置映射数据
        var bill = await SourceRep.AsQueryable().IncludeNavCol().FirstAsync(r => r.BillNo == input.BillNo || r.SrcBillKey == input.BillNo);
        if (bill == null) return null;
        return await GetBill(new PdaLocalBillLookSelectInput
        {
            Key = input.Key,
            TranId = input.TranId,
            LookupDataKey = null,
            LookupKey = null,
            Id = bill.Id + "",
            DetailIndex = null,
            IsLocalBill = false
        });
    }

    /// <inheritdoc/>
    protected override async Task<string> GetBillType(long tranId)
    {
        var billData = GetPdaData(tranId);
        // 通过单据类型获取目标编码
        // 假设只能有一种单据类型暂取第一个
        var billType = await Rep.Change<StkBillType>().AsQueryable()
            .LeftJoin<StkBillTypeNextEntry>((t1, t2) => t1.Id == t2.Id)
            .Where((t1, t2) => t1.Number == billData.ScanDetails[0].DestBillType && t1.EntityName == nameof(StkTask) && t2.NextEntityName == nameof(StkTransfer))
            .Select((t1, t2) => t2.NextBillTypeNumber)
            .FirstAsync();
        if (string.IsNullOrEmpty(billType))
            throw Oops.Bah(L.Text["没有找到单据类型配置: 源单类型[{0}], 目标单类型[{1}], 源单单据类型[{2}]", nameof(StkTask), nameof(StkTransfer), billData.ScanDetails[0].SrcBillKey]);
        return billType;
    }

    protected override void VerifySubmit(long tranId)
    {
        base.VerifySubmit(tranId);
        var pdaData = GetPdaData(tranId);
        if (string.IsNullOrEmpty(pdaData.ScanHead["DestWhAreaId"] + "")) throw Oops.Bah(L.Text["请先选择目标库区"]);
    }
}