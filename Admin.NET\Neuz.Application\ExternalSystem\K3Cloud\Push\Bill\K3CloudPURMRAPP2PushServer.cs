﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.ExternalSystem.Dto;
using Qiniu.Storage;
using SqlSugar;


namespace Neuz.Application.ExternalSystem.K3Cloud.Push.Bill;
/// <summary>
/// K3Cloud 退料通知单推送服务
/// </summary>
/// <remarks>
/// 金蝶FormId: PUR_MRAPP
/// </remarks>
[Injection(Named = "K3Cloud:PURMRAPP2")]
public class K3CloudPURMRAPP2PushService : K3CloudBasePushService<StkTransfer2PushData>
{
    public K3CloudPURMRAPP2PushService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider) :
         base(scopeFactory, serviceProvider)
    {
    }

    protected override BasePushHandle<StkTransfer2PushData> GetPushQuery()
    {
        return new InnerPushQuery(ServiceProvider, Rep.Context);
    }

    protected override string RuleId => "PUR_MRAPP";

    protected override string SourceFormId => "PUR_PurchaseOrder"; // 无源单

    protected override bool IsSumQtyToBeforePush => false; // 根据供应商分组并汇总数量

    protected override async Task AfterSetPushObject(EsSyncPushSetting pushSetting, StkTransfer2PushData localObject,
        Dictionary<string, object> pushObject)
    {
        await base.AfterSetPushObject(pushSetting, localObject, pushObject);

        // 添加供应商信息（从收货单获取）
        if (localObject.Bill_Supplier != null)
        {
            pushObject["FSupplierId.FNumber"] = localObject.Bill_Supplier.EsNumber;// ?? localObject.Bill_Supplier.Number;
            //pushObject["F_PBGK_Supplier"] = localObject.Bill_Supplier.Name;
        }

        pushObject["F_PBGK_Base.FNumber"] = "**********";

        pushObject["FDate"] = DateTime.Now;
        pushObject["F_PBGK_PdaUser"] = localObject.Bill.CreateUserName;

        // 根据单据类型设置不同的字段
        //switch (localObject.Bill.BillType)
        //{
        //    case "SCRKDBD":
        //        pushObject["F_PBGK_BillType"] = "生产调拨";
        //        break;
        //    case "ZJDBD":
        //        pushObject["F_PBGK_BillType"] = "直接调拨";
        //        break;
        //    default:
        //        pushObject["F_PBGK_BillType"] = localObject.Bill.BillType;
        //        break;
        //}

        // 设置明细字段
        pushObject["[FEntity].FMATERIALID.FNumber"] = localObject.Entry_Material.EsNumber ?? localObject.Entry_Material.Number;
        pushObject["[FEntity].FMRAPPQTY"] = localObject.Entry.Qty;
        pushObject["[FEntity].FPRICEUNITID_F.FNumber"] = localObject.Entry_Unit.EsNumber ?? localObject.Entry_Unit.Number;
        pushObject["[FEntity].FLot.FNumber"] = localObject.Entry.BatchNo;
        pushObject["[FEntity].F_PBGK_kc7Seq"] = localObject.Entry.Seq;

        pushObject["[FEntity].FProduceDate"] = localObject.Entry.ProduceDate;
        pushObject["[FEntity].FExpiryDate"] = localObject.Entry.ExpiryDate;

        // 设置源单信息（调拨单的出库通知单）
        //if (localObject.OutNotice != null)
        //{
        //    pushObject["[FBillEntry]._SourceBillId_"] = localObject.OutNotice.EsId;
        //    pushObject["[FBillEntry]._SourceBillEntryId_"] = localObject.OutNoticeEntry?.EsEntryId;
        //}

        // 添加通知单外部编号（从收货单明细获取）
        if (!string.IsNullOrEmpty(localObject.NoticeEsBillNo))
        {
            pushObject["[FEntity].FORDERNO"] = localObject.NoticeEsBillNo;
        }
    }

    /// <summary>
    /// 推送查询
    /// </summary>
    private class InnerPushQuery : StkTransfer2PushHandle
    {
        /// <summary>
        /// K3Cloud 接口
        /// </summary>
        protected K3CloudInterface K3CloudInterface { get; }

        public InnerPushQuery(IServiceProvider serviceProvider, ISqlSugarClient context) : base(serviceProvider,
            context)
        {
            K3CloudInterface = serviceProvider.GetService<K3CloudInterface>();
        }

        protected override List<string> QueryBillTypes => new() { "SCRKDBD", "ZJDBD", "WWYLQD", "WWYLQDDBD", "CJLLSQDBD" };

        
      
        
    
      


        public override async Task UpdateFlag(List<StkTransfer2PushData> localObjects, EsSyncPushResult esSyncPushResult,
            bool isByLastPushTime)
        {
            await base.UpdateFlag(localObjects, esSyncPushResult, isByLastPushTime);

            if (!esSyncPushResult.IsSuccess) return;

            // 项目需求，提交成功后，重新查询一次生成的单据，获取相关信息进行回写
            var firstLocalObject = localObjects.First();

            var client = K3CloudInterface.GetK3CloudClient();
            var billList = await client.QueryBillData(new QueryBillParam
            {
                FormId = "PUR_MRAPP",
                FieldKeys = new List<string>
                {
                    "FID",
                    "FBillNo",
                    "FBillEntry_FEntryID AS FEntryID",
                    "FBillEntry_FSeq AS FEntrySeq",
                },
                Filters = new List<QueryFilter> { new("FBillNo", QueryType.Equals, esSyncPushResult.TargetBillNo) }
            });

            // 调拨单
            var stkTransfer = await Context.Queryable<StkTransfer>().Includes(u => u.Entries)
                .FirstAsync(u => u.Id == firstLocalObject.Bill.Id);
            stkTransfer.EsId = esSyncPushResult.TargetId;
            stkTransfer.EsBillNo = esSyncPushResult.TargetBillNo;
            // 保存调拨单变更
            //await Context.Updateable(stkTransfer).ExecuteCommandAsync();

            foreach (var entry in stkTransfer.Entries)
            {
                var record = billList.FirstOrDefault(u => Convert.ToInt32(u["FEntrySeq"]) == entry.Seq);
                if (record == null)
                    continue;

                entry.EsEntryId = record["FEntryID"] + "";
            }

            // 保存调拨单明细变更
            //await Context.Updateable(stkTransfer.Entries).ExecuteCommandAsync();
        }
    }

}
