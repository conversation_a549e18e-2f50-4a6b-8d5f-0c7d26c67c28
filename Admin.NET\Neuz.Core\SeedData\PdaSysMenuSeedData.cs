﻿namespace Neuz.Core.SeedData;

[IgnoreUpdateSeed]
public class PdaSysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu { Id = 1350000000000, Pid = 0, Title = "手持终端", Path = "/pda", Name = "pda", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 9000 },
            new SysMenu { Id = 1350000001000, Pid = 1350000000000, Title = "K3Wise 入库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000001001, Pid = 1350000001000, Title = "采购订单_外购入库", Path = "/bill/71_1", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000001002, Pid = 1350000001000, Title = "收料通知单_外购入库", Path = "/bill/72_1", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000001003, Pid = 1350000001000, Title = "生产任务单_产品入库", Path = "/bill/85_2", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000001004, Pid = 1350000001000, Title = "销售订单_产品入库", Path = "/bill/81_2", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000001005, Pid = 1350000001000, Title = "任务单汇报_产品入库", Path = "/bill/551_2", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            new SysMenu { Id = 1350000001006, Pid = 1350000001000, Title = "外购入库", Path = "/bill/0_1", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000001007, Pid = 1350000001000, Title = "产品入库", Path = "/bill/0_2", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000001008, Pid = 1350000001000, Title = "委外加工入库", Path = "/bill/0_5", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 800 },
            new SysMenu { Id = 1350000001009, Pid = 1350000001000, Title = "其他入库", Path = "/bill/0_10", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000001010, Pid = 1350000001000, Title = "委外订单_委外加工入库", Path = "/bill/1007105_5", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000001011, Pid = 1350000001000, Title = "采购订单_收料通知单", Path = "/bill/71_72", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1100 },
            new SysMenu { Id = 1350000001012, Pid = 1350000001000, Title = "收料通知单_委外加工入库", Path = "/bill/72_5", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1200 },
            new SysMenu { Id = 1350000001013, Pid = 1350000001000, Title = "销售出库_销售出库(红)", Path = "/bill/21_21Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1300 },
            new SysMenu { Id = 1350000001014, Pid = 1350000001000, Title = "委外订单_收料通知单", Path = "/bill/1007105_72", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000001015, Pid = 1350000001000, Title = "生产领料_生产领料(红)", Path = "/bill/24_24Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1300 },
            new SysMenu { Id = 1350000001016, Pid = 1350000001000, Title = "生产领料(红)", Path = "/bill/0_24Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1300 },
            new SysMenu { Id = 1350000001017, Pid = 1350000001000, Title = "销售出库(红)", Path = "/bill/0_21Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1300 },
            new SysMenu { Id = 1350000001018, Pid = 1350000001000, Title = "退货通知单_销售出库(红)", Path = "/bill/82_21Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1500 },
            new SysMenu { Id = 1350000001019, Pid = 1350000001000, Title = "委外加工出库_委外加工出库(红)", Path = "/bill/28_28Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1600 },

            new SysMenu { Id = 1350000002000, Pid = 1350000000000, Title = "K3Wise 出库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000002001, Pid = 1350000002000, Title = "销售订单_销售出库", Path = "/bill/81_21", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000002002, Pid = 1350000002000, Title = "发货通知_销售出库", Path = "/bill/83_21", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000002003, Pid = 1350000002000, Title = "生产任务_领料单", Path = "/bill/85_24", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000002004, Pid = 1350000002000, Title = "销售出库", Path = "/bill/0_21", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000002005, Pid = 1350000002000, Title = "生产领料", Path = "/bill/0_24", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            new SysMenu { Id = 1350000002006, Pid = 1350000002000, Title = "委外加工出库", Path = "/bill/0_28", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000002007, Pid = 1350000002000, Title = "其它出库", Path = "/bill/0_29", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000002008, Pid = 1350000002000, Title = "产品入库(红)", Path = "/bill/0_2Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 800 },
            new SysMenu { Id = 1350000002009, Pid = 1350000002000, Title = "委外订单_委外加工出库", Path = "/bill/1007105_28", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000002010, Pid = 1350000002000, Title = "外购入库_外购入库(红)", Path = "/bill/1_1Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000002011, Pid = 1350000002000, Title = "收料通知单_退料通知单", Path = "/bill/72_73", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000002012, Pid = 1350000002000, Title = "退料通知单_外购入库(红)", Path = "/bill/73_1Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1100 },
            new SysMenu { Id = 1350000002013, Pid = 1350000002000, Title = "外购入库(红)", Path = "/bill/0_1Red", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1200 },

            new SysMenu { Id = 1350000003000, Pid = 1350000000000, Title = "K3Wise 其他", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003001, Pid = 1350000003000, Title = "盘点", Path = "/StockCount/WiseStockCount", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000003002, Pid = 1350000003000, Title = "盘点重置", Path = "/StockCountReset/WiseStockCount", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000003003, Pid = 1350000003000, Title = "调拨单", Path = "/bill/0_41", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003011, Pid = 1350000003000, Title = "即时库存条码制作", Path = "/buildBarcode/PdaK3WiseBarInventory", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003012, Pid = 1350000003000, Title = "物料条码制作", Path = "/buildBarcode/PdaK3WiseBarIcItem", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003013, Pid = 1350000003000, Title = "收料通知单条码制作", Path = "/buildBarcode/PdaK3WiseBar72", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003014, Pid = 1350000003000, Title = "委外订单条码制作", Path = "/buildBarcode/PdaK3WiseBar1007105", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000003019, Pid = 1350000003000, Title = "即时库存查询", Path = "/inventoryQuery/PdaWiseInventoryQueryModel", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },

            new SysMenu { Id = 1350000004000, Pid = 1350000000000, Title = "云星空 入库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000004001, Pid = 1350000004000, Title = "收料通知单_采购入库单", Path = "/bill/PUR_ReceiveBill_STK_InStock(PUR_ReceiveBill-STK_InStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000004002, Pid = 1350000004000, Title = "生产汇报单_生产入库单", Path = "/bill/PRD_MORPT_PRD_INSTOCK(PRD_MORPT2INSTOCK)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000004003, Pid = 1350000004000, Title = "采购入库单", Path = "/bill/STK_InStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000004004, Pid = 1350000004000, Title = "其他入库单", Path = "/bill/STK_MISCELLANEOUS", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000004005, Pid = 1350000004000, Title = "生产订单_生产入库单", Path = "/bill/PRD_MO_PRD_INSTOCK(PRD_MO2INSTOCK)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            new SysMenu { Id = 1350000004006, Pid = 1350000004000, Title = "采购订单_采购入库单", Path = "/bill/PUR_PurchaseOrder_STK_InStock(PUR_PurchaseOrder-STK_InStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000004007, Pid = 1350000004000, Title = "受托加工材料入库单", Path = "/bill/STK_OEMInStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000004008, Pid = 1350000004000, Title = "生产用料清单_生产退料单", Path = "/bill/PRD_PPBOM_PRD_ReturnMtrl(PRD_PPBOM2RETURNMTRL)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 800 },
            new SysMenu { Id = 1350000004009, Pid = 1350000004000, Title = "销售出库单_销售退货单", Path = "/bill/SAL_OUTSTOCK_SAL_RETURNSTOCK(OutStock-SalReturnStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000004010, Pid = 1350000004000, Title = "寄售结算单_销售退货单", Path = "/bill/SAL_ConsignmentSettle_SAL_RETURNSTOCK(ConsignmentSettle-ReturnStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000004011, Pid = 1350000004000, Title = "委外用料清单_委外退料单", Path = "/bill/SUB_PPBOM_SUB_RETURNMTRL(SUB_PPBOM2RETURNMTRL)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1100 },
            new SysMenu { Id = 1350000004012, Pid = 1350000004000, Title = "生产领料单_生产退料单", Path = "/bill/PRD_PickMtrl_PRD_ReturnMtrl(PRD_PICKMTRL2RETURNMTRL)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1200 },
            new SysMenu { Id = 1350000004013, Pid = 1350000004000, Title = "委外领料单_委外退料单", Path = "/bill/SUB_PickMtrl_SUB_RETURNMTRL(SUB_Pick_Return)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1300 },
            new SysMenu { Id = 1350000004014, Pid = 1350000004000, Title = "收料通知单_检验单", Path = "/bill/PUR_ReceiveBill_QM_InspectBill(QM_PURReceive2Inspect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000004015, Pid = 1350000004000, Title = "采购订单_收料通知单", Path = "/bill/PUR_PurchaseOrder_PUR_ReceiveBill(PUR_PurchaseOrder-PUR_ReceiveBill)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000004016, Pid = 1350000004000, Title = "生产汇报单_检验单", Path = "/bill/PRD_MORPT_QM_InspectBill(QM_PRDMoRpt2Inspect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000004017, Pid = 1350000004000, Title = "退货通知单_销售退货单", Path = "/bill/SAL_RETURNNOTICE_SAL_RETURNSTOCK(SalReturnNotice-SalReturnStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000004018, Pid = 1350000004000, Title = "分步式调出单_分步式调入单", Path = "/bill/STK_TRANSFEROUT_STK_TRANSFERIN(STK_TRANSFEROUT-STK_TRANSFERIN)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },
            new SysMenu { Id = 1350000004019, Pid = 1350000004000, Title = "生产订单_生产汇报单", Path = "/bill/PRD_MO_PRD_MORPT(PRD_MO2MORPT)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1400 },

            new SysMenu { Id = 1350000005000, Pid = 1350000000000, Title = "云星空 出库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            new SysMenu { Id = 1350000005001, Pid = 1350000005000, Title = "生产用料清单_生产领料单", Path = "/bill/PRD_PPBOM_PRD_PickMtrl(PRD_PPBOM2PICKMTRL_NORMAL)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000005002, Pid = 1350000005000, Title = "销售订单_销售出库单", Path = "/bill/SAL_SaleOrder_SAL_OUTSTOCK(SaleOrder-OutStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000005003, Pid = 1350000005000, Title = "销售出库单", Path = "/bill/SAL_OUTSTOCK", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000005004, Pid = 1350000005000, Title = "其他出库单", Path = "/bill/STK_MisDelivery", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000005005, Pid = 1350000005000, Title = "发货通知单_销售出库单", Path = "/bill/SAL_DELIVERYNOTICE_SAL_OUTSTOCK(DeliveryNotice-OutStock)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            new SysMenu { Id = 1350000005006, Pid = 1350000005000, Title = "生产用料清单_生产补料单", Path = "/bill/PRD_PPBOM_PRD_FeedMtrl(PRD_PPBOM2FEEDMTRL)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000005007, Pid = 1350000005000, Title = "采购入库单_采购退料单", Path = "/bill/STK_InStock_PUR_MRB(STK_InStock-PUR_MRB)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000005008, Pid = 1350000005000, Title = "生产入库单_生产退库单", Path = "/bill/PRD_INSTOCK_PRD_RetStock(InStock2ReStockConvert)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 800 },
            new SysMenu { Id = 1350000005009, Pid = 1350000005000, Title = "委外用料清单_委外领料单", Path = "/bill/SUB_PPBOM_SUB_PickMtrl(SUB_PPBOM_Pick)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000005010, Pid = 1350000005000, Title = "委外用料清单_委外补料单", Path = "/bill/SUB_PPBOM_SUB_FEEDMTRL(SUB_PPBOM_FEED)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000005011, Pid = 1350000005000, Title = "采购订单_采购退料单", Path = "/bill/PUR_PurchaseOrder_PUR_MRB(PUR_PurchaseOrder-PUR_MRB)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000005012, Pid = 1350000005000, Title = "收料通知单_采购退料单", Path = "/bill/PUR_ReceiveBill_PUR_MRB(PUR_ReceiveBill-PUR_MRB)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000005013, Pid = 1350000005000, Title = "调拨申请单_分步式调出单", Path = "/bill/STK_TRANSFERAPPLY_STK_TRANSFEROUT(STK_TRANSFERAPPLY-STK_TRANSFEROUT)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000005014, Pid = 1350000005000, Title = "分步式调出单", Path = "/bill/STK_TRANSFEROUT", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000005015, Pid = 1350000005000, Title = "出库申请单_其他出库单", Path = "/bill/STK_OutStockApply_STK_MisDelivery(STK_OutStockApplyToSTK_MisDelivery)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },

            new SysMenu { Id = 1350000006000, Pid = 1350000000000, Title = "云星空 其他", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000006001, Pid = 1350000006000, Title = "直接调拨单", Path = "/bill/STK_TransferDirect", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000006002, Pid = 1350000006000, Title = "销售订单_直接调拨单", Path = "/bill/SAL_SaleOrder_STK_TransferDirect(SaleOrder-StkTransferDirect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000006003, Pid = 1350000006000, Title = "物料盘点作业", Path = "/StockCount/CloudStockCount", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000006004, Pid = 1350000006000, Title = "物料盘点作业重置", Path = "/StockCountReset/CloudStockCount", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000006005, Pid = 1350000006000, Title = "发货通知单_直接调拨单", Path = "/bill/SAL_DELIVERYNOTICE_STK_TransferDirect(DeliveryNotice-StkTransferDirect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000006006, Pid = 1350000006000, Title = "生产用料清单_直接调拨单", Path = "/bill/PRD_PPBOM_STK_TransferDirect(PRD_PPBOM-STK_TransferDirect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000006007, Pid = 1350000006000, Title = "物料条码制作", Path = "/buildBarcode/PdaK3CloudBarBdMaterial", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000006008, Pid = 1350000006000, Title = "即时库存条码制作", Path = "/buildBarcode/PdaK3CloudBarStkInventory", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 800 },
            new SysMenu { Id = 1350000006009, Pid = 1350000006000, Title = "收料通知单条码制作", Path = "/buildBarcode/PdaK3CloudBarPurReceiveBill", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000006010, Pid = 1350000006000, Title = "调拨申请单_直接调拨单", Path = "/bill/STK_TRANSFERAPPLY_STK_TransferDirect(StkTransferApply-StkTransferDirect)", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 900 },
            new SysMenu { Id = 1350000006011, Pid = 1350000006000, Title = "即时库存查询", Path = "/inventoryQuery/PdaCloudInventoryQueryModel", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350000006012, Pid = 1350000006000, Title = "采购订单条码制作", Path = "/buildBarcode/PdaK3CloudPurPurchaseOrder", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1100 },

            new SysMenu { Id = 1350000008000, Pid = 1350000000000, Title = "WMS 入库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000008001, Pid = 1350000008000, Title = "收货", Path = "localBill/StkInNotice_StkReceive", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000008002, Pid = 1350000008000, Title = "上架", Path = "localBill/StkReceive_StkInStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000008003, Pid = 1350000008000, Title = "无源入库单", Path = "localBill/_StkInStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000008004, Pid = 1350000008000, Title = "其它入库", Path = "localBill/_StkAdjustmentIn", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000008005, Pid = 1350000008000, Title = "入库通知_入库单", Path = "localBill/StkInNotice_StkInStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },

            new SysMenu { Id = 1350000009000, Pid = 1350000000000, Title = "WMS 出库", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350000009001, Pid = 1350000009000, Title = "下架", Path = "localBill/StkTask_StkOutStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000009002, Pid = 1350000009000, Title = "出库通知_出库单", Path = "localBill/StkOutNotice_StkOutStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000009003, Pid = 1350000009000, Title = "无源出库单", Path = "localBill/_StkOutStock", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000009004, Pid = 1350000009000, Title = "其它出库", Path = "localBill/_StkAdjustmentOut", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000009005, Pid = 1350000009000, Title = "生产补料", Path = "localBill/StkOutNotice_StkOutStock_SCLLBLD", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },

            new SysMenu { Id = 1350001000000, Pid = 1350000000000, Title = "WMS 其他", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 600 },
            new SysMenu { Id = 1350001000001, Pid = 1350001000000, Title = "调拨单", Path = "localBill/_StkTransfer", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350001000002, Pid = 1350001000000, Title = "库位库存查询", Path = "/inventoryQuery/PdaLocalBillInventoryQueryModel", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1000 },
            new SysMenu { Id = 1350001000003, Pid = 1350001000000, Title = "盘点", Path = "/StkStockCount/StkStockCount", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1010 },
            new SysMenu { Id = 1350001000004, Pid = 1350001000000, Title = "调整单_调整单", Path = "/localBill/StkAdjustment_StkAdjustment", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 1010 },

            new SysMenu { Id = 1350000007000, Pid = 1350000000000, Title = "其他", Path = "", Name = "", Component = "", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 700 },
            new SysMenu { Id = 1350000007001, Pid = 1350000007000, Title = "装箱", Path = "/packageEx", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1350000007002, Pid = 1350000007000, Title = "拆条码", Path = "/splitBarcode", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            new SysMenu { Id = 1350000007003, Pid = 1350000007000, Title = "入库通知单制作条码", Path = "/buildBarcode/PdaLocalBillBarStkInNotice", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 300 },
            new SysMenu { Id = 1350000007004, Pid = 1350000007000, Title = "收货单制作条码", Path = "/buildBarcode/PdaLocalBillBarStkReceive", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 400 },
            new SysMenu { Id = 1350000007005, Pid = 1350000007000, Title = "出库通知单制作条码", Path = "/buildBarcode/PdaLocalBillBarStkOutNotice", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
            
            new SysMenu { Id = 1350000007006, Pid = 1350000007000, Title = "修改条码数量", Path = "/modifyBarcode", Name = "", Component = "", Icon = "ele-Tickets", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 500 },
        };
    }
}