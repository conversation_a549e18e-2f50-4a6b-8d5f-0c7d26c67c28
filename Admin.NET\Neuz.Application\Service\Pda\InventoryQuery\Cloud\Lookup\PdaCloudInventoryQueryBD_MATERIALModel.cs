﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Bill.Model.Basic.Cloud;

/// <summary>
/// 仓库仓位 Lookup 模型
/// </summary>
public class PdaCloudInventoryQueryBD_MATERIALModel : PdaCloudBasicLookupModel, IPdaInventoryQueryBasicLookupModel
{
    /// <summary>
    /// 仓库仓位 Lookup 模型
    /// </summary>
    public PdaCloudInventoryQueryBD_MATERIALModel(IServiceProvider serviceProvider, K3CloudInterface k3CloudInterface) : base(serviceProvider, k3CloudInterface)
    {
    }

    /// <inheritdoc />
    protected override string FormId => "BD_MATERIAL";

    public override string LookupKey => "InventoryQuery_BD_MATERIAL";

    /// <inheritdoc />
    public override List<PdaLookupOutput> GetLookupOutput(long tranId, DataTable table)
    {
        var lookupOutputs = new List<PdaLookupOutput>();
        foreach (DataRow row in table.Rows)
        {
            var lookupOutput = new PdaLookupOutput
            {
                Key = $"{row["FNumber"]}",
                Title = $"{row["FNumber"]} {row["FNumber"]}",
                SubTitle = $"{row["FName"]} {row["FName"]}"
            };
            lookupOutputs.Add(lookupOutput);
        }

        return lookupOutputs;
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateQueryBasicDataParam(long tranId, string keyword)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FMATERIALID AS FID",
                "FNumber AS FNumber",
                "FName AS FName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FNumber", QueryType.Contains, keyword, "group1"),
                new("FName", QueryType.Contains, keyword, "group1"),
            },
            Orders = new List<QueryOrder>
            {
                new("FNumber"),
            },
        };
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateGetBasicDataParam(long tranId, string id)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FMATERIALID AS FID",
                "FNumber AS FNumber",
                "FName AS FName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FNumber", QueryType.Equals, id),
            },
            Orders = new List<QueryOrder>
            {
                new("FNumber"),
            },
        };
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateQueryCustomDataParam(long tranId, string filter)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        var numbers = filter.Split("_");
        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FMATERIALID AS FID",
                "FNumber AS FNumber",
                "FName AS FName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FNumber", QueryType.Equals, numbers[0]),
            },
            Orders = new List<QueryOrder>
            {
                new("FNumber"),
            },
        };
    }

    public string ModelKey { get; set; }
}