﻿namespace Neuz.Application;

/// <summary>
/// 库存盘点提交输入参数
/// </summary>
public class StkStockCountSubmitInput
{
    /// <summary>
    /// 盘点Id
    /// </summary>
    public long StockCountId { get; set; }

    /// <summary>
    /// 事务Id
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 库存盘点提交明细集合
    /// </summary>
    public List<StkStockCountSubmitDetail> Details { get; set; }
}

/// <summary>
/// 库存盘点提交明细
/// </summary>
public class StkStockCountSubmitDetail
{
    /// <summary>
    /// 盘点明细Id，0 表示新增
    /// </summary>
    public long StockCountEntryId { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    public long WhLocId { get; set; }

    /// <summary>
    /// 库存盘点提交明细信息集合
    /// </summary>
    public List<StkStockCountSubmitDetailInfo> DetailInfos { get; set; }
}

/// <summary>
/// 库存盘点提交明细信息
/// </summary>
public class StkStockCountSubmitDetailInfo
{
    /// <summary>
    /// 条码Id
    /// </summary>
    public long BarcodeId { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal Qty { get; set; }
}

/// <summary>
/// 库存盘点数量保存输入参数
/// </summary>
public class StkStockCountCountSaveInput : IdInput
{
    /// <summary>
    /// 库存盘点数量保存明细集合
    /// </summary>
    public List<StkStockCountCountSaveDetail> Details { get; set; }
}

/// <summary>
/// 库存盘点数量保存明细
/// </summary>
public class StkStockCountCountSaveDetail
{
    /// <summary>
    /// 盘点明细Id
    /// </summary>
    public long EntryId { get; set; }

    /// <summary>
    /// 盘点数量
    /// </summary>
    public decimal CountQty { get; set; }
}