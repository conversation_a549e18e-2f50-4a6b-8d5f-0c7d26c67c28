﻿using Furion.Localization;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan;

public class PdaScanBarcodeOperation : PdaScanBarcodeOperationBase
{

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var barcodeService = App.GetService<BarBarcodeService>(_serviceProvider);
        var pdaCacheService = App.GetService<PdaCacheService>();
        var billData = pdaCacheService.GetBillData(args.TranId);
        var barcode = barcodeService.GetBarcodeAsync(args.BarcodeString).Result;
        if (barcode == null) return;
        var billModel = pdaCacheService.GetPdaBillModel(billData.BillModelKey);
        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        if (barcode.BarcodeType == BarcodeType.Deduct && barcode.Qty <= 0)
            throw Oops.Bah(PdaErrorCode.Pda1033, barcode.Barcode);
        if (barcode.BarcodeType == BarcodeType.Container && barcode.Qty <= 0)
        {
            //如果是出库/调拨类型,不允许扫描为0的容器条码
            if (billModel.Config.BillParams.OpType == BarOpType.OutStock || billModel.Config.BillParams.OpType == BarOpType.Transfer)
                throw Oops.Bah(PdaErrorCode.Pda1035, barcode.Barcode);
        }

        if (billModel.Config.BillParams.IsBarcodeOpTypeInOutLimit)
        {
            //如果是入库类型,需要不在库状态,如果是出库类型,必须在库
            if (billModel.Config.BillParams.OpType == BarOpType.InStock && barcode.IsInStock) throw Oops.Bah(L.Text["条码[{0}]已在库,不能入库", barcode.Barcode]);
            if (billModel.Config.BillParams.OpType == BarOpType.OutStock && !barcode.IsInStock) throw Oops.Bah(L.Text["条码[{0}]不在库,不能出库", barcode.Barcode]);
        }

        //可扣减/容器条码不应直接扫描,前端确认后才扫描
        if (barcode.BarcodeType == BarcodeType.Standard)
        {
            //普通条码
            billModel.ScanBarcode(args.TranId, barcode);
        }
        else if (barcode.BarcodeType == BarcodeType.Deduct)
        {
            if (!billModel.Config.BillParams.IsDeductShowModifyQty && (billModel.Config.BillParams.OpType == BarOpType.InStock || billModel.Config.BillParams.OpType == BarOpType.Transfer))
            {
                //如果是入库或者调拨,不弹窗
                billModel.ScanBarcode(args.TranId, barcode);
            }
            else
            {
                var barcodeExtras = GetMatchBarcodeExtras(args.TranId, barcode, billModel);
                
                //可扣减条码
                PdaRestfulCode restfulCode = PdaRestfulCode.P101;
                //如果返回只有一个条码,并且为一物一码
                PdaExtrasRestfulResult<PdaBarcodeOperationExtras> result = new PdaExtrasRestfulResult<PdaBarcodeOperationExtras>
                {
                    Code = (int)restfulCode,
                    Message = null,
                    Data = barcodeExtras,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
                UnifyContext.Fill(result);
            }
        }
        else if (barcode.BarcodeType == BarcodeType.Container)
        {
            var barcodeExtras = GetMatchBarcodeExtras(args.TranId, barcode, billModel);
            
            //容器条码
            PdaRestfulCode restfulCode = PdaRestfulCode.P103;
            //如果返回只有一个条码,并且为一物一码
            PdaExtrasRestfulResult<PdaBarcodeOperationExtras> result = new PdaExtrasRestfulResult<PdaBarcodeOperationExtras>
            {
                Code = (int)restfulCode,
                Message = null,
                Data = barcodeExtras,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);
        }
        else
        {
            //其它条码
            billModel.ScanBarcode(args.TranId, barcode);
        }
        args.Barcodes.Add(barcode);
        args.IsResult = true;
    }

    /// <summary>
    /// 检查是否需要匹配源单数量
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <param name="billModel"></param>
    /// <returns></returns>
    public PdaBarcodeOperationExtras GetMatchBarcodeExtras(long tranId, BarBarcode barcode, IPdaBillModel billModel)
    {
        var barcodeExtras = barcode.Adapt<PdaBarcodeOperationExtras>();
        barcodeExtras.ExecQty = barcodeExtras.Qty;
        if (!billModel.Config.BillParams.IsDeductMatchSourceQty) return barcodeExtras;
        // 如果需要匹配源单数量
        var pdaCacheService = App.GetService<PdaCacheService>();
        var billData = pdaCacheService.GetBillData(tranId);
        var matchBarcode = billModel.CheckMatchingQty(tranId, new PdaBarcode(barcode, barcode.Qty));
        barcodeExtras.ExecQty = matchBarcode.CalcBarcode.ScanQty;
        return barcodeExtras;
    }
    
    public PdaScanBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}