﻿namespace Neuz.Core.Entity;

/// <summary>
/// 装箱日志
/// </summary>
[SugarTable(null, "装箱日志")]
[SugarIndex("index_{table}_P", nameof(PackageBarcode), OrderByType.Asc)]
public class BarPackageLog : EntityTenant
{
    /// <summary>
    /// 操作事务Id
    /// </summary>
    /// <remarks>
    /// （Pda事务Id）
    /// <para>以后有可能会多个箱一起装，暂且保留</para>
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作事务Id")]
    public long OpTranId { get; set; }

    /// <summary>
    /// 箱码Id
    /// </summary>
    [SugarColumn(ColumnDescription = "箱码Id")]
    public long PackageId { get; set; }

    /// <summary>
    /// 箱码
    /// </summary>
    [SugarColumn(ColumnDescription = "箱码", Length = 200)]
    public string PackageBarcode { get; set; }

    /// <summary>
    /// 条码档案Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码档案Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码档案
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(BarcodeId))]
    public BarBarcode BarcodeEntity { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 日志类型
    /// </summary>
    [SugarColumn(ColumnDescription = "日志类型")]
    public PackageLogType LogType { get; set; }
}