﻿using Furion.Localization;

namespace Neuz.Core.Extension;

/// <summary>
/// 对象扩展操作
/// </summary>
public static class ObjectExtension
{
    /// <summary>是否包含属性</summary>
    /// <param name="instance"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    public static bool IsContainProperty(this object instance, string propertyName)
    {
        var propertyInfo = instance.GetType().GetProperty(propertyName);
        return propertyInfo != null;
    }

    /// <summary>获取属性值</summary>
    /// <param name="instance">对象实例</param>
    /// <param name="propertyName">属性名</param>
    /// <returns></returns>
    public static object GetPropertyValue(this object instance, string propertyName)
    {
        if (instance is Dictionary<string, object> dic)
            return dic[propertyName];

        var propertyInfo = instance.GetType().GetProperty(propertyName);
        if (propertyInfo == null)
            throw new MissingMemberException(instance.GetType().Name, propertyName);

        return propertyInfo.GetValue(instance);
    }

    /// <summary>获取属性值</summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="instance"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    public static T GetPropertyValue<T>(this object instance, string propertyName)
    {
        return (T)GetPropertyValue(instance, propertyName);
    }

    /// <summary>设置属性值</summary>
    /// <param name="instance">对象实例</param>
    /// <param name="propertyName">属性名</param>
    /// <param name="value">设置的值</param>
    /// <param name="throwError">出错时是否抛出异常</param>
    /// <returns></returns>
    public static void SetPropertyValue(this object instance, string propertyName, object value, bool throwError = true)
    {
        if (instance is Dictionary<string, object> dic)
        {
            dic[propertyName] = value;
            return;
        }

        var propertyInfo = instance.GetType().GetProperty(propertyName);
        if (propertyInfo == null)
        {
            if (throwError)
                throw new MissingMemberException(instance.GetType().Name, propertyName);
        }
        else
        {
            if (!propertyInfo.CanWrite)
            {
                if (throwError)
                    throw new ArgumentException(L.Text["属性没有 Set 方法"]);
            }
            else
                propertyInfo.SetValue(instance, value);
        }
    }
}