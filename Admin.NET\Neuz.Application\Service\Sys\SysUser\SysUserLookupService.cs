﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 系统用户 Lookup 服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysUserLookup", Order = 100)]
public class SysUserLookupService : IDynamicApiController, ITransient, ILookup<SysUserLookupInput, SysUserLookupOutput>
{
    private readonly SqlSugarRepository<SysUser> _userRep;

    /// <summary>
    /// 系统用户 Lookup 服务
    /// </summary>
    public SysUserLookupService(IServiceProvider serviceProvider)
    {
        _userRep = serviceProvider.GetService<SqlSugarRepository<SysUser>>();
    }

    /// <summary>
    /// Lookup 查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <remarks>
    /// 保持与基础资料返回结构一致，返回 Id, Number, Name
    /// </remarks>
    [HttpPost("lookupQuery")]
    public async Task<SqlSugarPagedList<SysUserLookupOutput>> LookupQueryAsync(SysUserLookupInput input)
    {
        var query = _userRep.AsQueryable();
        if (input.UseGivenTenantId)
            query.ClearFilter();

        var entities = await _userRep.Context
            .Queryable(
                query
                    .WhereIF(!string.IsNullOrEmpty(input.Keyword), u => u.Account.Contains(input.Keyword) || u.RealName.Contains(input.Keyword))
                    .WhereIF(!string.IsNullOrEmpty(input.Number), u => u.Account.Contains(input.Number))
                    .WhereIF(!string.IsNullOrEmpty(input.Name), u => u.RealName.Contains(input.Name))
                    .WhereIF(input.UseGivenTenantId, u => u.TenantId == input.TenantId)
                    .Select(u => new SysUserLookupOutput
                    {
                        Id = u.Id,
                        Account = u.Account,
                        RealName = u.RealName,
                    })
            )
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }

    /// <summary>
    /// 获取 Lookup 数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getLookupData")]
    public async Task<object> GetLookupDataAsync([FromQuery] StringIdInput input)
    {
        //一对多的导航属性数组
        var oneToManyNavigateCols = _userRep.Context.EntityMaintenance.GetEntityInfo<SysUser>().Columns
            .Where(u => u.Navigat != null && u.Navigat.GetNavigateType() == NavigateType.OneToMany)
            .Select(u => u.PropertyName)
            .ToArray();
        return await _userRep.AsQueryable().IncludesAllFirstLayer(oneToManyNavigateCols)
            .Select(u => new SysUserLookupOutput
            {
                Id = u.Id,
                Account = u.Account,
                RealName = u.RealName,
            })
            .FirstAsync(u => u.Id == Convert.ToInt64(input.Id));
    }
}