﻿using Furion.Localization;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 出库通知单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkOutNotice", Order = 100)]
public class StkOutNoticeService : StkBaseBillService<StkOutNotice>, IDynamicApiController, ITransient
{
    private readonly StkAllocatePolicyService _stkAllocatePolicyService;

    private readonly StkTaskService _stkTaskService;

    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkOutNotice);

    /// <summary>
    /// 出库通知单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkOutNoticeService(IServiceProvider serviceProvider, SqlSugarRepository<StkOutNotice> rep) : base(serviceProvider, rep)
    {
        _stkAllocatePolicyService = serviceProvider.GetService<StkAllocatePolicyService>();
        _stkTaskService = serviceProvider.GetService<StkTaskService>();
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Status", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "AllocateStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OrderBillNo", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "Status",
            "AllocateStatus",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "SupplierNumber",
            "SupplierName",
            "CustomerNumber",
            "CustomerName",
            "OrderBillNo",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_EntryStatus",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_WhAreaNumber",
            "Entries_WhAreaName",
            "Entries_Qty",
            "Entries_AllocateQty",
            "Entries_OutQty",
            "Entries_RemainOutQty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_Price",
            "Entries_TotalPrice",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
            "Entries_MadeQty",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkOutNotice entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkOutNotice entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkOutNotice entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", false);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 重新计算剩余数量
            entry.RemainOutQty = entry.Qty - entry.OutQty;

            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否为0
            if (entry.Qty == 0) throw Oops.Bah(StkErrorCode.Stk1014);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (!materialInfo.IsKfPeriod && entry.ProduceDate != null) throw Oops.Bah(StkErrorCode.Stk1027, materialInfo.Number);
            if (!materialInfo.IsKfPeriod && entry.ExpiryDate != null) throw Oops.Bah(StkErrorCode.Stk1028, materialInfo.Number);
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkOutNotice entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkOutNotice entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkOutNotice entity)
    {
        return new List<long>();
    }

    protected override void OnAfterAudit(StkOutNotice entity)
    {
        base.OnAfterAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkOutNotice entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
        if (entity.Status != StkOutNoticeStatus.UnHandle ||
            (entity.AllocateStatus != StkOutNoticeAllocateStatus.UnAllocate && entity.AllocateStatus != StkOutNoticeAllocateStatus.AllocateFail))
            throw Oops.Bah(StkErrorCode.StkOutNotice1001, entity.BillNo);

        // TODO: 出库通知单反审核，当下游单据创建了单据，但未审核时，无法根据业务状态或者回写的数量来判断是否能够反审核
    }

    protected override void OnAfterUnAudit(StkOutNotice entity)
    {
        base.OnAfterUnAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkOutNotice>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        // TODO: 手动完结状态需要跳过处理

        var srcBillKey = EntityName;

        // 查询
        var entities = Rep.Context.Queryable<StkOutNotice>().Includes(u => u.Entries).Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            // 出库单明细集合
            var stkOutStockEntryList = Rep.Context.Queryable<StkOutStockEntry>()
                .LeftJoin<StkOutStock>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .Where((te, t) => t.Status == StkOutStockStatus.Finish) // 已发运
                .ToList();

            // 任务明细集合
            var stkTaskEntryList = Rep.Context.Queryable<StkTaskEntry>()
                .LeftJoin<StkTask>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && t.SrcBillKey == srcBillKey && t.SrcBillId == entity.Id)
                .ToList();

            // 库存调拨单明细集合
            var stkTransferEntryList = Rep.Context.Queryable<StkTransferEntry>()
                .LeftJoin<StkTransfer>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .ToList();

            foreach (var entry in entity.Entries)
            {
                // 出库数量
                var outQty = stkOutStockEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);

                // 将库存调拨单明细的数量也加到出库数量中
                outQty += stkTransferEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);

                // 退货数量
                var returnQty = stkOutStockEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.ReturnQty);

                entry.ReturnQty = returnQty;
                entry.OutQty = outQty;
                entry.RemainOutQty = entry.Qty - entry.OutQty < 0 ? 0 : entry.Qty - entry.OutQty;

                // 如果不是手工关闭
                if (entry.EntryStatus != StkOutNoticeEntryStatus.ManualClose)
                {
                    // 处理状态，不考虑退货的情况（与入库通知单、收货单有区别）
                    if (entry.OutQty >= entry.Qty)
                        entry.EntryStatus = StkOutNoticeEntryStatus.Finish;
                    else if (entry.OutQty > 0)
                        entry.EntryStatus = StkOutNoticeEntryStatus.Handling;
                    else
                        entry.EntryStatus = StkOutNoticeEntryStatus.UnHandle;
                }

                // 分配数量
                var allocateQty = stkTaskEntryList.Where(u => u.SrcBillEntryId == entry.EntryId)
                    .Sum(u => u.EntryStatus == StkTaskEntryStatus.ManualClose ? u.ExecQty : u.Qty);

                entry.AllocateQty = allocateQty;
            }

            if (entity.Entries.All(u => u.EntryStatus is StkOutNoticeEntryStatus.Finish or StkOutNoticeEntryStatus.ManualClose))
                entity.Status = StkOutNoticeStatus.Finish;
            else if (entity.Entries.Any(u => u.EntryStatus == StkOutNoticeEntryStatus.Handling))
                entity.Status = StkOutNoticeStatus.Handling;
            else
                entity.Status = StkOutNoticeStatus.UnHandle;

            // 总分配数量
            var totalAllocateQty = entity.Entries.Sum(u => u.AllocateQty);
            // 总数量
            var totalQty = entity.Entries.Sum(u => u.Qty);

            if (totalAllocateQty >= totalQty)
                entity.AllocateStatus = StkOutNoticeAllocateStatus.FullAllocate;
            else if (totalAllocateQty > 0)
                entity.AllocateStatus = StkOutNoticeAllocateStatus.Allocating;
            else
                entity.AllocateStatus = entity.AllocateStatus == StkOutNoticeAllocateStatus.AllocateFail
                    ? StkOutNoticeAllocateStatus.AllocateFail
                    : StkOutNoticeAllocateStatus.UnAllocate;

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }
    }

    /// <summary>
    /// 分配
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("allocate")]
    public Task<List<ExecResult>> Allocate(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 查询单据实体、明细和物料，并加锁
            var entity = Rep.AsQueryable().Includes(u => u.Entries).Includes(u => u.Entries, p => p.Material).TranLock().First(u => u.Id == id);

            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);
            if (entity.IsCancel)
                throw Oops.Bah(BaseErrorCode.BaseBill1006, entity.BillNo);
            if (entity.DocumentStatus != DocumentStatus.Approve)
                throw Oops.Bah(StkErrorCode.StkOutNotice1004, entity.BillNo);
            if (entity.Status == StkOutNoticeStatus.Finish)
                throw Oops.Bah(StkErrorCode.StkOutNotice1002, entity.BillNo);
            if (entity.AllocateStatus == StkOutNoticeAllocateStatus.FullAllocate)
                throw Oops.Bah(StkErrorCode.StkOutNotice1003, entity.BillNo);

            // 提前创建好任务主键Id
            var taskId = YitIdHelper.NextId();

            // 分配结果
            var allocateResults = _stkAllocatePolicyService.Allocate(EntityName, id, new EntityAllocateDefine
            {
                IdField = "Id",
                BillNoField = "BillNo",
                BillTypeField = "BillType",
                EntryIdField = "Entries_EntryId",
                EntrySeqField = "Entries_Seq",
                WarehouseIdField = "WarehouseId",
                MaterialIdField = "Entries_MaterialId",
                GetBillExtraSelectFields = new List<string>
                {
                    "Entries_Qty",
                    "Entries_AllocateQty",
                    "Entries_EntryStatus",
                },
                GetCanAllocateQty = billRow =>
                {
                    // 如果是手动关闭，则直接返回0
                    if ((billRow.Field<StkOutNoticeEntryStatus>("Entries_EntryStatus")) == StkOutNoticeEntryStatus.ManualClose)
                        return 0;

                    return billRow.Field<decimal>("Entries_Qty") - billRow.Field<decimal>("Entries_AllocateQty");
                },
                InvLockLogType = StkInvLockLogType.TaskAllocatePlus,
                InvPreInLogType = StkInvPreInLogType.TaskAllocatePlus,
                RelTaskId = taskId,
                IsLockInv = true,
                IsPreIn = true,
            });

            if (allocateResults.Count == 0)
            {
                if (entity.Entries.All(u => u.AllocateQty == 0))
                {
                    // 如果全部明细都没有分配数量，则修改单据状态为分配失败
                    entity.AllocateStatus = StkOutNoticeAllocateStatus.AllocateFail;

                    // 保存
                    Rep.Update(entity);
                    // 提交事务
                    uow.Commit();
                }

                throw Oops.Bah(L.Text["单据: {0} 分配失败", entity.BillNo]);
            }

            // 重新查询库存，获取库存批号、库区、库位等信息来创建任务
            var invList = Rep.Context.Queryable<StkInventory>()
                .Where(u => allocateResults.Where(p => p.LockInventoryId != null).Select(p => p.LockInventoryId)
                    .Union(allocateResults.Where(p => p.PreInInventoryId != null).Select(p => p.PreInInventoryId))
                    .Contains(u.Id)
                ).ToList();

            // 创建任务
            var task = new StkTask
            {
                Id = taskId,
                DocumentStatus = DocumentStatus.Create,
                Memo = null,
                Date = DateTime.Now.Date,
                TaskType = StkTaskType.Pick,
                Status = StkTaskStatus.UnHandle,
                SrcBillType = entity.BillType,
                SrcBillKey = EntityName,
                SrcBillNo = entity.BillNo,
                SrcBillId = entity.Id,
                WarehouseId = entity.WarehouseId,
                Entries = allocateResults.Select((u, index) =>
                {
                    var entry = entity.Entries.First(p => p.EntryId == u.BillEntryId);
                    var lockInv = invList.FirstOrDefault(p => p.Id == u.LockInventoryId);
                    var preInInv = invList.FirstOrDefault(p => p.Id == u.PreInInventoryId);

                    return new StkTaskEntry
                    {
                        EntryId = YitIdHelper.NextId(),
                        Seq = index + 1,
                        EntryStatus = StkTaskEntryStatus.UnHandle,
                        MaterialId = entry.MaterialId,
                        BatchNo = lockInv?.BatchNo,
                        ProduceDate = lockInv?.ProduceDate,
                        ExpiryDate = lockInv?.ExpiryDate,
                        SrcWhAreaId = lockInv?.WhAreaId,
                        SrcWhLocId = lockInv?.WhLocId,
                        SrcContainerId = lockInv?.ContainerId,
                        DestWhAreaId = preInInv?.WhAreaId,
                        DestWhLocId = preInInv?.WhLocId,
                        DestContainerId = preInInv?.ContainerId,
                        Qty = u.AllocateQty,
                        ExecQty = 0,
                        RemainExecQty = u.AllocateQty,
                        UnitId = entry.UnitId,
                        OwnerId = entry.OwnerId,
                        AuxPropValueId = lockInv?.AuxPropValueId,
                        GrossWeight = u.AllocateQty * entry.Material.GrossWeight,
                        PackingVolume = u.AllocateQty * entry.Material.PackingVolume,
                        PackingQty = (int)Math.Ceiling(u.AllocateQty * entry.Material.StdPackingQty),
                        SrcBillEntrySeq = u.BillEntrySeq!.Value,
                        SrcBillEntryId = u.BillEntryId!.Value,
                        IsAllowLocReplace = u.IsAllowLocReplace,
                        IsAllowBatchNoReplace = u.IsAllowBatchNoReplace,
                        RelLockInvId = u.LockInventoryId,
                        RelPreInInvId = u.PreInInventoryId,
                        UsedRuleNumber = u.UsedRuleNumber,
                        UsedRuleName = u.UsedRuleName,
                    };
                }).ToList()
            };

            // 保存任务
            _stkTaskService.AddAsync(task).GetAwaiter().GetResult();

            // 审核任务
            var auditResult = _stkTaskService.AuditAsync(new IdsInput { Ids = new List<long> { task.Id } }).GetAwaiter().GetResult();
            if (!auditResult.First().IsSuccess)
                throw Oops.Bah(auditResult.First().Message);

            // 提交事务
            uow.Commit();

            return L.Text["单据: {0} 分配成功", entity.BillNo];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 取消分配
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("cancelAllocate")]
    public Task<List<ExecResult>> CancelAllocate(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 查询单据实体
            var entity = Rep.AsQueryable().First(u => u.Id == id);

            // 查找库存任务列表，审核、未处理状态的
            var taskEntities = Rep.Context.Queryable<StkTask>()
                .Where(u => u.DocumentStatus == DocumentStatus.Approve && u.Status == StkTaskStatus.UnHandle)
                .Where(u => u.SrcBillKey == EntityName && u.SrcBillId == id)
                .ToList();

            if (taskEntities.Count == 0)
                return L.Text["单据: {0} 没有可取消分配的任务", entity.BillNo];

            // 成功的任务单据编号集合
            var successTaskBillNoList = new List<string>();
            foreach (var taskEntity in taskEntities)
            {
                // 取消分配任务
                var cancelAllocateResult = _stkTaskService.CancelAllocate(new IdsInput { Ids = new List<long> { taskEntity.Id } }).GetAwaiter().GetResult();
                if (!cancelAllocateResult[0].IsSuccess)
                    throw Oops.Bah(cancelAllocateResult[0].Message);

                successTaskBillNoList.Add(taskEntity.BillNo);
            }

            // 提交事务
            uow.Commit();

            return L.Text["单据: {0} 取消分配成功，任务单号: {1}", entity.BillNo, string.Join(",", successTaskBillNoList)];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 手动关闭
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("manualClose")]
    public Task<List<ExecResult>> ManualClose(EntryIdsInput input)
    {
        var outNoticeIds = new List<long>();
        var execResults = ExecActions(input.EntryIds, entryId =>
        {
            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 查询单据明细实体
            var entry = Rep.Context.Queryable<StkOutNoticeEntry>().First(u => u.EntryId == entryId);
            if (entry == null)
                throw Oops.Bah(BaseErrorCode.Base1000, entryId);

            // 查询单据实体
            var entity = Rep.AsQueryable().First(u => u.Id == entry.Id);
            if (entity.IsCancel)
                throw Oops.Bah(BaseErrorCode.BaseBill1006, entity.BillNo);
            if (entry.EntryStatus is StkOutNoticeEntryStatus.Finish or StkOutNoticeEntryStatus.ManualClose)
                throw Oops.Bah(StkErrorCode.StkOutNotice1005, entity.BillNo, entry.Seq);

            // 添加到出库通知单Id集合
            if (!outNoticeIds.Contains(entity.Id))
                outNoticeIds.Add(entity.Id);

            // 查找状态为未完成、未手工关闭的任务明细
            var taskEntries = Rep.Context.Queryable<StkTaskEntry>()
                .LeftJoin<StkTask>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.SrcBillKey == EntityName && te.SrcBillEntryId == entryId)
                .Where((te, t) => te.EntryStatus == StkTaskEntryStatus.UnHandle || te.EntryStatus == StkTaskEntryStatus.Handling)
                .ToList();

            // 手动关闭任务明细
            var manualCloseResult = _stkTaskService.ManualClose(new EntryIdsInput { EntryIds = taskEntries.Select(u => u.EntryId).ToList() }).GetAwaiter().GetResult();
            if (manualCloseResult.Any(u => !u.IsSuccess))
                throw Oops.Bah(string.Join(",", manualCloseResult.Where(u => !u.IsSuccess).Select(u => u.Message)));

            var taskMessage = string.Join(",", manualCloseResult.Select(u => u.Message));

            // 更新状态
            entry.EntryStatus = StkOutNoticeEntryStatus.ManualClose;
            Rep.Context.Updateable(entry).ExecuteCommand();

            // 提交事务
            uow.Commit();

            return L.Text["单据编号: {0} 序号: {1} 手动关闭成功", entity.BillNo, entry.Seq] + " " + taskMessage;
        });

        // 刷新任务状态
        RefreshStatus(outNoticeIds);

        return Task.FromResult(execResults);
    }

    protected override void OnAfterQueryReportData(List<StkOutNotice> result)
    {
        base.OnAfterQueryReportData(result);

        // 填充关联的任务集合
        var tasks = Rep.Context.Queryable<StkTask>().IncludeNavCol().Where(u => result.Select(p => p.Id).Contains(u.SrcBillId));
        foreach (var item in result)
        {
            item.RelTasks = tasks.Where(u => u.SrcBillId == item.Id).ToList();
        }
    }

    protected override IDictionary<string, object> GetReportDataColAlias(EntityMaintenance entityMaintenance, Type entityType, int nestedLevel)
    {
        var colAlias = base.GetReportDataColAlias(entityMaintenance, entityType, nestedLevel);

        if (entityType == typeof(StkOutNotice))
        {
            colAlias[nameof(StkOutNotice.RelTasks)] = GetReportDataColAlias(entityMaintenance, typeof(StkTask), 0);
        }

        return colAlias;
    }
}