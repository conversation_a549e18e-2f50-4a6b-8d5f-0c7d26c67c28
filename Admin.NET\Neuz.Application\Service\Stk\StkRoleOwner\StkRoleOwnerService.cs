﻿namespace Neuz.Application;

/// <summary>
/// 角色货主权限服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkRoleOwner", Order = 100)]
public class StkRoleOwnerService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 角色货主权限仓储服务
    /// </summary>
    protected SqlSugarRepository<StkRoleOwner> RoleOwnerRep { get; }

    /// <summary>
    /// 货主仓储服务
    /// </summary>
    protected SqlSugarRepository<BdOwner> OwnerRep { get; }

    /// <summary>
    /// 用户角色服务
    /// </summary>
    protected SysUserRoleService UserRoleService { get; }

    /// <summary>
    /// 缓存服务
    /// </summary>
    protected SysCacheService CacheService { get; }

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    public StkRoleOwnerService(IServiceProvider serviceProvider)
    {
        RoleOwnerRep = serviceProvider.GetService<SqlSugarRepository<StkRoleOwner>>();
        OwnerRep = serviceProvider.GetService<SqlSugarRepository<BdOwner>>();
        UserRoleService = serviceProvider.GetService<SysUserRoleService>();
        CacheService = serviceProvider.GetService<SysCacheService>();
        ServiceProvider = serviceProvider;
    }

    /// <summary>
    /// 获取角色货主权限
    /// </summary>
    /// <returns></returns>
    [HttpGet("getRoleOwner")]
    public async Task<StkRoleOwnerOutput> GetRoleOwner([FromQuery] long roleId)
    {
        var roleOwnerIdList = (await RoleOwnerRep.GetListAsync(u => u.RoleId == roleId)).Select(u => u.OwnerId).ToList();

        return new StkRoleOwnerOutput
        {
            OwnerIds = roleOwnerIdList,
        };
    }

    /// <summary>
    /// 保存角色货主权限
    /// </summary>
    /// <returns></returns>
    [HttpPost("saveRoleOwner")]
    [UnitOfWork]
    public async Task SaveRoleOwner(StkRoleOwnerInput input)
    {
        var orgIds = input.OwnerIds.Where(u => u > 0).ToList();

        // 删除角色货主绑定
        await RoleOwnerRep.DeleteAsync(u => u.RoleId == input.RoleId);
        // 插入角色货主绑定
        await RoleOwnerRep.InsertRangeAsync(orgIds.Select(u => new StkRoleOwner
        {
            RoleId = input.RoleId,
            OwnerId = u,
        }).ToList());

        // 清除用户货主权限缓存
        var userIds = await UserRoleService.GetUserIdList(input.RoleId);
        foreach (var userId in userIds)
            CacheService.Remove(CacheConst.KeyUserOwner + userId);
    }

    /// <summary>
    /// 获取当前用户的货主权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserOwnerIds()
    {
        var userManager = App.GetService<UserManager>(ServiceProvider);
        return GetUserOwnerIdsByUserId(userManager.UserId);
    }

    /// <summary>
    /// 获取用户的货主权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserOwnerIdsByUserId(long userId)
    {
        var ownerIds = CacheService.Get<List<long>>(CacheConst.KeyUserOwner + userId);
        if (ownerIds != null && ownerIds.Count != 0)
            return ownerIds;

        // 用户所属的角色Id集合
        var roleIds = UserRoleService.GetUserRoleIdList(userId).GetAwaiter().GetResult();

        ownerIds = RoleOwnerRep.AsQueryable()
            .Where(u => roleIds.Contains(u.RoleId))
            .Select(u => u.OwnerId)
            .ToList();

        // 如果没有数据，返回一个不存在的假数据
        if (ownerIds.Count == 0)
            ownerIds.Add(-1);

        CacheService.Set(CacheConst.KeyUserOwner + userId, ownerIds, TimeSpan.FromDays(1));

        return ownerIds;
    }
}