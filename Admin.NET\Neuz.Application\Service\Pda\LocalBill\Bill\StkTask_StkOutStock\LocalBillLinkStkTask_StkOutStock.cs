﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.Link;

namespace Neuz.Application.Pda.LocalBill.Bill.StkTask_StkOutStock;

/// <summary>
/// 任务单->出库单
/// </summary>
public class LocalBillLinkStkTask_StkOutStock : ILocalBillLinkParam
{
    /// <inheritdoc/>
    public virtual string Key { get; set; } = "StkTask_StkOutStock";

    /// <summary>
    /// 任务单, 应该找源单的单据类型, 这里是发货通知单
    /// </summary>
    public virtual List<string> SourceBillTypes { get; set; } = new List<string>() { "StkOutNotice" };

    /// <inheritdoc/>
    public virtual List<LocalBillLinkMapping> Mappings { get; set; } = new()
    {
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.BillNo), ScanFiledName = nameof(PdaLocalBillScanHead.SrcBillNo), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillNo), ScanFiledName = "EsSrcBillNo", ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.Id), ScanFiledName = nameof(PdaLocalBillScanHead.SrcBillId), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillType), ScanFiledName = nameof(PdaLocalBillScanDetail.DestBillType), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillType), ScanFiledName = nameof(PdaLocalBillScanHead.DestBillType), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhArea.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhArea.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhAreaNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhArea.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhAreaName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhLoc.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhLocId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhLoc.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhLocNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhLoc.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhLocName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhArea.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhArea.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhArea.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhLoc.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhLoc.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhLoc.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.BatchNo", ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.ProduceDate", ScanFiledName = nameof(PdaLocalBillScanDetail.ProduceDate), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.OwnerId", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Owner.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerNumber), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Owner.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerName), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillKey), ScanFiledName = "SrcBillKey", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillNo), ScanFiledName = "SrcBillNo", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.SrcBillId), ScanFiledName = "SrcBillId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntryId", ScanFiledName = "SrcBillEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntrySeq", ScanFiledName = "SrcBillEntrySeq", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkTask.Id), ScanFiledName = "SrcTaskId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.EntryId", ScanFiledName = "SrcTaskEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.IsAllowBatchNoReplace", ScanFiledName = "IsAllowBatchNoReplace", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.EntryStatus", ScanFiledName = "EntryStatus", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Specification", ScanFiledName = "Specification", ScanType = LocalBillLinkMappingScanType.Detail },
    };

    /// <inheritdoc/>
    public virtual List<LocalBillQtyCalcMapping> CalcMappings { get; set; } = new List<LocalBillQtyCalcMapping>()
    {
        new LocalBillQtyCalcMapping()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.Qty),
            ScanType = LocalBillLinkMappingScanType.Detail,
            CalcFiledNames = new List<LocalBillCalcFieldMapping>()
            {
                new LocalBillCalcFieldMapping()
                {
                    LocalBillFiledName = "Entries.Qty",
                    CalcType = LocalBillCalcType.Sub
                },
                new LocalBillCalcFieldMapping()
                {
                    LocalBillFiledName = "Entries.ExecQty",
                    CalcType = LocalBillCalcType.Sub
                },
            }
        }
    };

    public List<LocalBillFilter> Filters { get; set; } = new List<LocalBillFilter>()
    {
        new LocalBillFilter
        {
            ScanType = LocalBillLinkMappingScanType.Detail,
            ScanFiledName = "EntryStatus",
            Condition = LocalBillFilterCondition.NotEquals,
            Value = StkTaskEntryStatus.Finish
        }
    };

    /// <inheritdoc/>
    public virtual List<LocalBillContentInfo> DetailContents { get; set; } = new List<LocalBillContentInfo>()
    {
        new LocalBillContentInfo()
        {
            ScanFiledName = "Specification",
            Title = L.Text["规格型号"],
            Col = 1
        },
        new LocalBillContentInfo()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo),
            Title = L.Text["批号"],
            Col = 1
        },
        new LocalBillContentInfo()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.SrcWhAreaName),
            Title = L.Text["库区"],
            Col = 1
        },
        new LocalBillContentInfo()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.SrcWhLocName),
            Title = L.Text["库位"],
            Col = 1
        },
        new LocalBillContentInfo()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.Qty),
            Title = L.Text["任务数量"],
            Col = 2
        },
        new LocalBillContentInfo()
        {
            ScanFiledName = nameof(PdaLocalBillScanDetail.ScanQty),
            Title = L.Text["执行数量"],
            Col = 2
        },
    };

    /// <inheritdoc/>
    public virtual List<LocalBillLinkBarcodeMapping> BarcodeMappings { get; set; } = new()
    {
        new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "Id", SaveBarcodeName = "BarcodeId" },
        new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "MaterialId", SaveBarcodeName = "MaterialId" },
        new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "BatchNo", SaveBarcodeName = "BatchNo" },
        new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "ScanQty", SaveBarcodeName = "Qty" },
        new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "AuxQty", SaveBarcodeName = "AuxQty" },
    };

    /// <inheritdoc/>
    public virtual Type SubmitBillType { get; set; } = typeof(StkOutStockService);

    /// <inheritdoc/>
    public virtual List<LocalBillLinkMapping> SubmitMappings { get; set; } = new List<LocalBillLinkMapping>()
    {
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkOutStock.Date), ScanFiledName = nameof(PdaLocalBillScanHead.Date), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkOutStock.CustomerId), ScanFiledName = nameof(PdaLocalBillScanHead.CustomerId), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkOutStock.DepartmentId), ScanFiledName = nameof(PdaLocalBillScanHead.DepartmentId), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkOutStock.BillType), ScanFiledName = nameof(PdaLocalBillScanDetail.DestBillType), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkOutStock.WarehouseId), ScanFiledName = nameof(PdaLocalBillScanHead.WareHouseId), ScanType = LocalBillLinkMappingScanType.Head },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.MaterialId", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhAreaId", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcWhLocId", ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhAreaId", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.DestWhLocId", ScanFiledName = nameof(PdaLocalBillScanDetail.DestWhLocId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.BatchNo", ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.ProduceDate", ScanFiledName = nameof(PdaLocalBillScanDetail.ProduceDate), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.ExpiryDate", ScanFiledName = nameof(PdaLocalBillScanDetail.ExpiryDate), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Qty", ScanFiledName = nameof(PdaLocalBillScanDetail.ScanQty), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.UnitId", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillKey", ScanFiledName = "SrcBillKey", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillNo", ScanFiledName = "SrcBillNo", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntrySeq", ScanFiledName = "SrcBillEntrySeq", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillId", ScanFiledName = "SrcBillId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntryId", ScanFiledName = "SrcBillEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcTaskId", ScanFiledName = "SrcTaskId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcTaskEntryId", ScanFiledName = "SrcTaskEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.OwnerId", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerId), ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxQty", ScanFiledName = "AuxQty", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxUnitId", ScanFiledName = "AuxUnitId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxPropValueId", ScanFiledName = "AuxPropValueId", ScanType = LocalBillLinkMappingScanType.Detail },
        new LocalBillLinkMapping() { LocalBillFiledName = "EmployeeId", ScanFiledName = "EmployeeId", ScanType = LocalBillLinkMappingScanType.Head },
    };

    /// <inheritdoc/>
    public virtual string SourceEntryName { get; set; } = "Entries";

    /// <inheritdoc/>
    public virtual string TargetEntryName { get; set; } = "Entries";

    /// <inheritdoc/>
    public virtual string BarcodeEntryName { get; set; } = "BarcodeEntries";
}