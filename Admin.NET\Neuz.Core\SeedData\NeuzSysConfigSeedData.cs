namespace Admin.NET.Core;

/// <summary>
/// Neuz系统配置表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class NeuzSysConfigSeedData : ISqlSugarEntitySeedData<SysConfig>
{
    /// <summary>
    /// Neuz系统配置表种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysConfig> HasData()
    {
        return new[]
        {
            new SysConfig{ Id=1350000001001, Name="Apk 下载地址", Code="neuz_apk_url", Value="", SysFlag=YesNoEnum.Y, Remark="配置后可在登录界面显示地址的二维码", OrderNo=100, GroupCode=ConfigConst.SysDefaultGroup, CreateTime=DateTime.Parse("2022-02-10 00:00:00") },
            new SysConfig{ Id=1350000001002, Name="Pda 服务器地址", Code="neuz_pda_server_url", Value="", SysFlag=YesNoEnum.Y, Remark="配置后可在登录界面显示地址的二维码", OrderNo=101, GroupCode=ConfigConst.SysDefaultGroup, CreateTime=DateTime.Parse("2022-02-10 00:00:00") },
            new SysConfig{ Id=1350000001003, Name="Pda闲置超时时间", Code="neuz_pda_idle_timeout", Value="0", SysFlag=YesNoEnum.Y, Remark="闲置超时时间（秒），超时强制退出，0 表示不限制", OrderNo=240, GroupCode=ConfigConst.SysDefaultGroup, CreateTime=DateTime.Parse("2024-12-20 00:00:00") },
        };
    }
}