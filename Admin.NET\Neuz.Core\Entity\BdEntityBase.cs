﻿namespace Neuz.Core.Entity;

/// <summary>
/// 基础资料基类实体
/// </summary>
[SugarIndex("index_{table}_N", nameof(Number), OrderByType.Asc)]
[SugarIndex("index_{table}_N2", nameof(Name), OrderByType.Asc)]
public abstract class BdEntityBase : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 80)]
    public virtual string Number { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 255)]
    public virtual string Name { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 500)]
    public string? Description { get; set; }

    /// <summary>
    /// 是否禁用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否禁用")]
    public virtual bool IsForbid { get; set; }

    /// <summary>
    /// 禁用时间
    /// </summary>
    [SugarColumn(ColumnDescription = "禁用时间")]
    public virtual DateTime? ForbiddenTime { get; set; }

    /// <summary>
    /// 禁用人Id
    /// </summary>
    [SugarColumn(ColumnDescription = "禁用人Id")]
    public virtual long? ForbiddenUserId { get; set; }

    /// <summary>
    /// 禁用人姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "禁用人姓名", Length = 50)]
    public virtual string? ForbiddenUserName { get; set; }
}