namespace Neuz.Core.Entity.Pda;

/// <summary>
/// Pda端Wms配置文件
/// </summary>
[SugarTable(null, "Pda端Wms配置文件")]
[SugarIndex("index_{table}_K", nameof(Key), OrderByType.Asc)]
public class PdaLocalBillConfig : EntityTenant
{
    /// <summary>
    /// 单据Key
    /// </summary>
    [SugarColumn(ColumnDescription = "单据Key", Length = 100)]
    public string Key { get; set; }

    /// <summary>
    /// 单据配置参数
    /// </summary>
    [SugarColumn(ColumnDescription = "单据配置参数", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string BillParams { get; set; }

    /// <summary>
    /// 单据显示架构
    /// </summary>
    [SugarColumn(ColumnDescription = "单据显示架构", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string BillSchema { get; set; }

    /// <summary>
    /// 单据转换映射
    /// </summary>
    [SugarColumn(ColumnDescription = "单据转换映射", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string LinkParam { get; set; }
}