namespace Neuz.Application.Pda.Proxy;

public class LockManager
{
    private static readonly ConcurrentDictionary<string, SemaphoreSlim> Locks = new ConcurrentDictionary<string, SemaphoreSlim>();

    public static async Task<IDisposable> AcquireLockAsync(string lockKey, TimeSpan? timeout = null)
    {
        var semaphore = Locks.GetOrAdd(lockKey, _ => new SemaphoreSlim(1, 1));
        var acquired = timeout.HasValue
            ? await semaphore.WaitAsync(timeout.Value)
            : await semaphore.WaitAsync(TimeSpan.Zero);

        if (!acquired)
            throw new TimeoutException($"Failed to acquire lock for {lockKey}");

        return new LockRelease(semaphore);
    }

    private class LockRelease : IDisposable
    {
        private readonly SemaphoreSlim _semaphore;

        public LockRelease(SemaphoreSlim semaphore) => _semaphore = semaphore;

        public void Dispose() => _semaphore.Release();
    }
}