﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案
/// </summary>
[SugarTable(null, "条码档案")]
[SugarIndex("index_{table}_B", nameof(Barcode), OrderByType.Asc)]
[SugarIndex("index_{table}_F", nameof(FuncKey), OrderByType.Asc)]
[SugarIndex("index_{table}_B2", nameof(BuildTranId), OrderByType.Asc)]
[SugarIndex("index_{table}_P", nameof(ParentPackageIds), OrderByType.Asc)]
public partial class BarBarcode : EntityTenant
{
    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 100)]
    public string? FuncKey { get; set; }

    /// <summary>
    /// 来源单据Key
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Key", Length = 50)]
    public string? SourceBillKey { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id", Length = 50)]
    public string? SourceBillId { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id", Length = 50)]
    public string? SourceBillEntryId { get; set; }

    /// <summary>
    /// 来源单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据编号", Length = 50)]
    public string? SourceBillNo { get; set; }

    /// <summary>
    /// 来源单据分录行号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录行号", Length = 50)]
    public string? SourceBillEntrySeq { get; set; }

    /// <summary>
    /// 来源单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据日期")]
    public DateTime? SourceBillDate { get; set; }

    /// <summary>
    /// 条码类型
    /// </summary>
    [SugarColumn(ColumnDescription = "条码类型")]
    public BarcodeType BarcodeType { get; set; }

    /// <summary>
    /// 条码状态
    /// </summary>
    [SugarColumn(ColumnDescription = "条码状态")]
    public BarcodeStatus Status { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id", Length = 50)]
    public string MaterialId { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [SugarColumn(ColumnDescription = "物料编码", Length = 100)]
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [SugarColumn(ColumnDescription = "物料名称", Length = 200)]
    public string MaterialName { get; set; }

    /// <summary>
    /// 物料短编码
    /// </summary>
    [SugarColumn(ColumnDescription = "物料短编码", Length = 50)]
    public string? MaterialShortNumber { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    [SugarColumn(ColumnDescription = "规格型号", Length = 200)]
    public string? MaterialSpec { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id", Length = 50)]
    public string UnitId { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    [SugarColumn(ColumnDescription = "单位编码", Length = 50)]
    public string UnitNumber { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [SugarColumn(ColumnDescription = "单位名称", Length = 50)]
    public string UnitName { get; set; }

    /// <summary>
    /// 启用批号管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用批号管理")]
    public bool IsBatchManage { get; set; }

    /// <summary>
    /// 启用序列号管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用序列号管理")]
    public bool IsSnManage { get; set; }

    /// <summary>
    /// 启用保质期管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用保质期管理")]
    public bool IsKfPeriod { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    [SugarColumn(ColumnDescription = "保质期")]
    public int ExpPeriod { get; set; }

    /// <summary>
    /// 保质期单位
    /// </summary>
    [SugarColumn(ColumnDescription = "保质期单位")]
    public ExpUnit ExpUnit { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 辅助属性Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性Id", Length = 50)]
    public string? AuxPropId { get; set; }

    /// <summary>
    /// 辅助属性编码
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性编码", Length = 100)]
    public string? AuxPropNumber { get; set; }

    /// <summary>
    /// 辅助属性名称
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性名称", Length = 100)]
    public string? AuxPropName { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id", Length = 50)]
    public string? StockId { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库编码", Length = 50)]
    public string? StockNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库名称", Length = 50)]
    public string? StockName { get; set; }

    /// <summary>
    /// 仓位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓位Id", Length = 50)]
    public string? StockLocId { get; set; }

    /// <summary>
    /// 仓位编码
    /// </summary>
    [SugarColumn(ColumnDescription = "仓位编码", Length = 50)]
    public string? StockLocNumber { get; set; }

    /// <summary>
    /// 仓位名称
    /// </summary>
    [SugarColumn(ColumnDescription = "仓位名称", Length = 50)]
    public string? StockLocName { get; set; }

    /// <summary>
    /// 是否曾经入库
    /// </summary>
    [SugarColumn(ColumnDescription = "是否曾经入库")]
    public bool IsOnceInStock { get; set; }

    /// <summary>
    /// 是否在库
    /// </summary>
    [SugarColumn(ColumnDescription = "是否在库")]
    public bool IsInStock { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "客户Id", Length = 50)]
    public string? CustomerId { get; set; }

    /// <summary>
    /// 客户编码
    /// </summary>
    [SugarColumn(ColumnDescription = "客户编码", Length = 50)]
    public string? CustomerNumber { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    [SugarColumn(ColumnDescription = "客户名称", Length = 255)]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    [SugarColumn(ColumnDescription = "供应商Id", Length = 50)]
    public string? SupplierId { get; set; }

    /// <summary>
    /// 供应商编码
    /// </summary>
    [SugarColumn(ColumnDescription = "供应商编码", Length = 50)]
    public string? SupplierNumber { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    [SugarColumn(ColumnDescription = "供应商名称", Length = 255)]
    public string? SupplierName { get; set; }

    /// <summary>
    /// 最后打印时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印时间")]
    public DateTime? LastPrintTime { get; set; }

    /// <summary>
    /// 已打印数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已打印数量")]
    public int PrintedQty { get; set; }

    /// <summary>
    /// 最后打印者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者Id")]
    public long? LastPrintUserId { get; set; }

    /// <summary>
    /// 最后打印者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者名称", Length = 20)]
    public string? LastPrintUserName { get; set; }

    /// <summary>
    /// 构建事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "构建事务Id")]
    public long BuildTranId { get; set; }

    /// <summary>
    /// 生产车间Id
    /// </summary>
    [SugarColumn(ColumnDescription = "生产车间Id", Length = 50)]
    public string? WorkShopId { get; set; }

    /// <summary>
    /// 生产车间编码
    /// </summary>
    [SugarColumn(ColumnDescription = "生产车间编码", Length = 50)]
    public string? WorkShopNumber { get; set; }

    /// <summary>
    /// 生产车间名称
    /// </summary>
    [SugarColumn(ColumnDescription = "生产车间名称", Length = 50)]
    public string? WorkShopName { get; set; }

    /// <summary>
    /// 作废时间
    /// </summary>
    [SugarColumn(ColumnDescription = "作废时间")]
    public DateTime? DisuseTime { get; set; }

    /// <summary>
    /// 作废者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者Id")]
    public long? DisuseUserId { get; set; }

    /// <summary>
    /// 作废者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者名称", Length = 20)]
    public string? DisuseUserName { get; set; }

    /// <summary>
    /// 父箱Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父箱Id")]
    public long? ParentPackageId { get; set; }

    /// <summary>
    /// 父箱
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ParentPackageId))]
    public BarPackage ParentPackage { get; set; }

    /// <summary>
    /// 父箱Ids
    /// </summary>
    /// <remarks>
    /// 与 <see cref="BarPackage"/> 的 ParentPackageId 存放的数据结构一样，描述整个父箱关系链
    /// </remarks>
    [SugarColumn(ColumnDescription = "父箱Ids", Length = 255)]
    public string? ParentPackageIds { get; set; }

    /// <summary>
    /// 使用的编码规则
    /// </summary>
    [SugarColumn(ColumnDescription = "使用的编码规则", Length = 2000)]
    public string? UsedCodeRule { get; set; }
}