﻿using System.Security.Cryptography;
using Furion.DataEncryption;
using Newtonsoft.Json;

namespace Neuz.License.WinForm;

/// <summary>
/// 授权信息
/// </summary>
public sealed class LicenseInfo
{
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 客户名称（使用主体）
    /// </summary>
    public string CustomerName { get; set; } = "";
    /// <summary>
    /// 授权类型
    /// </summary>
    public LicenseType LicenseType { get; set; }
    /// <summary>
    /// 授权开始日期
    /// </summary>
    public DateTime BeginDate { get; set; }
    /// <summary>
    /// 授权结束日期
    /// </summary>
    public DateTime EndDate { get; set; }
    /// <summary>
    /// 服务维护结束日期
    /// </summary>
    public DateTime? ServiceEndDate { get; set; }
    /// <summary>
    /// 最大连接数（0为不限制）
    /// </summary>
    public int MaxConnectCount { get; set; }
    /// <summary>
    /// 授权服务器序列号
    /// </summary>
    public string ServerKey { get; set; } = "";
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    public static string Generate(string privateKey, LicenseInfo licenseInfo)
    {
        var licenseData = JsonConvert.SerializeObject(licenseInfo);
        var signData = RSASign.Sign(HashAlgorithmName.SHA512, licenseData, privateKey);
        var desData = DESCEncryption.Encrypt(licenseData, signData);

        var signDataBytes = Convert.FromBase64String(signData);
        var desDataBytes = Convert.FromHexString(desData);
        var result = Convert.ToBase64String(signDataBytes.Concat(desDataBytes).ToArray());

        return result;
    }
}

/// <summary>
/// 授权类型
/// </summary>
public enum LicenseType
{
    /// <summary>
    /// 试用
    /// </summary>
    Trial = 1,
    /// <summary>
    /// 正式
    /// </summary>
    Official = 2,
}