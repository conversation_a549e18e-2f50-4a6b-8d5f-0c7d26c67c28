﻿namespace Neuz.Application;

/// <summary>
/// 仓储单据类型扩展设置服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkBillTypeExt", Order = 100)]
public class StkBillTypeExtService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 仓储服务
    /// </summary>
    public SqlSugarRepository<StkBillTypeExt> Rep { get; }

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 仓储单据类型扩展设置服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkBillTypeExtService(IServiceProvider serviceProvider, SqlSugarRepository<StkBillTypeExt> rep)
    {
        ServiceProvider = serviceProvider;
        Rep = rep;
    }

    /// <summary>
    /// 保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("save")]
    [UnitOfWork]
    public async Task Save(StkBillTypeExt input)
    {
        var entity = await Rep.AsQueryable().FirstAsync(u => u.Id == input.Id);
        if (entity == null) throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        input.Adapt(entity);
        await Rep.UpdateAsync(entity);
    }

    /// <summary>
    /// 获取或创建扩展设置
    /// </summary>
    /// <param name="id">单据类型Id</param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private async Task<T> GetOrCreateExt<T>(long id)
    {
        var billTypeExtData = await Rep.Context.Queryable<StkBillType>()
            .LeftJoin<StkBillTypeExt>((t, te) => t.Id == te.Id)
            .Where((t, te) => t.Id == id)
            .Select((t, te) => new { t, te })
            .FirstAsync();

        if (billTypeExtData == null) throw Oops.Bah(BaseErrorCode.Base1000, id);
        return await GetOrCreateExt<T>(billTypeExtData.t, billTypeExtData.te);
    }

    /// <summary>
    /// 获取或创建扩展设置
    /// </summary>
    /// <param name="billType">单据类型</param>
    /// <param name="billTypeExt">单据类型扩展设置</param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private async Task<T> GetOrCreateExt<T>(StkBillType billType, StkBillTypeExt billTypeExt)
    {
        if (billTypeExt == null || billTypeExt.Id == 0)
        {
            // 创建
            billTypeExt = new StkBillTypeExt
            {
                Id = billType.Id,
                TenantId = billType.TenantId,
                SettingJson = JSON.Serialize(Activator.CreateInstance(Type.GetType(typeof(T).FullName!)!))
            };
            await Rep.InsertAsync(billTypeExt);
        }

        var setting = JSON.Deserialize<T>(billTypeExt.SettingJson);
        return setting;
    }

    /// <summary>
    /// 获取单据类型扩展设置集合
    /// </summary>
    /// <param name="entityName">实体名称</param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    [NonAction]
    public async Task<List<(StkBillType, T)>> GetExtList<T>(string entityName)
    {
        var billTypeList = await Rep.Context.Queryable<StkBillType>().IncludeNavCol().Where(u => u.EntityName == entityName).ToListAsync();
        var billTypeExtDataList = await Rep.AsQueryable().Where(u => billTypeList.Select(p => p.Id).Contains(u.Id)).ToListAsync();

        var billTypeExtList = new List<(StkBillType, T)>();
        foreach (var billType in billTypeList)
        {
            var billTypeExt = billTypeExtDataList.FirstOrDefault(u => u.Id == billType.Id);
            billTypeExtList.Add((billType, await GetOrCreateExt<T>(billType, billTypeExt)));
        }

        return billTypeExtList;
    }

    /// <summary>
    /// 获取扩展设置-入库通知单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkInNotice")]
    public Task<StkBillTypeExtStkInNotice> GetExtStkInNotice([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkInNotice>(input.Id!.Value);
    }


    /// <summary>
    /// 获取扩展设置-收货单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkReceive")]
    public Task<StkBillTypeExtStkReceive> GetExtStkReceive([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkReceive>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkInStock")]
    public Task<StkBillTypeExtStkInStock> GetExtStkInStock([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkInStock>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-出库通知单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkOutNotice")]
    public Task<StkBillTypeExtStkOutNotice> GetExtStkOutNotice([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkOutNotice>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-出库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkOutStock")]
    public Task<StkBillTypeExtStkOutStock> GetExtStkOutStock([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkOutStock>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-调拨通知单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkTransferNotice")]
    public Task<StkBillTypeExtStkTransferNotice> GetExtStkTransferNotice([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkTransferNotice>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-库存调拨单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkTransfer")]
    public Task<StkBillTypeExtStkTransfer> GetExtStkTransfer([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkTransfer>(input.Id!.Value);
    }

    /// <summary>
    /// 获取扩展设置-库存调整单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getExtStkAdjustment")]
    public Task<StkBillTypeExtStkAdjustment> GetExtStkAdjustment([FromQuery] IdInput input)
    {
        return GetOrCreateExt<StkBillTypeExtStkAdjustment>(input.Id!.Value);
    }
}