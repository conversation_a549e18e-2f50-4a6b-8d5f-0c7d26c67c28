﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using static Neuz.Application.Pda.Package.Data.PdaPackageData;

namespace Neuz.Application.Pda.Package.Data;

/// <summary>
/// 装箱模型
/// </summary>
public class PdaPackageModel : PdaModelBase<PdaDataShow, PdaPackageData>
{
    /// <inheritdoc />
    public override string Key { get; } = "Package";

    /// <inheritdoc />
    public override IPdaSchema BillSchema { get; } = null;

    public PdaPackageModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    // /// <summary>
    // /// 获取数据模型
    // /// </summary>
    // /// <param name="tranId"></param>
    // /// <returns></returns>
    // public PdaPackageData GetPdaData(long tranId)
    // {
    //     var data = DataCacheService.GetBillData(Key, tranId);
    //     return (PdaPackageData)data;
    // }
    //
    // /// <summary>
    // /// 获取PDA显示模型
    // /// </summary>
    // /// <param name="tranId"></param>
    // /// <returns></returns>
    // public PdaDataShow GetPdaShow(long tranId)
    // {
    //     var pdaData = GetPdaData(tranId);
    //     return (PdaDataShow)pdaData.DataShow;
    // }

    /// <inheritdoc />
    public override Type DataType { get; } = typeof(PdaPackageData);

    /// <inheritdoc />
    public override void Initialization()
    {

    }
    /// <inheritdoc />
    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        PdaDataShow dataShow = GetPdaShow(tranId);
        dataShow.PackageNo = pdaData.Package?.PackageBarcode;
        dataShow.Barcodes.Clear();
        pdaData.Barcodes.ForEach(r =>
        {
            dataShow.Barcodes.Add(new PdaPackageDataBarcodeInfo
            {
                BarcodeId = r.Id,
                Title = r.Barcode,
                Label = r.MaterialNumber,
                Value = r.MaterialName,
                Qty = r.Qty
            });
        });
        DataCacheService.SaveRedis(Key, tranId);
    }

    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <returns></returns>
    public async Task ScanBarcode(long tranId, string barcode)
    {
        //1. 先判断PdaData的箱,是否空,如果空,先找箱 并记录到PdaData
        //2. 如果有箱,扫条码,并记录到PdaData
        //3. 调用RefreshShow
        //4. 返回 DataShow
        var pdaData = GetPdaData(tranId);
        var packageService = App.GetService<BarPackageService>(ServiceProvider);
        var barcodeService = App.GetService<BarBarcodeService>(ServiceProvider);
        var barcodeRep = App.GetService<SqlSugarRepository<BarBarcode>>(ServiceProvider);
        if (pdaData.Package == null)
        {
            //扫箱
            var package = await packageService.GetPackageByPackageBarcode(new GetPackageByPackageBarcodeInput() { PackageBarcode = barcode });
            if (package == null) throw Oops.Bah(BarErrorCode.BarPackage1005, barcode);
            pdaData.Package = package;
            //带出已装箱条码
            var barcodes = await barcodeRep.GetListAsync(r => r.ParentPackageId == package.Id && r.Status != BarcodeStatus.Disuse && !r.IsDelete);
            foreach (var barBarcode in barcodes)
                pdaData.Barcodes.Add(barBarcode);
        }
        else
        {
            //扫条码
            var barBarcode = await barcodeService.GetBarcodeAsync(barcode);
            if (barBarcode == null) throw Oops.Bah(BarErrorCode.BarBarcode1005, barcode);
            //判断条码是否存在
            var isExist = pdaData.Barcodes.Exists(r => r.Id == barBarcode.Id);
            if (isExist) throw Oops.Bah(BarErrorCode.BarBarcode1006);
            //判断条码是否已装在其它箱
            if (barBarcode.ParentPackageId != null && barBarcode.ParentPackageId != 0)
            {
                var package = await packageService.GetAsync(new IdInput() { Id = barBarcode.ParentPackageId });
                throw Oops.Bah(BarErrorCode.BarBarcode1007, package.PackageBarcode);
            }
            pdaData.Barcodes.Add(barBarcode);
        }

        RefreshShow(tranId);
    }

    /// <summary>
    /// 移除条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcodeId"></param>
    public void DeleteBarcode(long tranId, long barcodeId)
    {
        var pdaData = GetPdaData(tranId);
        var delBarcode = pdaData.Barcodes.FirstOrDefault(r => r.Id == barcodeId);
        if (delBarcode == null) throw Oops.Bah(BarErrorCode.BarBarcode1008);
        pdaData.Barcodes.Remove(delBarcode);

        RefreshShow(tranId);
    }

    /// <summary>
    /// 提交装箱
    /// </summary>
    /// <param name="tranId"></param>
    public async Task Submit(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var packageService = App.GetService<BarPackageService>(ServiceProvider);
        await packageService.BindBarcodeAsync(new BarPackageBindBarcodeInput
        {
            BarcodeIds = pdaData.Barcodes.Select(r => r.Id).ToList(),
            PackageId = pdaData.Package.Id
        });
        DataCacheService.DelBillData(tranId);
    }

    /// <inheritdoc />
    public override void BillDataInitialization(IPdaData pdaData)
    {

    }
}