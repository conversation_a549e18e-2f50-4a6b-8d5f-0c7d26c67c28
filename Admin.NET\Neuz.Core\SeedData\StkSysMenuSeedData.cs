﻿namespace Neuz.Core.SeedData;

[IgnoreUpdateSeed]
public class StkSysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu { Id = 1370000010000, Pid = 0, Title = "仓储管理", Path = "/stk", Name = "", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2100 },
            new SysMenu { Id = 1370000110000, Pid = 1370000010000, Title = "入库管理", Path = "/stk/in", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000210000, Pid = 1370000010000, Title = "出库管理", Path = "/stk/out", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000310000, Pid = 1370000010000, Title = "任务管理", Path = "/stk/task", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410000, Pid = 1370000010000, Title = "库内管理", Path = "/stk/stock", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000510000, Pid = 1370000010000, Title = "规则策略", Path = "/stk/rule", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000610000, Pid = 1370000010000, Title = "WMS设置", Path = "/stk/stkSetting", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000710000, Pid = 1370000010000, Title = "报表分析", Path = "/stk/report", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000110001, Pid = 1370000110000, Title = "入库通知单", Path = "/stk/stkInNotice", Name = "stkInNotice", Component = "/business/stk/stkInNotice/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000110002, Pid = 1370000110000, Title = "收货单", Path = "/stk/stkReceive", Name = "stkReceive", Component = "/business/stk/stkReceive/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000110003, Pid = 1370000110000, Title = "入库单", Path = "/stk/stkInStock", Name = "stkInStock", Component = "/business/stk/stkInStock/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000210001, Pid = 1370000210000, Title = "出库通知单", Path = "/stk/stkOutNotice", Name = "stkOutNotice", Component = "/business/stk/stkOutNotice/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000210002, Pid = 1370000210000, Title = "出库单", Path = "/stk/stkOutStock", Name = "stkOutStock", Component = "/business/stk/stkOutStock/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000310001, Pid = 1370000310000, Title = "库存任务", Path = "/stk/stkTask", Name = "stkTask", Component = "/business/stk/stkTask/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000410001, Pid = 1370000710000, Title = "库位库存", Path = "/stk/stkInventory", Name = "stkInventory", Component = "/business/stk/stkInventory/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410002, Pid = 1370000710000, Title = "库存事务", Path = "/stk/stkInventoryLog", Name = "stkInventoryLog", Component = "/business/stk/stkInventoryLog/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410003, Pid = 1370000310000, Title = "库存锁定事务", Path = "/stk/stkInventoryLockLog", Name = "stkInventoryLockLog", Component = "/business/stk/stkInventoryLockLog/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410004, Pid = 1370000410000, Title = "调拨通知单", Path = "/stk/stkTransferNotice", Name = "stkTransferNotice", Component = "/business/stk/stkTransferNotice/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410005, Pid = 1370000410000, Title = "库存调拨单", Path = "/stk/stkTransfer", Name = "stkTransfer", Component = "/business/stk/stkTransfer/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410006, Pid = 1370000410000, Title = "库存调整单", Path = "/stk/stkAdjustment", Name = "stkAdjustment", Component = "/business/stk/stkAdjustment/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410007, Pid = 1370000410000, Title = "库存盘点", Path = "/stk/stkStockCount", Name = "stkStockCount", Component = "/business/stk/stkStockCount/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410008, Pid = 1370000310000, Title = "库存预入库事务", Path = "/stk/stkInventoryPreInLog", Name = "stkInventoryPreInLog", Component = "/business/stk/stkInventoryPreInLog/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000410009, Pid = 1370000410000, Title = "批号调整单", Path = "/stk/stkBatchAdjust", Name = "stkBatchAdjust", Component = "/business/stk/stkBatchAdjust/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000510001, Pid = 1370000510000, Title = "分配策略", Path = "/stk/stkAllocatePolicy", Name = "stkAllocatePolicy", Component = "/business/stk/stkAllocatePolicy/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000510002, Pid = 1370000510000, Title = "分配规则", Path = "/stk/stkAllocateRule", Name = "stkAllocateRule", Component = "/business/stk/stkAllocateRule/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1370000610001, Pid = 1370000610000, Title = "单据类型", Path = "/stk/stkBillType", Name = "stkBillType", Component = "/business/stk/stkBillType/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000610002, Pid = 1370000610000, Title = "路线配置", Path = "/stk/pdaLocalBillConfig", Name = "pdaLocalBillConfig", Component = "/business/stk/pdaLocalBillConfig/index", Icon = "ele-Box", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 200 },
            
            new SysMenu { Id = 1370000710003, Pid = 1370000710000, Title = "物料有效期报表", Path = "/stkReport/stkMaterialValidityReport", Name = "stkMaterialValidityReport", Component = "/business/stkReport/stkMaterialValidityReport/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2025-05-15 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000710004, Pid = 1370000710000, Title = "缺料报表", Path = "/stkReport/stkMaterialShortageReport", Name = "stkMaterialShortageReport", Component = "/business/stkReport/stkMaterialShortageReport/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2025-05-15 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000710005, Pid = 1370000710000, Title = "入库报表", Path = "/stkReport/stkInReport", Name = "stkInReport", Component = "/business/stkReport/stkInReport/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2025-05-16 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1370000710006, Pid = 1370000710000, Title = "出库报表", Path = "/stkReport/stkOutReport", Name = "stkOutReport", Component = "/business/stkReport/stkOutReport/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2025-05-16 00:00:00"), OrderNo = 100 },
        };
    }
}