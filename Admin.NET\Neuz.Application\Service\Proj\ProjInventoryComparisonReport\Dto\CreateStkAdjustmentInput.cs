﻿namespace Neuz.Application.Proj.ProjInventoryComparisonReport.Dto;

public class CreateStkAdjustmentInput
{
    public List<CreateStkAdjustmentInputDetail> List { get; set; }
}

public class CreateStkAdjustmentInputDetail
{
    public string EsBillNo { get; set; }
    public long WarehouseId { get; set; }
    public long MaterialId { get; set; }
    public string BatchNo { get; set; }
    public long WhAreaId { get; set; }
    public decimal Qty { get; set; }
    public long UnitId { get; set; }
    public long OwnerId { get; set; }
    public long WhLocId { get; set; }
    public string WhLocNo { get; set; }
    public long? AuxPropValueId { get; set; }
}