using Furion.Localization;
using Magicodes.ExporterAndImporter.Excel;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库存盘点服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkStockCount", Order = 100)]
public class StkStockCountService : StkBaseBillService<StkStockCount>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkStockCount);

    /// <summary>
    /// 库存调整单服务
    /// </summary>
    private readonly StkAdjustmentService _adjustmentService;

    /// <summary>
    /// 库存盘点服务
    /// </summary>
    public StkStockCountService(IServiceProvider serviceProvider, SqlSugarRepository<StkStockCount> rep) : base(serviceProvider, rep)
    {
        _adjustmentService = serviceProvider.GetService<StkAdjustmentService>();
    }

    [NonAction]
    public override Task<List<ExecResult>> CancelAsync(IdsInput input)
    {
        throw new NotSupportedException();
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Name", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "Name",
            "BillType",
            "DocumentStatus",
            "StockCountStatus",
            "WarehouseNumber",
            "WarehouseName",
            "PushFlag",
            "EsBillNo",
            "CreateTime",
            "CreateUserName",
            "ApproveTime",
            "ApproveUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    /// <summary>
    /// 获取条码日志
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getBarcodeLogList")]
    public Task<List<StkStockCountBarcodeLog>> GetBarcodeLogList([FromQuery] IdInput input)
    {
        return Rep.Context.Queryable<StkStockCountBarcodeLog>()
            .IncludeNavCol()
            .Where(u => u.StockCountId == input.Id)
            .ToListAsync();
    }

    protected override void OnBeforeAdd(StkStockCount entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkStockCount entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkStockCount entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 重新计算剩余数量
            entry.GainQty = entry.CountQty > entry.AcctQty ? entry.CountQty - entry.AcctQty : 0;
            entry.LossQty = entry.AcctQty > entry.CountQty ? entry.AcctQty - entry.CountQty : 0;

            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);

            // 判断库区是否已填
            if (entry.WhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1005);
            // 判断库位是否已填
            if (entry.WhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1006);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            // 批号 null 时填充空字符串
            entry.BatchNo ??= "";
        }
    }

    protected override void OnAfterAudit(StkStockCount entity)
    {
        base.OnAfterAudit(entity);

        // 更新状态
        entity.StockCountStatus = StkStockCountStatus.GeneratedDetail;
        Rep.Update(entity);

        // 如果不存在外部Id，表示是本地创建的盘点表，统计库存信息并插入明细
        if (string.IsNullOrEmpty(entity.EsId))
        {
            // 统计盘点范围
            var inventoryQuery = Rep.Change<StkInventory>().AsQueryable();
            inventoryQuery.Where(u => u.WarehouseId == entity.WarehouseId && u.Qty > 0);

            // 是否有指定库区
            var whAreaIds = entity.WhAreaEntries?.Select(u => u.WhAreaId).ToList() ?? new List<long>();
            inventoryQuery.WhereIF(whAreaIds.Count > 0, u => whAreaIds.Contains(u.WhAreaId));
            // 是否有指定物料
            var materialIds = entity.MaterialEntries?.Select(u => u.MaterialId).ToList() ?? new List<long>();
            inventoryQuery.WhereIF(materialIds.Count > 0, u => materialIds.Contains(u.MaterialId));

            var inventories = inventoryQuery.ToList();

            var details = inventories
                .Select((inv, i) => new StkStockCountEntry
                {
                    EntryId = YitIdHelper.NextId(),
                    Id = entity.Id,
                    Seq = i + 1,
                    MaterialId = inv.MaterialId,
                    BatchNo = inv.BatchNo,
                    ProduceDate = inv.ProduceDate,
                    ExpiryDate = inv.ExpiryDate,
                    OwnerId = inv.OwnerId,
                    AuxPropValueId = inv.AuxPropValueId,
                    WhAreaId = inv.WhAreaId,
                    WhLocId = inv.WhLocId,
                    ContainerId = inv.ContainerId,
                    AcctQty = inv.Qty,
                    CountQty = 0,
                    GainQty = 0,
                    LossQty = inv.Qty,
                    UnitId = inv.UnitId,
                    IsSystem = true,
                })
                .ToList();

            // 插入盘点明细
            Rep.Change<StkStockCountEntry>().InsertRange(details);
        }
    }

    protected override void OnBeforeUnAudit(StkStockCount entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);

        if (Rep.Change<StkStockCountBarcodeLog>().Count(u => u.StockCountId == entity.Id) > 0)
            throw Oops.Bah(StkErrorCode.StkStockCount1001, entity.BillNo);

        if (entity.StockCountStatus == StkStockCountStatus.Finish)
            throw Oops.Bah(StkErrorCode.StkStockCount1002, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkStockCount entity)
    {
        base.OnAfterUnAudit(entity);

        // 更新状态
        entity.StockCountStatus = StkStockCountStatus.NoDetail;
        Rep.Update(entity);

        // 如果不存在外部Id，表示是本地创建的盘点表，清空明细
        if (string.IsNullOrEmpty(entity.EsId))
        {
            // 删除盘点明细
            Rep.Change<StkStockCountEntry>().AsDeleteable().Where(u => u.Id == entity.Id).ExecuteCommand();
        }

        // 删除盘点条码日志
        Rep.Change<StkStockCountBarcodeLog>().AsDeleteable().Where(u => u.StockCountId == entity.Id).ExecuteCommand();
    }

    [NonAction]
    public async Task Submit(StkStockCountSubmitInput input)
    {
        // 开启事务，如有外部事务，内部事务用外部事务
        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

        // var entity = await InnerGetAsync(u => u.Id == input.StockCountId);

        // 防止多用户同时提交造成覆盖上一个用户的更新
        var entity = await Rep.AsQueryable().TranLock().IncludeNavCol().FirstAsync(f => f.Id == input.StockCountId);

        var barcodeIds = input.Details.SelectMany(u => u.DetailInfos.Select(p => p.BarcodeId)).ToList();
        var barcodes = await Rep.Change<BdBarcode>().AsQueryable().IncludeNavCol().Where(u => barcodeIds.Contains(u.Id)).ToListAsync();

        var updList = new List<StkStockCountEntry>();
        var newList = new List<StkStockCountEntry>();

        foreach (var submitDetail in input.Details)
        {
            var firstDetailInfo = submitDetail.DetailInfos.FirstOrDefault();
            if (firstDetailInfo == null)
                throw Oops.Bah(StkErrorCode.StkStockCount1003);

            // 获取第一行条码，作为盘点外的数据的来源
            // 假定传进来的条码已经按分组条件分类好，不再校验条码列表中的条码是否满足同一分组条件
            var firstBarcode = barcodes.First(u => u.Id == firstDetailInfo.BarcodeId);

            StkStockCountEntry entry;
            if (submitDetail.StockCountEntryId != 0)
            {
                entry = entity.Entries.FirstOrDefault(u => u.EntryId == submitDetail.StockCountEntryId);
                if (entry == null)
                    throw Oops.Bah(StkErrorCode.StkStockCount1004, submitDetail.StockCountEntryId);
                updList.Add(entry);
            }
            else
            {
                // 条码上记录的物料、仓库、仓位等信息，不一定是本地数据的主键Id，所以需要按编码等唯一值重新查询
                var material = await Rep.Change<BdMaterial>().AsQueryable().WithCache(10).FirstAsync(u => firstBarcode.Material.Number == u.Number);

                //盘点明细之外的行
                entry = new StkStockCountEntry
                {
                    EntryId = YitIdHelper.NextId(),
                    Id = entity.Id,
                    Seq = entity.Entries.Count + 1,
                    MaterialId = material.Id,
                    BatchNo = firstBarcode.BatchNo,
                    ProduceDate = firstBarcode.ProduceDate,
                    ExpiryDate = firstBarcode.ExpiryDate,
                    OwnerId = firstBarcode.OwnerId,
                    AuxPropValueId = firstBarcode.AuxPropValueId,
                    WhAreaId = submitDetail.WhAreaId,
                    WhLocId = submitDetail.WhLocId,
                    ContainerId = null, //TODO:盘点明细之外的行，容器如何取值？
                    AcctQty = 0,
                    CountQty = 0,
                    GainQty = 0,
                    LossQty = 0,
                    UnitId = material.UnitId,
                    IsSystem = false,
                };
                entity.Entries.Add(entry);

                // 重新赋值给 StockCountEntryId，以便后面的日志记录关系
                submitDetail.StockCountEntryId = entry.EntryId;
                newList.Add(entry);
            }

            // 更新盘点数量
            var countQty = submitDetail.DetailInfos.Sum(u => u.Qty);

            entry.CountQty += countQty;
            entry.GainQty = entry.CountQty > entry.AcctQty ? entry.CountQty - entry.AcctQty : 0;
            entry.LossQty = entry.AcctQty > entry.CountQty ? entry.AcctQty - entry.CountQty : 0;
        }

        // 数据保存
        await Rep.Change<StkStockCountEntry>().UpdateRangeAsync(updList);
        await Rep.Change<StkStockCountEntry>().InsertRangeAsync(newList);

        // 保存日志
        await Rep.Change<StkStockCountBarcodeLog>().InsertRangeAsync(
            input.Details.SelectMany(u => u.DetailInfos
                .Select(p => new StkStockCountBarcodeLog
                {
                    Id = YitIdHelper.NextId(),
                    TranId = input.TranId,
                    StockCountId = input.StockCountId,
                    StockCountEntryId = u.StockCountEntryId,
                    BarcodeId = p.BarcodeId,
                    Barcode = barcodes.First(o => o.Id == p.BarcodeId).Barcode,
                    WhAreaId = u.WhAreaId,
                    WhLocId = u.WhLocId,
                    Qty = p.Qty,
                })).ToList()
        );

        // 提交事务
        uow.Commit();
    }

    /// <summary>
    /// 库存盘点数量保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("countSave")]
    [UnitOfWork]
    public async Task CountSave(StkStockCountCountSaveInput input)
    {
        var entity = await Rep.AsQueryable().Includes(u => u.Entries).FirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        foreach (var detail in input.Details)
        {
            var entry = entity.Entries.FirstOrDefault(u => u.EntryId == detail.EntryId);
            if (entry == null)
                throw Oops.Bah(StkErrorCode.StkStockCount1008, detail.EntryId);

            // 更新盘点数量
            entry.CountQty = detail.CountQty;
            entry.GainQty = entry.CountQty > entry.AcctQty ? entry.CountQty - entry.AcctQty : 0;
            entry.LossQty = entry.AcctQty > entry.CountQty ? entry.AcctQty - entry.CountQty : 0;

            // 保存
            await Rep.Context.Updateable(entry).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取差异数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getDiffQty")]
    public async Task<StkStockCount> GetDiffQty([FromQuery] IdInput input)
    {
        var entity = await InnerGetAsync(u => u.Id == input.Id);
        if (entity == null) return null;

        // 过滤盘盈或盘亏数量不为0的明细
        entity.Entries = entity.Entries.Where(u => u.GainQty != 0 || u.LossQty != 0).ToList();

        return entity;
    }

    /// <summary>
    /// 盘点结束
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("finish")]
    public Task<List<ExecResult>> Finish(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = InnerGetAsync(u => u.Id == id).GetAwaiter().GetResult();
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.StockCountStatus != StkStockCountStatus.GeneratedDetail)
                throw Oops.Bah(StkErrorCode.StkStockCount1005, entity.BillNo);

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 准备数据
            var stockCountBarcodeLogs = Rep.Context.Queryable<StkStockCountBarcodeLog>().Where(u => u.StockCountId == entity.Id).ToList();
            var barcodeIds = stockCountBarcodeLogs.Select(u => u.BarcodeId).ToList();
            var barcodes = Rep.Context.Queryable<BdBarcode>().Where(u => barcodeIds.Contains(u.Id)).ToList();

            // 更新条码档案
            UpdateBarcodeInfo(entity, stockCountBarcodeLogs, barcodes);

            // 更新盘点状态
            entity.StockCountStatus = StkStockCountStatus.Finish;
            Rep.Update(entity);

            var returnMessages = new List<string> { L.Text["单据: {0} 盘点结束成功", entity.BillNo] };
            // 生成盘亏库存调整单
            var adjustmentLoss = CreateAdjustmentLoss(entity, stockCountBarcodeLogs, barcodes);
            if (adjustmentLoss != null)
                returnMessages.Add(L.Text["生成盘亏库存调整单：{0}", adjustmentLoss.BillNo]);
            // 生成盘盈库存调整单
            var adjustmentGain = CreateAdjustmentGain(entity, stockCountBarcodeLogs, barcodes);
            if (adjustmentGain != null)
                returnMessages.Add(L.Text["生成盘盈库存调整单：{0}", adjustmentGain.BillNo]);

            // 提交事务
            uow.Commit();

            return string.Join(", ", returnMessages);
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 反盘点结束
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("unFinish")]
    public Task<List<ExecResult>> UnFinish(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = InnerGetAsync(u => u.Id == id).GetAwaiter().GetResult();
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.StockCountStatus != StkStockCountStatus.Finish)
                throw Oops.Bah(StkErrorCode.StkStockCount1006, entity.BillNo);

            // 检查是否有下游的盘盈盘亏单
            if (Rep.Context.Queryable<StkAdjustmentEntry>().Any(u => u.SrcBillKey == EntityName && u.SrcBillId == id))
                throw Oops.Bah(StkErrorCode.StkStockCount1007, entity.BillNo);

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 更新盘点状态
            entity.StockCountStatus = StkStockCountStatus.GeneratedDetail;
            Rep.Update(entity);

            // 回滚条码档案
            if (Rep.Context.Queryable<StkStockCountBarcodeLog>().Any(u => u.StockCountId == id))
                BarcodeService.RollbackBarcodeInfo(EntityName, id);

            // 提交事务
            uow.Commit();

            return L.Text["单据: {0} 反盘点结束成功", entity.BillNo];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    /// <param name="stockCountBarcodeLogs">库存盘点条码日志集合</param>
    /// <param name="barcodes">库存盘点条码日志关联的条码信息集合</param>
    private void UpdateBarcodeInfo(StkStockCount entity, List<StkStockCountBarcodeLog> stockCountBarcodeLogs, List<BdBarcode> barcodes)
    {
        // 更新条码档案
        var barcodeChanges = stockCountBarcodeLogs
            .Where(u =>
            {
                var barcode = barcodes.First(p => p.Id == u.BarcodeId);
                return barcode.Qty != u.Qty || barcode.WhAreaId != u.WhAreaId || barcode.WhLocId != u.WhLocId;
            })
            .Select(u =>
            {
                var barcode = barcodes.First(p => p.Id == u.BarcodeId);
                var stockCountEntry = entity.Entries.First(p => p.EntryId == u.StockCountEntryId);

                return new BdBarcodeChange
                {
                    OpTranId = u.TranId,
                    BarcodeId = u.BarcodeId,
                    OpQty = u.Qty,
                    OpQtyType = OpQtyType.Replace,
                    CurStatus = barcode.Status,
                    BatchNo = barcode.BatchNo,
                    ProduceDate = barcode.ProduceDate,
                    ExpiryDate = barcode.ExpiryDate,
                    RelBillKey = EntityName,
                    RelBillId = u.StockCountId,
                    RelBillEntryId = u.StockCountEntryId,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelBillEntrySeq = stockCountEntry.Seq,
                    SrcWhAreaId = barcode.WhAreaId,
                    SrcWhLocId = barcode.WhLocId,
                    DestWhAreaId = u.WhAreaId,
                    DestWhLocId = u.WhLocId,
                };
            }).ToList();

        BarcodeService.UpdateBarcodeInfo(barcodeChanges);
    }

    /// <summary>
    /// 生成盘盈库存调整单
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    /// <param name="stockCountBarcodeLogs">库存盘点条码日志集合</param>
    /// <param name="barcodes">库存盘点条码日志关联的条码信息集合</param>
    /// <returns>生成的库存调整单，如果无需生成则返回 null</returns>
    private StkAdjustment CreateAdjustmentGain(StkStockCount entity, List<StkStockCountBarcodeLog> stockCountBarcodeLogs, List<BdBarcode> barcodes)
    {
        if (entity.Entries.All(u => u.GainQty == 0)) return null;

        // 库存调整单
        var adjustment = new StkAdjustment
        {
            Memo = null,
            EsBillNo = null,
            EsId = null,
            Date = DateTime.Now.Date,
            DocumentStatus = DocumentStatus.Create,
            BillType = !string.IsNullOrEmpty(entity.EsId) ? "KCQTPY" : "KCPY", // 如果存在外部Id，表示是本地创建的盘点表
            WarehouseId = entity.WarehouseId,
            DepartmentId = null,
            PushFlag = PushFlag.None,
            Entries = entity.Entries.Where(u => u.GainQty > 0).Select((u, index) =>
            {
                // 库存调整单明细
                var qty = u.GainQty;
                var adjustmentEntry = new StkAdjustmentEntry
                {
                    EntryId = YitIdHelper.NextId(),
                    Seq = index + 1,
                    EsEntryId = null,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    WhAreaId = u.WhAreaId,
                    WhLocId = u.WhLocId,
                    Qty = qty,
                    UnitId = u.UnitId,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    GrossWeight = qty * u.Material.GrossWeight,
                    PackingVolume = qty * u.Material.PackingVolume,
                    PackingQty = (int)Math.Ceiling(qty * u.Material.StdPackingQty),
                    SrcBillKey = EntityName,
                    SrcBillNo = entity.BillNo,
                    SrcBillEntrySeq = u.Seq,
                    SrcBillId = entity.Id,
                    SrcBillEntryId = u.EntryId,
                    EntryMemo = null,
                    BarcodeEntries = null,
                };

                return adjustmentEntry;
            }).ToList()
        };

        var adjustmentId = _adjustmentService.AddAsync(adjustment).GetAwaiter().GetResult();
        return _adjustmentService.Rep.GetFirst(u => u.Id == adjustmentId);
    }

    /// <summary>
    /// 生成盘亏库存调整单
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    /// <param name="stockCountBarcodeLogs">库存盘点条码日志集合</param>
    /// <param name="barcodes">库存盘点条码日志关联的条码信息集合</param>
    /// <returns>生成的库存调整单，如果无需生成则返回 null</returns>
    private StkAdjustment CreateAdjustmentLoss(StkStockCount entity, List<StkStockCountBarcodeLog> stockCountBarcodeLogs, List<BdBarcode> barcodes)
    {
        if (entity.Entries.All(u => u.LossQty == 0)) return null;

        // 库存调整单
        var adjustment = new StkAdjustment
        {
            Memo = null,
            EsBillNo = null,
            EsId = null,
            Date = DateTime.Now.Date,
            DocumentStatus = DocumentStatus.Create,
            BillType = !string.IsNullOrEmpty(entity.EsId) ? "KCQTPK" : "KCPK", // 如果存在外部Id，表示是本地创建的盘点表
            WarehouseId = entity.WarehouseId,
            DepartmentId = null,
            PushFlag = PushFlag.None,
            Entries = entity.Entries.Where(u => u.LossQty > 0).Select((u, index) =>
            {
                // 库存调整单明细
                var qty = -u.LossQty;
                var adjustmentEntry = new StkAdjustmentEntry
                {
                    EntryId = YitIdHelper.NextId(),
                    Seq = index + 1,
                    EsEntryId = null,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    WhAreaId = u.WhAreaId,
                    WhLocId = u.WhLocId,
                    Qty = qty,
                    UnitId = u.UnitId,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    GrossWeight = qty * u.Material.GrossWeight,
                    PackingVolume = qty * u.Material.PackingVolume,
                    PackingQty = (int)Math.Ceiling(qty * u.Material.StdPackingQty),
                    SrcBillKey = EntityName,
                    SrcBillNo = entity.BillNo,
                    SrcBillEntrySeq = u.Seq,
                    SrcBillId = entity.Id,
                    SrcBillEntryId = u.EntryId,
                    EntryMemo = null,
                    BarcodeEntries = null,
                };

                return adjustmentEntry;
            }).ToList()
        };

        var adjustmentId = _adjustmentService.AddAsync(adjustment).GetAwaiter().GetResult();
        return _adjustmentService.Rep.GetFirst(u => u.Id == adjustmentId);
    }

    public override async Task<FileContentResult> Export([FromBody] IdsInput input)
    {
        // 只导出集合的第一个盘点Id
        var id = input.Ids.FirstOrDefault();
        var entity = await base.Rep.AsQueryable().IncludeNavCol().Where(c => c.Id == id).FirstAsync();

        var entityDtoList = new List<StkStockCountExportDto>();
        var whAreaEntryDtoList = new List<StkStockCountWhAreaEntryExportDto>();
        var materialEntryDtoList = new List<StkStockCountMaterialEntryExportDto>();
        var entryDtoList = new List<StkStockCountEntryExportDto>();

        entityDtoList.Add(new StkStockCountExportDto
        {
            BillNo = entity.BillNo,
            Name = entity.Name,
            DocumentStatus = entity.DocumentStatus,
            StockCountStatus = entity.StockCountStatus,
            WarehouseNumber = entity.Warehouse.Number,
            WarehouseName = entity.Warehouse.Name,
            EsBillNo = entity.EsBillNo,
            PushFlag = entity.PushFlag,
            ApproveTime = entity.ApproveTime,
            ApproveUserName = entity.ApproveUserName,
            CreateTime = entity.CreateTime,
            CreateUserName = entity.CreateUserName,
        });
        whAreaEntryDtoList.AddRange(entity.WhAreaEntries.OrderBy(u => u.Seq).Select(u => new StkStockCountWhAreaEntryExportDto
        {
            Seq = u.Seq,
            WhAreaNumber = u.WhArea.Number,
            WhAreaName = u.WhArea.Name,
        }));
        materialEntryDtoList.AddRange(entity.MaterialEntries.OrderBy(u => u.Seq).Select(u => new StkStockCountMaterialEntryExportDto
        {
            Seq = u.Seq,
            MaterialNumber = u.Material.Number,
            MaterialName = u.Material.Name,
        }));
        entryDtoList.AddRange(entity.Entries.OrderBy(u => u.Seq).Select(u => new StkStockCountEntryExportDto
        {
            Seq = u.Seq,
            MaterialNumber = u.Material.Number,
            MaterialName = u.Material.Name,
            BatchNo = u.BatchNo,
            ProduceDate = u.ProduceDate,
            ExpiryDate = u.ExpiryDate,
            WhAreaNumber = u.WhArea.Number,
            WhAreaName = u.WhArea.Name,
            WhLocNumber = u.WhLoc.Number,
            WhLocName = u.WhLoc.Name,
            ContainerNumber = u.Container?.Number,
            AcctQty = u.AcctQty,
            CountQty = u.CountQty,
            GainQty = u.GainQty,
            LossQty = u.LossQty,
            UnitNumber = u.Unit.Number,
            UnitName = u.Unit.Name,
            OwnerNumber = u.Owner.Number,
            OwnerName = u.Owner.Name,
            AuxPropValueNumber = u.AuxPropValue.Number,
            AuxPropValueName = u.AuxPropValue.Name,
            IsSystem = u.IsSystem,
            EntryMemo = u.EntryMemo,
        }));

        var exporter = new ExcelExporter();
        var result = exporter
            .Append(entityDtoList, L.Text["库存盘点"])
            .SeparateBySheet()
            .Append(whAreaEntryDtoList, L.Text["库区范围"])
            .SeparateBySheet()
            .Append(materialEntryDtoList, L.Text["物料范围"])
            .SeparateBySheet()
            .Append(entryDtoList, L.Text["明细"])
            .ExportAppendDataAsByteArray().Result;

        var fileName = typeof(StkStockCount).GetDescriptionValue<SugarTable>().TableDescription + $"_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        return await Task.FromResult(new FileContentResult(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") { FileDownloadName = fileName });
    }

    /// <summary>
    /// 导入物料数据
    /// </summary>
    /// <returns></returns>
    [HttpPost("importMaterialData")]
    public async Task<StkStockCountMaterialImportOutput> ImportMaterialData(IFormFile file)
    {
        var ms = new MemoryStream();
        await file.CopyToAsync(ms);

        var importer = new ExcelImporter();
        var result = await importer.Import<StkStockCountMaterialImportDto>(ms);
        if (result.Exception != null)
            throw Oops.Bah(L.Text["导入异常:"] + result.Exception);
        if (result.RowErrors.Count > 0)
            throw Oops.Bah(L.Text["数据校验:"] + JSON.Serialize(result.RowErrors));
        if (result.TemplateErrors.Count > 0)
            throw Oops.Bah(L.Text["模版错误:"] + JSON.Serialize(result.TemplateErrors));

        var materialList = await Rep.Context.Queryable<BdMaterial>()
            .Where(u => result.Data.Select(p => p.MaterialNumber).Contains(u.Number))
            .ToListAsync();

        // 不存在的编码
        var notExistsMaterialNumbers = result.Data.Select(p => p.MaterialNumber).Except(materialList.Select(u => u.Number)).ToList();
        if (notExistsMaterialNumbers.Count > 0)
            throw Oops.Bah(L.Text["物料编码: {0} 不存在", string.Join(",", notExistsMaterialNumbers)]);

        return new StkStockCountMaterialImportOutput
        {
            Materials = materialList
        };
    }
}