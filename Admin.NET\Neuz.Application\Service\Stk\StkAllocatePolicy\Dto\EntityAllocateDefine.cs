﻿namespace Neuz.Application;

/// <summary>
/// 实体分配信息定义
/// </summary>
public class EntityAllocateDefine
{
    /// <summary>
    /// 主键Id字段名
    /// </summary>
    public string IdField { get; set; }

    /// <summary>
    /// 单据编号字段名
    /// </summary>
    public string BillNoField { get; set; }

    /// <summary>
    /// 单据类型字段名
    /// </summary>
    public string BillTypeField { get; set; }

    /// <summary>
    /// 分录主键Id字段名，如果没有可留空
    /// </summary>
    public string EntryIdField { get; set; }

    /// <summary>
    /// 分录序号字段名，如果没有可留空
    /// </summary>
    public string EntrySeqField { get; set; }

    /// <summary>
    /// 仓库Id字段
    /// </summary>
    public string WarehouseIdField { get; set; }

    /// <summary>
    /// 物料Id字段
    /// </summary>
    public string MaterialIdField { get; set; }

    /// <summary>
    /// 关联的任务主键Id，如果最终分配的结果需要生成库存任务，需要提前提供任务主键Id用于库存相关日志和任务主键Id的关联
    /// </summary>
    public long? RelTaskId { get; set; }

    /// <summary>
    /// 获取单据扩展的查询字段，多用于计算可分配数量
    /// </summary>
    public List<string> GetBillExtraSelectFields { get; set; }

    /// <summary>
    /// 获取可分配数量方法
    /// </summary>
    public Func<DataRow, decimal> GetCanAllocateQty { get; set; }

    /// <summary>
    /// 是否执行分配锁定库存
    /// </summary>
    public bool IsLockInv { get; set; }

    /// <summary>
    /// 是否执行分配库位预入库
    /// </summary>
    public bool IsPreIn { get; set; }

    /// <summary>
    /// 库存锁定事务（当 <see cref="IsLockInv"/> 不为 true 时，需要有值）
    /// </summary>
    public StkInvLockLogType? InvLockLogType { get; set; }

    /// <summary>
    /// 库存预入库事务（当 <see cref="IsPreIn"/> 不为 true 时，需要有值）
    /// </summary>
    public StkInvPreInLogType? InvPreInLogType { get; set; }

    /// <summary>
    /// 批号字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取批号
    /// </summary>
    public string BatchNoField { get; set; }

    /// <summary>
    /// 生产日期字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取生产日期
    /// </summary>
    public string ProduceDateField { get; set; }

    /// <summary>
    /// 有效期至字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取有效期至
    /// </summary>
    public string ExpiryDateField { get; set; }

    /// <summary>
    /// 容器Id字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取容器Id
    /// </summary>
    public string ContainerIdField { get; set; }

    /// <summary>
    /// 单位Id字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取单位Id且不能为空
    /// </summary>
    public string UnitIdField { get; set; }

    /// <summary>
    /// 货主Id字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取货主Id且不能为空
    /// </summary>
    public string OwnerIdField { get; set; }
    
    /// <summary>
    /// 辅助属性值Id字段名，如果没有可留空，用于 <see cref="IsLockInv"/> = false 且 <see cref="IsPreIn"/> = true 时，获取辅助属性值Id且不能为空
    /// </summary>
    public string AuxPropValueIdField { get; set; }
}