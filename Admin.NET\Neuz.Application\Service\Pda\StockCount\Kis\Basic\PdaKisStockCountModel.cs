﻿using Furion.Localization;
using Neuz.Application.Adapter.K3Wise;
using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;

namespace Neuz.Application.Pda.StockCount.Wise.Basic;

/// <summary>
/// 盘点单据
/// </summary>
public class PdaKisStockCountModel : IPdaBillLookupModel
{
    protected ErpInterface ErpInterface => App.GetService<ErpInterface>(_serviceProvider);

    protected readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 单据Link
    /// </summary>
    public PdaBillLink BillLink => new PdaBillLink
    {
        SourceKey = "3",
        SourceTitle = "",
        DestKey = "StockCount",
        DestTitle = L.Text["盘点方案"],
        Rob = 1
    };

    public List<PdaLookupOutput> GetLookupOutput(long tranId, DataTable table)
    {
        List<PdaLookupOutput> lookupOutputs = new List<PdaLookupOutput>();
        foreach (DataRow row in table.Rows)
        {
            PdaLookupOutput lookupOutput = new PdaLookupOutput();
            lookupOutput.Key = $"{row["SourceBillId"]}";
            lookupOutput.Title = $"{row["SourceBillNo"]}";
            lookupOutput.SubTitle = $"{row["FDate"]}";
            lookupOutputs.Add(lookupOutput);
        }

        return lookupOutputs;
    }

    /// <summary>
    /// 唯一标识
    /// </summary>
    public string LookupKey => "KisStockCount";

    public LookupType LookupType => LookupType.bill;
    public DataTable QuerySourceHead(long tranId, string billNo)
    {
        return ErpInterface.QueryData(string.Format(HeadSql, $"a.FProcessID LIKE '%{billNo}%'"));
    }

    public DataTable GetSourceHeadForId(long tranId, string id)
    {
        return ErpInterface.QueryData(string.Format(HeadSql, $"a.FID = '{id}'"));
    }

    public DataTable GetSourceHeadForBillNo(long tranId, string billNo)
    {
        return ErpInterface.QueryData(string.Format(HeadSql, $"a.FProcessID = '{billNo}'"));
    }

    public DataTable GetSourceDetailForIds(long tranId, List<string> ids)
    {
        var id = ids.FirstOrDefault();
        var table = ErpInterface.QueryData(string.Format(DetailSql, $"a.FID = '{id}'"));
        var logRep = App.GetService<SqlSugarRepository<BarBarcodeLog>>(_serviceProvider);
        var log = logRep.GetFirst(r => r.TargetBillId == id && r.OpType == BarOpType.StockCount);
        if (log == null)
        {
            foreach (DataRow dataRow in table.Rows)
            {
                dataRow["CheckQty"] = 0;
            }
        }
        return table;
    }

    //WISE有FCheckId, Kis没有,用FStatus代替
    public string HeadSql = @"
SELECT  a.FID AS SourceBillId ,
        a.FProcessID AS SourceBillNo ,
        'StockCount' AS SourceTranType ,
        a.FDate AS FDate ,
        b.FName AS UserName
FROM    ICStockCheckProcess a
        JOIN t_User b ON a.FOperatorID = b.FUserID
WHERE   ISNULL(a.FStatus, 0) = 0 AND {0}
ORDER BY FID DESC
";

    //Wise有FSupplyId, Kis没有,去掉
    public string DetailSql = @"
SELECT  a.FID AS SourceBillId ,
        a.FProcessID AS SourceBillNo ,
        'StockCount' AS SourceTranType ,
        a.FDate AS FDate ,
        b.FName AS username ,
        c.FBrNo ,
        c.FBatchNo AS BatchNo ,
        c.FKFDate AS ProduceDate ,
        c.FKFPeriod AS ExpPeriod ,
        c.FAuxPropID AS AuxPropId ,
        c.FMTONo ,
        c.FAuxQty AS Qty ,
        c.FAuxCheckQty AS CheckQty,
        c.FQty ,
        c.FSecQty ,
        d.FItemID AS MaterialId ,
        d.FShortNumber AS MaterialShortNumber ,
        d.FNumber AS MaterialNumber ,
        d.FName AS MaterialName ,
        e.FItemID AS StockId ,
        e.FNumber AS StockNumber ,
        e.FName AS StockName ,
        f.FSPID AS StockLocId ,
        f.FNumber AS StockLocNumber ,
        f.FName AS StockLocName ,
        g.FMeasureUnitID AS UnitId ,
        g.FNumber AS UnitNumber ,
        g.FName AS UnitName ,
        h.FMeasureUnitID AS FSecUnitId ,
        h.FNumber AS FSecUnitNumber ,
        h.FName AS FSecUnitName ,
		g.FCoefficient AS FCoefficient ,
		d.FSecCoefficient AS FSecCoefficient
FROM    ICStockCheckProcess a
        LEFT JOIN t_User b ON a.FOperatorID = b.FUserID
        LEFT JOIN dbo.ICInvBackup c ON a.FID = c.FInterID
        LEFT JOIN dbo.t_ICItem d ON d.FItemID = c.FItemID
        LEFT JOIN dbo.t_Stock e ON e.FItemID = c.FStockID
        LEFT JOIN dbo.t_StockPlace f ON f.FSPID = c.FStockPlaceID
        LEFT JOIN dbo.t_MeasureUnit g ON g.FMeasureUnitID = c.FUnitID
        LEFT JOIN dbo.t_MeasureUnit h ON h.FMeasureUnitID = c.FSecUnitID
WHERE   ISNULL(a.FStatus, 0) = 0 AND {0}
ORDER BY FID ASC
";

    public PdaKisStockCountModel(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }
}