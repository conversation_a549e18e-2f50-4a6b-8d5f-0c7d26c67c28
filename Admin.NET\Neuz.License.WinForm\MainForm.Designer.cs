﻿namespace Neuz.License.WinForm
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            btnCreate = new Button();
            tableLayoutPanel1 = new TableLayoutPanel();
            label1 = new Label();
            label2 = new Label();
            label3 = new Label();
            label4 = new Label();
            label5 = new Label();
            label6 = new Label();
            label7 = new Label();
            label8 = new Label();
            txtCustomerName = new TextBox();
            panel2 = new Panel();
            rbLicenseTypeOfficial = new RadioButton();
            rbLicenseTypeTrial = new RadioButton();
            dtBeginDate = new DateTimePicker();
            dtEndDate = new DateTimePicker();
            numMaxConnectCount = new NumericUpDown();
            txtServerKey = new TextBox();
            dtServiceEndDate = new DateTimePicker();
            txtDescription = new TextBox();
            txtPrivateKey = new TextBox();
            panel4 = new Panel();
            btnOpen = new Button();
            panel5 = new Panel();
            label9 = new Label();
            panel3 = new Panel();
            openFileDialog1 = new OpenFileDialog();
            saveFileDialog1 = new SaveFileDialog();
            groupBox1 = new GroupBox();
            txtLicense = new TextBox();
            panel6 = new Panel();
            btnSave = new Button();
            tableLayoutPanel1.SuspendLayout();
            panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numMaxConnectCount).BeginInit();
            panel4.SuspendLayout();
            panel5.SuspendLayout();
            groupBox1.SuspendLayout();
            panel6.SuspendLayout();
            SuspendLayout();
            // 
            // btnCreate
            // 
            btnCreate.Location = new Point(2, 3);
            btnCreate.Margin = new Padding(2, 3, 2, 3);
            btnCreate.Name = "btnCreate";
            btnCreate.Size = new Size(73, 25);
            btnCreate.TabIndex = 0;
            btnCreate.Text = "生成";
            btnCreate.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 2;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 117F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Controls.Add(label1, 0, 1);
            tableLayoutPanel1.Controls.Add(label2, 0, 2);
            tableLayoutPanel1.Controls.Add(label3, 0, 3);
            tableLayoutPanel1.Controls.Add(label4, 0, 4);
            tableLayoutPanel1.Controls.Add(label5, 0, 5);
            tableLayoutPanel1.Controls.Add(label6, 0, 6);
            tableLayoutPanel1.Controls.Add(label7, 0, 7);
            tableLayoutPanel1.Controls.Add(label8, 0, 8);
            tableLayoutPanel1.Controls.Add(txtCustomerName, 1, 1);
            tableLayoutPanel1.Controls.Add(panel2, 1, 2);
            tableLayoutPanel1.Controls.Add(dtBeginDate, 1, 3);
            tableLayoutPanel1.Controls.Add(dtEndDate, 1, 4);
            tableLayoutPanel1.Controls.Add(numMaxConnectCount, 1, 5);
            tableLayoutPanel1.Controls.Add(txtServerKey, 1, 6);
            tableLayoutPanel1.Controls.Add(dtServiceEndDate, 1, 7);
            tableLayoutPanel1.Controls.Add(txtDescription, 1, 8);
            tableLayoutPanel1.Controls.Add(txtPrivateKey, 1, 0);
            tableLayoutPanel1.Controls.Add(panel4, 0, 0);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(0, 3);
            tableLayoutPanel1.Margin = new Padding(2, 3, 2, 3);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 9;
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.RowStyles.Add(new RowStyle());
            tableLayoutPanel1.Size = new Size(361, 389);
            tableLayoutPanel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Dock = DockStyle.Right;
            label1.Location = new Point(59, 92);
            label1.Margin = new Padding(2, 0, 2, 0);
            label1.Name = "label1";
            label1.Size = new Size(56, 29);
            label1.TabIndex = 0;
            label1.Text = "使用主体";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Dock = DockStyle.Right;
            label2.Location = new Point(59, 121);
            label2.Margin = new Padding(2, 0, 2, 0);
            label2.Name = "label2";
            label2.Size = new Size(56, 29);
            label2.TabIndex = 1;
            label2.Text = "授权类型";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Dock = DockStyle.Right;
            label3.Location = new Point(59, 150);
            label3.Margin = new Padding(2, 0, 2, 0);
            label3.Name = "label3";
            label3.Size = new Size(56, 29);
            label3.TabIndex = 2;
            label3.Text = "开始日期";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Dock = DockStyle.Right;
            label4.Location = new Point(59, 179);
            label4.Margin = new Padding(2, 0, 2, 0);
            label4.Name = "label4";
            label4.Size = new Size(56, 29);
            label4.TabIndex = 3;
            label4.Text = "结束日期";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Dock = DockStyle.Right;
            label5.Location = new Point(8, 208);
            label5.Margin = new Padding(2, 0, 2, 0);
            label5.Name = "label5";
            label5.Size = new Size(107, 29);
            label5.TabIndex = 4;
            label5.Text = "最大连接数(0不限)";
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Dock = DockStyle.Right;
            label6.Location = new Point(35, 237);
            label6.Margin = new Padding(2, 0, 2, 0);
            label6.Name = "label6";
            label6.Size = new Size(80, 29);
            label6.TabIndex = 5;
            label6.Text = "服务器序列号";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Dock = DockStyle.Right;
            label7.Location = new Point(11, 266);
            label7.Margin = new Padding(2, 0, 2, 0);
            label7.Name = "label7";
            label7.Size = new Size(104, 29);
            label7.TabIndex = 6;
            label7.Text = "服务维护结束日期";
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Dock = DockStyle.Right;
            label8.Location = new Point(83, 295);
            label8.Margin = new Padding(2, 0, 2, 0);
            label8.Name = "label8";
            label8.Size = new Size(32, 117);
            label8.TabIndex = 7;
            label8.Text = "描述";
            // 
            // txtCustomerName
            // 
            txtCustomerName.Dock = DockStyle.Fill;
            txtCustomerName.Location = new Point(119, 95);
            txtCustomerName.Margin = new Padding(2, 3, 2, 3);
            txtCustomerName.Name = "txtCustomerName";
            txtCustomerName.Size = new Size(240, 23);
            txtCustomerName.TabIndex = 8;
            // 
            // panel2
            // 
            panel2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panel2.Controls.Add(rbLicenseTypeOfficial);
            panel2.Controls.Add(rbLicenseTypeTrial);
            panel2.Location = new Point(119, 124);
            panel2.Margin = new Padding(2, 3, 2, 3);
            panel2.Name = "panel2";
            panel2.Size = new Size(240, 23);
            panel2.TabIndex = 9;
            // 
            // rbLicenseTypeOfficial
            // 
            rbLicenseTypeOfficial.AutoSize = true;
            rbLicenseTypeOfficial.Location = new Point(54, 0);
            rbLicenseTypeOfficial.Margin = new Padding(2, 3, 2, 3);
            rbLicenseTypeOfficial.Name = "rbLicenseTypeOfficial";
            rbLicenseTypeOfficial.Size = new Size(50, 21);
            rbLicenseTypeOfficial.TabIndex = 1;
            rbLicenseTypeOfficial.TabStop = true;
            rbLicenseTypeOfficial.Text = "正式";
            rbLicenseTypeOfficial.UseVisualStyleBackColor = true;
            // 
            // rbLicenseTypeTrial
            // 
            rbLicenseTypeTrial.AutoSize = true;
            rbLicenseTypeTrial.Location = new Point(2, 0);
            rbLicenseTypeTrial.Margin = new Padding(2, 3, 2, 3);
            rbLicenseTypeTrial.Name = "rbLicenseTypeTrial";
            rbLicenseTypeTrial.Size = new Size(50, 21);
            rbLicenseTypeTrial.TabIndex = 0;
            rbLicenseTypeTrial.TabStop = true;
            rbLicenseTypeTrial.Text = "试用";
            rbLicenseTypeTrial.UseVisualStyleBackColor = true;
            // 
            // dtBeginDate
            // 
            dtBeginDate.Dock = DockStyle.Fill;
            dtBeginDate.Location = new Point(119, 153);
            dtBeginDate.Margin = new Padding(2, 3, 2, 3);
            dtBeginDate.Name = "dtBeginDate";
            dtBeginDate.Size = new Size(240, 23);
            dtBeginDate.TabIndex = 10;
            // 
            // dtEndDate
            // 
            dtEndDate.Dock = DockStyle.Fill;
            dtEndDate.Location = new Point(119, 182);
            dtEndDate.Margin = new Padding(2, 3, 2, 3);
            dtEndDate.Name = "dtEndDate";
            dtEndDate.Size = new Size(240, 23);
            dtEndDate.TabIndex = 11;
            // 
            // numMaxConnectCount
            // 
            numMaxConnectCount.Dock = DockStyle.Fill;
            numMaxConnectCount.Location = new Point(119, 211);
            numMaxConnectCount.Margin = new Padding(2, 3, 2, 3);
            numMaxConnectCount.Maximum = new decimal(new int[] { 99999, 0, 0, 0 });
            numMaxConnectCount.Name = "numMaxConnectCount";
            numMaxConnectCount.Size = new Size(240, 23);
            numMaxConnectCount.TabIndex = 12;
            // 
            // txtServerKey
            // 
            txtServerKey.Dock = DockStyle.Fill;
            txtServerKey.Location = new Point(119, 240);
            txtServerKey.Margin = new Padding(2, 3, 2, 3);
            txtServerKey.Name = "txtServerKey";
            txtServerKey.Size = new Size(240, 23);
            txtServerKey.TabIndex = 13;
            // 
            // dtServiceEndDate
            // 
            dtServiceEndDate.Dock = DockStyle.Fill;
            dtServiceEndDate.Location = new Point(119, 269);
            dtServiceEndDate.Margin = new Padding(2, 3, 2, 3);
            dtServiceEndDate.Name = "dtServiceEndDate";
            dtServiceEndDate.Size = new Size(240, 23);
            dtServiceEndDate.TabIndex = 14;
            // 
            // txtDescription
            // 
            txtDescription.Dock = DockStyle.Fill;
            txtDescription.Location = new Point(119, 298);
            txtDescription.Margin = new Padding(2, 3, 2, 3);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.Size = new Size(240, 111);
            txtDescription.TabIndex = 15;
            // 
            // txtPrivateKey
            // 
            txtPrivateKey.Dock = DockStyle.Fill;
            txtPrivateKey.Location = new Point(119, 3);
            txtPrivateKey.Margin = new Padding(2, 3, 2, 3);
            txtPrivateKey.Multiline = true;
            txtPrivateKey.Name = "txtPrivateKey";
            txtPrivateKey.ScrollBars = ScrollBars.Vertical;
            txtPrivateKey.Size = new Size(240, 86);
            txtPrivateKey.TabIndex = 0;
            // 
            // panel4
            // 
            panel4.Controls.Add(btnOpen);
            panel4.Controls.Add(panel5);
            panel4.Dock = DockStyle.Fill;
            panel4.Location = new Point(2, 3);
            panel4.Margin = new Padding(2, 3, 2, 3);
            panel4.Name = "panel4";
            panel4.Size = new Size(113, 86);
            panel4.TabIndex = 18;
            // 
            // btnOpen
            // 
            btnOpen.Location = new Point(47, 26);
            btnOpen.Margin = new Padding(2, 3, 2, 3);
            btnOpen.Name = "btnOpen";
            btnOpen.Size = new Size(63, 25);
            btnOpen.TabIndex = 1;
            btnOpen.Text = "打开";
            btnOpen.UseVisualStyleBackColor = true;
            // 
            // panel5
            // 
            panel5.Controls.Add(label9);
            panel5.Dock = DockStyle.Top;
            panel5.Location = new Point(0, 0);
            panel5.Margin = new Padding(2, 3, 2, 3);
            panel5.Name = "panel5";
            panel5.Size = new Size(113, 20);
            panel5.TabIndex = 0;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Dock = DockStyle.Right;
            label9.ForeColor = Color.BlueViolet;
            label9.Location = new Point(45, 0);
            label9.Margin = new Padding(2, 0, 2, 0);
            label9.Name = "label9";
            label9.Size = new Size(68, 17);
            label9.TabIndex = 0;
            label9.Text = "私钥字符串";
            // 
            // panel3
            // 
            panel3.Dock = DockStyle.Top;
            panel3.Location = new Point(0, 0);
            panel3.Margin = new Padding(2, 3, 2, 3);
            panel3.Name = "panel3";
            panel3.Size = new Size(610, 3);
            panel3.TabIndex = 2;
            // 
            // openFileDialog1
            // 
            openFileDialog1.Filter = "xml 文件|*.xml|所有文件|*.*";
            // 
            // saveFileDialog1
            // 
            saveFileDialog1.Filter = "key 文件|*.key|所有文件|*.*";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(txtLicense);
            groupBox1.Controls.Add(panel6);
            groupBox1.Dock = DockStyle.Right;
            groupBox1.Location = new Point(361, 3);
            groupBox1.Margin = new Padding(2, 3, 2, 3);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(2, 3, 2, 3);
            groupBox1.Size = new Size(249, 389);
            groupBox1.TabIndex = 3;
            groupBox1.TabStop = false;
            groupBox1.Text = "授权数据";
            // 
            // txtLicense
            // 
            txtLicense.Dock = DockStyle.Fill;
            txtLicense.Location = new Point(2, 50);
            txtLicense.Margin = new Padding(2, 3, 2, 3);
            txtLicense.Multiline = true;
            txtLicense.Name = "txtLicense";
            txtLicense.ReadOnly = true;
            txtLicense.ScrollBars = ScrollBars.Vertical;
            txtLicense.Size = new Size(245, 336);
            txtLicense.TabIndex = 18;
            // 
            // panel6
            // 
            panel6.Controls.Add(btnSave);
            panel6.Controls.Add(btnCreate);
            panel6.Dock = DockStyle.Top;
            panel6.Location = new Point(2, 19);
            panel6.Margin = new Padding(2, 3, 2, 3);
            panel6.Name = "panel6";
            panel6.Size = new Size(245, 31);
            panel6.TabIndex = 19;
            // 
            // btnSave
            // 
            btnSave.Location = new Point(80, 3);
            btnSave.Margin = new Padding(2, 3, 2, 3);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(73, 25);
            btnSave.TabIndex = 1;
            btnSave.Text = "保存为文件";
            btnSave.UseVisualStyleBackColor = true;
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(610, 392);
            Controls.Add(tableLayoutPanel1);
            Controls.Add(groupBox1);
            Controls.Add(panel3);
            Margin = new Padding(2, 3, 2, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(626, 431);
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Neuz 授权生成";
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numMaxConnectCount).EndInit();
            panel4.ResumeLayout(false);
            panel5.ResumeLayout(false);
            panel5.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            panel6.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        private Button btnCreate;
        private TableLayoutPanel tableLayoutPanel1;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label4;
        private Label label5;
        private Label label6;
        private Label label7;
        private Label label8;
        private TextBox txtCustomerName;
        private Panel panel2;
        private DateTimePicker dtBeginDate;
        private DateTimePicker dtEndDate;
        private NumericUpDown numMaxConnectCount;
        private TextBox txtServerKey;
        private DateTimePicker dtServiceEndDate;
        private TextBox txtDescription;
        private Panel panel3;
        private RadioButton rbLicenseTypeOfficial;
        private RadioButton rbLicenseTypeTrial;
        private TextBox txtPrivateKey;
        private Panel panel4;
        private Button btnOpen;
        private Panel panel5;
        private Label label9;
        private OpenFileDialog openFileDialog1;
        private SaveFileDialog saveFileDialog1;
        private GroupBox groupBox1;
        private TextBox txtLicense;
        private Panel panel6;
        private Button btnSave;
    }
}