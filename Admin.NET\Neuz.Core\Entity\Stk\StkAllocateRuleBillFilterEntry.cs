﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储分配规则单据条件明细
/// </summary>
[SugarTable(null, "仓储分配规则单据条件明细")]
public class StkAllocateRuleBillFilterEntry : EntryEntityBase
{
    /// <summary>
    /// 字段名
    /// </summary>
    [SugarColumn(ColumnDescription = "字段名", Length = 100)]
    public string FieldName { get; set; }

    /// <summary>
    /// 操作符
    /// </summary>
    [SugarColumn(ColumnDescription = "操作符", Length = 30)]
    public string Op { get; set; }

    ///// <summary>
    ///// 目标值字段名
    ///// </summary>
    //[SugarColumn(ColumnDescription = "目标值字段名", Length = 100)]
    //public string TargetValueFieldName { get; set; }

    /// <summary>
    /// 常量值
    /// </summary>
    [SugarColumn(ColumnDescription = "常量值", Length = 50)]
    public string? ConstValue { get; set; }

    /// <summary>
    /// 或条件分组名称
    /// </summary>
    /// <remarks>
    /// 当值不为空时，相同分组名称的条件会用 Or 连接
    /// </remarks>
    [SugarColumn(ColumnDescription = "或条件分组名称", Length = 50)]
    public string? OrGroup { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool IsEnable { get; set; }
}