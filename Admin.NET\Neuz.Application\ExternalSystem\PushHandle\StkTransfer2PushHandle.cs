﻿using Neuz.Application.ExternalSystem.Dto;
using SqlSugar;
using System.Diagnostics.CodeAnalysis;

namespace Neuz.Application.ExternalSystem;

/// <summary>
/// 库存调拨单推送处理基础服务
/// </summary>
public abstract class StkTransfer2PushHandle : BasePushHandle<StkTransfer2PushData>
{
    /// <summary>
    /// 查询库存调拨单的单据类型集合
    /// </summary>
    protected abstract List<string> QueryBillTypes { get; }

    protected StkTransfer2PushHandle(IServiceProvider serviceProvider, ISqlSugarClient context) : base(serviceProvider, context)
    {
    }

    public override async Task<List<StkTransfer2PushData>> QueryLocalObject(DateTime? queryBeginTime = null, DateTime? queryEndTime = null, IList<string> queryBillNos = null)
    {
        var query = Context.Queryable<StkTransfer>().IncludeNavCol();

        // 查询条件
        query.Where(u => u.DocumentStatus == DocumentStatus.Approve);
        query.Where(u => QueryBillTypes.Contains(u.BillType));
        query.Where(u => u.PushFlag == PushFlag.None || u.PushFlag == PushFlag.Failure);
        query.WhereIF(queryBillNos is { Count: > 0 }, u => queryBillNos.Contains(u.BillNo));
        query.WhereIF(queryBeginTime != null, u => u.ApproveTime >= queryBeginTime);
        query.WhereIF(queryEndTime != null, u => u.ApproveTime <= queryEndTime);

        var list = await query.ToListAsync();

        // 出库通知单Id
        var transferNoticeIds = list.SelectMany(u =>
            u.Entries.Where(p => p.SrcBillId != null && p.SrcBillKey == nameof(StkTransferNotice))
                .Select(p => p.SrcBillId)).Distinct().ToList();
        var transferNoticeList = await Context.Queryable<StkTransferNotice>().Includes(u => u.Entries)
            .Where(u => transferNoticeIds.Contains(u.Id)).ToListAsync();

        // 出库通知单Id
        var outNoticeIds = list.SelectMany(u =>
            u.Entries.Where(p => p.SrcBillId != null && p.SrcBillKey == nameof(StkOutNotice))
                .Select(p => p.SrcBillId)).Distinct().ToList();
        var outNoticeList = await Context.Queryable<StkOutNotice>().Includes(u => u.Entries)
            .Where(u => outNoticeIds.Contains(u.Id)).ToListAsync();

        // 查询创建用户
        var createUsers = await QueryUserList(list.Where(u => u.CreateUserId != null).Select(p => p.CreateUserId.Value).Distinct().ToList());

        return list.SelectMany(u => u.Entries.Select(p => new StkTransfer2PushData
        {
            TransferNotice = transferNoticeList.FirstOrDefault(o => o.Id == p.SrcBillId),
            TransferNoticeEntry = transferNoticeList.SelectMany(o => o.Entries).FirstOrDefault(o => o.EntryId == p.SrcBillEntryId),
            OutNotice = outNoticeList.FirstOrDefault(o => o.Id == p.SrcBillId),
            OutNoticeEntry = outNoticeList.SelectMany(o => o.Entries).FirstOrDefault(o => o.EntryId == p.SrcBillEntryId),
            BillId = u.Id + "",
            BillNo = u.BillNo,
            CreateUser = createUsers.FirstOrDefault(o => o.Id == u.CreateUserId),
            Bill = u,
            Bill_Warehouse = u.Warehouse,
            Entry = p,
            Entry_Material = p.Material,
            Entry_BatchFile = p.BatchFile,
            Entry_SrcWhArea = p.SrcWhArea,
            Entry_SrcWhLoc = p.SrcWhLoc,
            Entry_DestWhArea = p.DestWhArea,
            Entry_DestWhLoc = p.DestWhLoc,
            Entry_DestContainer = p.DestContainer,
            Entry_SrcContainer = p.SrcContainer,
            Entry_Unit = p.Unit,
            Entry_Owner = p.Owner,
        })).ToList();
    }

    public override async Task UpdateFlag(List<StkTransfer2PushData> localObjects, EsSyncPushResult esSyncPushResult,
        bool isByLastPushTime)
    {
        var firstLocalObjects = localObjects[0];

        // 更新推送标记
        await Context.Updateable<StkTransfer>().SetColumns(u =>
                new StkTransfer
                {
                    PushFlag = esSyncPushResult.IsSuccess ? PushFlag.Success : PushFlag.Failure
                })
            .Where(u => u.BillNo == firstLocalObjects.BillNo)
            .ExecuteCommandAsync();
    }
}

/// <summary>
/// 调拨单推送数据
/// </summary>
[SuppressMessage("ReSharper", "InconsistentNaming")]
public class StkTransfer2PushData : EsBasePush
{
    /// <summary>
    /// 调拨通知单
    /// </summary>
    public StkTransferNotice TransferNotice { get; set; }

    /// <summary>
    /// 调拨通知单明细
    /// </summary>
    public StkTransferNoticeEntry TransferNoticeEntry { get; set; }

    /// <summary>
    /// 出库通知单
    /// </summary>
    public StkOutNotice OutNotice { get; set; }

    /// <summary>
    /// 出库通知单明细
    /// </summary>
    public StkOutNoticeEntry OutNoticeEntry { get; set; }
    public StkInNoticeEntry InNoticeEntry { get; set; }

    /// <summary>
    /// 调拨单
    /// </summary>
    public StkTransfer Bill { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    public BdWarehouse Bill_Warehouse { get; set; }

    /// <summary>
    /// 调拨单明细
    /// </summary>
    public StkTransferEntry Entry { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    public BdMaterial Entry_Material { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    public BdBatchFile Entry_BatchFile { get; set; }

    /// <summary>
    /// 调出库区
    /// </summary>
    public BdWhArea Entry_SrcWhArea { get; set; }

    /// <summary>
    /// 调出库位
    /// </summary>
    public BdWhLoc Entry_SrcWhLoc { get; set; }

    /// <summary>
    /// 调出容器
    /// </summary>
    public BdContainer Entry_SrcContainer { get; set; }

    /// <summary>
    /// 调入库区
    /// </summary>
    public BdWhArea Entry_DestWhArea { get; set; }

    /// <summary>
    /// 调入库位
    /// </summary>
    public BdWhLoc Entry_DestWhLoc { get; set; }

    /// <summary>
    /// 调入容器
    /// </summary>
    public BdContainer Entry_DestContainer { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public BdUnit Entry_Unit { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    public BdOwner Entry_Owner { get; set; }

    /// <summary>
    /// 通知单外部编号（通过批次查询收货单明细获取）
    /// </summary>
    public string NoticeEsBillNo { get; set; }

    /// <summary>
    /// 供应商（通过批次查询收货单获取）
    /// </summary>
    public BdSupplier Bill_Supplier { get; set; }


}