﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy.Dto;

namespace Neuz.Application.Pda.InventoryQuery;

public class PdaInventoryQueryShow : PdaShow
{
    //源单列值
    public ExtensionObject SourceCells = new ExtensionObject();
    /// <summary>
    /// 源单分页
    /// </summary>
    public PdaApiPagination SourcePage = new PdaApiPagination();

    /// <summary>
    /// 源单数据
    /// </summary>
    public List<PdaApiVanCell> Sources = new List<PdaApiVanCell>();
}