# ========================================================================
# 【注意】：执行此脚本后，会直接对代码进行【修改】，请确保代码已进行【版本管理】
# ========================================================================
#
# 此脚本用于查找源代码中的中文字符串，并进行替换处理
#
# 脚本默认只查找 _findCnScriptTestFile.cs 文件进行处理
# 可手工调整 main 方法中的 target 参数，改为 "." 即为处理当前目录
#
# ------------------------------------------------------------------------
# 已知问题：
# 1. $"中文{var}中文" 这种存在变量的字符串，脚本会替换，但是需要手动处理变量
# 2. @"中文
#     换行中文" 这种多行的字符串，脚本不会处理
#
# ------------------------------------------------------------------------
# 脚本依赖 chardet 组件，需要执行以下命令进行安装
#
# pip install chardet
# ------------------------------------------------------------------------
# 正则表达式验证：https://regex101.com/
# ------------------------------------------------------------------------

import os
import re
import json
import chardet
import codecs


def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, "rb") as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        has_bom = raw_data.startswith(codecs.BOM_UTF8)

        # 检测换行符类型
        newline_type = "\n"
        if b"\r\n" in raw_data:
            newline_type = "\r\n"
        elif b"\n" in raw_data:
            newline_type = "\n"
        elif b"\r" in raw_data:
            newline_type = "\r"

    # 不使用检测的编码，使用默认的 utf8
    # encoding = result["encoding"]
    encoding = "utf8"

    return encoding, has_bom, newline_type


def is_ignored(path, ignore_patterns):
    """检查路径是否被忽略"""
    for pattern in ignore_patterns:
        if re.match(pattern, path):
            return True
    return False


def find_and_replace_chinese_strings_1(line):
    """查找类似 "测试3" 或者前面有$@且没有L.Text[ 的字符串"""
    matches = re.finditer(
        r"(?<!L\.Text\[)(?<!ErrorCodeItemMetadata\()\$?@?\"(\s*?[\u4e00-\u9fff]+.*?)\"",
        line,
    )
    chinese_strings = {}
    for match in matches:
        match1 = match.group(0)
        match2 = match.group(1)
        chinese_strings[match2] = match2
        print(f"find_and_replace_chinese_strings_1 found: {match1}")
        # "测试3" 替换为 L.Text["测试3"]
        line = line.replace(match1, f'L.Text["{match2}"]')
    return line, chinese_strings


def find_and_replace_chinese_strings_final(line):
    """查找类似 L.Text["操作成功"] 的字符串"""
    matches = re.finditer(r"L\.Text\[\"(.+?)\"", line)
    chinese_strings = {}
    for match in matches:
        match1 = match.group(0)
        match2 = match.group(1)

        chinese_strings[match2] = match2
        print(f"find_and_replace_chinese_strings_final found: {match1}")

        # 不做处理
    return line, chinese_strings


def find_and_replace_chinese_strings_final2(line):
    """查找类似 [ErrorCodeItemMetadata("账号不存在")] 的字符串"""
    matches = re.finditer(r"\[ErrorCodeItemMetadata\(\"(.+?)\"", line)
    chinese_strings = {}
    for match in matches:
        match1 = match.group(0)
        match2 = match.group(1)

        chinese_strings[match2] = match2
        print(f"find_and_replace_chinese_strings_final2 found: {match1}")

        # 不做处理
    return line, chinese_strings


def replace_chinese_strings_in_code(
    code, filename, file_extension, newline_type, ignore_replace_patterns
):
    """查找并替换代码中的中文字符串为 $t('中文字符串')"""

    # 定义注释的正则表达式模式
    comment_patterns = [
        (r"(?<!https:)(?<!http:)(//.+)", re.IGNORECASE),  # 单行注释
        (r"(/\*.*?\*/)", re.DOTALL),  # 多行注释
        (r"(#.+)", re.IGNORECASE),  # region
        (r"(///.+)", re.IGNORECASE),  # 三斜杠注释
        (
            r"^\s*(?<!L\.Text)\[(?!(ErrorCodeItemMetadata|L\.Text)\b).+[^\"]\]",
            re.MULTILINE,
        ),  # 将 C#特性 当作注释，不进行中文提取，除非是 ErrorCodeItemMetadata 特性
        (
            r"Debug\.Write.*\(.+?\)",
            re.MULTILINE,
        ),  # 将 Debug.Write 当作注释，不进行中文提取
    ]

    # 提取注释并替换为占位符
    comments = []
    for pattern, flag in comment_patterns:
        matches = list(re.finditer(pattern, code, flag))
        for match in matches:
            comment = match.group(0)
            placeholder = f"__COMMENT_{len(comments)}__"
            code = code.replace(comment, placeholder)
            comments.append((placeholder, comment))

    chinese_strings = {}
    lines = code.split("\n")
    updated_lines = []

    for i, line in enumerate(lines, start=1):

        # 如果不是忽略替换的文件
        if not is_ignored(filename, ignore_replace_patterns):
            # 查找并替换中文字符串
            line, chinese_strings_1 = find_and_replace_chinese_strings_1(line)

            # 合并结果
            chinese_strings.update(chinese_strings_1)

        # 重新查找需要多语言的字符串
        line, chinese_strings_final = find_and_replace_chinese_strings_final(line)
        line, chinese_strings_final2 = find_and_replace_chinese_strings_final2(line)

        # 合并结果
        chinese_strings.update(chinese_strings_final)
        chinese_strings.update(chinese_strings_final2)

        updated_lines.append(line)

    updated_code = newline_type.join(updated_lines)

    # 将注释放回
    for placeholder, comment in reversed(comments):
        updated_code = updated_code.replace(
            placeholder, comment.replace("\n", newline_type)
        )

    return chinese_strings, updated_code


def process_file(filename, ignore_patterns, ignore_replace_patterns):
    """处理单个文件"""
    if is_ignored(filename, ignore_patterns):
        return {}, ""

    # 获取文件扩展名
    _, file_extension = os.path.splitext(filename)
    if file_extension not in {".cs"}:
        return {}, ""

    # 检测文件编码及是否有BOM
    encoding, has_bom, newline_type = detect_encoding(filename)

    with open(filename, "r", encoding=encoding, errors="replace") as file:
        code = file.read()

    file_extension = os.path.splitext(filename)[1]
    chinese_strings, updated_code = replace_chinese_strings_in_code(
        code, filename, file_extension, newline_type, ignore_replace_patterns
    )

    # 写回文件时保留原始编码和BOM
    with open(filename, "wb", newline=None) as file_handle:
        if has_bom and encoding.lower().startswith("utf-8"):
            file_handle.write(codecs.BOM_UTF8)
        file_handle.write(updated_code.encode(encoding))

    return chinese_strings, updated_code


def process_directory(directory, ignore_patterns, ignore_replace_patterns):
    """处理指定目录下的所有文件"""
    all_chinese_strings = {}
    for root, dirs, files in os.walk(directory):
        # 排除被忽略的目录
        dirs[:] = [
            d for d in dirs if not is_ignored(os.path.join(root, d), ignore_patterns)
        ]

        # 排除被忽略的目录
        for file in files:
            filename = os.path.join(root, file)
            if not is_ignored(filename, ignore_patterns):
                chinese_strings, updated_code = process_file(
                    filename, ignore_patterns, ignore_replace_patterns
                )
                if chinese_strings:
                    all_chinese_strings.update(chinese_strings)
    return all_chinese_strings


def read_existing_json(file_path):
    """读取现有的 json 文件并返回 JSON 内容"""

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    if os.path.exists(file_path):
        with open(file_path, "r", encoding="utf-8") as file:
            return json.load(file)
    else:
        # 如果文件不存在，创建一个新的文件并初始化为空对象
        with open(file_path, "w", encoding="utf-8") as file:
            file.write("export default {}")
        return {}

    return {}


def write_to_json(file_path, data):
    """将 JSON 内容写入现有的 json 文件"""
    with open(file_path, "w", encoding="utf-8") as file:
        file.write(json.dumps(data, ensure_ascii=False, indent=2))
        file.write("\n")


def read_and_write_json_file(file_path, all_chinese_strings, isWriteEmptyString=False):
    """读取并写入 json 文件"""

    # 读取现有文件（如果存在）
    existing_zh_cn = read_existing_json(file_path)

    # 更新现有的 JSON 字典
    for key in all_chinese_strings:
        if key not in existing_zh_cn:
            existing_zh_cn[key] = "" if isWriteEmptyString else all_chinese_strings[key]

    # 写回文件
    write_to_json(file_path, existing_zh_cn)


def main(target, ignore_patterns=None, ignore_replace_patterns=None):
    if ignore_patterns is None:
        ignore_patterns = []

    all_chinese_strings = {}

    if os.path.isfile(target):
        # 如果目标是文件，则处理单个文件
        chinese_strings, updated_code = process_file(
            target, ignore_patterns, ignore_replace_patterns
        )
        if chinese_strings:
            all_chinese_strings.update(chinese_strings)
    elif os.path.isdir(target):
        # 如果目标是目录，则处理目录下的所有文件
        all_chinese_strings = process_directory(
            target, ignore_patterns, ignore_replace_patterns
        )
    else:
        print(f"Error: {target} is neither a file nor a directory.")
        return

    # zh-cn.json 文件
    read_and_write_json_file(
        "Admin.NET.Web.Entry/Resources/Lang.zh-CN.json", all_chinese_strings, False
    )
    # en.json 文件
    read_and_write_json_file(
        "Admin.NET.Web.Entry/Resources/Lang.en.json", all_chinese_strings, True
    )

    print("Processing complete.")


if __name__ == "__main__":
    # 指定需要处理的文件或目录，处理当前目录则改为 "."
    # target = "_findCnScriptTestFile.cs"  # 修改这里指定文件或目录
    target = "."  # 修改这里指定文件或目录

    ignore_patterns = [
        r".*\\bin\\*",
        r".*\\obj\\*",
        r".*\.vscode.*",
        r".*SeedData.*",
        # Neuz 文件
        r".*Neuz.UnitTest.*",
        r".*Neuz.License.WinForm.*",
        r".*Admin.NET.Plugin.ApprovalFlow.*",
        r".*Admin.NET.Plugin.DingTalk.*",
        r".*Admin.NET.Plugin.Elsa.*",
        r".*Admin.NET.Plugin.GoView.*",
        r".*Admin.NET.Plugin.ReZero.*",
        r".*Service\\APIJSON.*",
    ]  # 排除某些文件或文件夹

    ignore_replace_patterns = [
        r".*Neuz.Application\\Service\\Sys\\SysCodeRule\\SysCodeRuleService.cs",
        r".*Neuz.Application\\Service\\Sys\\SysModelBar\\SysModelBarService.cs",
        r".*Neuz.Application\\Service\\Sys\\SysReportTemplate\\SysReportTemplateService.Report.cs",
        r".*Neuz.Application\\Service\\Pda\\StockCount\\Cloud\\Bill\\PdaCloudStockCountBillModel.cs",
        r".*Admin.NET.Core\\Job.*",
        r".*Admin.NET.Core\\Logging.*",
        r".*Admin.NET.Core\\Service\\CodeGen.*",
        r".*Admin.NET.Core\\SignalR\\SignalRSetup.cs",
        r".*Admin.NET.Core\\Service\\Tenant\\SysTenantService.cs",
        r".*Admin.NET.Core\\Service\\OpenAccess\\SysOpenAccessService.cs",
        r".*Admin.NET.Core\\SignatureAuth\\SignatureAuthenticationEvent.cs",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\K3CloudClient\\K3CloudClient.Save.cs",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\K3CloudClient\\K3CloudClient.UnitConvert.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise0_28BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise0_5BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise1007105_28BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise1007105_5BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise21_21RedBillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise28_28RedBillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise72_5BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise81_21BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise82_21RedBillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise83_21BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Model\\Bill\\Wise\\PdaWise85_24BillModel.cs",
        r".*Neuz.Application\\Service\\Pda\\Bill\\Interface\\Bill\\K3Cloud\\PdaCloudBillModelBase.cs",
        r".*Neuz.Application\\Service\\Erp\\Barcode\\K3Cloud.*",
        r".*Neuz.Application\\Service\\Erp\\Barcode\\K3Wise.*",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\K3CloudClient\\Query\\StkInventoryQuery.cs",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\ApiClient\\CommitResult.cs",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\ApiClient\\K3CloudWebApiClient.cs",
        r".*Neuz.Application\\Service\\Adapter\\K3Cloud\\ApiClient\\WebApiClientBase.cs",
        r".*Neuz.Application\\ExternalSystem\\BaseEsSyncPullService.cs",
        r".*Neuz.Application\\ExternalSystem\\BaseEsSyncPushService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkAdjustment\\StkAdjustmentService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkAllocatePolicy\\StkAllocatePolicyService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkBatchAdjust\\StkBatchAdjustService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkInStock\\StkInStockService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkInventory\\StkInventoryService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkOutStock\\StkOutStockService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkReceive\\StkReceiveService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkStockCount\\StkStockCountService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkTask\\StkTaskService.cs",
        r".*Neuz.Application\\Service\\Stk\\StkTransfer\\StkTransferService.cs",
    ]  # 排除查找替换的某些文件或文件夹

    # 如果 target 不是 _findCnScriptTestFile.cs，则添加到排除列表
    if target != "_findCnScriptTestFile.cs":
        ignore_patterns.append(r".*\\_findCnScriptTestFile.cs")

    main(target, ignore_patterns, ignore_replace_patterns)
