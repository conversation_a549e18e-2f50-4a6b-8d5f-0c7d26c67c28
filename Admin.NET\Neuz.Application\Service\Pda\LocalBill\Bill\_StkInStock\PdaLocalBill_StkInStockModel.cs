﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.LocalBill.LocalBillDto;

namespace Neuz.Application.Pda.LocalBill.Bill._StkInStock;

/// <summary>
/// 无源入库单
/// </summary>
public class PdaLocalBill_StkInStockModel : PdaLocalBillModel<StkTask, StkInStock, StkTaskEntry, StkInStockEntry>
{
    public PdaLocalBill_StkInStockModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "_StkInStock";

    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destBillTypeName",
                Caption = L.Text["单据类型"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdBillTypeModel",
                    LookupDataKey = "ScanHead.DestBillType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestBillType", "Number"),
                        new PdaLookupMapping("DestBillTypeName", "Name"),
                    },
                    Properties = { ["EntityName"] = "StkInStock" }
                },
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "supplierName",
                Caption = L.Text["供应商"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdSupplierModel",
                    LookupDataKey = "ScanHead.Supplier",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("SupplierId", "Id"),
                        new PdaLookupMapping("SupplierNumber", "Number"),
                        new PdaLookupMapping("SupplierName", "Name"),
                    }
                }
            }
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "",
            SourceTitle = "",
            DestKey = "StkInStock",
            DestTitle = L.Text["入库单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceItem = true,
            IsOverSourceQty = true,
            FirstStockType = FirstStockType.Select
        }
    };

    /// <inheritdoc/>
    protected override Task<string> GetBillType(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        return Task.FromResult(pdaData.ScanHead.DestBillType);
    }

    /// <inheritdoc/>
    public override Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        return Task.FromResult<dynamic>(null);
    }
}