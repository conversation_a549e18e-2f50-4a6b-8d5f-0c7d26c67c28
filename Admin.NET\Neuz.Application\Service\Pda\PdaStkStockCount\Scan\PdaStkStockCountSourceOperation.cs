﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.PdaStkStockCount.Scan;

/// <summary>
/// 盘点带源单
/// </summary>
public class PdaStkStockCountSourceOperation : PdaLocalBillScanBarcodeOperationBase
{
    public PdaStkStockCountSourceOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var rep = App.GetService<SqlSugarRepository<StkStockCount>>(_serviceProvider);
        var stkStockCount = rep.GetFirst(r => r.BillNo == args.BarcodeString && r.StockCountStatus == StkStockCountStatus.GeneratedDetail);
        if (stkStockCount == null) return;
        var dataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var pdaModel = dataCacheService.GetPdaModel(args.Key);
        var stkStockCountModel = pdaModel as PdaStkStockCountModel;
        if (stkStockCountModel == null) return;
        stkStockCountModel.SelectLookupDataSourceInfo(args.TranId, stkStockCount.Id + "").GetAwaiter().GetResult();
        var billData = dataCacheService.GetBillData(args.Key, args.TranId) as PdaStkStockCountData;
        if (billData == null) return;
        // 如果没有盘点单据
        if (billData.StkStockCount == null) return;
        args.IsResult = true;
    }
}