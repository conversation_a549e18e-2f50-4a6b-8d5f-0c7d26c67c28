﻿namespace Neuz.Core.Entity;

/// <summary>
/// 单据基类实体，包含外部系统关联字段
/// </summary>
public abstract class EsBillEntityBase : BillEntityBase
{
    /// <summary>
    /// 外部系统单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统单据编号", Length = 80)]
    public string? EsBillNo { get; set; }

    /// <summary>
    /// 外部系统主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统主键Id", Length = 50)]
    public string? EsId { get; set; }
}