﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill;

public class PdaLocalBillScanBdContainerOperation : PdaScanBarcodeOperationBase
{

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var dataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
        var billData = (PdaLocalBillData)dataCacheService.GetBillData(args.Key, args.TranId);
        var billModel = (IPdaLocalBillModel)dataCacheService.GetPdaModel(billData.ModelKey);

        //查询箱带条码
        var containerService = App.GetService<BdContainerService>(ServiceProvider);
        var output = containerService.QueryContainerBarcode(args.BarcodeString, false).Result;
        if (output.Barcodes.Count == 0) return;

        output.Barcodes.ForEach(r =>
        {
            if (billData.BarcodeList.Exists(b => b.Barcode.Id == r.Id))
                throw Oops.Bah(PdaErrorCode.Pda1015, r.Barcode);
            
            // 检查条码状态
            var pdaLocalBillCheckBarcodeStatusService = App.GetService<PdaLocalBillCheckStatusService>(ServiceProvider);
            pdaLocalBillCheckBarcodeStatusService.CheckBarcodeStatus(args.TranId, args, r);
        });
        billModel.ScanBarcodes(args.TranId, output.Barcodes, output.Container);
        args.IsResult = true;
    }

    public PdaLocalBillScanBdContainerOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}