﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Service.Contract;

namespace Neuz.Application.Pda.LocalBill;

public class PdaLocalBillData : IPdaData
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public PdaLocalBillData()
    {
        CreateDateTime = DateTime.Now;
    }

    public string ModelKey { get; set; }

    /// <summary>
    /// 数据Id
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// PDA显示数据
    /// </summary>
    public PdaShow DataShow { get; set; } = new PdaLocalBillShow();

    public bool IsEmptyData()
    {
        return ScanDetails.Count <= 0;
    }

    /// <summary>
    /// 数据模型Key
    /// </summary>
    public string BillModelKey { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CreateDateTime { get; set; }

    ///// <summary>
    ///// BillModel
    ///// </summary>
    //[JsonConverter(typeof(PdaBillDataConverter))]
    //public IPdaBillModel BillModel { get; set; }

    /// <summary>
    /// 仓库信息
    /// </summary>
    public PdaLocalBillStockInfo StockInfo { get; set; } = new PdaLocalBillStockInfo();

    /// <summary>
    /// 源单头
    /// </summary>
    public List<PdaLocalBillSourceHead> SourceHeads { get; } = new List<PdaLocalBillSourceHead>();

    /// <summary>
    /// 源单明细
    /// </summary>
    public List<PdaLocalBillSourceDetail> SourceDetails { get; } = new List<PdaLocalBillSourceDetail>();

    /// <summary>
    /// 扫描表头（目标单表头）
    /// </summary>
    public PdaLocalBillScanHead ScanHead { get; set; } = new PdaLocalBillScanHead();

    /// <summary>
    /// 扫描表体（目标单表体）
    /// </summary>
    public List<PdaLocalBillScanDetail> ScanDetails { get; } = new List<PdaLocalBillScanDetail>();

    public List<PdaLocalBillBarcode> BarcodeList { get; } = new List<PdaLocalBillBarcode>();

    public Dictionary<string, List<PdaLocalBillIncludeBarcode>> DetailIncludeBarcodes { get; } = new Dictionary<string, List<PdaLocalBillIncludeBarcode>>();

    /// <summary>
    /// 扩展属性
    /// CurrentCreateBillDetailId - 当前新增明细Id (PDA点新增的时候写入,方便PDA判断新增明细Id)
    /// </summary>
    public Dictionary<string, object> ExtendArgs { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// 是否隐藏扫描完成的列
    /// </summary>
    public bool IsHideFinishScanQty { get; set; }
}

public class PdaLocalBillDataUserConfig : IModelUserConfig
{
    public bool IsHideFinishScanQty { get; set; }
}