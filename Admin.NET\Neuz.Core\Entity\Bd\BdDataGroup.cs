﻿namespace Neuz.Core.Entity;

/// <summary>
/// 数据分组
/// </summary>
[SugarTable(null, "数据分组")]
[SugarIndex("index_{table}_G", nameof(GroupKey), OrderByType.Asc)]
public class BdDataGroup : EsBdEntityBase
{
    /// <summary>
    /// 所属分组Key
    /// </summary>
    /// <remarks>
    /// 实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "所属分组Key")]
    public string GroupKey { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    /// <summary>
    /// 分组子项
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<BdDataGroup> Children { get; set; }
}