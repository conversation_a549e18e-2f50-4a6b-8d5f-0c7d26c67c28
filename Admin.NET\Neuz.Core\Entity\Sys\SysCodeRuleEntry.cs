﻿namespace Neuz.Core.Entity;

/// <summary>
/// 编码规则明细
/// </summary>
[SugarTable(null, "编码规则明细")]
public class SysCodeRuleEntry : EntryEntityBase
{
    /// <summary>
    /// 类型
    /// </summary>
    [SugarColumn(ColumnDescription = "类型")]
    public ElementType ElementType { get; set; }

    /// <summary>
    /// 元素来源名称
    /// </summary>
    [SugarColumn(ColumnDescription = "元素来源名称", Length = 50)]
    public string? ElementName { get; set; }

    /// <summary>
    /// 元素来源属性
    /// </summary>
    [SugarColumn(ColumnDescription = "元素来源属性", Length = 100)]
    public string? ElementProperty { get; set; }

    /// <summary>
    /// 长度
    /// </summary>
    [SugarColumn(ColumnDescription = "长度")]
    public int Length { get; set; }

    /// <summary>
    /// 格式化
    /// </summary>
    [SugarColumn(ColumnDescription = "格式化", Length = 50)]
    public string? Format { get; set; }

    /// <summary>
    /// 常量值
    /// </summary>
    [SugarColumn(ColumnDescription = "常量值", Length = 50)]
    public string? ConstValue { get; set; }

    /// <summary>
    /// 起始值
    /// </summary>
    [SugarColumn(ColumnDescription = "起始值")]
    public int Seed { get; set; }

    /// <summary>
    /// 步长
    /// </summary>
    [SugarColumn(ColumnDescription = "步长")]
    public int Increment { get; set; }

    /// <summary>
    /// 替代符
    /// </summary>
    [SugarColumn(ColumnDescription = "替代符", Length = 10)]
    public string? ReChar { get; set; }

    /// <summary>
    /// 补位符
    /// </summary>
    [SugarColumn(ColumnDescription = "补位符", Length = 10)]
    public string? AddChar { get; set; }

    /// <summary>
    /// 右侧截断（True右侧截断 False不截断）
    /// </summary>
    [SugarColumn(ColumnDescription = "右侧截断（True右侧截断 False不截断）")]
    public bool CutStyle { get; set; }

    /// <summary>
    /// 右侧填充（True右侧填充 False左侧填充）
    /// </summary>
    [SugarColumn(ColumnDescription = "右侧填充（True右侧填充 False左侧填充）")]
    public bool AddStyle { get; set; }

    /// <summary>
    /// 分组依据
    /// </summary>
    [SugarColumn(ColumnDescription = "分组依据")]
    public bool CodeOnlyBy { get; set; }

    /// <summary>
    /// 参与编码
    /// </summary>
    [SugarColumn(ColumnDescription = "参与编码")]
    public bool CodeElement { get; set; }
}