﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 仓储默认导入模型
/// </summary>
public class StkDefaultImportModel : DefaultImportModel
{
    protected override List<ImportField> GetNavColImportFields(EntityMaintenance entityMaintenance, EntityInfo entityInfo, EntityColumnInfo navCol, string parentPropName,
        string parentPropDescription, ushort level)
    {
        if (navCol.UnderType == typeof(BdBarcode))
        {
            return new List<ImportField>
            {
                GenNavColImportField(entityMaintenance, entityInfo, navCol, nameof(BdBarcode.Barcode), parentPropName, parentPropDescription, true),
            };
        }

        return base.GetNavColImportFields(entityMaintenance, entityInfo, navCol, parentPropName, parentPropDescription, level);
    }
}