﻿namespace Neuz.Application;

/// <summary>
/// 库存任务任务处理输入参数
/// </summary>
public class StkTaskTaskHandleInput
{
    /// <summary>
    /// 任务Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 领料人Id
    /// </summary>
    public long? EmployeeId { get; set; }

    /// <summary>
    /// 任务处理明细
    /// </summary>
    public List<StkTaskTaskHandleEntry> Entries { get; set; }
}

/// <summary>
/// 库存任务任务处理明细
/// </summary>
public class StkTaskTaskHandleEntry
{
    /// <summary>
    /// 任务明细Id
    /// </summary>
    public long EntryId { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 来源库区Id
    /// </summary>
    public long? SrcWhAreaId { get; set; }

    /// <summary>
    /// 来源库位Id
    /// </summary>
    public long? SrcWhLocId { get; set; }

    /// <summary>
    /// 来源容器Id
    /// </summary>
    public long? SrcContainerId { get; set; }

    /// <summary>
    /// 目标库区Id
    /// </summary>
    public long DestWhAreaId { get; set; }

    /// <summary>
    /// 目标库位Id
    /// </summary>
    public long DestWhLocId { get; set; }

    /// <summary>
    /// 目标容器Id
    /// </summary>
    public long? DestContainerId { get; set; }

    /// <summary>
    /// 处理数量
    /// </summary>
    public decimal HandleQty { get; set; }
}