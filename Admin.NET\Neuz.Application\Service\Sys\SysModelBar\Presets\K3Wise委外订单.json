{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "t2.FBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t6.<PERSON><PERSON><PERSON><PERSON>", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t6.<PERSON><PERSON><PERSON>", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t2.FDate", "title": "单据日期", "inputCtrl": "DateRange", "op": "Between"}], "billListColumns": [{"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "日期", "sortable": true, "format": "Date"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "FDepartmentNumber", "title": "部门编码"}, {"fieldName": "FDepartmentName", "title": "部门名称"}, {"fieldName": "<PERSON><PERSON><PERSON>", "title": "已制作数量"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillDate", "title": "单据日期", "sortable": true, "format": "Date"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Wise", "idFieldName": "t1.FInterID", "entryIdFieldName": "t1.FEntryID", "content": "SELECT\r\n    t1.FInterID AS _id,\r\n    t1.FEntryID AS _entryId,\r\n    '1007105' AS SourceBillKey,\r\n    t1.FInterID AS SourceBillId,\r\n    t1.FEntryID AS SourceBillEntryId,\r\n    t2.FBillNo AS SourceBillNo,\r\n    t2.FDate AS SourceBillDate,\r\n    t1.FItemID AS MaterialId,\r\n    t6.FNumber AS MaterialNumber,\r\n    t6.FName AS MaterialName,\r\n    t6.FShortNumber AS MaterialShortNumber,\r\n    t6.FModel AS MaterialSpec,\r\n    t1.FAuxQty AS Qty,\r\n    t1.FUnitID AS UnitId,\r\n    t7.FNumber AS UnitNumber,\r\n    t7.FName AS UnitName,\r\n    t6.FBatchManager AS IsBatchManage,\r\n    t6.FIsSnManage AS IsSnManage,\r\n    t6.FISKFPeriod AS IsKfPeriod,\r\n    t6.FKFPeriod AS ExpPeriod,\r\n    t5.FNumber AS FDepartmentNumber,\r\n    t5.FName AS FDepartmentName\r\n    -- t2.FCheckDate,\r\n    -- t2.FBillerID,\r\n    -- t2.F<PERSON>tat<PERSON>,\r\n    -- t4.FName AS FBillerName,\r\n    -- t6.FErpClsID AS FItemErpClsID,\r\n    -- t6.FUnitGroupID,\r\n    -- t7.FCoefficient AS FUnitCoefficient\r\nFROM\r\n    ICSubContractEntry t1\r\n    LEFT JOIN ICSubContract t2 ON (t2.FInterID = t1.FInterID)\r\n    LEFT JOIN t_User t4 ON (t4.FUserID = t2.FBillerID)\r\n    LEFT JOIN t_Department t5 ON (t5.FItemID = t2.FDepartment)\r\n    LEFT JOIN t_ICItem t6 ON (t6.FItemID = t1.FItemID)\r\n    LEFT JOIN t_MeasureUnit t7 ON (t7.FMeasureUnitID = t1.FUnitID)\r\nWHERE\r\n    (t2.FStatus = 1)"}}