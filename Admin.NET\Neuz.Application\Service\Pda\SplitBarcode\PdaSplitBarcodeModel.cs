﻿using Furion.Localization;
using Neuz.Application.Pda.Barcode;
using Neuz.Application.Pda.Barcode.Dto;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Application.Service.Pda.SplitBarcode.Dto;
using SqlSugar;
using Stimulsoft.Report;
using Stimulsoft.Report.Export;

namespace Neuz.Application.Service.Pda.SplitBarcode;

public class PdaSplitBarcodeModel : PdaModelBillBase<PdaSplitBarcodeShow, PdaSplitBarcodeData>
{
    public PdaSplitBarcodeModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = nameof(PdaSplitBarcodeModel);

    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    public override void Initialization()
    {
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.BarcodeCells = pdaData.BarcodeCells;
        pdaShow.TemplateCells = pdaData.TemplateCells;
        pdaShow.TemplateValues = pdaData.TemplateValues;
        pdaShow.IsSubmit = pdaData.IsSubmit;
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
        ((PdaSplitBarcodeData)pdaData).UserConfig = GetUserConfig<PdaSplitBarcodeModelUserConfig>().GetAwaiter().GetResult() ?? new PdaSplitBarcodeModelUserConfig();
    }

    /// <summary>
    /// 已拆分,重新打印
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="submitData"></param>
    /// <returns></returns>
    private async Task<object> SubmitReturnDataPrint(long tranId, object submitData)
    {
        var pdaData = GetPdaData(tranId);
        var service = App.GetService<SysReportTemplateService>(ServiceProvider);
        if (string.IsNullOrEmpty(pdaData.CurTemplateId)) throw Oops.Bah(L.Text["请先选择模板"]);

        // 这里打印有三种方式,
        // 1. 蓝牙打印 
        // 2. 服务器端打印
        // 3. 打印服务器打印
        var printInfo = JsonConvert.DeserializeObject<PdaPrintInfo>(submitData + "");

        var ids = pdaData.SplitBarcodes.Where(r => printInfo.PrintTemplates.Contains(r.Key)).Select(r => r.Barcode.Id + "").ToList();
        if (ids.Count <= 0) throw Oops.Bah(L.Text["没有需要打印的条码"]);
        var report = service.GetReport(Convert.ToInt64(pdaData.CurTemplateId), typeof(BarBarcodeService).AssemblyQualifiedName, ids).Result;

        switch (printInfo.PrintType)
        {
            case PdaPrintType.Ble:
                return await PrintBle(tranId, report, printInfo);
            case PdaPrintType.Server:
                await PrintService(tranId, report, printInfo);
                break;
            case PdaPrintType.PrintService:
                break;
            default:
                throw new NotImplementedException();
        }

        return L.Text["打印成功"];
    }

    private async Task SelectFieldDataTemplate(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        pdaData.CurTemplateId = fieldValue;
        pdaData.TemplateValues["template"] = fieldValue;

        // 选择后,保存当前功能默认模板
        string funcKey = $"bar_{pdaData.CurBarcode.FuncKey}~barcode";
        var config = pdaData.UserConfig.TemplateInfos.FirstOrDefault(r => r.FuncKey == funcKey);
        if (config == null)
        {
            pdaData.UserConfig.TemplateInfos.Add(new PdaSplitTemplateInfo
            {
                FuncKey = funcKey,
                CurTemplateId = fieldValue
            });
            await SetUserConfig(pdaData.UserConfig);
        }
        else
        {
            if (config.CurTemplateId == fieldValue) return;
            config.CurTemplateId = fieldValue;
            await SetUserConfig(pdaData.UserConfig);
        }
    }

    private async Task SubmitSubmit(long tranId, object jsonStr)
    {
        var pdaData = GetPdaData(tranId);
        if (pdaData.IsSubmit) return;
        var info = JsonConvert.DeserializeObject<PdaSplitBarcodeSubmitInfo>(jsonStr + "");
        if (info.SplitQtys.Count <= 0) throw Oops.Bah(L.Text["请确认拆分数量"]);
        if (pdaData.CurBarcode == null) throw Oops.Bah(L.Text["请先扫描需要拆标的条码"]);
        if (info.SplitQtys.Any(r => r < 0)) throw Oops.Bah(L.Text["拆分数量有负数数量"]);
        if (info.SplitQtys.Sum() > pdaData.CurBarcode.Qty) throw Oops.Bah(L.Text["拆分数量不能大于原条码数量"]);
        if (info.PrintTemplates != null && info.PrintTemplates.Count > 0)
        {
            if (string.IsNullOrEmpty(pdaData.CurTemplateId)) throw Oops.Bah(L.Text["请先选择打印模板"]);
        }

        //判断条码是否已装箱,如果装箱,不允许拆条码
        if (pdaData.CurBarcode.ParentPackageId != null && pdaData.CurBarcode.ParentPackageId != 0) throw Oops.Bah(L.Text["已装箱条码不能拆条码"]);
        // 这里用a来区别是原来的条码,其它的为拆分的条码
        List<(string, decimal)> splitQtys = new List<(string, decimal)>
        {
            new("a", pdaData.CurBarcode.Qty - info.SplitQtys.Sum()),
        };
        foreach (decimal splitQty in info.SplitQtys)
        {
            splitQtys.Add(new("b", splitQty));
        }

        string startBar = $"{pdaData.CurBarcode.Barcode}-";

        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

        foreach (var splitQty in splitQtys)
        {
            // 如果数量为0,不生成条码
            if (splitQty.Item2 <= 0) continue;

            // 查询加锁,防止数据被更新
            var barcodes = await Rep.Change<BarBarcode>().AsQueryable().TranLock(DbLockType.Wait)
                .Where(r => r.Barcode.StartsWith(startBar))
                //.Where(r => SqlFunc.Substring(r.Barcode, startBar.Length, r.Barcode.Length - startBar.Length).Contains("-"))
                .ToListAsync();
            barcodes = barcodes.Where(r => !r.Barcode.Substring(startBar.Length, r.Barcode.Length - startBar.Length).Contains("-")).ToList();
            var maxNum = 0;
            if (barcodes.Count > 0)
            {
                maxNum = barcodes.Select(r =>
                {
                    var ss = r.Barcode.Split("-");
                    var numStr = ss[^1];
                    int num = 0;
                    int.TryParse(numStr, out num);
                    return num;
                }).Max();
            }

            string newBarcodeStr = $"{pdaData.CurBarcode.Barcode}-{(maxNum + 1)}";
            var newBarcode = pdaData.CurBarcode.Adapt<BarBarcode>();
            //更新字段
            newBarcode.Barcode = newBarcodeStr;
            newBarcode.Id = YitIdHelper.NextId();
            newBarcode.CreateTime = DateTime.Now;
            newBarcode.CreateUserId = CurUserId;
            newBarcode.CreateUserName = CurUserName;
            newBarcode.UpdateTime = DateTime.Now;
            newBarcode.UpdateUserId = CurUserId;
            newBarcode.UpdateUserName = CurUserName;
            //更新数量   
            newBarcode.Qty = splitQty.Item2;
            pdaData.SplitBarcodes.Add(new PdaSplitBarcode() { Key = splitQty.Item1, Barcode = newBarcode });
            await Rep.Change<BarBarcode>().InsertAsync(newBarcode);
        }

        uow.Commit();
        pdaData.IsSubmit = true;
    }

    private async Task<object> PrintBle(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        await report.RenderAsync();
        List<string> base64Strings = new();
        StiPngExportService stiPngExportService = new();
        for (int i = 0; i < report.RenderedPages.Count; i++)
        {
            var ms = new MemoryStream();
            //report.ExportDocument(StiExportFormat.ImagePng, ms, new StiEmfExportSettings() { PageRange = new StiPagesRange(i + 1) });
            StiImageExportSettings settings = new StiPngExportSettings
            {
                //加了下面这个就会报错,应该是用了默认格式保存
                //settings.ImageType = StiImageType.Png;
                PageRange = new StiPagesRange(i + 1),
                ImageResolution = printInfo.Dpi ?? 0
            };
            stiPngExportService.ExportImage(report, ms, settings);
            ms.Seek(0, SeekOrigin.Begin);

            //服务器端处理返回的图像指令
            var resultBytes = PdaHelper.GetBitmapData(ms.ToArray());
            var base64String = Convert.ToBase64String(resultBytes);
            base64Strings.Add(base64String);
        }

        return base64Strings;
    }

    private Task PrintService(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        //打印
        var printService = App.GetService<PdaServerPrintService>(ServiceProvider);
        printService.Prints(new List<PdaReportPrintInfo>()
        {
            new()
            {
                Report = report,
                PrintName = printInfo.PrintName,
                Count = 1
            }
        });

        return Task.CompletedTask;
    }

    #region 继承的方法

    public override async Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        var bar = await Rep.Change<BarBarcode>().AsQueryable().FirstAsync(r => r.Barcode.Contains(barcode));
        if (bar == null) throw Oops.Bah(L.Text["没有找到条码[{0}]", barcode]);
        if (bar.ParentPackageId != null && bar.ParentPackageId != 0) throw Oops.Bah(L.Text["已装箱条码不能拆条码"]);

        var pdaSchema = (PdaSchema)BillSchema;
        var pdaData = GetPdaData(tranId);
        foreach (var cell in pdaSchema.BarcodeCells)
        {
            var propertyInfo = bar.GetType().GetProperty(cell.Fieldname, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance);
            if (propertyInfo == null) throw Oops.Bah(L.Text["条码不存在字段[{0}]", cell.Fieldname]);
            var value = propertyInfo.GetValue(bar);
            pdaData.BarcodeCells[cell.Fieldname] = value + "";
        }

        pdaData.CurBarcode = bar;
        //处理模板
        string funcKey = $"bar_{pdaData.CurBarcode.FuncKey}~barcode";
        var templates = await Rep.Change<SysReportTemplate>().AsQueryable().Where(r => r.FuncKey == funcKey).OrderBy(r => r.Number).ToListAsync();
        pdaData.Templates = templates;
        var templateObj = pdaData.TemplateCells.First(r => r.Fieldname == "template");
        templateObj.Options.Clear();
        pdaData.TemplateValues["template"] = "";
        pdaData.CurTemplateId = "";
        pdaData.Templates.ForEach(r => { templateObj.Options.Add(new PdaColumnOption(r.Id + "", r.Name)); });

        // 处理最后一次打印选择的模板
        if (pdaData.UserConfig != null && pdaData.UserConfig.TemplateInfos.Count > 0)
        {
            var info = pdaData.UserConfig.TemplateInfos.FirstOrDefault(r => r.FuncKey == funcKey);
            if (info != null)
            {
                if (pdaData.Templates.Any(r => (r.Id + "") == info.CurTemplateId))
                {
                    pdaData.TemplateValues["template"] = info.CurTemplateId;
                    pdaData.CurTemplateId = info.CurTemplateId;
                }
            }
        }

        pdaData.IsSubmit = false;
        pdaData.SplitBarcodes.Clear();
        RefreshShow(tranId);
    }

    public override Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        throw new NotImplementedException();
    }

    public override Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        switch (fieldKey)
        {
            case "template.template":
                await SelectFieldDataTemplate(tranId, fieldValue);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "submit":
                await SubmitSubmit(tranId, submitData);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "print":
                return await SubmitReturnDataPrint(tranId, submitData);
            default:
                throw new NotImplementedException();
        }
    }

    #endregion

    public class PdaSchema : IPdaSchema
    {
        /// <summary>
        /// 条码明细列,显示的是BarBarcode的属性
        /// </summary>
        public List<PdaColumn> BarcodeCells { get; } = new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条码"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "qty",
                Caption = L.Text["数量"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "stockName",
                Caption = L.Text["仓库名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "stockLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialNumber",
                Caption = L.Text["物料编码"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialName",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialSpec",
                Caption = L.Text["规格型号"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "batchNo",
                Caption = L.Text["批号"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
        };
    }
}