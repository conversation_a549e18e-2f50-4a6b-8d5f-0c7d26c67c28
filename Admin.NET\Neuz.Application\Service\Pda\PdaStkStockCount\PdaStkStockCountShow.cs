﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.PdaStkStockCount.Dto;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.PdaStkStockCount;

public class PdaStkStockCountShow : PdaShow
{
    /// <summary>
    /// Pda源单显示
    /// </summary>
    public string SourceBill { get; set; }

    /// <summary>
    /// Pda仓库显示,默认 [仓库]仓位
    /// </summary>
    public string StockCaption { get; set; }

    public PdaStockCountSummaryShow Summary { get; set; } = new PdaStockCountSummaryShow();
    /// <summary>
    /// 明细
    /// </summary>
    public List<PdaStkStockCountDetailShowInfo> Details { get; set; } = new List<PdaStkStockCountDetailShowInfo>();
    /// <summary>
    /// 条码
    /// </summary>
    public List<PdaBarcodeShowInfo> Barcodes { get; set; } = new List<PdaBarcodeShowInfo>();
    /// <summary>
    /// 明细分页
    /// </summary>
    public PdaApiPagination DetailPagination { get; set; } = new PdaApiPagination();
    /// <summary>
    /// 条码分页
    /// </summary>
    public PdaApiPagination BarcodePagination { get; set; } = new PdaApiPagination();
    /// <summary>
    /// 扫描的条码数
    /// </summary>
    public int ScanBarcodeCount { get; set; }
}