{"$schema": "http://barModelSchema.json", "modelServiceName": "K3CloudBarModelService", "modelParams": {"billTypeofIReportData": "Neuz.Application.Erp.Barcode.K3Cloud.K3CloudBdStockStockLocService, Neuz.Application", "billDataMaterialNumberFieldName": "", "billDataQtyFieldName": "", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "FStockIdNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockIdName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockLocIdNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockLocIdName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FUseOrgIdNumber", "title": "使用组织编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FUseOrgIdName", "title": "使用组织名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "FStockIdNumber", "title": "仓库编码", "sortable": true}, {"fieldName": "FStockIdName", "title": "仓库名称", "sortable": true}, {"fieldName": "FStockLocIdNumber", "title": "仓位编码", "sortable": true}, {"fieldName": "FStockLocIdName", "title": "仓位名称", "sortable": true}, {"fieldName": "FUseOrgIdNumber", "title": "使用组织编码"}, {"fieldName": "FUseOrgIdName", "title": "使用组织名称"}], "barcodeSearchColumns": [], "barcodeListColumns": [], "barcodeEditColumns": []}, "billDataQuery": {"type": "K3Cloud", "idFieldName": "FID", "entryIdFieldName": "", "content": "{\r\n    \"$schema\": \"http://k3CloudDataQuerySchema.json\",\r\n    \"formId\": \"BD_StockStockLoc\",\r\n    \"fieldKeys\": [\r\n        \"FID AS _id\",\r\n        \"FStockId\",\r\n        \"FStockIdNumber\",\r\n        \"FStockIdName\",\r\n        \"FStockLocId\",\r\n        \"FStockLocIdNumber\",\r\n        \"FStockLocIdName\",\r\n        \"FUseOrgId\",\r\n        \"FUseOrgIdNumber\",\r\n        \"FUseOrgIdName\"\r\n    ],\r\n    \"filters\": [],\r\n    \"customFilter\": \"\"\r\n}"}}