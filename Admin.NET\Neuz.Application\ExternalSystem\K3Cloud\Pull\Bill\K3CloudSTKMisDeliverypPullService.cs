namespace Neuz.Application.ExternalSystem.K3Cloud.Pull;


/// <summary>
/// 其他出库单拉取服务
/// </summary>
/// <remarks>
/// 金蝶FormId: STK_MisDelivery
/// </remarks>
[Injection(Named = "K3Cloud:StkMisDeliveryp")]
internal class K3CloudStkMisDeliverypPullService : K3CloudStkAdjustmentPullService
{
    public K3CloudStkMisDeliverypPullService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider) : base(scopeFactory, serviceProvider)
    {
    }
    protected override Task<StkAdjustmentEntry> GetExistLocalDetailObject(IList<StkAdjustmentEntry> localDetailObjects, Dictionary<string, object> esBillRow)
    {
        return Task.FromResult(localDetailObjects.FirstOrDefault(u => u.EsEntryId == esBillRow["FEntryID"] + ""));
    }
    protected override string GetEsObjectBillNoField()
    {
        return "FBillNo";
    }
    protected override string GetLocalObjectBillNoField()
    {
        return nameof(EsBillEntityBase.EsBillNo);
    }
    protected override string GetQueryBillNoField(EsSyncPullSetting pullSetting)
    {
        return "FBillNo";
    }
    protected override List<string> GetQueryTimeFields(EsSyncPullSetting pullSetting)
    {
        return new List<string>() { "FApproveDate" /*审核日期*/ };
    }
    protected override async Task OnLocalDetailObjectMapped(StkAdjustmentEntry localDetailObject, Dictionary<string, object> esBillRow)
    {
        await base.OnLocalDetailObjectMapped(localDetailObject, esBillRow);
        localDetailObject.Qty = -Convert.ToDecimal(esBillRow["FQty"]);
    }
    protected override string GetBillTypeNumber(Dictionary<string, object> esBillRow)
    {
        if (esBillRow.TryGetValue("FBillTypeNumber", out var billTypeNumber) && billTypeNumber is string typeNumber)
        {
            return typeNumber;
        }
        else
        {
            return "QTCKD01_SYS"; // 默认类型

        }
    }
}

