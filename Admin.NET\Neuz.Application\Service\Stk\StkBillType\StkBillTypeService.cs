﻿using Neuz.Application.Model;

namespace Neuz.Application;

/// <summary>
/// 仓储单据类型服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkBillType", Order = 100)]
public class StkBillTypeService : BaseBdService<StkBillType, StkBillTypeLookupInput, LookupOutput>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 仓储单据类型服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkBillTypeService(IServiceProvider serviceProvider, SqlSugarRepository<StkBillType> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理实体名称的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "EntityName");
        var entityService = ServiceProvider.GetService<SysEntityService>();
        var entityList = entityService.ListAsync().GetAwaiter().GetResult();
        billTypeColumn.Options = entityList.Select(u => new SelectOption { Value = u.EntityName, Title = $"[{u.EntityName}]{u.Description}" }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "Number", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Name", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EntityName", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "Number",
            "Name",
            "EntityName",
            "Description",
            "IsForbid",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
        ];
    }

    protected override void OnBeforeAdd(StkBillType entity)
    {
        // base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkBillType entity)
    {
        // base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkBillType entity)
    {
        var checkEntity = Rep.GetFirst(u => u.Number == entity.Number && u.EntityName == entity.EntityName && u.Id != entity.Id);
        if (checkEntity != null)
            throw Oops.Bah(StkErrorCode.StkBillType1001, entity.EntityName, entity.Number);

        if (entity.NextEntries == null) return;
    }

    public override async Task<SqlSugarPagedList<LookupOutput>> LookupQueryAsync(StkBillTypeLookupInput input)
    {
        var entities = await Rep.AsQueryable()
            .Where(u => u.IsForbid == false)
            .WhereIF(!string.IsNullOrEmpty(input.Keyword), u => u.Number.Contains(input.Keyword) || u.Name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrEmpty(input.Number), u => u.Number.Contains(input.Number))
            .WhereIF(!string.IsNullOrEmpty(input.Name), u => u.Name.Contains(input.Name))
            .WhereIF(!string.IsNullOrEmpty(input.EntityName), u => u.EntityName == input.EntityName)
            .OrderBuilder(input)
            .Select(u => new LookupOutput(), true)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }

    [HttpGet("getBillType")]
    public async Task<List<StkBillTypeOutput>> GetBillType(string entityName)
    {
        var entities = await Rep.AsQueryable()
            .Where(u => u.IsForbid == false)
            .Where(u => u.EntityName == entityName)
            .Select(u => new StkBillTypeOutput
            {
                Id = u.Id,
                Number = u.Number,
                Name = u.Name,
                EntityName = u.EntityName,
            })
            .ToListAsync();

        return entities;
    }

    [HttpGet("getAllBillType")]
    public async Task<List<StkBillTypeOutput>> GetAllBillType()
    {
        var entities = await Rep.AsQueryable()
            .Where(u => u.IsForbid == false)
            .Select(u => new StkBillTypeOutput
            {
                Id = u.Id,
                Number = u.Number,
                Name = u.Name,
                EntityName = u.EntityName,
            })
            .ToListAsync();

        return entities;
    }
}