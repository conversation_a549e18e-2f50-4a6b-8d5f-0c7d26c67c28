{"$schema": "http://barModelSchema.json", "modelServiceName": "BarModelService", "modelParams": {"billDataMaterialNumberFieldName": "", "billDataQtyFieldName": ""}, "modelSchema": {"billSearchColumns": [], "billListColumns": [], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "ParentPackagePackageBarcode", "title": "箱码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "title": "功能点", "width": 140}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "ParentPackagePackageBarcode", "title": "箱码", "sortable": true}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "单据日期", "sortable": true, "format": "Date"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": []}, "billDataQuery": {"content": ""}}