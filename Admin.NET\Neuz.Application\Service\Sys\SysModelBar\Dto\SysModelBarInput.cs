﻿namespace Neuz.Application;

/// <summary>
/// 条码档案模型输入参数
/// </summary>
public class SysModelBarInput : BasePageInput
{
    /// <summary>
    /// 模型Key
    /// </summary>
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; }
}

/// <summary>
/// 添加预设条码档案模型输入参数
/// </summary>
public class SysModelBarAddByPresetsInput
{
    /// <summary>
    /// 模型Key
    /// </summary>
    public string ModelKey { get; set; }
}