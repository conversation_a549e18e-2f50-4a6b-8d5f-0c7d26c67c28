﻿namespace Neuz.Core.Entity;

/// <summary>
/// 外部系统同步拉取设置明细
/// </summary>
[SugarTable(null, "外部系统同步拉取设置明细")]
public class EsSyncPullSettingEntry : EntryEntityBase
{
    /// <summary>
    /// 外部系统业务字段名称
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统业务字段名称", Length = 255)]
    public string EsBizFieldName { get; set; }

    /// <summary>
    /// 本地对象属性名称
    /// </summary>
    [SugarColumn(ColumnDescription = "本地对象属性名称", Length = 255)]
    public string? LocalPropName { get; set; }

    /// <summary>
    /// 关联的拉取设置编码
    /// </summary>
    [SugarColumn(ColumnDescription = "关联的拉取设置编码", Length = 255)]
    public string? RefPullSettingNumber { get; set; }
}