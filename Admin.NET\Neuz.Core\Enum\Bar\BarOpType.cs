﻿namespace Neuz.Core.Enum;

/// <summary>
/// 条码操作类型
/// </summary>
public enum BarOpType
{
    /// <summary>
    /// 计划
    /// </summary>
    [Description("计划"), Theme("info")]
    Plan = 0,

    /// <summary>
    /// 入库
    /// </summary>
    [Description("入库"), Theme("primary")]
    InStock = 1,

    /// <summary>
    /// 出库
    /// </summary>
    [Description("出库"), Theme("success")]
    OutStock = 2,

    /// <summary>
    /// 调拨
    /// </summary>
    [Description("调拨"), Theme("info")]
    Transfer = 3,

    /// <summary>
    /// 盘点
    /// </summary>
    [Description("盘点"), Theme("info")]
    StockCount = 4,

    /// <summary>
    /// 调整
    /// </summary>
    [Description("调整"), Theme("info")]
    Adjust = 5
}