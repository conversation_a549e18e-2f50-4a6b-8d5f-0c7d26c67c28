﻿namespace Neuz.Core.Entity;

/// <summary>
/// 单据基类实体
/// </summary>
[SugarIndex("index_{table}_B", nameof(BillNo), OrderByType.Asc)]
public abstract class BillEntityBase : EntityTenant
{
    /// <summary>
    /// 单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "单据编号", Length = 80)]
    public virtual string BillNo { get; set; }

    /// <summary>
    /// 单据状态
    /// </summary>
    [SugarColumn(ColumnDescription = "单据状态")]
    public virtual DocumentStatus DocumentStatus { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(ColumnDescription = "审核时间")]
    public virtual DateTime? ApproveTime { get; set; }

    /// <summary>
    /// 审核人Id
    /// </summary>
    [SugarColumn(ColumnDescription = "审核人Id")]
    public virtual long? ApproveUserId { get; set; }

    /// <summary>
    /// 审核人姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "审核人姓名", Length = 50)]
    public virtual string? ApproveUserName { get; set; }

    /// <summary>
    /// 是否作废
    /// </summary>
    [SugarColumn(ColumnDescription = "是否作废")]
    public virtual bool IsCancel { get; set; }

    /// <summary>
    /// 作废时间
    /// </summary>
    [SugarColumn(ColumnDescription = "作废时间")]
    public virtual DateTime? CancelTime { get; set; }

    /// <summary>
    /// 作废人Id
    /// </summary>
    [SugarColumn(ColumnDescription = "作废人Id")]
    public virtual long? CancelUserId { get; set; }

    /// <summary>
    /// 作废人姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "作废人姓名", Length = 50)]
    public virtual string? CancelUserName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 255)]
    public string? Memo { get; set; }
}