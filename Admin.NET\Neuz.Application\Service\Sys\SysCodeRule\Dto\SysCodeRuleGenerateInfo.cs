﻿namespace Neuz.Application;

/// <summary>
/// 编码规则生成信息
/// </summary>
public class SysCodeRuleGenerateInfo
{
    /// <summary>
    /// 生成的编号
    /// </summary>
    public string GenerateNo { get; set; }

    /// <summary>
    ///  生成的流水号
    /// </summary>
    public decimal SerialNo { get; set; }

    /// <summary>
    ///  生成的格式化流水号
    /// </summary>
    public string FormatSerialNo { get; set; }

    /// <summary>
    /// 生成时使用的编码规则对象
    /// </summary>
    public SysCodeRule CodeRule { get; set; }
}