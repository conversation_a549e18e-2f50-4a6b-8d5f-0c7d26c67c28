﻿using Admin.NET.Core.Proj.Totf;
using Furion.Localization;

namespace Admin.NET.Core.Service;

/// <summary>
/// 系统用户服务
/// </summary>
public partial class SysUserService
{
    // 20231124 增加导入用户功能
    /// <summary>
    /// 获取导入用户模版
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取导入用户模版")]
    public async Task<FileContentResult> ImportUserTemplate()
    {
        IImporter importer = new ExcelImporter();
        var res = await importer.GenerateTemplateBytes<UserImportDto>();
        return new FileContentResult(res, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            { FileDownloadName = L.Text["用户导入模版.xlsx"] };
    }

    /// <summary>
    /// 导入用户
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入用户")]
    [UnitOfWork]
    public async Task ImportUser(IFormFile file)
    {
        IImporter importer = new ExcelImporter();
        var importResult = await importer.Import<UserImportDto>(file.OpenReadStream());
        // 成功导入数据
        var importedData = importResult.Data;

        var errMsgs = new List<string>();

        //依赖类型的名称字段与主键的映射
        var sysOrgName = nameof(UserImportDto.SysOrgName);
        var sysPosName = nameof(UserImportDto.SysPosName);
        var sysRoleName = "SysRoleNames";
        var depMappings = new Dictionary<string, Dictionary<string, object>>
        {
            [sysOrgName] = new(),
            [sysPosName] = new(),
            [sysRoleName] = new(),
        };
        foreach (var record in importedData)
        {
            if (!string.IsNullOrWhiteSpace(record.SysOrgName) &&
                !depMappings[sysOrgName].ContainsKey(record.SysOrgName))
            {
                var depEntity = await _sysUserRep.Context.Queryable<SysOrg>().Where(u => u.Name == record.SysOrgName)
                    .FirstAsync();
                if (depEntity == null)
                    errMsgs.Add(L.Text["机构名称: {0} 不存在", record.SysOrgName]);
                else
                    depMappings[sysOrgName][record.SysOrgName] = depEntity;
            }

            if (!string.IsNullOrWhiteSpace(record.SysPosName) &&
                !depMappings[sysPosName].ContainsKey(record.SysPosName))
            {
                var depEntity = await _sysUserRep.Context.Queryable<SysPos>().Where(u => u.Name == record.SysPosName)
                    .FirstAsync();
                if (depEntity == null)
                    errMsgs.Add(L.Text["职位名称: {0} 不存在", record.SysPosName]);
                else
                    depMappings[sysPosName][record.SysPosName] = depEntity;
            }

            if (!string.IsNullOrWhiteSpace(record.SysRoleNames) &&
                !depMappings[sysPosName].ContainsKey(record.SysRoleNames))
            {
                var roleNames = record.SysRoleNames.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(u => u.Trim());
                foreach (string roleName in roleNames)
                {
                    var depEntity = await _sysUserRep.Context.Queryable<SysRole>().Where(u => u.Name == roleName)
                        .FirstAsync();
                    if (depEntity == null)
                        errMsgs.Add(L.Text["角色名称: {0} 不存在", roleName]);
                    else
                        depMappings[sysRoleName][roleName] = depEntity;
                }
            }
        }

        if (errMsgs.Count > 0)
            throw Oops.Bah(string.Join("\r\n", errMsgs));

        foreach (var item in importedData)
        {
            var isExist = await _sysUserRep.AsQueryable().Filter(null, true).AnyAsync(u => u.Account == item.Account);
            if (isExist) throw Oops.Oh(ErrorCodeEnum.D1003);

            var password = await _sysConfigService.GetConfigValue<string>(CommonConst.SysPassword);

            var user = item.Adapt<SysUser>();
            user.Id = YitIdHelper.NextId();
            user.Password = CryptogramUtil.Encrypt(password);
            if (user.Sex == 0)
                user.Sex = GenderEnum.Male;

            //处理依赖类型的名称字段与主键的映射
            if (depMappings[sysOrgName].ContainsKey(item.SysOrgName ?? ""))
            {
                var sysOrg = (SysOrg)depMappings[sysOrgName][item.SysOrgName];
                user.OrgId = sysOrg.Id;
                user.TenantId = sysOrg.TenantId; //使用机构的租户作为用户的所在租户
            }

            user.PosId = depMappings[sysPosName].ContainsKey(item.SysPosName ?? "")
                ? ((SysPos)depMappings[sysPosName][item.SysPosName]).Id
                : null;

            // 授权用户角色
            var roleIds = item.SysRoleNames?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                              .Select(u => ((SysRole)depMappings[sysRoleName][u.Trim()]).Id).Distinct().ToList() ??
                          new List<long>();
            if (roleIds.Count > 0)
                await GrantRole(new UserRoleInput { UserId = user.Id, RoleIdList = roleIds });

            await _sysUserRep.AsInsertable(user).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 修改Erp信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改Erp信息")]
    public async Task ChangeErpInfo(ChangeErpInfoInput input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D0009);
        user.ErpUserName = input.ErpUserName;
        user.ErpPassword = input.ErpPassword;
        await _sysUserRep.AsUpdateable(user).UpdateColumns(u => new { u.ErpUserName, u.ErpPassword })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取用户mfa绑定信息
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "GetMfaBindInfo"), HttpPost]
    [DisplayName("获取用户mfa绑定信息")]
    public async Task<dynamic> GetMfaBindInfo(GetMfaBindInfoInput input)
    {
        long userid = input.Id;
        if (input.Id == 0)
        {
            userid = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
        }

        var user = await _sysUserRep.GetFirstAsync(u => u.Id == userid);

        if (user.Account == "superadmin")
        {
            throw Oops.Oh(L.Text["超级管理员暂不支持绑定"]);
        }

        var totpGenerator = App.GetService<TotpGeneratorService>();
        var key = "";
        if (string.IsNullOrEmpty(user.MfaKey))
        {
            key = totpGenerator.GenerateSecretKey();
            user.IsMfa = true;
            user.MfaKey = key;
            await _sysUserRep.UpdateAsync(user);
        }
        else
        {
            key = user.MfaKey;
        }

        var url = @$"otpauth://totp/WMS:{HttpUtility.UrlEncode(user.RealName, System.Text.Encoding.UTF8)}?secret={key}";
        return new
        {
            user.IsMfa,
            user.MfaKey,
            url
        };
    }

    /// <summary>
    /// 解除MFA绑定
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UnlockMfa"), HttpPost]
    [DisplayName("解除MFA绑定")]
    public async Task UnlockMfa(UnlockMfaInput input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D0009);

        user.MfaKey = null;
        await _sysUserRep.UpdateAsync(user);
    }

    /// <summary>
    /// 修改绑定供应商编码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改绑定供应商编码")]
    public async Task ChangeBindSupplierNumber(ChangeBindSupplierNumberInput input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D0009);
        user.BindSupplierNumber = input.BindSupplierNumber;
        await _sysUserRep.AsUpdateable(user).UpdateColumns(u => new { u.BindSupplierNumber }).ExecuteCommandAsync();
    }
}