using Furion.Localization;
using Neuz.Application.Model;
using Neuz.Application.Pda.Proxy;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库存任务服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkTask", Order = 100)]
public class StkTaskService : StkBaseBillService<StkTask>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 库存任务服务构造函数
    /// </summary>
    public StkTaskService(IServiceProvider serviceProvider, SqlSugarRepository<StkTask> rep) : base(serviceProvider, rep)
    {
    }

    [NonAction]
    public override Task<long> AddAsync(StkTask input)
    {
        return base.AddAsync(input);
    }

    [NonAction]
    public override Task UpdateAsync(StkTask input)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<List<ExecResult>> DeleteAsync(IdsInput input)
    {
        return base.DeleteAsync(input);
    }

    [NonAction]
    public override Task Import(IFormFile file)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<FileContentResult> ImportTemplate()
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<List<ExecResult>> CancelAsync(IdsInput input)
    {
        throw new NotSupportedException();
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "TaskType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Status", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "TaskType",
            "Status",
            "SrcBillType",
            "SrcBillNo",
            "WarehouseNumber",
            "WarehouseName",
            "Entries_Seq",
            "Entries_EntryStatus",
            "Entries_SrcBillEntrySeq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_SrcWhAreaNumber",
            "Entries_SrcWhAreaName",
            "Entries_SrcWhLocNumber",
            "Entries_SrcWhLocName",
            "Entries_SrcContainerNumber",
            "Entries_DestWhAreaNumber",
            "Entries_DestWhAreaName",
            "Entries_DestWhLocNumber",
            "Entries_DestWhLocName",
            "Entries_DestContainerNumber",
            "Entries_Qty",
            "Entries_ExecQty",
            "Entries_RemainExecQty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_IsAllowLocReplace",
            "Entries_IsAllowBatchNoReplace",
            "Entries_ManualCloseTime",
            "Entries_ManualCloseUserName",
            "Entries_EntryMemo",
            "Entries_UsedRuleNumber",
            "Entries_UsedRuleName",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_SrcWhAreaId"); // 库区权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_DestWhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkTask entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkTask entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkTask entity)
    {
        foreach (var entry in entity.Entries)
        {
            // 重新计算剩余数量
            entry.RemainExecQty = entry.Qty - entry.ExecQty;
        }
    }

    // ReSharper disable once RedundantOverriddenMember
    protected override void OnAfterAdd(StkTask entity)
    {
        base.OnAfterAdd(entity);

        // 任务的特殊性，先锁定库存，再生成任务，所以这里不处理库存
    }

    protected override void OnBeforeDelete(StkTask entity)
    {
        base.OnBeforeDelete(entity);

        // 回滚库存
        InventoryService.RollbackInventoryByTaskId(entity.Id);
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkTask entity)
    {
        return new List<string> { entity.SrcBillKey };
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkTask entity, string srcBillKey)
    {
        return new List<long> { entity.SrcBillId };
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkTask entity)
    {
        return new List<long>();
    }

    protected override void OnAfterAudit(StkTask entity)
    {
        base.OnAfterAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkTask entity)
    {
        if (entity.Status != StkTaskStatus.UnHandle)
            throw Oops.Bah(StkErrorCode.StkTask1001, entity.BillNo);

        base.OnBeforeUnAudit(entity);
    }

    protected override void OnAfterUnAudit(StkTask entity)
    {
        base.OnAfterUnAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 取消分配
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("cancelAllocate")]
    public Task<List<ExecResult>> CancelAllocate(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var entity = Rep.Context.Queryable<StkTask>().First(u => u.Id == id);
            if (entity.Status != StkTaskStatus.UnHandle)
                throw Oops.Bah(StkErrorCode.StkTask1003, entity.BillNo);

            // 反审核任务
            var unAuditResult = UnAuditAsync(new IdsInput { Ids = new List<long> { id } }).GetAwaiter().GetResult();
            if (!unAuditResult[0].IsSuccess)
                throw Oops.Bah(unAuditResult[0].Message);

            // 删除任务
            var deleteResult = DeleteAsync(new IdsInput { Ids = new List<long> { id } }).GetAwaiter().GetResult();
            if (!deleteResult[0].IsSuccess)
                throw Oops.Bah(deleteResult[0].Message);

            // 提交事务
            uow.Commit();

            return L.Text["单据：{0}，任务: {1} 取消分配成功", entity.SrcBillNo, entity.BillNo];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 设置为允许更换批号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("setBatchNoReplace")]
    [UnitOfWork]
    public async Task SetBatchNoReplace(EntryIdsInput input)
    {
        await Rep.Context.Updateable<StkTaskEntry>().SetColumns(u =>
                new StkTaskEntry
                {
                    IsAllowBatchNoReplace = true
                })
            .Where(u => input.EntryIds.Contains(u.EntryId))
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 手动关闭
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("manualClose")]
    public Task<List<ExecResult>> ManualClose(EntryIdsInput input)
    {
        var taskIds = new List<long>();
        var execResults = ExecActions(input.EntryIds, entryId =>
        {
            // 查找任务明细
            var entry = Rep.Context.Queryable<StkTaskEntry>().First(u => u.EntryId == entryId);
            if (entry == null)
                throw Oops.Bah(BaseErrorCode.Base1000, entryId);

            // 查找任务
            var entity = Rep.Context.Queryable<StkTask>().First(u => u.Id == entry.Id);
            if (entry.EntryStatus is StkTaskEntryStatus.Finish or StkTaskEntryStatus.ManualClose)
                throw Oops.Bah(StkErrorCode.StkTask1002, entity.BillNo, entry.Seq);

            // 添加到任务Id集合
            if (!taskIds.Contains(entity.Id))
                taskIds.Add(entity.Id);

            var invChangeList = new List<StkInvChange>();

            // 处理明细状态
            entry.EntryStatus = StkTaskEntryStatus.ManualClose;
            entry.ManualCloseTime = DateTime.Now;
            entry.ManualCloseUserId = CurUserId;
            entry.ManualCloseUserName = CurUserName;

            if (entry.RelLockInvId != null)
            {
                // 锁定数量库存变更
                invChangeList.Add(new StkInvChange
                {
                    InvLockLogType = StkInvLockLogType.ManualCloseMinus,
                    MaterialId = entry.MaterialId,
                    BatchNo = entry.BatchNo,
                    ProduceDate = entry.ProduceDate,
                    ExpiryDate = entry.ExpiryDate,
                    LockQty = -(entry.Qty - entry.ExecQty),
                    OwnerId = entry.OwnerId,
                    AuxPropValueId = entry.AuxPropValueId,
                    WhLocId = entry.SrcWhLocId!.Value,
                    ContainerId = entry.SrcContainerId,
                    UnitId = entry.UnitId,
                    RelBillKey = entity.SrcBillKey,
                    RelBillNo = entity.SrcBillNo,
                    RelBillType = entity.SrcBillType,
                    RelSeq = entry.SrcBillEntrySeq,
                    RelBillId = entity.SrcBillId,
                    RelBillEntryId = entry.SrcBillEntryId,
                    RelSourceBillNo = null,
                    InventoryId = entry.RelLockInvId,
                    RelTaskId = entity.Id,
                });
            }

            if (entry.RelPreInInvId != null)
            {
                // 预入库数量库存变更
                invChangeList.Add(new StkInvChange
                {
                    InvPreInLogType = StkInvPreInLogType.ManualCloseMinus,
                    MaterialId = entry.MaterialId,
                    BatchNo = entry.BatchNo,
                    ProduceDate = entry.ProduceDate,
                    ExpiryDate = entry.ExpiryDate,
                    PreInQty = -(entry.Qty - entry.ExecQty),
                    OwnerId = entry.OwnerId,
                    AuxPropValueId = entry.AuxPropValueId,
                    WhLocId = entry.DestWhLocId!.Value,
                    ContainerId = entry.DestContainerId,
                    UnitId = entry.UnitId,
                    RelBillKey = entity.SrcBillKey,
                    RelBillNo = entity.SrcBillNo,
                    RelBillType = entity.SrcBillType,
                    RelSeq = entry.SrcBillEntrySeq,
                    RelBillId = entity.SrcBillId,
                    RelBillEntryId = entry.SrcBillEntryId,
                    RelSourceBillNo = null,
                    InventoryId = entry.RelPreInInvId,
                    RelTaskId = entity.Id,
                });
            }

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 更新明细
            Rep.Context.Updateable(entry).ExecuteCommand();

            // 提交库存变更
            InventoryService.UpdateInventory(invChangeList, "任务手动关闭");

            // 上游单据刷新状态
            SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);

            // 提交事务
            uow.Commit();

            return L.Text["单据编号: {0} 序号: {1} 手动关闭成功", entity.BillNo, entry.Seq];
        });

        // 刷新任务状态
        RefreshStatus(taskIds);

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkTask>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        // 查询
        var entities = Rep.Context.Queryable<StkTask>().Includes(u => u.Entries).Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            var srcBillKey = entity.SrcBillKey;
            List<SrcEntryQtyInfo> srcEntryQtyInfoList;
            switch (srcBillKey)
            {
                // 任务来源：收货单，查入库单
                case nameof(StkReceive):
                    srcEntryQtyInfoList = Rep.Context.Queryable<StkInStockEntry>()
                        .LeftJoin<StkInStock>((te, t) => te.Id == t.Id)
                        .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcTaskId == entity.Id)
                        .Select((te, t) => new SrcEntryQtyInfo
                        {
                            SrcTaskEntryId = te.SrcTaskEntryId.Value,
                            Qty = te.Qty,
                        })
                        .ToList();
                    break;
                // 任务来源：出库通知单，查出库单，库存调拨单
                case nameof(StkOutNotice):
                    // 出库单
                    srcEntryQtyInfoList = Rep.Context.Queryable<StkOutStockEntry>()
                        .LeftJoin<StkOutStock>((te, t) => te.Id == t.Id)
                        .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcTaskId == entity.Id)
                        .Select((te, t) => new SrcEntryQtyInfo
                        {
                            SrcTaskEntryId = te.SrcTaskEntryId.Value,
                            Qty = te.Qty,
                        })
                        .ToList();
                    // 库存调拨单
                    srcEntryQtyInfoList.AddRange(
                        Rep.Context.Queryable<StkTransferEntry>()
                            .LeftJoin<StkTransfer>((te, t) => te.Id == t.Id)
                            .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcTaskId == entity.Id)
                            .Select((te, t) => new SrcEntryQtyInfo
                            {
                                SrcTaskEntryId = te.SrcTaskEntryId.Value,
                                Qty = te.Qty,
                            })
                            .ToList()
                    );
                    break;
                // 任务来源：调拨通知单，查库存调拨单
                case nameof(StkTransferNotice):
                    srcEntryQtyInfoList = Rep.Context.Queryable<StkTransferEntry>()
                        .LeftJoin<StkTransfer>((te, t) => te.Id == t.Id)
                        .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcTaskId == entity.Id)
                        .Select((te, t) => new SrcEntryQtyInfo
                        {
                            SrcTaskEntryId = te.SrcTaskEntryId.Value,
                            Qty = te.Qty,
                        })
                        .ToList();
                    break;
                default:
                    throw new NotImplementedException($"{srcBillKey} 刷新状态未实现");
            }

            foreach (var entry in entity.Entries)
            {
                // 执行数量
                var execQty = srcEntryQtyInfoList.Where(u => u.SrcTaskEntryId == entry.EntryId).Sum(u => u.Qty);

                entry.ExecQty = execQty;
                entry.RemainExecQty = entry.Qty - entry.ExecQty < 0 ? 0 : entry.Qty - entry.ExecQty;

                // 如果不是手工关闭
                if (entry.EntryStatus != StkTaskEntryStatus.ManualClose)
                {
                    if (entry.ExecQty >= entry.Qty)
                        entry.EntryStatus = StkTaskEntryStatus.Finish;
                    else if (entry.ExecQty > 0)
                        entry.EntryStatus = StkTaskEntryStatus.Handling;
                    else
                        entry.EntryStatus = StkTaskEntryStatus.UnHandle;
                }
            }

            if (entity.Entries.All(u => u.EntryStatus is StkTaskEntryStatus.Finish or StkTaskEntryStatus.ManualClose))
                entity.Status = StkTaskStatus.Finish;
            else if (entity.Entries.Any(u => u.EntryStatus is StkTaskEntryStatus.Handling or StkTaskEntryStatus.Finish or StkTaskEntryStatus.ManualClose))
                entity.Status = StkTaskStatus.Handling;
            else
                entity.Status = StkTaskStatus.UnHandle;

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }
    }

    /// <summary>
    /// 获取未完成的任务明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getUnFinishTaskEntry")]
    public async Task<StkTask> GetUnFinishTaskEntry([FromQuery] IdInput input)
    {
        var entity = await InnerGetAsync(u => u.Id == input.Id);
        if (entity.Status == StkTaskStatus.Finish) return null;

        entity.Entries = entity.Entries.Where(u => u.EntryStatus != StkTaskEntryStatus.Finish && u.EntryStatus != StkTaskEntryStatus.ManualClose && u.RemainExecQty > 0).ToList();
        if (entity.Entries.Count == 0) return null;

        return entity;
    }

    /// <summary>
    /// 任务处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("taskHandle")]
    [UnitOfWork]
    public async Task<string> TaskHandle(StkTaskTaskHandleInput input)
    {
        var entity = await InnerGetAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, input.Id);
        if (entity.Status == StkTaskStatus.Finish || entity.Entries.All(u => u.EntryStatus is StkTaskEntryStatus.Finish or StkTaskEntryStatus.ManualClose))
            throw Oops.Bah(StkErrorCode.StkTask1004, entity.BillNo);

        if (input.Entries.Count(u => u.HandleQty > 0) == 0)
            throw Oops.Bah(StkErrorCode.StkTask1008, entity.BillNo);

        // 项目增加的条件
        if (entity.SrcBillType is "SCYLQD" or "WWYLQD" && input.EmployeeId == null)
            throw Oops.Bah("领料人不能为空");

        foreach (var inputEntry in input.Entries)
        {
            var entry = entity.Entries.FirstOrDefault(u => u.EntryId == inputEntry.EntryId);
            if (entry == null)
                throw Oops.Bah(BaseErrorCode.Base1002, inputEntry.EntryId);

            // 是否允许更换批号
            if (!entry.IsAllowBatchNoReplace && (inputEntry.BatchNo != entry.BatchNo || inputEntry.ProduceDate != entry.ProduceDate || inputEntry.ExpiryDate != entry.ExpiryDate))
                throw Oops.Bah(StkErrorCode.StkTask1005, entity.BillNo, entry.Seq);
            // 是否允许更换库位
            if (!entry.IsAllowLocReplace && (inputEntry.SrcWhAreaId != entry.SrcWhAreaId || inputEntry.DestWhLocId != entry.DestWhLocId ||
                                             inputEntry.SrcContainerId != entry.SrcContainerId))
                throw Oops.Bah(StkErrorCode.StkTask1006, entity.BillNo, entry.Seq);

            // 项目增加的条件
            if (entity.SrcBillType != "SCYLQD" && entity.SrcBillType != "WWYLQD")
            {
                // 是否超剩余执行数量
                if (inputEntry.HandleQty > entry.RemainExecQty)
                    throw Oops.Bah(StkErrorCode.StkTask1007, entity.BillNo, entry.Seq);
            }
        }

        // TODO：如果有其他源单，继续补充逻辑
        var outStockService = ServiceProvider.GetService<StkOutStockService>();

        var outNoticeEntity = entity.SrcBillKey == nameof(StkOutNotice)
            ? (await Rep.Context.Queryable<StkOutNotice>().IncludeNavCol().Where(u => u.Id == entity.SrcBillId).FirstAsync())
            : null;
        var departmentId = outNoticeEntity?.DepartmentId;
        var supplierId = outNoticeEntity?.SupplierId;
        var customerId = outNoticeEntity?.CustomerId;

        // 生成出库单
        var outStock = new StkOutStock
        {
            Id = 0,
            Date = DateTime.Now.Date,
            BillType = GetNextBillTypeNumber(entity.SrcBillKey, entity.SrcBillType, nameof(StkOutStock)),
            Status = StkOutStockStatus.UnShip,
            WarehouseId = entity.WarehouseId,
            DepartmentId = departmentId,
            SupplierId = supplierId,
            CustomerId = customerId,
            PushFlag = PushFlag.None,
            EmployeeId = input.EmployeeId,
            Entries = input.Entries.Where(u => u.HandleQty > 0)
                .Select((inputEntry, i) =>
                {
                    // 任务明细
                    var entry = entity.Entries.First(p => p.EntryId == inputEntry.EntryId);
                    return new StkOutStockEntry
                    {
                        Seq = i + 1,
                        MaterialId = entry.MaterialId,
                        BatchNo = inputEntry.BatchNo,
                        ProduceDate = inputEntry.ProduceDate,
                        ExpiryDate = inputEntry.ExpiryDate,
                        SrcWhAreaId = inputEntry.SrcWhAreaId,
                        SrcWhLocId = inputEntry.SrcWhLocId,
                        DestWhAreaId = inputEntry.DestWhAreaId,
                        DestWhLocId = inputEntry.DestWhLocId,
                        Qty = inputEntry.HandleQty,
                        UnitId = entry.UnitId,
                        ContainerId = inputEntry.DestContainerId,
                        OwnerId = entry.OwnerId,
                        AuxPropValueId = entry.AuxPropValueId,
                        GrossWeight = inputEntry.HandleQty * entry.Material.GrossWeight,
                        PackingVolume = inputEntry.HandleQty * entry.Material.PackingVolume,
                        PackingQty = (int)Math.Ceiling(inputEntry.HandleQty * entry.Material.StdPackingQty),
                        Price = entry.Material.Price,
                        TotalPrice = inputEntry.HandleQty * entry.Material.Price,
                        SrcBillKey = entity.SrcBillKey,
                        SrcBillNo = entity.SrcBillNo,
                        SrcBillEntrySeq = entry.SrcBillEntrySeq,
                        SrcBillId = entity.SrcBillId,
                        SrcBillEntryId = entry.SrcBillEntryId,
                        SrcTaskId = entity.Id,
                        SrcTaskEntryId = entry.EntryId,
                    };
                }).ToList(),
        };

        // 保存
        var outStockId = await outStockService.AddAsync(outStock);

        // 审核
        var billAuditResult = await outStockService.AuditAsync(new IdsInput { Ids = [outStockId] });
        if (!billAuditResult.First().IsSuccess)
            throw Oops.Bah(billAuditResult.First().Message);

        // 重新查询，获取单号
        outStock = await outStockService.GetAsync(new IdInput { Id = outStockId });
        return L.Text["任务处理完成，生成出库单：{0}", outStock.BillNo];
    }

    /// <summary>
    /// 获取下级单据类型
    /// </summary>
    /// <param name="curEntityName"></param>
    /// <param name="curBillType"></param>
    /// <param name="nextEntityName"></param>
    /// <returns></returns>
    private string GetNextBillTypeNumber(string curEntityName, string curBillType, string nextEntityName)
    {
        var billTypeNumber = Rep.Change<StkBillType>().AsQueryable()
            .LeftJoin<StkBillTypeNextEntry>((t1, t2) => t1.Id == t2.Id)
            .Where((t1, t2) => t1.Number == curBillType && t1.EntityName == curEntityName && t2.NextEntityName == nextEntityName)
            .Select((t1, t2) => t2.NextBillTypeNumber)
            .First();

        return billTypeNumber;
    }

    /// <summary>
    /// 来源任务明细数量信息
    /// </summary>
    private class SrcEntryQtyInfo
    {
        /// <summary>
        /// 来源任务明细Id
        /// </summary>
        public long SrcTaskEntryId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }
    }
}