﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存盘点
/// </summary>
[SugarTable(null, "库存盘点")]
public class StkStockCount : EsBillEntityBase
{
    /// <summary>
    /// 盘点名称
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点名称", Length = 255)]
    public string Name { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "单据类型", Length = 80)]
    public string BillType { get; set; }

    /// <summary>
    /// 盘点状态
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点状态")]
    public StkStockCountStatus StockCountStatus { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [SugarColumn(ColumnDescription = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 库存盘点库区范围
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkStockCountWhAreaEntry.Id))]
    public List<StkStockCountWhAreaEntry> WhAreaEntries { get; set; }

    /// <summary>
    /// 库存盘点物料范围
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Id))]
    public List<StkStockCountMaterialEntry> MaterialEntries { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkStockCountEntry.Id))]
    public List<StkStockCountEntry> Entries { get; set; }
}