﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <PackageId>PluginCore</PackageId>
    <Version>2.2.5</Version>
    <Company>yiyun</Company>
    <Authors>yiyun</Authors>
    <Description>Lightweight plugin framework</Description>
    <Copyright>Copyright (c) 2021-present yiyun</Copyright>
    <RepositoryUrl>https://github.com/yiyungent/PluginCore</RepositoryUrl>
    <PackageLicenseUrl>https://github.com/yiyungent/PluginCore/blob/main/LICENSE</PackageLicenseUrl>
    <PackageTags>PluginCore</PackageTags>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
  </PropertyGroup>

  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <!-- 生成注释xml -->
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|netcoreapp3.1|AnyCPU'">
    <DocumentationFile>bin\Release\netcoreapp3.1\PluginCore.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net5.0|AnyCPU'">
    <DocumentationFile>bin\Release\net5.0\PluginCore.xml</DocumentationFile>
  </PropertyGroup>

  <!-- 方便开发debug,与发布到nuget -->
  <ItemGroup Condition="'$(Configuration)' == 'Release'">
    <PackageReference Include="PluginCore.IPlugins" Version="0.9.1" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)' == 'Debug'">
    <ProjectReference Include="..\PluginCore.IPlugins\PluginCore.IPlugins.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="BackgroundServices\" />
    <Folder Include="Extensions\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
  </ItemGroup>

</Project>
