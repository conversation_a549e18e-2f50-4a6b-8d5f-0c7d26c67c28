﻿using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.StockCount;

/// <summary>
/// Pda盘点重置服务
/// </summary>
[ApiDescriptionSettings("PDA", Name = "StockCountReset", Order = 300)]
public class PdaStockCountResetService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly UserManager _userManager;

    public PdaStockCountResetService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _userManager = serviceProvider.GetService<UserManager>();
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaModel")]
    public async Task<dynamic> GetPdaModel([FromQuery] string key)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var model = pdaCacheService.GetPdaModel(key);
        return await Task.FromResult(model);
    }

    /// <summary>
    /// 创建新的单据数据
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("createBillData")]
    public async Task<dynamic> CreateBillData([FromQuery] string key)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var userId = _userManager.UserId;
        var billData = pdaCacheService.CreateBillData(key, userId);
        var model = pdaCacheService.GetPdaModel(key);
        model.BillDataInitialization(billData);
        var pdaModel = (PdaStockCountModel)model;
        pdaModel.RefreshShow(billData.TranId);
        billData.DataShow.Properties["tranId"] = billData.TranId;
        billData.DataShow.Properties["userId"] = billData.UserId;
        return await Task.FromResult(billData);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShow")]
    public async Task<dynamic> GetPdaBillDataShow([FromQuery] string key)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var userId = _userManager.UserId;
        var billData = pdaDataCacheService.GetBillDataForKey(key, userId);
        if (billData != null)
        {
            billData.DataShow.Properties["tranId"] = billData.TranId;
            billData.DataShow.Properties["userId"] = billData.UserId;
        }
        return await Task.FromResult(billData);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShowForTranId")]
    public async Task<dynamic> GetPdaBillDataShowForTranId([FromQuery] long tranId, [FromQuery] string key)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.RefreshShow(tranId);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 删除单据模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delPdaBillData")]
    public async Task<dynamic> DelPdaBillData([FromBody] PdaDelPdaBillDataInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        pdaDataCacheService.DelBillData(input.TranId);
        return await Task.FromResult("");
    }

    /// <summary>
    /// 查询Lookup数据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="lookupKey"></param>
    /// <param name="lookupValue"></param>
    /// <returns></returns>
    [HttpGet("queryLookupData")]
    public async Task<dynamic> QueryLookupData([FromQuery] long tranId, [FromQuery] string key, [FromQuery] string lookupKey, [FromQuery] string lookupValue)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        var lookupOutpust = pdaModel.QueryLookupData(tranId, lookupKey, lookupValue);
        return await Task.FromResult(lookupOutpust);
    }

    /// <summary>
    /// 选择Lookup数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("selectLookupData")]
    public async Task<dynamic> SelectLookupData([FromBody] PdaSelectLookupDataInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.SelectLookupData(input.TranId, input.Key, input.LookupKey, input.ValueKey, input.LookupDataKey);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("billSubmit")]
    public async Task<dynamic> BillSubmit([FromBody] PdaBillSubmitInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        var message = ((PdaStockCountModel)pdaModel).ResetStockCount(input.TranId, input.Key);
        return await Task.FromResult(message);
    }
}