﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill;

/// <summary>
/// WMS库区扫描
/// </summary>
public class PdaLocalBillScanWhAreaOperation : PdaScanBarcodeOperationBase
{
    public PdaLocalBillScanWhAreaOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        string regexStr = "CK(?<CK>.+)\\|CW(?<CW>.+)";
        Regex regex = new Regex(regexStr, RegexOptions.IgnoreCase);
        var match = regex.Match(args.BarcodeString);
        if (!match.Success) return;
        var dataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
        var billData = (PdaLocalBillData)dataCacheService.GetBillData(args.Key, args.TranId);
        var billModel = (IPdaLocalBillModel)dataCacheService.GetPdaModel(billData.ModelKey);
        string ck = match.Groups["CK"].Value;
        string cw = match.Groups["CW"].Value;
        // 是否有组织(仓库)
        var warehouseIdStr = billModel.GetWarehouseId(args.TranId);
        long.TryParse(warehouseIdStr, out long warehouseId);
        // 查库区库位
        var rep = App.GetService<SqlSugarRepository<BdWhArea>>();
        var areaLocs = rep.Context
            .AddWarehouseFilter<BdWhArea>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<BdWhArea>(ServiceProvider, u => u.Id)
            .Queryable(rep.AsQueryable().InnerJoin<BdWhLoc>((t1, t2) => t1.Id == t2.WhAreaId)
                .Where((t1, t2) => t1.Number == ck && t2.Number == cw)
                .WhereIF(warehouseId != 0, (t1, t2) => t1.WarehouseId == warehouseId)
                .Select((t1, t2) => new PdaLocalBillStockInfo
                {
                    WhAreaId = t1.Id + "",
                    WhAreaNumber = t1.Number,
                    WhAreaName = t1.Name,
                    WhLocId = t2.Id + "",
                    WhLocNumber = t2.Number,
                    WhLocName = t2.Name
                }))
            .ToList();
        if (areaLocs.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1043, ck, cw);
        if (areaLocs.Count > 1) throw Oops.Bah(PdaErrorCode.Pda1044, ck, cw);
        // 调用标准带库区库位方法
        billModel.LocalBillWhAreaSelect(new PdaLocalBillLookSelectInput
        {
            Key = billData.ModelKey,
            TranId = args.TranId,
            LookupDataKey = "StockInfo",
            LookupKey = "BdWhAreaLocModel",
            Id = $"{areaLocs[0].WhAreaId}_{areaLocs[0].WhLocId}",
            DetailIndex = null,
            IsLocalBill = false
        }).GetAwaiter().GetResult();
        args.IsResult = true;
    }
}