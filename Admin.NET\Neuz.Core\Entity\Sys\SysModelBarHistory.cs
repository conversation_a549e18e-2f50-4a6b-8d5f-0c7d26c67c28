﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案模型历史
/// </summary>
[SugarTable(null, "条码档案模型历史")]
[SysTable]
[SugarIndex("index_{table}_M", nameof(ModelKey), OrderByType.Asc)]
public class SysModelBarHistory : EntityBase
{
    /// <summary>
    /// 模型Key
    /// </summary>
    [SugarColumn(ColumnDescription = "模型Key", Length = 100)]
    public string ModelKey { get; set; }

    /// <summary>
    /// 模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "模型名称", Length = 100)]
    public string ModelName { get; set; }

    /// <summary>
    /// 模型内容
    /// </summary>
    [SugarColumn(ColumnDescription = "模型内容", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ModelContent { get; set; }
}