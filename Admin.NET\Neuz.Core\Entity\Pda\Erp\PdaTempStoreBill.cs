﻿namespace Neuz.Core.Entity.Pda.Erp;

/// <summary>
/// Pda暂存单据
/// </summary>
[SugarTable(null, "Pda暂存单据")]
public class PdaTempStoreBill : EntityTenant
{
    /// <summary>
    /// Pda提交事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Pda事务Id")]
    public long TranId { get; set; }

    /// <summary>
    /// Pda单据数据用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Pda单据数据用户Id")]
    public long UserId { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 200)]
    public string? FuncKey { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "暂存描述", Length = 500)]
    public string? Description { get; set; }

    /// <summary>
    /// 暂存数据
    /// </summary>
    [SugarColumn(ColumnDescription = "暂存数据")]
    public byte[] BillData { get; set; }
}