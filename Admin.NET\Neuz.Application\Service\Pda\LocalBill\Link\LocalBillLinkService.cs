﻿using Furion.Localization;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.LocalBill.Link;

/// <summary>
/// 单据转换规则基类
/// </summary>
public class LocalBillLinkService
{
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 单据转换规则字典,访止每次都需要new一次,影响性能
    /// </summary>
    protected static Dictionary<string, Type> LinkTypes { get; set; }

    /// <summary>
    /// 单据转换规则配置
    /// </summary>
    public ILocalBillLinkParam LinkParam { get; }

    public LocalBillLinkService(IServiceProvider serviceProvider, string key, bool isExplain = false)
    {
        ServiceProvider = serviceProvider;
        // 初始化就需要获取LinkParam
        // 暂时先自己创建类的方式
        LinkParam = GetLocalBillLinkParam(key, isExplain);
    }

    /// <summary>
    /// 根据key获取ILocalBillLinkParam
    /// </summary>
    /// <param name="key"></param>
    /// <param name="isExplain"></param>
    /// <returns></returns>
    private ILocalBillLinkParam GetLocalBillLinkParam(string key, bool isExplain)
    {
        if (LinkTypes == null)
        {
            // 指定要查找的接口类型
            Type interfaceType = typeof(ILocalBillLinkParam);

            // 获取当前程序集中所有类型
            var types = Assembly.GetExecutingAssembly().GetTypes();

            // 查找所有实现了指定接口的类
            var ts = types.Where(t => t.IsClass && interfaceType.IsAssignableFrom(t)).ToList();
            LinkTypes = new Dictionary<string, Type>();
            foreach (Type type in ts)
            {
                if (type.IsAbstract) continue;
                if (type == typeof(LocalBillLinkParamBase)) continue;
                var instance = (ILocalBillLinkParam)Activator.CreateInstance(type);
                if (instance == null) continue;
                LinkTypes.Add(instance.Key, type);
            }
        }

        if (!LinkTypes.ContainsKey(key)) throw Oops.Bah(L.Text["没有找到对应的ILocalBillLinkParam"]);
        var localBillLinkParam = (ILocalBillLinkParam)Activator.CreateInstance(LinkTypes[key]);

        if (isExplain) return localBillLinkParam;
        
        var settingService = App.GetService<PdaLocalBillConfigService>(ServiceProvider);
        localBillLinkParam = settingService.GetLocalBillLinkParamExplain(localBillLinkParam).Result;

        return localBillLinkParam;
    }
}