using Furion.Localization;
using Neuz.Application.ExternalSystem;
using Neuz.Application.ExternalSystem.Dto;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库存调整单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkAdjustment", Order = 100)]
public class StkAdjustmentService : StkBaseBillService<StkAdjustment>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkAdjustment);

    /// <summary>
    /// 库存调整单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkAdjustmentService(IServiceProvider serviceProvider, SqlSugarRepository<StkAdjustment> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_WhAreaNumber",
            "Entries_WhAreaName",
            "Entries_WhLocNumber",
            "Entries_WhLocName",
            "Entries_Qty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkAdjustment entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkAdjustment entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkAdjustment entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
        var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkAdjustment>(EntityName).GetAwaiter()
            .GetResult().FirstOrDefault(u => u.Item1.Number == entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否为0
            if (entry.Qty == 0) throw Oops.Bah(StkErrorCode.Stk1014);
            // 判断库区是否已填
            if (entry.WhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1005);
            // 判断库位是否已填
            if (entry.WhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1006);
            // 判断调整方向为增加，数量是否小于0
            if (billTypeExt.Item2.Direction == StkAdjustmentDirection.Increase && entry.Qty < 0)
                throw Oops.Bah(StkErrorCode.StkAdjustment1001, entity.BillType);
            // 判断调整方向为减少，数量是否大于0
            if (billTypeExt.Item2.Direction == StkAdjustmentDirection.Decrease && entry.Qty > 0)
                throw Oops.Bah(StkErrorCode.StkAdjustment1002, entity.BillType);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (Math.Abs(entry.Qty) != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }

        // 单据类型必填判断
        switch (entity.BillType)
        {
            case "QTRKD01_SYS": // 标准其他入库单
                if (entity.DepartmentId == null)
                    throw Oops.Bah(L.Text["部门不能为空"]);
                break;
            case "QTCKD01_SYS": // 标准其他出库单
                if (string.IsNullOrEmpty(entity.OutType))
                    throw Oops.Bah(L.Text["出库类型不能为空"]);
                if (entity.DepartmentId == null)
                    throw Oops.Bah(L.Text["部门不能为空"]);
                break;
            case "QTCKD02_SYS": // 资产出库
                if (entity.DepartmentId == null)
                    throw Oops.Bah(L.Text["部门不能为空"]);
                break;
            case "QTCKD06_SYS": // 现场安装
                if (string.IsNullOrEmpty(entity.ContractProject))
                    throw Oops.Bah(L.Text["合同项目名称不能为空"]);
                if (entity.CustomerId == null)
                    throw Oops.Bah(L.Text["客户不能为空"]);
                break;
            case "003": // 研发领料单
                if (string.IsNullOrEmpty(entity.ContractProject))
                    throw Oops.Bah(L.Text["合同项目名称不能为空"]);
                if (entity.DepartmentId == null && entity.CustomerId == null)
                    throw Oops.Bah(L.Text["部门或客户不能为空"]);
                break;
            case "004": // 售后领料单
                if (string.IsNullOrEmpty(entity.ProjectName))
                    throw Oops.Bah(L.Text["项目名称不能为空"]);
                if (entity.DepartmentId == null && entity.CustomerId == null)
                    throw Oops.Bah(L.Text["部门或客户不能为空"]);
                break;
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkAdjustment entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkAdjustment entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkAdjustment entity)
    {
        return new List<long>();
    }

    protected override void OnAfterAudit(StkAdjustment entity)
    {
        base.OnAfterAudit(entity);

        // 任务库存数量处理（如果有则在此处调用）

        // 更新条码档案
        UpdateBarcodeInfo(entity);

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkAdjustment entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkAdjustment entity)
    {
        base.OnAfterUnAudit(entity);

        // 回滚条码档案
        if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
            BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

        // TODO：批号档案处理

        // 回滚库存更新
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkAdjustment entity)
    {
        var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
        var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkAdjustment>(EntityName).GetAwaiter()
            .GetResult().FirstOrDefault(u => u.Item1.Number == entity.BillType);

        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            // 操作数量类型 = 增加 ? 替换 : 减少
            var opQtyType = billTypeExt.Item2.Direction == StkAdjustmentDirection.Increase ? OpQtyType.Replace : OpQtyType.Decrease;
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = opQtyType,
                CurStatus = billTypeExt.Item2.Direction == StkAdjustmentDirection.Increase ? BdBarcodeStatus.In : BdBarcodeStatus.Out,
                BatchNo = barcodeEntry.BatchNo,
                ProduceDate = barcodeEntry.Barcode.ProduceDate,
                ExpiryDate = barcodeEntry.Barcode.ExpiryDate,
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = null,
                SrcWhLocId = null,
                DestWhAreaId = entry.WhAreaId,
                DestWhLocId = entry.WhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkAdjustment entity)
    {
        var changes = entity.Entries.SelectMany(u =>
        {
            var innerChanges = new List<StkInvChange>
            {
                new StkInvChange
                {
                    InvLogType = u.Qty > 0 ? StkInvLogType.AdjustmentPlus : StkInvLogType.AdjustmentMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                },
            };

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }

    /// <summary>
    /// 推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("push")]
    public Task<List<ExecResult>> Push(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = Rep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.Stk1029, entity.BillNo);

            var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
            var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkAdjustment>(EntityName).GetAwaiter().GetResult()
                .FirstOrDefault(u => u.Item1.Number == entity.BillType);

            if (string.IsNullOrEmpty(billTypeExt.Item2.PushSettingNumberToUse))
                throw Oops.Bah(StkErrorCode.Stk1030, entity.BillType);

            // 查询推送设置
            var pushSettingRep = ServiceProvider.GetService<SqlSugarRepository<EsSyncPushSetting>>();
            var pushSetting = pushSettingRep.GetFirst(u => u.Number == billTypeExt.Item2.PushSettingNumberToUse);

            var esType = pushSetting?.EsType;
            var settingNumber = pushSetting?.Number;

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var externalSyncService = ServiceProvider.GetService<ExternalSyncService>();
            var pushResults = externalSyncService.Push(new EsSyncPushInput2 { BillNo = entity.BillNo, SettingNumber = settingNumber, EsType = esType }).GetAwaiter().GetResult();
            var pushResult = pushResults.First();
            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            // 提交事务
            uow.Commit();

            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            return pushResult.Message;
        });

        return Task.FromResult(execResults);
    }
}