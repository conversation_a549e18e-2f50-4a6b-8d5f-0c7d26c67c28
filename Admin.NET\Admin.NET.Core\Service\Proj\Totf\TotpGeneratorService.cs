﻿using System.Security.Cryptography;

namespace Admin.NET.Core.Proj.Totf;

public class TotpGeneratorService:ITransient
{
    public TotpGeneratorService()
    {

    }
    // 生成随机Base32密钥（推荐长度16字节）
    public string GenerateSecretKey(int length = 16)
    {
        var rng = RandomNumberGenerator.Create();
        byte[] bytes = new byte[length];
        rng.GetBytes(bytes);
        return Base32Encode(bytes);
    }

    // 生成TOTP密码
    public string GenerateTotp(string secretKey, int digits = 6, int step = 30)
    {
        byte[] key = Base32Decode(secretKey);
        long counter = DateTimeOffset.UtcNow.ToUnixTimeSeconds() / step;
        byte[] counterBytes = BitConverter.GetBytes(counter);

        if (BitConverter.IsLittleEndian)
            Array.Reverse(counterBytes);

        using HMACSHA1 hmac = new HMACSHA1(key);
        byte[] hash = hmac.ComputeHash(counterBytes);

        int offset = hash[^1] & 0x0F;
        int binary =
            ((hash[offset] & 0x7F) << 24) |
            (hash[offset + 1] << 16) |
            (hash[offset + 2] << 8) |
            hash[offset + 3];

        int otp = binary % (int)Math.Pow(10, digits);
        return otp.ToString().PadLeft(digits, '0');
    }

    // 验证TOTP密码（允许时间偏差）
    public bool ValidateTotp(string secretKey, string code, int step = 30, int tolerance = 1)
    {
        for (int i = -tolerance; i <= tolerance; i++)
        {
            if (GenerateTotpWithOffset(secretKey, i * step) == code)
                return true;
        }
        return false;
    }

    private string GenerateTotpWithOffset(string secretKey, int timeOffset, int digits = 6, int step = 30)
    {
        byte[] key = Base32Decode(secretKey);
        long counter = (DateTimeOffset.UtcNow.ToUnixTimeSeconds() + timeOffset) / step;
        byte[] counterBytes = BitConverter.GetBytes(counter);

        if (BitConverter.IsLittleEndian)
            Array.Reverse(counterBytes);

        using HMACSHA1 hmac = new HMACSHA1(key);
        byte[] hash = hmac.ComputeHash(counterBytes);

        int offset = hash[^1] & 0x0F;
        int binary =
            ((hash[offset] & 0x7F) << 24) |
            (hash[offset + 1] << 16) |
            (hash[offset + 2] << 8) |
            hash[offset + 3];

        int otp = binary % (int)Math.Pow(10, digits);
        return otp.ToString().PadLeft(digits, '0');
    }

    // Base32编码
    private string Base32Encode(byte[] data)
    {
        const string alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        StringBuilder result = new StringBuilder((data.Length * 8 + 4) / 5);

        int buffer = 0, next = 0, bitsLeft = 0;
        foreach (byte b in data)
        {
            buffer = (buffer << 8) | b;
            bitsLeft += 8;
            while (bitsLeft >= 5)
            {
                bitsLeft -= 5;
                int index = (buffer >> bitsLeft) & 0x1F;
                result.Append(alphabet[index]);
            }
        }

        if (bitsLeft > 0)
        {
            buffer <<= (5 - bitsLeft);
            int index = buffer & 0x1F;
            result.Append(alphabet[index]);
        }

        return result.ToString();
    }

    // Base32解码
    private byte[] Base32Decode(string input)
    {
        const string alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        input = input.TrimEnd('=').ToUpper();
        byte[] output = new byte[input.Length * 5 / 8];

        int buffer = 0, next = 0, bitsLeft = 0;
        foreach (char c in input)
        {
            int value = alphabet.IndexOf(c);
            if (value < 0) throw new ArgumentException("Invalid Base32 character");

            buffer = (buffer << 5) | value;
            bitsLeft += 5;
            if (bitsLeft >= 8)
            {
                bitsLeft -= 8;
                output[next++] = (byte)((buffer >> bitsLeft) & 0xFF);
            }
        }
        return output;
    }
}