﻿namespace Neuz.Application.Pda.Dto;

public class PdaTempStoreDto
{

}

/// <summary>
/// 新增单据暂存数据
/// </summary>
public class PdaNewBillTempStoreInput
{
    /// <summary>
    /// 事务Id
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 模型Key
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 暂存描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 暂存数据
/// </summary>
public class TempStoreInput
{
    /// <summary>
    /// 
    /// </summary>
    public long Id { get; set; }
}