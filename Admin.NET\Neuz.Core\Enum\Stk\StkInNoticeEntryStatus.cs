﻿namespace Neuz.Core.Enum;

/// <summary>
/// 入库通知单明细状态
/// </summary>
public enum StkInNoticeEntryStatus
{
    /// <summary>
    /// 未处理
    /// </summary>
    [Description("未处理"), Theme("info")]
    UnHandle = 0,

    /// <summary>
    /// 处理中
    /// </summary>
    [Description("处理中"), Theme("warning")]
    Handling = 1,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成"), Theme("success")]
    Finish = 9999,
}