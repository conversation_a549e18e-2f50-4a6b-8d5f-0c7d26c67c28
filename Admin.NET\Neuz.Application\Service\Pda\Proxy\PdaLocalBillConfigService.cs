using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.Link;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Core.Entity.Pda;

namespace Neuz.Application.Pda.Proxy;

/// <summary>
/// PDA 配置Api
/// </summary>
[ApiDescriptionSettings("手持终端设置", Name = "PdaLocalBillConfig", Order = 300)]
public partial class PdaLocalBillConfigService : IDynamicApiController, ITransient
{
    private IServiceProvider ServiceProvider { get; }

    private SysCacheService SysCacheService { get; }

    private SqlSugarRepository<PdaLocalBillConfig> Rep { get; }

    /// <summary>
    /// 暂存类型Key
    /// </summary>
    private string ModelTypesKey { get; } = "PdaLocalBillModelType";

    /// <summary>
    /// 暂存配置Key
    /// </summary>
    private string ModelSettingKey { get; } = "PdaLocalBillModelSetting";

    /// <summary>
    /// 锁Key
    /// </summary>
    private string LockKey { get; } = "PdaLocalBillModelSettingLock";

    public PdaLocalBillConfigService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        SysCacheService = App.GetService<SysCacheService>(ServiceProvider);
        Rep = App.GetService<SqlSugarRepository<PdaLocalBillConfig>>(ServiceProvider);
    }

    /// <summary>
    /// 获取继承自 PdaLocalBillModel 的所有模型
    /// </summary>
    /// <returns></returns>
    [HttpPost("getLocalBillModels")]
    public Dictionary<string, PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>> GetLocalBillModels()
    {
        // 先判断缓存是否存在
        var bills = SysCacheService.Get<Dictionary<string, PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>>>(ModelTypesKey);
        if (bills != null) return bills;

        // 获取泛型类型定义
        var genericBaseTypeDefinition = typeof(PdaLocalBillModelBillBase<,>);
        var types = GetInheritedClassesFromGenericTypeDefinition(genericBaseTypeDefinition);
        Dictionary<string, PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>> models = new();
        foreach (var type in types)
        {
            var instance = (PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>)Activator.CreateInstance(type, ServiceProvider);
            if (instance != null)
            {
                models.Add(instance.Key, instance);
            }
        }

        SysCacheService.Set(ModelTypesKey, models);
        return models;
    }

    /// <summary>
    /// 获取单个模型
    /// </summary>
    /// <returns></returns>
    [HttpPost("getLocalBillModel")]
    public async Task<PdaLocalBillConfigGetOutput> GetLocalBillModel(string key)
    {
        using (await LockManager.AcquireLockAsync(LockKey))
        {
            var bills = SysCacheService.Get<Dictionary<string, PdaLocalBillConfigGetOutput>>(ModelSettingKey);
            if (bills != null)
            {
                if (bills.TryGetValue(key, out PdaLocalBillConfigGetOutput cacheModel))
                {
                    return cacheModel.Adapt<PdaLocalBillConfigGetOutput>();
                }
            }
            else
            {
                bills = new Dictionary<string, PdaLocalBillConfigGetOutput>();
            }

            // 判断key是否有效
            var models = GetLocalBillModels();
            if (!models.TryGetValue(key, out PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData> model)) throw Oops.Bah($"PdaLocalBillModelBillBase不存在Key[{key}]");

            // 查询数据库设置存不存在
            // 查询key是否存在数据库
            var setting = await Rep.AsQueryable().FirstAsync(r => r.Key == key);

            // 如果暂存不存在
            var output = new PdaLocalBillConfigGetOutput()
            {
                Key = key,
                Name = $"{model.Config.BillSchema.BillLink.SourceTitle}_{model.Config.BillSchema.BillLink.DestTitle}",
                BillParams = setting != null ? JSON.Deserialize<PdaLocalBillModelParams>(setting.BillParams) : model.Config.BillParams,
                BillSchema = setting != null ? JSON.Deserialize<PdaLocalBillSchemaBase>(setting.BillSchema) : model.Config.BillSchema,
                LinkParam = setting != null ? JSON.Deserialize<LocalBillLinkParamBase>(setting.LinkParam) : new LocalBillLinkService(ServiceProvider, key, true).LinkParam,
            };

            bills.Add(key, output);
            SysCacheService.Set(ModelSettingKey, bills);

            return output.Adapt<PdaLocalBillConfigGetOutput>();
        }
    }

    /// <summary>
    /// 保存单个模型
    /// </summary>
    /// <returns></returns>
    [HttpPost("saveLocalBillModel")]
    public async Task SaveLocalBillModel(PdaLocalBillConfigSaveInput input)
    {
        // 查询key是否存在数据库
        var setting = await Rep.AsQueryable().FirstAsync(r => r.Key == input.Key);
        if (setting == null)
        {
            setting = new PdaLocalBillConfig()
            {
                Key = input.Key
            };
        }

        setting.BillParams = JsonConvert.SerializeObject(input.BillParams);
        setting.BillSchema = JsonConvert.SerializeObject(input.BillSchema);
        setting.LinkParam = JsonConvert.SerializeObject(input.LinkParam);
        await Rep.InsertOrUpdateAsync(setting);

        // 删除缓存
        SysCacheService.Remove(ModelSettingKey);
    }

    /// <summary>
    /// 删除单个模型
    /// </summary>
    /// <returns></returns>
    [HttpPost("delLocalBillModel")]
    public async Task DelLocalBillModel(string key)
    {
        // 查询key是否存在数据库
        var setting = await Rep.AsQueryable().FirstAsync(r => r.Key == key);
        if (setting != null)
        {
            await Rep.DeleteAsync(setting);
        }

        // 删除缓存
        SysCacheService.Remove(ModelSettingKey);
    }
    
    /// <summary>
    /// 返回配置的IPdaModel
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<IPdaModel> GetPdaModelExplain(IPdaModel model)
    {
        var setting = await GetLocalBillModel(model.Key);
        if (model is not PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData> newModel) return model;

        newModel.Config.BillParams = setting.BillParams;
        newModel.Config.BillSchema = setting.BillSchema;

        return newModel;
    }

    /// <summary>
    /// 返回LocalBillLink
    /// </summary>
    /// <param name="localBillLinkParam"></param>
    /// <returns></returns>
    public async Task<ILocalBillLinkParam> GetLocalBillLinkParamExplain(ILocalBillLinkParam localBillLinkParam)
    {
        var setting = await GetLocalBillModel(localBillLinkParam.Key);
        if (setting == null) return localBillLinkParam;

        return setting.LinkParam;
    }

    #region 私有方法

    /// <summary>
    /// 获取所有继承自指定泛型类型定义（不固定泛型参数）的类
    /// </summary>
    /// <param name="genericTypeDefinition">泛型类型定义，如 typeof(PdaLocalBillModelBillBase<,>)</param>
    /// <param name="assembly">要搜索的程序集，默认为当前程序集</param>
    /// <returns>继承自该泛型类型定义的所有非抽象类</returns>
    private Type[] GetInheritedClassesFromGenericTypeDefinition(Type genericTypeDefinition, Assembly assembly = null)
    {
        if (!genericTypeDefinition.IsGenericTypeDefinition)
        {
            throw new ArgumentException("提供的类型必须是泛型类型定义（如 typeof(PdaLocalBillModelBillBase<,>)）", nameof(genericTypeDefinition));
        }

        assembly ??= Assembly.GetExecutingAssembly();

        return assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract && IsSubclassOfGenericTypeDefinition(t, genericTypeDefinition))
            .ToArray();
    }

    /// <summary>
    /// 检查类型是否继承自指定的泛型类型定义
    /// </summary>
    private bool IsSubclassOfGenericTypeDefinition(Type type, Type genericTypeDefinition)
    {
        // 检查类型本身
        if (type.IsGenericType && type.GetGenericTypeDefinition() == genericTypeDefinition) return true;

        // 检查基类
        if (type.BaseType == null || type.BaseType == typeof(object)) return false;

        if (type.BaseType.IsGenericType && type.BaseType.GetGenericTypeDefinition() == genericTypeDefinition) return true;

        return IsSubclassOfGenericTypeDefinition(type.BaseType, genericTypeDefinition);
    }

    #endregion
}