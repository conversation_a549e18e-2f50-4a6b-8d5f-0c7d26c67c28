﻿namespace Neuz.Core.Entity;

/// <summary>
/// 角色货主权限
/// </summary>
[SugarTable(null, "角色货主权限")]
public class StkRoleOwner : EntityBaseId
{
    /// <summary>
    /// 角色Id
    /// </summary>
    [SugarColumn(ColumnDescription = "角色Id")]
    public long RoleId { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }
}