﻿namespace Neuz.Core.Entity;

/// <summary>
/// 角色仓库权限
/// </summary>
[SugarTable(null, "角色仓库权限")]
public class StkRoleWarehouse : EntityBaseId
{
    /// <summary>
    /// 角色Id
    /// </summary>
    [SugarColumn(ColumnDescription = "角色Id")]
    public long RoleId { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }
}