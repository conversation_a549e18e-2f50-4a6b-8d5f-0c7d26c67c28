﻿namespace Neuz.Application;

/// <summary>
/// Web端表格配置服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StgWebTable", Order = 100)]
public class StgWebTableService : IDynamicApiController
{
    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 当前用户Id
    /// </summary>
    protected long CurUserId => long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");

    /// <summary>
    /// 仓储服务
    /// </summary>
    protected SqlSugarRepository<StgWebTable> Rep { get; }

    /// <summary>
    /// Web端表格配置服务构造函数
    /// </summary>
    /// <param name="serviceProvider"></param>
    public StgWebTableService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        Rep = App.GetRequiredService<SqlSugarRepository<StgWebTable>>(serviceProvider);
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("saveConfig")]
    public async Task SaveConfig(StgWebTableInput input)
    {
        var hasChange = await Rep.AsUpdateable().SetColumns(u => new StgWebTable { Json = input.Json })
            .Where(u => u.TableId == input.TableId && u.UserId == CurUserId)
            .ExecuteCommandHasChangeAsync();

        if (hasChange) return;

        await Rep.InsertAsync(new StgWebTable { TableId = input.TableId, UserId = CurUserId, Json = input.Json });
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("getConfig")]
    public async Task<string> GetConfig(StringIdInput input)
    {
        var stgWebTable = await Rep.GetFirstAsync(r => r.TableId == input.Id && r.UserId == CurUserId);
        return stgWebTable == null || string.IsNullOrEmpty(stgWebTable.Json) ? null : stgWebTable.Json;
    }

    /// <summary>
    /// 保存搜素配置
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("saveSearchConfig")]
    public async Task SaveSearchConfig(StgWebTableSearchInput input)
    {
        var hasChange = await Rep.AsUpdateable().SetColumns(u => new StgWebTable { SearchJson = input.SearchJson })
            .Where(u => u.TableId == input.TableId && u.UserId == CurUserId)
            .ExecuteCommandHasChangeAsync();

        if (hasChange) return;

        await Rep.InsertAsync(new StgWebTable { TableId = input.TableId, UserId = CurUserId, SearchJson = input.SearchJson });
    }

    /// <summary>
    /// 获取搜素配置
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("getSearchConfig")]
    public async Task<string> GetSearchConfig(StringIdInput input)
    {
        var stgWebTable = await Rep.GetFirstAsync(r => r.TableId == input.Id && r.UserId == CurUserId);
        return stgWebTable == null || string.IsNullOrEmpty(stgWebTable.SearchJson) ? null : stgWebTable.SearchJson;
    }

    /// <summary>
    /// 搜素配置恢复默认
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("searchConfigRestoreDefault")]
    public async Task SearchConfigRestoreDefault(StringIdInput input)
    {
        var stgWebTable = await Rep.GetFirstAsync(r => r.TableId == input.Id && r.UserId == CurUserId);
        if (stgWebTable != null)
        {
            stgWebTable.SearchJson = null;
            await Rep.UpdateAsync(stgWebTable);
        }
    }
}