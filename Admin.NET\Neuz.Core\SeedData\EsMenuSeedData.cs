﻿namespace Neuz.Core.SeedData;

[IgnoreUpdateSeed]
public class EsMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu { Id = 1360000010011, Pid = 0, Title = "外部系统", Path = "/es", Name = "es", Component = "Layout", Icon = "ele-Guide", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2100 },
            new SysMenu { Id = 1360000010012, Pid = 1360000010011, Title = "拉取设置", Path = "/es/esSyncPullSetting", Name = "esSyncPullSetting", Component = "/business/es/esSyncPullSetting/index.vue", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1360000010013, Pid = 1360000010011, Title = "推送设置", Path = "/es/esSyncPushSetting", Name = "esSyncPushSetting", Component = "/business/es/esSyncPushSetting/index.vue", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 110 },
            new SysMenu { Id = 1360000010014, Pid = 1360000010011, Title = "拉取日志", Path = "/es/esSyncPullLog", Name = "esSyncPullLog", Component = "/business/es/esSyncPullLog/index.vue", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2023-11-08 00:00:00"), OrderNo = 120 },
            new SysMenu { Id = 1360000010015, Pid = 1360000010011, Title = "推送日志", Path = "/es/esSyncPushLog", Name = "esSyncPushLog", Component = "/business/es/esSyncPushLog/index.vue", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2023-11-08 00:00:00"), OrderNo = 130 },
        };
    }
}
