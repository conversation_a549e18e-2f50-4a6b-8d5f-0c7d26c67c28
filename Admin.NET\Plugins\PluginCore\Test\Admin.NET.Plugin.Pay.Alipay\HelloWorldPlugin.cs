
// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Admin.NET.Plugin.Pay.Alipay.Middlewares;
using Microsoft.AspNetCore.Builder;
using PluginCore.IPlugins;

namespace Admin.NET.Plugin.Pay.Alipay
{
    public class HelloWorldPlugin : BasePlugin, IStartupXPlugin, IWidgetPlugin
    {
        public override (bool IsSuccess, string Message) AfterEnable()
        {
            Console.WriteLine($"{nameof(HelloWorldPlugin)}: {nameof(AfterEnable)}");
            return base.AfterEnable();
        }

        public override (bool IsSuccess, string Message) BeforeDisable()
        {
            Console.WriteLine($"{nameof(HelloWorldPlugin)}: {nameof(BeforeDisable)}");
            return base.BeforeDisable();
        }

        public void ConfigureServices(IServiceCollection services)
        {

        }

        public void Configure(IApplicationBuilder app)
        {
            app.UseMiddleware<AlipaySayHelloMiddleware>();
        }

        public int ConfigureOrder
        {
            get
            {
                return 2;
            }
        }


        public int ConfigureServicesOrder
        {
            get
            {
                return 2;
            }
        }

        public async Task<string> Widget(string widgetKey, params string[] extraPars)
        {
            string rtnStr = null;
            if (widgetKey == "PluginCore.Admin.Footer")
            {
                if (extraPars != null)
                {
                    Console.WriteLine(string.Join(",", extraPars));
                }
                rtnStr = @"<div style=""border:1px solid green;width:300px;"">
                                <h3>HelloWorldPlugin 注入</h3>
                                <div>HelloWorldPlugin 挂件</div>
                           </div>";

            }

            return await Task.FromResult(rtnStr);
        }
    }
}
