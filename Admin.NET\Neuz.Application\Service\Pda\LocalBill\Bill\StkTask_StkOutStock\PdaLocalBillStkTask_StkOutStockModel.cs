﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.LocalBill.Link;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Scan.LocalBill;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill.StkTask_StkOutStock;

/// <summary>
/// 任务单->出库单
/// </summary>
public class PdaLocalBillStkTask_StkOutStockModel : PdaLocalBillModel<StkTask, StkOutStock, StkTaskEntry, StkOutStockEntry>
{
    public PdaLocalBillStkTask_StkOutStockModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkTask_StkOutStock";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "customerName",
                Caption = L.Text["客户"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdCustomerModel",
                    LookupDataKey = "ScanHead.Customer",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("CustomerId", "Id"),
                        new PdaLookupMapping("CustomerNumber", "Number"),
                        new PdaLookupMapping("CustomerName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "departmentName",
                Caption = L.Text["部门"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdDepartmentModel",
                    LookupDataKey = "ScanHead.Department",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DepartmentId", "Id"),
                        new PdaLookupMapping("DepartmentNumber", "Number"),
                        new PdaLookupMapping("DepartmentName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "employeeName",
                Caption = L.Text["领料人"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdEmployeeModel",
                    LookupDataKey = "ScanHead.Employee",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("EmployeeId", "Id"),
                        new PdaLookupMapping("EmployeeNumber", "Number"),
                        new PdaLookupMapping("EmployeeName", "Name"),
                    }
                }
            },
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkTask",
            SourceTitle = L.Text["任务单"],
            DestKey = "StkOutStock",
            DestTitle = L.Text["出库单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceQty = true,
            IsMultiSource = false
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkTask>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkTaskEntry>(ServiceProvider, u => u.SrcWhAreaId)
            .AddOwnerFilter<StkTaskEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .LeftJoin<StkOutNotice>((t1, t2) => t1.SrcBillId == t2.Id)
                .WhereIF(linkParam.SourceBillTypes.Count > 0, (t1, t2) => linkParam.SourceBillTypes.Contains(t1.SrcBillKey))
                .Where((t1, t2) => t1.DocumentStatus == DocumentStatus.Approve && t1.Status != StkTaskStatus.Finish &&
                                   (t1.BillNo.Contains(input.Keyword) || t1.SrcBillNo.Contains(input.Keyword) ||
                                    t2.EsBillNo.Contains(input.Keyword) || t2.OrderBillNo.Contains(input.Keyword)))
                .Where(t1 => SqlFunc.Subqueryable<StkTaskEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(r => r.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = $"{r.BillNo}[{r.SrcBillNo}]", SubTitle = r.Date.ToString("yyyy-MM-dd") }).ToArray();
        return result;
    }

    public override void RefreshShow(long tranId)
    {
        base.RefreshShow(tranId);
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => $"{r.SrcBillNo} - {r["EsSrcBillNo"]}").ToList();
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override async Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        // 获取Link
        // 按配置映射数据
        var bill = await SourceRep.AsQueryable()
            .LeftJoin<StkOutNotice>((t1, t2) => t1.SrcBillId == t2.Id)
            .Where((t1, t2) => t1.DocumentStatus == DocumentStatus.Approve)
            .Where((t1, t2) => t1.BillNo == input.BillNo || t1.SrcBillNo == input.BillNo || t2.EsBillNo == input.BillNo || t2.OrderBillNo == input.BillNo)
            .Select((t1, t2) => t1)
            .IncludeNavCol()
            .FirstAsync();
        if (bill == null) return null;
        return await GetBill(new PdaLocalBillLookSelectInput
        {
            Key = input.Key,
            TranId = input.TranId,
            LookupDataKey = null,
            LookupKey = null,
            Id = bill.Id + "",
            DetailIndex = null,
            IsLocalBill = false
        });
    }

    /// <inheritdoc/>
    protected override async Task<string> GetBillType(long tranId)
    {
        var billData = GetPdaData(tranId);
        // 通过单据类型获取目标编码
        // 假设只能有一种单据类型暂取第一个
        var billType = await Rep.Change<StkBillType>().AsQueryable()
            .LeftJoin<StkBillTypeNextEntry>((t1, t2) => t1.Id == t2.Id)
            .Where((t1, t2) => t1.Number == billData.ScanDetails[0].DestBillType && t1.EntityName == billData.ScanDetails[0].SrcBillKey && t2.NextEntityName == nameof(StkOutStock))
            .Select((t1, t2) => t2.NextBillTypeNumber)
            .FirstAsync();
        if (string.IsNullOrEmpty(billType))
            throw Oops.Bah(L.Text["没有找到单据类型配置: 源单类型[{0}], 目标单类型[{1}], 源单单据类型[{2}]", billData.ScanHead.DestBillType, nameof(StkOutStock), billData.ScanDetails[0].SrcBillKey]);
        return billType;
    }

    /// <summary>
    /// 判断领料人是否为空WWYLQD, SCYLQD源单类型
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="linkParam"></param>
    /// <param name="summaryDetails"></param>
    /// <returns></returns>
    protected override StkOutStock SetSubmitObj(long tranId, ILocalBillLinkParam linkParam, Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails)
    {
        var submitObj = base.SetSubmitObj(tranId, linkParam, summaryDetails);

        var pdaData = GetPdaData(tranId);
        if (new[] { "WWYLQD", "SCYLQD" }.Contains(pdaData.SourceHeads[0]["destbilltype"] + ""))
        {
            if (string.IsNullOrEmpty(pdaData.ScanHead["EmployeeId"] + "")) throw Oops.Bah("请选择领料人");
        }

        // 如果是SCBLTZD类型, 需要校验
        if (pdaData.SourceHeads.Count > 0 && pdaData.SourceHeads[0]["destbilltype"] + "" == "SCBLTZD")
        {
            var errors = new List<string>();
            var index = 0;
            foreach (PdaLocalBillScanDetail scanDetail in pdaData.ScanDetails)
            {
                index++;
                if (scanDetail.ScanQty != scanDetail.Qty)
                {
                    errors.Add($"物料[{scanDetail.MaterialNumber}], 任务数量: {scanDetail.Qty}, 扫描数量: {scanDetail.ScanQty}");
                }
            }

            if (errors.Count > 0) throw Oops.Bah(string.Join("\r\n", errors));
        }

        return submitObj;
    }
}