﻿using Furion.Localization;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库位库存服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkInventory", Order = 100)]
public partial class StkInventoryService : NeuzBaseService<StkInventory>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 库位库存服务构造函数
    /// </summary>
    public StkInventoryService(IServiceProvider serviceProvider, SqlSugarRepository<StkInventory> rep) : base(serviceProvider, rep)
    {
    }

    protected override IQueryDefine GetQueryDefine()
    {
        return new StkBillQueryDefine();
    }

    [NonAction]
    public override Task<long> AddAsync(StkInventory input)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task UpdateAsync(StkInventory input)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<List<ExecResult>> DeleteAsync(IdsInput input)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task Import(IFormFile file)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<FileContentResult> ImportTemplate()
    {
        throw new NotSupportedException();
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "MaterialNumber",
            "MaterialName",
            "MaterialSpecification",
            "BatchNo",
            "ProduceDate",
            "ExpiryDate",
            "Qty",
            "LockQty",
            "AvailableQty",
            "PreInQty",
            "UnitName",
            "WhAreaName",
            "WhAreaNumber",
            "WhLocNumber",
            "WhLocName",
            "ContainerNumber",
            "WarehouseNumber",
            "WarehouseName",
            "OwnerNumber",
            "OwnerName",
            "AuxPropValueNumber",
            "AuxPropValueName",
            "CreateTime",
            "UpdateTime",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="stkInvChange">库存变更信息</param>
    /// <param name="groupName">分组名称（给定一个名称，描述同一关联单据同一个操作的日志集合，用于同一关联单据的不同操作的日志回滚）</param>
    /// <param name="isNormal">是否为正常的库存更新，true: 正常的库存更新，false: 由反审核之类的场景发起的回滚库存更新，日志会写入 Rollback 结尾的数据表中</param>
    /// <returns>库存主键Id</returns>
    /// <exception cref="Exception"></exception>
    [NonAction]
    public long UpdateInventory(StkInvChange stkInvChange, string groupName, bool isNormal = true)
    {
        return UpdateInventory(new List<StkInvChange> { stkInvChange }, groupName, isNormal).First().Value;
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="stkInvChanges">库存变更信息集合</param>
    /// <param name="groupName">分组名称（给定一个名称，描述同一关联单据同一个操作的日志集合，用于同一关联单据的不同操作的日志回滚）</param>
    /// <param name="isNormal">是否为正常的库存更新，true: 正常的库存更新，false: 由反审核之类的场景发起的回滚库存更新，日志会写入 Rollback 结尾的数据表中</param>
    /// <returns>库存变更信息与库存主键Id映射</returns>
    /// <exception cref="Exception"></exception>
    [NonAction]
    public Dictionary<StkInvChange, long> UpdateInventory(List<StkInvChange> stkInvChanges, string groupName, bool isNormal = true)
    {
        // 库存变更信息与库存Id映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        // 库存变更执行结果
        var invChangeExecResultList = new List<InvChangeExecResult>();

        if (stkInvChanges == null || stkInvChanges.Count == 0) return invChangeMapping;

        try
        {
            // 校验库存变更参数
            var invRelInfo = ValidationParameterAndReturnEntity(stkInvChanges);

            // 空值处理
            foreach (var stkInvChange in stkInvChanges)
            {
                stkInvChange.BatchNo ??= "";
            }

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 加锁并查询库存集合
            var invList = stkInvChanges.Count > 2000 // 超过2000行的数据直接按物料查
                ? Rep.AsQueryable().Where(u => stkInvChanges.Any(p => p.MaterialId == u.MaterialId)).TranLock().ToList()
                : Rep.AsQueryable().Where(u => stkInvChanges.Any(p => p.MaterialId == u.MaterialId && p.BatchNo == u.BatchNo)).TranLock().ToList();

            // 加库存数量
            var qtyPlusResult = QtyPlus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(qtyPlusResult);

            // 加锁定数量
            var lockQtyPlusResult = LockQtyPlus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(lockQtyPlusResult);

            // 减锁定数量
            var lockQtyMinusResult = LockQtyMinus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(lockQtyMinusResult);

            // 减库存数量
            var qtyMinusResult = QtyMinus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(qtyMinusResult);

            // 加预入库数量
            var preInQtyPlusResult = PreInQtyPlus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(preInQtyPlusResult);

            // 减预入库数量
            var preInQtyMinusResult = PreInQtyMinus(stkInvChanges, invList, groupName, invRelInfo);
            invChangeExecResultList.Add(preInQtyMinusResult);

            // 添加库存变更信息与库存Id映射
            foreach (var mapping in invChangeExecResultList.Select(u => u.InvChangeAndInvIdMapping))
            {
                foreach (var item in mapping)
                    invChangeMapping.TryAdd(item.Key, mapping[item.Key]);
            }

            // 检查锁定数量是否大于库存数量
            CheckLockQtyOverQty(invList, invRelInfo);

            // 【所有】数量都为0的库存行，添加到移除列表中
            var removeInvList = invList.Where(u => u.Qty == 0 && u.LockQty == 0 && u.AvailableQty == 0 && u.PreInQty == 0).ToList();

            // 兜底检查，检查是否有小于0的数量，如果存在，表示上面的逻辑处理有问题
            var errInvList = invList.Where(u => u.Qty < 0 || u.LockQty < 0 || u.AvailableQty < 0 || u.PreInQty < 0).ToList();
            if (errInvList.Count > 0)
            {
                // TODO: 库存更新，可记录有问题 stkInvChanges 和 invList 到日志，后续可以写测试进行重现
                throw new SystemException(L.Text["内部计算逻辑错误：库存结果存在负数，请联系开发人员"]);
            }

            // 保存数据
            var storage = Rep.Context.Storageable(invList)
                .TranLock() // invList 是加锁查询的，这里也需要加锁查询，否则 MySql 下 SplitInsert 查询的结果快照版本而不是当前版本，从而导致可能出现的主键重复错误
                .SplitInsert(u => u.NotAny()).SplitUpdate(u => u.Any()).SplitDelete(u => removeInvList.Contains(u.Item))
                .ToStorage();
            storage.AsInsertable.ExecuteCommand();
            storage.AsUpdateable.ExecuteCommand();
            storage.AsDeleteable.ExecuteCommand();

            // 找出预入库数量大于0，且库位有数量容量限制的库位
            var qtyLimitWhLocIdList = stkInvChanges.Where(u => u.PreInQty > 0 && invRelInfo.WhLocs[u.WhLocId].QtyCapacity > 0)
                .Select(u => u.WhLocId).Distinct().ToList();
            // 校验库位的数量容量是否已超容量限制
            if (qtyLimitWhLocIdList.Count > 0)
            {
                // 查询库位库存数量集合
                var invQtyInfoList = Rep.Context.Queryable<StkInventory>()
                    .Where(u => qtyLimitWhLocIdList.Contains(u.WhLocId))
                    .GroupBy(u => new { u.WhLocId })
                    .Select(u => new { u.WhLocId, Qty = SqlFunc.AggregateSum(u.Qty), PreInQty = SqlFunc.AggregateSum(u.PreInQty) })
                    .ToList();

                foreach (var qtyLimitWhLocId in qtyLimitWhLocIdList)
                {
                    var whLoc = invRelInfo.WhLocs[qtyLimitWhLocId];
                    var whArea = invRelInfo.WhAreas[whLoc.WhAreaId];
                    var invQtyInfo = invQtyInfoList.FirstOrDefault(u => u.WhLocId == qtyLimitWhLocId);
                    // 如果数量容量 < 库位数量 + 预入库数量
                    if (whLoc.QtyCapacity < ((invQtyInfo?.Qty ?? 0) + (invQtyInfo?.PreInQty ?? 0)))
                        throw Oops.Bah(StkErrorCode.StkInventory1002, whArea.Number, whLoc.Number);
                }
            }

            // 日志列表
            var invLogList = invChangeExecResultList.SelectMany(u => u.AddInvLogList).ToList();
            var invLockLogList = invChangeExecResultList.SelectMany(u => u.AddInvLockLogList).ToList();
            var invPreInLogList = invChangeExecResultList.SelectMany(u => u.AddInvPreInLogList).ToList();

            if (isNormal)
            {
                // 写入正常的日志表，使用 BulkCopy
                Rep.Context.Fastest<StkInventoryLog>().BulkCopy(invLogList);
                Rep.Context.Fastest<StkInventoryLockLog>().BulkCopy(invLockLogList);
                Rep.Context.Fastest<StkInventoryPreInLog>().BulkCopy(invPreInLogList);
            }
            else
            {
                // 写入回滚的日志表，使用 BulkCopy
                Rep.Context.Fastest<StkInventoryLogRollback>().BulkCopy(invLogList.Select(u => u.Adapt<StkInventoryLogRollback>()).ToList());
                Rep.Context.Fastest<StkInventoryLockLogRollback>().BulkCopy(invLockLogList.Select(u => u.Adapt<StkInventoryLockLogRollback>()).ToList());
                Rep.Context.Fastest<StkInventoryPreInLogRollback>().BulkCopy(invPreInLogList.Select(u => u.Adapt<StkInventoryPreInLogRollback>()).ToList());
            }

            // 提交事务
            uow.Commit();

            return invChangeMapping;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 检查锁定数量是否大于库存数量
    /// </summary>
    private static void CheckLockQtyOverQty(List<StkInventory> invList, InvRelInfo invRelInfo)
    {
        var errInv = invList.FirstOrDefault(u => u.Qty < u.LockQty);
        if (errInv == null) return;

        throw Oops.Bah(GetInvErrMessage(errInv, invRelInfo) + $"Q[{errInv.Qty}]L[{errInv.LockQty}]" + L.Text["库存数量不足以锁定"]);
    }

    /// <summary>
    /// 查找匹配的库存数据行（正常情况下，最多只有一行）
    /// </summary>
    /// <param name="change">库存变更对象</param>
    /// <param name="invList">物料相关的库存集合</param>
    /// <param name="whLoc">库存变更中库位的实体</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private static List<StkInventory> GetMatchInvList(StkInvChange change, List<StkInventory> invList, BdWhLoc whLoc)
    {
        var matchInvList = change.InventoryId != null
            ? new List<StkInventory> { invList.FirstOrDefault(u => u.Id == change.InventoryId) ?? throw new Exception($"InventoryId: {change.InventoryId} 不存在") }
            : invList.Where(u => u.MaterialId == change.MaterialId && u.BatchNo == change.BatchNo &&
                                 u.ProduceDate == change.ProduceDate && u.ExpiryDate == change.ExpiryDate &&
                                 u.WhAreaId == whLoc.WhAreaId && u.WhLocId == change.WhLocId &&
                                 u.ContainerId == change.ContainerId && u.OwnerId == change.OwnerId &&
                                 u.AuxPropValueId == change.AuxPropValueId)
                .ToList();
        return matchInvList;
    }

    private static string GetInvErrMessage(StkInventory inv, InvRelInfo invRelInfo)
    {
        var material = invRelInfo.Materials[inv.MaterialId];
        var whLoc = invRelInfo.WhLocs[inv.WhLocId];
        var whWhArea = invRelInfo.WhAreas[whLoc.WhAreaId];
        var warehouse = invRelInfo.Warehouses[whLoc.WarehouseId];
        var container = inv.ContainerId == null ? null : invRelInfo.Containers[inv.ContainerId.Value];
        var owner = invRelInfo.Owners[inv.OwnerId];
        var auxPropValue = inv.AuxPropValueId == null ? null : invRelInfo.AuxPropValues[inv.AuxPropValueId.Value];

        var sb = new StringBuilder();
        sb.Append($"{L.Text["物料"]}[{material.Number}]");
        if (!string.IsNullOrEmpty(inv.BatchNo))
            sb.Append($" {L.Text["批号"]}[{inv.BatchNo}]");
        if (inv.ProduceDate != null)
            sb.Append($" {L.Text["生产日期"]}[{inv.ProduceDate}]");
        if (inv.ExpiryDate != null)
            sb.Append($" {L.Text["有效期至"]}[{inv.ExpiryDate}]");
        if (container != null)
            sb.Append($" {L.Text["容器"]}[{container.Number}]");
        sb.Append($" {L.Text["库区"]}[{whWhArea.Number}]");
        sb.Append($" {L.Text["库位"]}[{whLoc.Number}]");
        sb.Append($" {L.Text["仓库"]}[{warehouse.Number}]");
        sb.Append($" {L.Text["货主"]}[{owner.Number}]");
        if (auxPropValue != null)
            sb.Append($" {L.Text["辅助属性值"]}[{auxPropValue.Number}]");

        sb.Append(" ");

        return sb.ToString();
    }

    private static string GetInvChangeErrMessage(StkInvChange change, InvRelInfo invRelInfo)
    {
        var material = invRelInfo.Materials[change.MaterialId];
        var whLoc = invRelInfo.WhLocs[change.WhLocId];
        var whWhArea = invRelInfo.WhAreas[whLoc.WhAreaId];
        var warehouse = invRelInfo.Warehouses[whLoc.WarehouseId];
        var container = change.ContainerId == null ? null : invRelInfo.Containers[change.ContainerId.Value];
        var owner = invRelInfo.Owners[change.OwnerId];
        var auxPropValue = change.AuxPropValueId == null ? null : invRelInfo.AuxPropValues[change.AuxPropValueId.Value];

        var sb = new StringBuilder();
        if (!string.IsNullOrEmpty(change.RelBillNo))
            sb.Append($"{L.Text["单号"]}: {change.RelBillNo} ");
        if (change.RelSeq != null)
            sb.Append($"{L.Text["序号"]}: {change.RelSeq} ");
        sb.Append($"{L.Text["物料"]}[{material.Number}]");
        if (!string.IsNullOrEmpty(change.BatchNo))
            sb.Append($" {L.Text["批号"]}[{change.BatchNo}]");
        if (change.ProduceDate != null)
            sb.Append($" {L.Text["生产日期"]}[{change.ProduceDate}]");
        if (change.ExpiryDate != null)
            sb.Append($" {L.Text["有效期至"]}[{change.ExpiryDate}]");
        if (container != null)
            sb.Append($" {L.Text["容器"]}[{container.Number}]");
        sb.Append($" {L.Text["库区"]}[{whWhArea.Number}]");
        sb.Append($" {L.Text["库位"]}[{whLoc.Number}]");
        sb.Append($" {L.Text["仓库"]}[{warehouse.Number}]");
        sb.Append($" {L.Text["货主"]}[{owner.Number}]");
        if (auxPropValue != null)
            sb.Append($" {L.Text["辅助属性值"]}[{auxPropValue.Number}]");

        sb.Append(" ");

        return sb.ToString();
    }

    /// <summary>
    /// 加库存数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult QtyPlus(List<StkInvChange> stkInvChanges, List<StkInventory> invList,
        string groupName, InvRelInfo invRelInfo)
    {
        // 数量 > 0
        var handleChanges = stkInvChanges.Where(u => u.Qty > 0).ToList();

        var addInvList = new List<StkInventory>();
        var updateInvList = new List<StkInventory>();
        var invLogList = new List<StkInventoryLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];
            var sourceWhLoc = change.SourceWhLocId == null ? null : invRelInfo.WhLocs[change.SourceWhLocId.Value];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 如果库存不存在
            if (change.Qty > 0 && matchInvList.Count == 0)
            {
                // 创建一行数量为0的库存数据
                var inv = CreateZeroQtyStkInventory(change, whLoc);
                matchInvList.Add(inv);
                invList.Add(inv);
                addInvList.Add(inv);
            }

            // 未处理的绝对值数量
            var unHandleAbsQty = Math.Abs(change.Qty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            StkInventory firstMatchInv = null;
            foreach (var matchInv in matchInvList)
            {
                firstMatchInv ??= matchInv.Adapt<StkInventory>();
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                var curAbsQty = unHandleAbsQty;
                var curQty = curAbsQty; // 正数
                unHandleAbsQty -= curAbsQty;
                matchInv.Qty += curQty;
                invLogList.Add(CreateStkInventoryLog(change, groupName, curQty, whLoc, sourceWhLoc));
                updateInvList.Add(matchInv);

                // 更新可用数量
                matchInv.AvailableQty = CalcAvailableQty(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsQty == 0)
                    break;
            }

            if (unHandleAbsQty > 0)
                throw Oops.Bah(GetInvChangeErrMessage(change, invRelInfo) + $"Q[{firstMatchInv?.Qty ?? 0}]L[{firstMatchInv?.LockQty ?? 0}]C[{change.Qty}]" + L.Text["库存数量不足"]);
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            AddInvList = addInvList,
            UpdateInvList = updateInvList,
            AddInvLogList = invLogList,
        };
    }

    /// <summary>
    /// 减库存数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult QtyMinus(List<StkInvChange> stkInvChanges, List<StkInventory> invList, string groupName, InvRelInfo invRelInfo)
    {
        // 数量 < 0
        var handleChanges = stkInvChanges.Where(u => u.Qty < 0).ToList();

        var updateInvList = new List<StkInventory>();
        var invLogList = new List<StkInventoryLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];
            var sourceWhLoc = change.SourceWhLocId == null ? null : invRelInfo.WhLocs[change.SourceWhLocId.Value];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 未处理的绝对值数量
            var unHandleAbsQty = Math.Abs(change.Qty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            StkInventory firstMatchInv = null;
            foreach (var matchInv in matchInvList)
            {
                firstMatchInv ??= matchInv.Adapt<StkInventory>();
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                var curAbsQty = Math.Min(matchInv.Qty - matchInv.LockQty, unHandleAbsQty);
                var curQty = -curAbsQty; // 负数
                unHandleAbsQty -= curAbsQty;
                matchInv.Qty += curQty;
                invLogList.Add(CreateStkInventoryLog(change, groupName, curQty, whLoc, sourceWhLoc));
                updateInvList.Add(matchInv);

                // 更新可用数量
                matchInv.AvailableQty = CalcAvailableQty(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsQty == 0)
                    break;
            }

            if (unHandleAbsQty > 0)
                throw Oops.Bah(GetInvChangeErrMessage(change, invRelInfo) + $"Q[{firstMatchInv?.Qty ?? 0}]L[{firstMatchInv?.LockQty ?? 0}]C[{change.Qty}]" + L.Text["库存数量不足"]);
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            UpdateInvList = updateInvList,
            AddInvLogList = invLogList,
        };
    }

    /// <summary>
    /// 加锁定数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult LockQtyPlus(List<StkInvChange> stkInvChanges, List<StkInventory> invList, string groupName,
        InvRelInfo invRelInfo)
    {
        // 锁定数量 > 0
        var handleChanges = stkInvChanges.Where(u => u.LockQty > 0).ToList();

        var updateInvList = new List<StkInventory>();
        var invLockLogList = new List<StkInventoryLockLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 未处理的绝对值数量
            var unHandleAbsLockQty = Math.Abs(change.LockQty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            foreach (var matchInv in matchInvList)
            {
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                // 直接使用 unHandleAbsLockQty，在所有变更处理完后通过 CheckLockQtyOverQty 校验
                var curAbsLockQty = unHandleAbsLockQty; // Math.Min(matchInv.Qty - matchInv.LockQty, unHandleAbsLockQty);
                var curLockQty = curAbsLockQty; // 正数
                unHandleAbsLockQty -= curAbsLockQty;
                matchInv.LockQty += curLockQty;
                invLockLogList.Add(CreateStkInventoryLockLog(change, groupName, curLockQty, whLoc));
                updateInvList.Add(matchInv);

                // 更新可用数量
                matchInv.AvailableQty = CalcAvailableQty(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsLockQty == 0)
                    break;
            }

            // 在所有变更处理完后通过 CheckLockQtyOverQty 校验
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            UpdateInvList = updateInvList,
            AddInvLockLogList = invLockLogList,
        };
    }

    /// <summary>
    /// 减锁定数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult LockQtyMinus(List<StkInvChange> stkInvChanges, List<StkInventory> invList, string groupName,
        InvRelInfo invRelInfo)
    {
        // 锁定数量 < 0
        var handleChanges = stkInvChanges.Where(u => u.LockQty < 0).ToList();

        var updateInvList = new List<StkInventory>();
        var invLockLogList = new List<StkInventoryLockLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 未处理的绝对值数量
            var unHandleAbsLockQty = Math.Abs(change.LockQty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            StkInventory firstMatchInv = null;
            foreach (var matchInv in matchInvList)
            {
                firstMatchInv ??= matchInv.Adapt<StkInventory>();
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                // 锁定数量 < 0，减锁定数量
                if (change.LockQty < 0)
                {
                    var curAbsLockQty = Math.Min(matchInv.LockQty, unHandleAbsLockQty);
                    var curLockQty = -curAbsLockQty; // 负数
                    unHandleAbsLockQty -= curAbsLockQty;
                    matchInv.LockQty += curLockQty;
                    invLockLogList.Add(CreateStkInventoryLockLog(change, groupName, curLockQty, whLoc));
                    updateInvList.Add(matchInv);
                }

                // 更新可用数量
                matchInv.AvailableQty = CalcAvailableQty(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsLockQty == 0)
                    break;
            }

            if (unHandleAbsLockQty > 0)
                throw Oops.Bah(GetInvChangeErrMessage(change, invRelInfo) + $"L[{firstMatchInv?.LockQty ?? 0}]C[{change.LockQty}]" + L.Text["锁定数量不足"]);
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            UpdateInvList = updateInvList,
            AddInvLockLogList = invLockLogList,
        };
    }

    /// <summary>
    /// 加预入库数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult PreInQtyPlus(List<StkInvChange> stkInvChanges, List<StkInventory> invList, string groupName,
        InvRelInfo invRelInfo)
    {
        // 预入库数量 > 0
        var handleChanges = stkInvChanges.Where(u => u.PreInQty > 0).ToList();

        var addInvList = new List<StkInventory>();
        var updateInvList = new List<StkInventory>();
        var invPreInLogList = new List<StkInventoryPreInLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 如果库存不存在
            if (change.PreInQty > 0 && matchInvList.Count == 0)
            {
                // 创建一行数量为0的库存数据
                var inv = CreateZeroQtyStkInventory(change, whLoc);
                matchInvList.Add(inv);
                invList.Add(inv);
                addInvList.Add(inv);
            }

            // 未处理的绝对值数量
            var unHandleAbsPreInQty = Math.Abs(change.PreInQty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            StkInventory firstMatchInv = null;
            foreach (var matchInv in matchInvList)
            {
                firstMatchInv ??= matchInv.Adapt<StkInventory>();
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                // 预入库数量计算
                var curAbsPreInQty = unHandleAbsPreInQty;
                var curPreInQty = curAbsPreInQty; // 正数
                unHandleAbsPreInQty -= curAbsPreInQty;
                matchInv.PreInQty += curPreInQty;
                invPreInLogList.Add(CreateStkInventoryPreInLog(change, groupName, curPreInQty, whLoc));
                updateInvList.Add(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsPreInQty == 0)
                    break;
            }

            if (unHandleAbsPreInQty > 0)
                throw Oops.Bah(GetInvChangeErrMessage(change, invRelInfo) + $"P[{firstMatchInv?.PreInQty ?? 0}]C[{change.PreInQty}]" + L.Text["预入库数量不足"]);
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            AddInvList = addInvList,
            UpdateInvList = updateInvList,
            AddInvPreInLogList = invPreInLogList,
        };
    }

    /// <summary>
    /// 减预入库数量，根据变更内容更新 invList 入参，返回库存变更信息与库存Id的映射和日志列表
    /// </summary>
    private InvChangeExecResult PreInQtyMinus(List<StkInvChange> stkInvChanges, List<StkInventory> invList, string groupName,
        InvRelInfo invRelInfo)
    {
        // 预入库数量 < 0
        var handleChanges = stkInvChanges.Where(u => u.PreInQty < 0).ToList();

        var updateInvList = new List<StkInventory>();
        var invPreInLogList = new List<StkInventoryPreInLog>();
        var invChangeMapping = new Dictionary<StkInvChange, long>();
        foreach (var change in handleChanges)
        {
            var whLoc = invRelInfo.WhLocs[change.WhLocId];

            // 查找匹配的库存数据行（正常情况下，最多只有一行）
            var matchInvList = GetMatchInvList(change, invList, whLoc);

            // 未处理的绝对值数量
            var unHandleAbsPreInQty = Math.Abs(change.PreInQty);

            // 遍历匹配的库存行（正常情况下，最多只有一行）
            StkInventory firstMatchInv = null;
            foreach (var matchInv in matchInvList)
            {
                firstMatchInv ??= matchInv.Adapt<StkInventory>();
                // 记录库存变更信息与库存Id的映射，正常情况下，最多只有一行，如果真的有多行，只取第一行
                if (!invChangeMapping.ContainsKey(change))
                    invChangeMapping[change] = matchInv.Id;

                // 预入库数量计算
                var curAbsPreInQty = Math.Min(matchInv.PreInQty, unHandleAbsPreInQty);
                var curPreInQty = -curAbsPreInQty; // 负数
                unHandleAbsPreInQty -= curAbsPreInQty;
                matchInv.PreInQty += curPreInQty;
                invPreInLogList.Add(CreateStkInventoryPreInLog(change, groupName, curPreInQty, whLoc));
                updateInvList.Add(matchInv);

                // 如果未处理数量为0，跳出循环
                if (unHandleAbsPreInQty == 0)
                    break;
            }

            if (unHandleAbsPreInQty > 0)
                throw Oops.Bah(GetInvChangeErrMessage(change, invRelInfo) + $"P[{firstMatchInv?.PreInQty ?? 0}]C[{change.PreInQty}]" + L.Text["预入库数量不足"]);
        }

        return new InvChangeExecResult
        {
            InvChangeAndInvIdMapping = invChangeMapping,
            UpdateInvList = updateInvList,
            AddInvPreInLogList = invPreInLogList,
        };
    }

    /// <summary>
    /// 计算可用数量
    /// </summary>
    /// <param name="inv">库位库存对象</param>
    /// <returns></returns>
    private decimal CalcAvailableQty(StkInventory inv)
    {
        return inv.Qty - inv.LockQty;
    }

    /// <summary>
    /// 校验库存变更参数并返回实体
    /// </summary>
    /// <param name="stkInvChanges">库存变更信息列表</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    private InvRelInfo ValidationParameterAndReturnEntity(List<StkInvChange> stkInvChanges)
    {
        var errMsgs = new List<string>();

        if (stkInvChanges.Any(u => u.Qty != 0 && u.InvLogType == null))
            errMsgs.Add(L.Text["当 {0} 不等于 0，需要设置 {1}", nameof(StkInvChange.Qty), nameof(StkInvChange.InvLogType)]);
        if (stkInvChanges.Any(u => u.LockQty != 0 && u.InvLockLogType == null))
            errMsgs.Add(L.Text["当 {0} 不等于 0，需要设置 {1}", nameof(StkInvChange.LockQty), nameof(StkInvChange.InvLockLogType)]);
        if (stkInvChanges.Any(u => u.PreInQty != 0 && u.InvPreInLogType == null))
            errMsgs.Add(L.Text["当 {0} 不等于 0，需要设置 {1}", nameof(StkInvChange.PreInQty), nameof(StkInvChange.InvPreInLogType)]);

        // 获取基础资料数据
        var materials = Rep.Context.Queryable<BdMaterial>().Where(u => stkInvChanges.Select(p => p.MaterialId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);
        var whLocs = Rep.Context.Queryable<BdWhLoc>().Where(u => stkInvChanges.Select(p => p.WhLocId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);
        var whAreas = Rep.Context.Queryable<BdWhArea>().Where(u => whLocs.Values.Select(p => p.WhAreaId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);
        var warehouses = Rep.Context.Queryable<BdWarehouse>().Where(u => whLocs.Values.Select(p => p.WarehouseId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);
        var containers = Rep.Context.Queryable<BdContainer>().Where(u => stkInvChanges.Where(p => p.ContainerId != null).Select(p => p.ContainerId).Distinct().Contains(u.Id))
            .With(SqlWith.NoLock).ToList().ToDictionary(u => u.Id);
        var owners = Rep.Context.Queryable<BdOwner>().Where(u => stkInvChanges.Select(p => p.OwnerId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);
        var auxPropValues = Rep.Context.Queryable<BdAuxPropValue>().Where(u => stkInvChanges.Select(p => p.AuxPropValueId).Distinct().Contains(u.Id)).With(SqlWith.NoLock).ToList()
            .ToDictionary(u => u.Id);

        // 校验传入的数据
        foreach (var change in stkInvChanges)
        {
            var material = materials.TryGetValue(change.MaterialId, out var material1) ? material1 : null;
            var whLoc = whLocs.TryGetValue(change.WhLocId, out var whLoc1) ? whLoc1 : null;
            var container = change.ContainerId != null && containers.TryGetValue(change.ContainerId.Value, out var container1) ? container1 : null;
            var auxPropValue = change.AuxPropValueId != null && auxPropValues.TryGetValue(change.AuxPropValueId.Value, out var auxPropValue1) ? auxPropValue1 : null;

            if (material == null)
                errMsgs.Add(L.Text["物料Id：{0} 不存在", change.MaterialId]);
            if (whLoc == null)
                errMsgs.Add(L.Text["库位Id：{0} 不存在", change.WhLocId]);
            if (change.ContainerId != null && container == null)
                errMsgs.Add(L.Text["容器Id：{0} 不存在", change.ContainerId]);
            if (change.AuxPropValueId != null && auxPropValue == null)
                errMsgs.Add(L.Text["辅助属性值Id：{0} 不存在", change.AuxPropValueId]);
        }

        if (errMsgs.Count > 0)
            throw new ArgumentException(string.Join(", ", errMsgs));

        return new InvRelInfo
            { Materials = materials, WhLocs = whLocs, WhAreas = whAreas, Warehouses = warehouses, Containers = containers, Owners = owners, AuxPropValues = auxPropValues };
    }

    private StkInventory CreateZeroQtyStkInventory(StkInvChange change, BdWhLoc whLoc)
    {
        // 提前赋值主键，用于返回
        return new StkInventory
        {
            Id = YitIdHelper.NextId(),
            MaterialId = change.MaterialId,
            BatchNo = change.BatchNo,
            ProduceDate = change.ProduceDate,
            ExpiryDate = change.ExpiryDate,
            OwnerId = change.OwnerId,
            AuxPropValueId = change.AuxPropValueId,
            WarehouseId = whLoc.WarehouseId,
            WhAreaId = whLoc.WhAreaId,
            WhLocId = change.WhLocId,
            ContainerId = change.ContainerId,
            Qty = 0,
            LockQty = 0,
            PreInQty = 0,
            AvailableQty = 0,
            UnitId = change.UnitId,
        };
    }

    private StkInventoryLog CreateStkInventoryLog(StkInvChange change, string groupName, decimal realHandleQty, BdWhLoc whLoc, BdWhLoc sourceWhLoc)
    {
        var userManage = ServiceProvider.GetService<UserManager>();
        // 提前赋值创建用户名、时间、主键、租户、是否删除，用于 BulkCopy
        return new StkInventoryLog
        {
            Id = YitIdHelper.NextId(),
            InvLogType = change.InvLogType!.Value,
            MaterialId = change.MaterialId,
            BatchNo = change.BatchNo,
            ProduceDate = change.ProduceDate,
            ExpiryDate = change.ExpiryDate,
            Qty = realHandleQty,
            OwnerId = change.OwnerId,
            AuxPropValueId = change.AuxPropValueId,
            WarehouseId = whLoc.WarehouseId,
            WhAreaId = whLoc.WhAreaId,
            WhLocId = change.WhLocId,
            ContainerId = change.ContainerId,
            UnitId = change.UnitId,
            SourceWhAreaId = sourceWhLoc?.WhAreaId,
            SourceWhLocId = change.SourceWhLocId,
            RelBillKey = change.RelBillKey,
            RelBillNo = change.RelBillNo,
            RelBillType = change.RelBillType,
            RelSeq = change.RelSeq,
            RelBillId = change.RelBillId,
            RelBillEntryId = change.RelBillEntryId,
            RelSourceBillNo = change.RelSourceBillNo,
            RelTaskId = change.RelTaskId,
            GroupName = groupName,
            CreateTime = DateTime.Now,
            CreateUserId = CurUserId,
            CreateUserName = CurUserName,
            TenantId = userManage.TenantId,
            IsDelete = false,
        };
    }

    private StkInventoryLockLog CreateStkInventoryLockLog(StkInvChange change, string groupName, decimal realHandleQty, BdWhLoc whLoc)
    {
        var userManage = ServiceProvider.GetService<UserManager>();
        // 提前赋值创建用户名、时间、主键、租户、是否删除，用于 BulkCopy
        return new StkInventoryLockLog
        {
            Id = YitIdHelper.NextId(),
            InvLockLogType = change.InvLockLogType!.Value,
            MaterialId = change.MaterialId,
            BatchNo = change.BatchNo,
            ProduceDate = change.ProduceDate,
            ExpiryDate = change.ExpiryDate,
            Qty = realHandleQty,
            OwnerId = change.OwnerId,
            AuxPropValueId = change.AuxPropValueId,
            WarehouseId = whLoc.WarehouseId,
            WhAreaId = whLoc.WhAreaId,
            WhLocId = change.WhLocId,
            ContainerId = change.ContainerId,
            UnitId = change.UnitId,
            RelBillKey = change.RelBillKey,
            RelBillNo = change.RelBillNo,
            RelBillType = change.RelBillType,
            RelSeq = change.RelSeq,
            RelBillId = change.RelBillId,
            RelBillEntryId = change.RelBillEntryId,
            RelSourceBillNo = change.RelSourceBillNo,
            RelTaskId = change.RelTaskId,
            GroupName = groupName,
            CreateTime = DateTime.Now,
            CreateUserId = CurUserId,
            CreateUserName = CurUserName,
            TenantId = userManage.TenantId,
            IsDelete = false,
        };
    }

    private StkInventoryPreInLog CreateStkInventoryPreInLog(StkInvChange change, string groupName, decimal realHandleQty, BdWhLoc whLoc)
    {
        var userManage = ServiceProvider.GetService<UserManager>();
        // 提前赋值创建用户名、时间、主键、租户、是否删除，用于 BulkCopy
        return new StkInventoryPreInLog
        {
            Id = YitIdHelper.NextId(),
            InvPreInLogType = change.InvPreInLogType!.Value,
            MaterialId = change.MaterialId,
            BatchNo = change.BatchNo,
            ProduceDate = change.ProduceDate,
            ExpiryDate = change.ExpiryDate,
            Qty = realHandleQty,
            OwnerId = change.OwnerId,
            AuxPropValueId = change.AuxPropValueId,
            WarehouseId = whLoc.WarehouseId,
            WhAreaId = whLoc.WhAreaId,
            WhLocId = change.WhLocId,
            ContainerId = change.ContainerId,
            UnitId = change.UnitId,
            RelBillKey = change.RelBillKey,
            RelBillNo = change.RelBillNo,
            RelBillType = change.RelBillType,
            RelSeq = change.RelSeq,
            RelBillId = change.RelBillId,
            RelBillEntryId = change.RelBillEntryId,
            RelSourceBillNo = change.RelSourceBillNo,
            RelTaskId = change.RelTaskId,
            GroupName = groupName,
            CreateTime = DateTime.Now,
            CreateUserId = CurUserId,
            CreateUserName = CurUserName,
            TenantId = userManage.TenantId,
            IsDelete = false,
        };
    }

    /// <summary>
    /// 根据任务Id回滚库存
    /// </summary>
    /// <param name="taskId">库存任务主键Id</param>
    [NonAction]
    public void RollbackInventoryByTaskId(long taskId)
    {
        var taskEntryList = Rep.Change<StkTaskEntry>().GetList(u => u.Id == taskId);
        if (taskEntryList.All(u => u.SrcWhLocId == null && u.DestWhLocId == null)) return;

        // 查找关联单据主键Id的事务日志
        var invLogs = Rep.Change<StkInventoryLog>().GetList(u => u.RelTaskId == taskId);
        var invLockLogs = Rep.Change<StkInventoryLockLog>().GetList(u => u.RelTaskId == taskId);
        var invPreInLogs = Rep.Change<StkInventoryPreInLog>().GetList(u => u.RelTaskId == taskId);

        InnerRollbackInventory(invLogs, invLockLogs, invPreInLogs);
    }

    /// <summary>
    /// 根据单据Id回滚库存
    /// </summary>
    /// <param name="relBillKey">关联单据标识</param>
    /// <param name="relBillId">关联单据主键Id</param>
    /// <param name="groupName">分组名称（给定一个名称，描述同一关联单据同一个操作的日志集合，用于同一关联单据的不同操作的日志回滚）</param>
    /// <exception cref="Exception"></exception>
    [NonAction]
    public void RollbackInventory(string relBillKey, long relBillId, string groupName)
    {
        // 查找关联单据主键Id的事务日志
        var invLogs = Rep.Change<StkInventoryLog>().GetList(u => u.RelBillKey == relBillKey && u.RelBillId == relBillId && u.GroupName == groupName);
        var invLockLogs = Rep.Change<StkInventoryLockLog>().GetList(u => u.RelBillKey == relBillKey && u.RelBillId == relBillId && u.GroupName == groupName);
        var invPreInLogs = Rep.Change<StkInventoryPreInLog>().GetList(u => u.RelBillKey == relBillKey && u.RelBillId == relBillId && u.GroupName == groupName);

        InnerRollbackInventory(invLogs, invLockLogs, invPreInLogs);
    }

    /// <summary>
    /// 回滚库存内部实现
    /// </summary>
    /// <param name="invLogs"></param>
    /// <param name="invLockLogs"></param>
    /// <param name="invPreInLogs"></param>
    private void InnerRollbackInventory(List<StkInventoryLog> invLogs, List<StkInventoryLockLog> invLockLogs, List<StkInventoryPreInLog> invPreInLogs)
    {
        try
        {
            if (invLogs.Count == 0 && invLockLogs.Count == 0 && invPreInLogs.Count == 0)
                throw Oops.Bah(StkErrorCode.StkInventory1001);

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var invChanges = new List<StkInvChange>();
            invChanges.AddRange(
                invLogs.Select(u => new StkInvChange
                {
                    InvLogType = u.InvLogType,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = -u.Qty, // 回滚，负数
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    SourceWhLocId = u.SourceWhLocId,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = u.RelBillKey,
                    RelBillNo = u.RelBillNo,
                    RelBillType = u.RelBillType,
                    RelSeq = u.RelSeq,
                    RelBillId = u.RelBillId,
                    RelBillEntryId = u.RelBillEntryId,
                    RelSourceBillNo = u.RelSourceBillNo,
                    RelTaskId = u.RelTaskId,
                }));
            invChanges.AddRange(
                invLockLogs.Select(u => new StkInvChange
                {
                    InvLockLogType = u.InvLockLogType,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    LockQty = -u.Qty, // 回滚，负数
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = u.RelBillKey,
                    RelBillNo = u.RelBillNo,
                    RelBillType = u.RelBillType,
                    RelSeq = u.RelSeq,
                    RelBillId = u.RelBillId,
                    RelBillEntryId = u.RelBillEntryId,
                    RelSourceBillNo = u.RelSourceBillNo,
                    RelTaskId = u.RelTaskId,
                }));
            invChanges.AddRange(
                invPreInLogs.Select(u => new StkInvChange
                {
                    InvPreInLogType = u.InvPreInLogType,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    PreInQty = -u.Qty, // 回滚，负数
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = u.RelBillKey,
                    RelBillNo = u.RelBillNo,
                    RelBillType = u.RelBillType,
                    RelSeq = u.RelSeq,
                    RelBillId = u.RelBillId,
                    RelBillEntryId = u.RelBillEntryId,
                    RelSourceBillNo = u.RelSourceBillNo,
                    RelTaskId = u.RelTaskId,
                }));

            // 更新库存
            UpdateInventory(invChanges, "回滚", false);

            // 写入回滚的日志表
            Rep.Context.Insertable(invLogs.Select(u => u.Adapt<StkInventoryLogRollback>()).ToList()).ExecuteCommand();
            Rep.Context.Insertable(invLockLogs.Select(u => u.Adapt<StkInventoryLockLogRollback>()).ToList()).ExecuteCommand();
            Rep.Context.Insertable(invPreInLogs.Select(u => u.Adapt<StkInventoryPreInLogRollback>()).ToList()).ExecuteCommand();

            // 删除事务日志
            Rep.Context.Deleteable(invLogs).ExecuteCommand();
            // 删除锁定事务日志
            Rep.Context.Deleteable(invLockLogs).ExecuteCommand();
            // 删除预入库事务日志
            Rep.Context.Deleteable(invPreInLogs).ExecuteCommand();

            // 提交事务
            uow.Commit();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 库存关联信息
    /// </summary>
    private class InvRelInfo
    {
        /// <summary>
        /// 物料信息
        /// </summary>
        public Dictionary<long, BdMaterial> Materials { get; set; }

        /// <summary>
        /// 库位信息
        /// </summary>
        public Dictionary<long, BdWhLoc> WhLocs { get; set; }

        /// <summary>
        /// 库区信息
        /// </summary>
        public Dictionary<long, BdWhArea> WhAreas { get; set; }

        /// <summary>
        /// 仓库信息
        /// </summary>
        public Dictionary<long, BdWarehouse> Warehouses { get; set; }

        /// <summary>
        /// 容器信息
        /// </summary>
        public Dictionary<long, BdContainer> Containers { get; set; }

        /// <summary>
        /// 货主信息
        /// </summary>
        public Dictionary<long, BdOwner> Owners { get; set; }

        /// <summary>
        /// 辅助属性值信息
        /// </summary>
        public Dictionary<long, BdAuxPropValue> AuxPropValues { get; set; }
    }

    /// <summary>
    /// 库存变更执行结果
    /// </summary>
    private class InvChangeExecResult
    {
        /// <summary>
        /// 库存变更信息与库存Id的映射
        /// </summary>
        public Dictionary<StkInvChange, long> InvChangeAndInvIdMapping { get; set; } = new();

        /// <summary>
        /// 新增的库存事务日志列表
        /// </summary>
        public List<StkInventoryLog> AddInvLogList { get; set; } = new();

        /// <summary>
        /// 新增的库存锁定事务日志列表
        /// </summary>
        public List<StkInventoryLockLog> AddInvLockLogList { get; set; } = new();

        /// <summary>
        /// 新增的库存预入库事务日志列表
        /// </summary>
        public List<StkInventoryPreInLog> AddInvPreInLogList { get; set; } = new();

        /// <summary>
        /// 需要新增的库存列表
        /// </summary>
        public List<StkInventory> AddInvList { get; set; } = new();

        /// <summary>
        /// 需要更新的库存列表
        /// </summary>
        public List<StkInventory> UpdateInvList { get; set; } = new();
    }
}