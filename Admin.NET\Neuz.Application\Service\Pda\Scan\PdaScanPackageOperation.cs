﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan;

public class PdaScanPackageOperation : PdaScanBarcodeOperationBase
{

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaCacheService = App.GetService<PdaCacheService>();
        var billData = pdaCacheService.GetBillData(args.TranId);

        //查询箱带条码
        var packageService = App.GetService<BarPackageService>(_serviceProvider);
        var output = packageService.QueryPackageBarcodeNotError(args.BarcodeString).Result;
        if (output.Barcodes.Count == 0) return;

        output.Barcodes.ForEach(r =>
        {
            if (billData.BarcodeList.Exists(b => b.Barcode.Id == r.Id))
                throw Oops.Bah(PdaErrorCode.Pda1015, r.Barcode);
        });
        var billModel = pdaCacheService.GetPdaBillModel(billData.BillModelKey);
        billModel.ScanBarcodes(args.TranId, output.Barcodes, output.Package);
        args.IsResult = true;
    }

    public PdaScanPackageOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}