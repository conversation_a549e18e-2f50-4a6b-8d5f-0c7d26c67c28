﻿using Furion.Localization;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Bill.Model.Bill;

namespace Neuz.Application.Pda.Proxy;

/// <summary>
/// Pda操作 (注意: 仅限单据)
/// </summary>
public class PdaCacheService : PdaCacheBaseService
{
    /// <summary>
    /// 
    /// </summary>
    protected ILogger<PdaCacheService> Logger => App.GetService<ILogger<PdaCacheService>>(_serviceProvider);

    protected SysCacheService SysCacheService => App.GetService<SysCacheService>(_serviceProvider);

    /// <summary>
    /// 单据数据集合
    /// </summary>
    private static Dictionary<long, PdaBillData> PdaBillDatas = new Dictionary<long, PdaBillData>();

    #region BillData

    /// <summary>
    /// 加载BillData
    /// </summary>
    /// <param name="pdaBillData"></param>
    /// <returns></returns>
    public PdaBillData ReLoadBillData(PdaBillData pdaBillData)
    {
        List<long> removeDatas = new List<long>();
        foreach (KeyValuePair<long, PdaBillData> pair in PdaBillDatas)
        {
            if (pair.Value.BillModelKey.Equals(pdaBillData.BillModelKey) && pair.Value.UserId.Equals(pdaBillData.UserId))
            {
                removeDatas.Add(pair.Key);
            }
        }
        removeDatas.ForEach(r =>
        {
            Logger.LogWarning(L.Text["新建事务并删除事务Id:[{0}]", r]);
            if (PdaBillDatas.ContainsKey(r)) PdaBillDatas.Remove(r);
            SysCacheService.Remove($"pda_tranId_{r}");
        });
        PdaBillDatas.Add(pdaBillData.TranId, pdaBillData);
        var billModel = GetPdaBillModel(pdaBillData.BillModelKey);
        billModel.RefreshShow(pdaBillData.TranId);
        return pdaBillData;
    }

    /// <summary>
    /// 新增BillData
    /// </summary>
    /// <param name="modelKey"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public PdaBillData CreateBillData(string modelKey, long userId)
    {
        //TODO: 现在如果有相同的键值，先删除旧的
        List<long> removeDatas = new List<long>();
        foreach (KeyValuePair<long, PdaBillData> pair in PdaBillDatas)
        {
            if ((pair.Value.BillModelKey + "").Equals(modelKey) && pair.Value.UserId.Equals(userId))
            {
                removeDatas.Add(pair.Key);
            }
        }
        removeDatas.ForEach(r =>
        {
            Logger.LogWarning(L.Text["新建事务并删除事务Id:[{0}]", r]);
            if (PdaBillDatas.ContainsKey(r)) PdaBillDatas.Remove(r);
            SysCacheService.Remove($"pda_tranId_{r}");
        });
        var billModel = GetPdaBillModel(modelKey);
        PdaBillData pdaBillData = new PdaBillData();
        pdaBillData.BillModelKey = modelKey;
        pdaBillData.TranId = YitIdHelper.NextId();
        pdaBillData.UserId = userId;
        PdaBillDatas.Add(pdaBillData.TranId, pdaBillData);
        billModel.RefreshShow(pdaBillData.TranId);
        return pdaBillData;
    }

    /// <summary>
    /// 删除BillData
    /// </summary>
    /// <param name="tranId"></param>
    public void DelBillData(long tranId)
    {
        if (PdaBillDatas.ContainsKey(tranId))
        {
            Logger.LogWarning(L.Text["删除事务Id:[{0}]", tranId]);
            PdaBillDatas.Remove(tranId);
            SysCacheService.Remove($"pda_tranId_{tranId}");
        }
    }

    /// <summary>
    /// 获取BillData
    /// </summary>
    /// <param name="tranId"></param>
    /// <returns></returns>
    public PdaBillData GetBillData(long tranId)
    {
        if (!PdaBillDatas.ContainsKey(tranId))
        {
            Logger.LogWarning(L.Text["没找到[{0}],找Redis还原", tranId]);
            var redisTran = SysCacheService.Get<byte[]>($"pda_tranId_{tranId}");
            if (redisTran == null) throw Oops.Bah(PdaErrorCode.Pda1004, tranId);
            var jsonStr = Encoding.UTF8.GetString(redisTran);
            var billData = JsonConvert.DeserializeObject<PdaBillData>(jsonStr);
            PdaBillDatas[tranId] = billData;
        }
        return PdaBillDatas[tranId];
    }

    /// <summary>
    /// 获取BillData是否存在数据
    /// </summary>
    /// <param name="key"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public PdaBillData GetBillDataForKey(string key, long userId)
    {
        var billData = PdaBillDatas.Values.FirstOrDefault(f =>
            (f.BillModelKey + "").Equals(key) && f.UserId.Equals(userId));
        if (billData == null)
        {
            var tranId = SysCacheService.Get<string>($"pda_billdata_{key}_{userId}");
            if (string.IsNullOrEmpty(tranId)) return null;
            var redisTran = SysCacheService.Get<byte[]>($"pda_tranId_{tranId}");
            if (redisTran == null) return null;
            var jsonStr = Encoding.UTF8.GetString(redisTran);
            var redisBillData = JsonConvert.DeserializeObject<PdaBillData>(jsonStr);
            if (redisBillData == null || redisBillData.ScanDetails.Count == 0) return null;
            PdaBillDatas[Convert.ToInt64(tranId)] = redisBillData;
            return redisBillData;
        }

        if (billData.ScanDetails.Count == 0) return null;
        return billData;
    }

    #endregion

    /// <summary>
    /// 获取PDA BillModel
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public IPdaBillModel GetPdaBillModel(string key)
    {
        if (!PdaProxy.BillModels.ContainsKey(key)) throw Oops.Bah(L.Text["没有找到BillModel的Key[{0}]", key]);
        var model = _resolveNamed(PdaProxy.BillModels[key].GetType().Name, default) as IPdaBillModel;
        model.Initialization();
        return model;
    }

    public PdaCacheService(IServiceProvider serviceProvider, Func<string, ITransient, object> resolveNamed) : base(serviceProvider, resolveNamed)
    {
        _serviceProvider = serviceProvider;
    }
}