﻿using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Scan.LocalBill;

public abstract class PdaScanBarcodeOperationBase: IPdaLocalBillScanBarcodeOperation
{
    protected readonly IServiceProvider ServiceProvider;

    public PdaScanBarcodeOperationBase(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
    }

    /// <summary>
    /// 
    /// </summary>
    public string Key => GetType().Name;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="args"></param>
    public abstract void Operation(PdaLocalBillScanBarcodeArgs args);
}