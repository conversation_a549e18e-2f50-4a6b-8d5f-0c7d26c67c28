﻿using Furion.Localization;
using Neuz.Application.Pda.Barcode;
using Neuz.Application.Pda.Barcode.Dto;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Application.Service.Pda.SplitBarcode;
using Neuz.Application.Service.Pda.SplitBarcode.Dto;
using Stimulsoft.Report;
using Stimulsoft.Report.Export;

namespace Neuz.Application.Proj.ProjModifyBarcode;

public class ProjPdaModifyBarcodeModel : PdaModelBillBase<ProjPdaModifyBarcodeShow, ProjPdaModifyBarcodeData>
{
    public ProjPdaModifyBarcodeModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = nameof(ProjPdaModifyBarcodeModel);

    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    public override void Initialization()
    {
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.BarcodeCells = pdaData.BarcodeCells;
        pdaShow.TemplateCells = pdaData.TemplateCells;
        pdaShow.TemplateValues = pdaData.TemplateValues;
        pdaShow.IsSubmit = pdaData.IsSubmit;
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
        ((ProjPdaModifyBarcodeData)pdaData).UserConfig = GetUserConfig<ProjPdaModifyBarcodeModelUserConfig>().GetAwaiter().GetResult() ?? new ProjPdaModifyBarcodeModelUserConfig();
    }

    /// <summary>
    /// 已拆分,重新打印
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="submitData"></param>
    /// <returns></returns>
    private async Task<object> SubmitReturnDataPrint(long tranId, object submitData)
    {
        var pdaData = GetPdaData(tranId);
        var service = App.GetService<SysReportTemplateService>(ServiceProvider);
        if (string.IsNullOrEmpty(pdaData.CurTemplateId)) throw Oops.Bah(L.Text["请先选择模板"]);

        // 这里打印有三种方式,
        // 1. 蓝牙打印 
        // 2. 服务器端打印
        // 3. 打印服务器打印
        var printInfo = JsonConvert.DeserializeObject<PdaPrintInfo>(submitData + "");

        var report = service.GetReport(Convert.ToInt64(pdaData.CurTemplateId), typeof(BdBarcodeService).AssemblyQualifiedName, new List<string>() { pdaData.CurBarcode.Id + "" }).Result;

        switch (printInfo.PrintType)
        {
            case PdaPrintType.Ble:
                return await PrintBle(tranId, report, printInfo);
            case PdaPrintType.Server:
                await PrintService(tranId, report, printInfo);
                break;
            case PdaPrintType.PrintService:
                break;
            default:
                throw new NotImplementedException();
        }

        return L.Text["打印成功"];
    }

    private async Task SelectFieldDataTemplate(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        pdaData.CurTemplateId = fieldValue;
        pdaData.TemplateValues["template"] = fieldValue;

        // 选择后,保存当前功能默认模板
        string funcKey = $"bar_{pdaData.CurBarcode.FuncKey}~barcode";
        var config = pdaData.UserConfig.TemplateInfos.FirstOrDefault(r => r.FuncKey == funcKey);
        if (config == null)
        {
            pdaData.UserConfig.TemplateInfos.Add(new PdaSplitTemplateInfo
            {
                FuncKey = funcKey,
                CurTemplateId = fieldValue
            });
            await SetUserConfig(pdaData.UserConfig);
        }
        else
        {
            if (config.CurTemplateId == fieldValue) return;
            config.CurTemplateId = fieldValue;
            await SetUserConfig(pdaData.UserConfig);
        }
    }

    private async Task SubmitSubmit(long tranId, object jsonStr)
    {
        var pdaData = GetPdaData(tranId);
        if (pdaData.IsSubmit) return;
        var info = JsonConvert.DeserializeObject<PdaSplitBarcodeSubmitInfo>(jsonStr + "");
        if (pdaData.CurBarcode == null) throw Oops.Bah(L.Text["请先扫描需要拆标的条码"]);
        if (info.PrintTemplates != null && info.PrintTemplates.Count > 0)
        {
            if (string.IsNullOrEmpty(pdaData.CurTemplateId)) throw Oops.Bah(L.Text["请先选择打印模板"]);
        }

        if (!pdaData.BarcodeCells.ContainsKey("ModifyQty") || IsEmptyValue(pdaData.BarcodeCells["ModifyQty"])) throw Oops.Bah(L.Text["请输入修改数量"]);

        var modifyQty = decimal.Parse(pdaData.BarcodeCells["ModifyQty"]);
        if (pdaData.CurBarcode.Qty == modifyQty) throw Oops.Bah(L.Text["数量未修改"]);

        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

        var bdBar = await Rep.Change<BdBarcode>().AsQueryable().FirstAsync(r => r.Id == pdaData.CurBarcode.Id);
        bdBar.Qty = modifyQty;
        // 保存条码档案日志
        BdBarcodeLog log = new BdBarcodeLog
        {
            OpTranId = 0,
            BarcodeId = bdBar.Id,
            Barcode = bdBar.Barcode,
            OriginQty = bdBar.Qty,
            OpQty = modifyQty,
            UnitId = bdBar.UnitId,
            OriginStatus = bdBar.Status,
            CurStatus = bdBar.Status,
            MaterialId = bdBar.MaterialId,
            BatchNo = bdBar.BatchNo,
            ProduceDate = bdBar.ProduceDate,
            ExpiryDate = bdBar.ExpiryDate,
            RelBillKey = bdBar.SrcBillKey,
            RelBillId = bdBar.SrcBillId,
            RelBillEntryId = bdBar.SrcBillEntryId,
            RelBillNo = bdBar.SrcBillNo,
            RelBillType = "ModifyBarcode",
            RelBillEntrySeq = bdBar.SrcBillEntrySeq,
            SrcWhAreaId = bdBar.WhAreaId,
            SrcWhLocId = bdBar.WhLocId,
            DestWhAreaId = bdBar.WhAreaId,
            DestWhLocId = bdBar.WhLocId,
            OriginBarcodeJson = bdBar.ToJson(),
            // 沃能没有这几个字段
            // SrcBillKey = bdBar.SrcBillKey,
            // SrcBillType = "ModifyBarcode",
            // SrcBillId = bdBar.SrcBillId,
            // SrcBillEntryId = bdBar.SrcBillEntryId,
            // SrcBillNo = bdBar.SrcBillNo,
            // SrcBillEntrySeq = bdBar.SrcBillEntrySeq
        };
        bdBar.Qty = modifyQty;
        await Rep.Change<BdBarcodeLog>().InsertAsync(log);
        await Rep.Change<BdBarcode>().UpdateAsync(bdBar);

        uow.Commit();
        pdaData.IsSubmit = true;
    }

    /// <summary>
    /// 判断空值
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public bool IsEmptyValue(object value)
    {
        if (value == null) return true;
        if (string.IsNullOrEmpty(value + "")) return true;
        if (value + "" == "0") return true;
        return false;
    }

    private async Task<object> PrintBle(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        await report.RenderAsync();
        List<string> base64Strings = new();
        StiPngExportService stiPngExportService = new();
        for (int i = 0; i < report.RenderedPages.Count; i++)
        {
            var ms = new MemoryStream();
            //report.ExportDocument(StiExportFormat.ImagePng, ms, new StiEmfExportSettings() { PageRange = new StiPagesRange(i + 1) });
            StiImageExportSettings settings = new StiPngExportSettings
            {
                //加了下面这个就会报错,应该是用了默认格式保存
                //settings.ImageType = StiImageType.Png;
                PageRange = new StiPagesRange(i + 1),
                ImageResolution = printInfo.Dpi ?? 0
            };
            stiPngExportService.ExportImage(report, ms, settings);
            ms.Seek(0, SeekOrigin.Begin);

            //服务器端处理返回的图像指令
            var resultBytes = PdaHelper.GetBitmapData(ms.ToArray());
            var base64String = Convert.ToBase64String(resultBytes);
            base64Strings.Add(base64String);
        }

        return base64Strings;
    }

    private Task PrintService(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        //打印
        var printService = App.GetService<PdaServerPrintService>(ServiceProvider);
        printService.Prints(new List<PdaReportPrintInfo>()
        {
            new()
            {
                Report = report,
                PrintName = printInfo.PrintName,
                Count = 1
            }
        });

        return Task.CompletedTask;
    }

    #region 继承的方法

    public override async Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        var barStr = (barcode + "").Split(';')[0];
        var bar = await Rep.Change<BdBarcode>().AsQueryable().FirstAsync(r => r.Barcode.Contains(barStr) && r.Status != BdBarcodeStatus.Disuse);
        if (bar == null) throw Oops.Bah(L.Text["没有找到条码[{0}]", barStr]);

        var pdaSchema = (PdaSchema)BillSchema;
        var pdaData = GetPdaData(tranId);
        foreach (var cell in pdaSchema.BarcodeCells)
        {
            var propertyInfo = bar.GetType().GetProperty(cell.Fieldname, BindingFlags.Public | BindingFlags.IgnoreCase | BindingFlags.Instance);
            if (propertyInfo == null) continue;
            var value = propertyInfo.GetValue(bar);
            pdaData.BarcodeCells[cell.Fieldname] = value + "";
        }

        pdaData.CurBarcode = bar;
        //处理模板
        string funcKey = $"{pdaData.CurBarcode.FuncKey}";
        var templates = await Rep.Change<SysReportTemplate>().AsQueryable().Where(r => r.FuncKey == funcKey).OrderBy(r => r.Number).ToListAsync();
        pdaData.Templates = templates;
        var templateObj = pdaData.TemplateCells.First(r => r.Fieldname == "template");
        templateObj.Options.Clear();
        pdaData.TemplateValues["template"] = "";
        pdaData.CurTemplateId = "";
        pdaData.Templates.ForEach(r => { templateObj.Options.Add(new PdaColumnOption(r.Id + "", r.Name)); });

        // 处理最后一次打印选择的模板
        if (pdaData.UserConfig != null && pdaData.UserConfig.TemplateInfos.Count > 0)
        {
            var info = pdaData.UserConfig.TemplateInfos.FirstOrDefault(r => r.FuncKey == funcKey);
            if (info != null)
            {
                if (pdaData.Templates.Any(r => (r.Id + "") == info.CurTemplateId))
                {
                    pdaData.TemplateValues["template"] = info.CurTemplateId;
                    pdaData.CurTemplateId = info.CurTemplateId;
                }
            }
        }

        pdaData.IsSubmit = false;
        RefreshShow(tranId);
    }

    public override Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        throw new NotImplementedException();
    }

    public override Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        switch (fieldKey)
        {
            case "template.template":
                await SelectFieldDataTemplate(tranId, fieldValue);
                break;
            case "BarcodeCells.ModifyQty":
                await SelectFieldDataModifyQty(tranId, fieldValue);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    private Task SelectFieldDataModifyQty(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        pdaData.BarcodeCells["ModifyQty"] = fieldValue;
        return Task.CompletedTask;
    }

    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "submit":
                await SubmitSubmit(tranId, submitData);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "print":
                return await SubmitReturnDataPrint(tranId, submitData);
            default:
                throw new NotImplementedException();
        }
    }

    #endregion

    public class PdaSchema : IPdaSchema
    {
        /// <summary>
        /// 条码明细列,显示的是BdBarcode的属性
        /// </summary>
        public List<PdaColumn> BarcodeCells { get; } = new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "modifyQty",
                Caption = L.Text["修改后数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                FieldDataKey = "BarcodeCells.ModifyQty"
            },
            new PdaColumn
            {
                Fieldname = "qty",
                Caption = L.Text["数量"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条码"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "extStr01",
                Caption = L.Text["箱号"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "stockName",
                Caption = L.Text["仓库名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "stockLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialNumber",
                Caption = L.Text["物料编码"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialName",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "materialSpec",
                Caption = L.Text["规格型号"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
            new PdaColumn
            {
                Fieldname = "batchNo",
                Caption = L.Text["批号"],
                Type = "string",
                Readonly = true,
                IsShow = true
            },
        };
    }
}