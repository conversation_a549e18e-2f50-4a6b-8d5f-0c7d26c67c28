﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill.StkOutNotice_StkOutStock;

/// <summary>
/// 发货通知单->出库单
/// </summary>
public class PdaLocalBillStkOutNotice_StkOutStock_SCLLBLDModel : PdaLocalBillModel<StkOutNotice, StkOutStock, StkOutNoticeEntry, StkOutStockEntry>
{
    public PdaLocalBillStkOutNotice_StkOutStock_SCLLBLDModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkOutNotice_StkOutStock_SCLLBLD";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "customerName",
                Caption = L.Text["客户"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdCustomerModel",
                    LookupDataKey = "ScanHead.Customer",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("CustomerId", "Id"),
                        new PdaLookupMapping("CustomerNumber", "Number"),
                        new PdaLookupMapping("CustomerName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "departmentName",
                Caption = L.Text["部门"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdDepartmentModel",
                    LookupDataKey = "ScanHead.Department",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DepartmentId", "Id"),
                        new PdaLookupMapping("DepartmentNumber", "Number"),
                        new PdaLookupMapping("DepartmentName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "employeeName",
                Caption = L.Text["领料人"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdEmployeeModel",
                    LookupDataKey = "ScanHead.Employee",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("EmployeeId", "Id"),
                        new PdaLookupMapping("EmployeeNumber", "Number"),
                        new PdaLookupMapping("EmployeeName", "Name"),
                    }
                }
            },
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkOutNotice",
            SourceTitle = L.Text["发货通知单"],
            DestKey = "StkOutStock",
            DestTitle = L.Text["出库单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceItem = true,
            IsOverSourceQty = true
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkOutNotice>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkOutNoticeEntry>(ServiceProvider, u => u.WhAreaId)
            .AddOwnerFilter<StkOutNoticeEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .WhereIF(linkParam.SourceBillTypes.Count > 0, t1 => linkParam.SourceBillTypes.Contains(t1.BillType))
                .Where(t1 => t1.DocumentStatus == DocumentStatus.Approve && t1.Status != StkOutNoticeStatus.Finish &&
                             (t1.BillNo.Contains(input.Keyword)
                              || t1.EsBillNo.Contains(input.Keyword)
                              || t1.OrderBillNo.Contains(input.Keyword)
                              || t1.Id == SqlFunc.Subqueryable<StkOutNoticeEntry>()
                                  .LeftJoin<BdMaterial>((m1, m2) => m1.MaterialId == m2.Id)
                                  .Where((m1, m2) => m1.Id == t1.Id && m1.EntryStatus != StkOutNoticeEntryStatus.Finish)
                                  .WhereIF(!string.IsNullOrEmpty(input.Keyword), (m1, m2) => m2.Number.Contains(input.Keyword))
                                  .GroupBy((m1, m2) => m1.Id)
                                  .Select((m1, m2) => m1.Id)))
                .Where(t1 => SqlFunc.Subqueryable<StkOutNoticeEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(t1 => t1.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = $"{r.BillNo}[{r.EsBillNo}]", SubTitle = $"{r.Date:yyyy-MM-dd}\r\n订单号:{r.OrderBillNo}" }).ToArray();
        return result;
    }

    public override void RefreshShow(long tranId)
    {
        base.RefreshShow(tranId);
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => $"{r.SrcBillNo} - {r["OrderBillNo"]}").ToList();
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override async Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        // 获取Link
        // 按配置映射数据
        var bill = await SourceRep.AsQueryable().IncludeNavCol().FirstAsync(r => r.BillNo == input.BillNo || r.EsBillNo == input.BillNo || r.OrderBillNo == input.BillNo);
        if (bill == null) return null;
        return await GetBill(new PdaLocalBillLookSelectInput
        {
            Key = input.Key,
            TranId = input.TranId,
            LookupDataKey = null,
            LookupKey = null,
            Id = bill.Id + "",
            DetailIndex = null,
            IsLocalBill = false
        });
    }

    /// <inheritdoc/>
    protected override Task<string> GetBillType(long tranId)
    {
        return Task.FromResult("SCLLBLD");
    }

    /// <inheritdoc/>
    public override void BeforeSetSourceInfo(long tranId, List<PdaLocalBillSourceHead> headDics, List<PdaLocalBillSourceDetail> detailDics, BdBarcode barcode)
    {
        base.BeforeSetSourceInfo(tranId, headDics, detailDics, barcode);
        detailDics.Clear();
    }

    /// <inheritdoc/>
    protected override void BeforeMatchingBarcode(long tranId, BdBarcode barcode)
    {
        base.BeforeMatchingBarcode(tranId, barcode);
        var pdaData = GetPdaData(tranId);
        if (pdaData.SourceHeads.Count <= 0) throw Oops.Bah("请先选择源单");
    }
}