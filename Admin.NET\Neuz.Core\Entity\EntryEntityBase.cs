﻿namespace Neuz.Core.Entity;

/// <summary>
/// 单据体基类实体
/// </summary>
public abstract class EntryEntityBase
{
    /// <summary>
    /// 分录主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "分录主键Id", IsPrimaryKey = true, IsIdentity = false)]
    public virtual long EntryId { get; set; }

    /// <summary>
    /// 主键Id
    /// </summary>
    /// <remarks>
    /// 关联单据头的主键Id（又名：主实体Id、表头Id）
    /// </remarks>
    [SugarColumn(ColumnDescription = "主键Id")]
    public virtual long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    [SugarColumn(ColumnDescription = "序号")]
    public virtual int Seq { get; set; }
}