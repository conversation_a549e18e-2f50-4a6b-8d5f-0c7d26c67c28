﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Proj.ProjModifyBarcode;

public class ProjPdaModifyBarcodeShow : PdaShow
{
    // 源单数据
    public Dictionary<string, string> BarcodeCells = new Dictionary<string, string>();

    /// <summary>
    /// 打印模板
    /// </summary>
    public List<PdaColumn> TemplateCells { get; set; }

    // 模板数据
    public Dictionary<string, string> TemplateValues = new Dictionary<string, string>();

    /// <summary>
    /// 是否已提交(生成条码)
    /// </summary>
    public bool IsSubmit { get; set; }
}