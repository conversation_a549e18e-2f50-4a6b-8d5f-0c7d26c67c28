﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.Bill.Model.Basic.Cloud;

/// <summary>
/// 仓库仓位 Lookup 模型
/// </summary>
public class PdaCloudInventoryQueryBD_StockStockLocModel : PdaCloudBasicLookupModel, IPdaInventoryQueryBasicLookupModel
{
    /// <summary>
    /// 仓库仓位 Lookup 模型
    /// </summary>
    public PdaCloudInventoryQueryBD_StockStockLocModel(IServiceProvider serviceProvider, K3CloudInterface k3CloudInterface) : base(serviceProvider, k3CloudInterface)
    {
    }

    /// <inheritdoc />
    protected override string FormId => "BD_StockStockLoc";

    public override string LookupKey => "InventoryQuery_BD_StockStockLoc";

    /// <inheritdoc />
    public override List<PdaLookupOutput> GetLookupOutput(long tranId, DataTable table)
    {
        var lookupOutputs = new List<PdaLookupOutput>();
        foreach (DataRow row in table.Rows)
        {
            var lookupOutput = new PdaLookupOutput
            {
                Key = $"{row["StockId"]}_{row["StockLocId"]}",
                Title = $"{row["StockNumber"]} {row["StockLocNumber"]}",
                SubTitle = $"{row["StockName"]} {row["StockLocName"]}"
            };
            lookupOutputs.Add(lookupOutput);
        }

        return lookupOutputs;
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateQueryBasicDataParam(long tranId, string keyword)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FStockId AS StockId",
                "FStockIdNumber AS StockNumber",
                "FStockIdName AS StockName",
                "FStockLocId AS StockLocId",
                "FStockLocIdNumber AS StockLocNumber",
                "FStockLocIdName AS StockLocName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FStockIdNumber", QueryType.Contains, keyword, "group1"),
                new("FStockIdName", QueryType.Contains, keyword, "group1"),
                new("FStockLocIdNumber", QueryType.Contains, keyword, "group1"),
                new("FStockLocIdName", QueryType.Contains, keyword, "group1"),
            },
            Orders = new List<QueryOrder>
            {
                new("FStockIdNumber"),
                new("FStockLocIdNumber"),
            },
        };
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateGetBasicDataParam(long tranId, string id)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        var ids = id.Split("_");
        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FStockId AS StockId",
                "FStockIdNumber AS StockNumber",
                "FStockIdName AS StockName",
                "FStockLocId AS StockLocId",
                "FStockLocIdNumber AS StockLocNumber",
                "FStockLocIdName AS StockLocName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FStockId", QueryType.Equals, ids[0]),
                new("FStockLocId", QueryType.Equals, ids[1]),
            },
            Orders = new List<QueryOrder>
            {
                new("FStockIdNumber"),
                new("FStockLocIdNumber"),
            },
        };
    }

    /// <inheritdoc />
    protected override QueryBillParam CreateQueryCustomDataParam(long tranId, string filter)
    {
        var orgId = PdaCloudInventoryQueryHelper.GetScanHeadOrgId(tranId, ModelKey, ServiceProvider);

        var numbers = filter.Split("_");
        return new QueryBillParam
        {
            FormId = FormId,
            FieldKeys = new List<string>
            {
                "FStockId AS StockId",
                "FStockIdNumber AS StockNumber",
                "FStockIdName AS StockName",
                "FStockLocId AS StockLocId",
                "FStockLocIdNumber AS StockLocNumber",
                "FStockLocIdName AS StockLocName",
            },
            Filters = new List<QueryFilter>
            {
                new("FUseOrgId", QueryType.Equals, orgId),
                new("FStockIdNumber", QueryType.Equals, numbers[0]),
                new("FStockLocIdNumber", QueryType.Equals, numbers[1]),
            },
            Orders = new List<QueryOrder>
            {
                new("FStockIdNumber"),
                new("FStockLocIdNumber"),
            },
        };
    }

    public string ModelKey { get; set; }
}