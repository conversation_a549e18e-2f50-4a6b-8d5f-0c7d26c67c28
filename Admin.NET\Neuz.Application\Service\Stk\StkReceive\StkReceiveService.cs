using Neuz.Application.ExternalSystem;
using Neuz.Application.ExternalSystem.Dto;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 收货单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkReceive", Order = 100)]
public class StkReceiveService : StkBaseBillService<StkReceive>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkReceive);

    /// <summary>
    /// 收货单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkReceiveService(IServiceProvider serviceProvider, SqlSugarRepository<StkReceive> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Status", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_NoticeBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_NoticeEsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "Status",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "SupplierNumber",
            "SupplierName",
            "CustomerNumber",
            "CustomerName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_EntryStatus",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_WhAreaNumber",
            "Entries_WhAreaName",
            "Entries_WhLocNumber",
            "Entries_WhLocName",
            "Entries_Qty",
            "Entries_InQty",
            "Entries_RemainInQty",
            "Entries_CheckQty",
            "Entries_ReceiveQty",
            "Entries_RefuseQty",
            "Entries_ReturnQty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_Price",
            "Entries_TotalPrice",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkReceive entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkReceive entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkReceive entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 重新计算剩余数量
            entry.RemainInQty = entry.Qty - entry.InQty;

            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);
            // 判断库区是否已填
            if (entry.WhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1005);
            // 判断库位是否已填
            if (entry.WhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1006);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (entry.Qty != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkReceive entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkReceive entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkReceive entity)
    {
        return new List<long>();
    }

    protected override void OnAfterAudit(StkReceive entity)
    {
        base.OnAfterAudit(entity);

        // 如果物料没有启用收货检验，直接更新检验数量和合格数量
        foreach (var entry in entity.Entries)
        {
            if (!entry.Material.IsCheckReceive)
            {
                entry.CheckQty = entry.Qty;
                entry.ReceiveQty = entry.Qty;
            }

            // 保存数据
            Rep.Context.Updateable(entry).ExecuteCommand();
        }

        // 任务库存数量处理（如果有则在此处调用）

        // 更新条码档案
        UpdateBarcodeInfo(entity);

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkReceive entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
        if (entity.Status != StkReceiveStatus.UnHandle)
            throw Oops.Bah(StkErrorCode.StkReceive1001, entity.BillNo);

        // TODO: 收货单反审核，当下游单据创建了单据，但未审核时，无法根据业务状态或者回写的数量来判断是否能够反审核
    }

    protected override void OnAfterUnAudit(StkReceive entity)
    {
        base.OnAfterUnAudit(entity);

        // 如果物料没有启用收货检验，直接回滚检验数量和合格数量
        foreach (var entry in entity.Entries)
        {
            if (!entry.Material.IsCheckReceive)
            {
                entry.CheckQty = 0;
                entry.ReceiveQty = 0;
            }

            // 保存数据
            Rep.Context.Updateable(entry).ExecuteCommand();
        }

        // 回滚条码档案
        if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
            BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

        // TODO：批号档案处理

        // 回滚库存更新
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkReceive entity)
    {
        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = OpQtyType.Replace,
                CurStatus = barcodeEntry.Material.IsCheckReceive ? BdBarcodeStatus.WaitInspect : BdBarcodeStatus.Qualified,
                BatchNo = barcodeEntry.BatchNo,
                ProduceDate = barcodeEntry.Barcode.ProduceDate,
                ExpiryDate = barcodeEntry.Barcode.ExpiryDate,
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = null,
                SrcWhLocId = null,
                DestWhAreaId = entry.WhAreaId,
                DestWhLocId = entry.WhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkReceive entity)
    {
        var changes = entity.Entries.SelectMany(u =>
        {
            var innerChanges = new List<StkInvChange>
            {
                new StkInvChange
                {
                    InvLogType = StkInvLogType.ReceivePlus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                },
            };

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkReceive>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        // TODO: 手动完结状态需要跳过处理

        var srcBillKey = EntityName;

        // 查询
        var entities = Rep.Context.Queryable<StkReceive>()
            .Includes(u => u.Entries)
            .Includes(u => u.Entries, p => p.Material) // 包含物料的导航属性
            .Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            // 入库单明细集合
            var stkInStockEntryList = Rep.Context.Queryable<StkInStockEntry>()
                .LeftJoin<StkInStock>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .ToList();

            // 出库单明细集合
            var stkOutStockEntryList = Rep.Context.Queryable<StkOutStockEntry>()
                .LeftJoin<StkOutStock>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .Where((te, t) => t.Status == StkOutStockStatus.Finish) // 已发运
                .ToList();

            foreach (var entry in entity.Entries)
            {
                // 入库数量
                var inQty = stkInStockEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);
                // 出库数量（收货后退货）
                var outQty = stkOutStockEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);
                // 退货数量（上架后退货）
                var returnQty = stkInStockEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.ReturnQty);

                entry.InQty = inQty;
                entry.RemainInQty = entry.Qty - entry.InQty < 0 ? 0 : entry.Qty - entry.InQty;

                // 已退货数量 = 收货后退货 + 上架后退货
                entry.ReturnQty = outQty + returnQty;

                if (entry.InQty >= entry.Qty)
                    entry.EntryStatus = StkReceiveEntryStatus.Finish;
                else if (entry.InQty > 0)
                    entry.EntryStatus = StkReceiveEntryStatus.Handling;
                else
                    entry.EntryStatus = StkReceiveEntryStatus.UnHandle;
            }

            if (entity.Entries.All(u => u.EntryStatus == StkReceiveEntryStatus.Finish))
                entity.Status = StkReceiveStatus.Finish;
            else if (entity.Entries.Any(u => u.EntryStatus is StkReceiveEntryStatus.Handling or StkReceiveEntryStatus.Finish))
                entity.Status = StkReceiveStatus.Handling;
            else
                entity.Status = StkReceiveStatus.UnHandle;

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }

        // 刷新入库通知单状态
        var inNoticeIds = entities
            .SelectMany(u => u.Entries
                .Where(p => p.SrcBillKey == nameof(StkInNotice) && p.SrcBillId != null)
                .Select(p => p.SrcBillId.Value)
            )
            .Distinct()
            .ToList();
        if (inNoticeIds.Count > 0)
        {
            var stkInNoticeService = App.GetService<StkInNoticeService>(ServiceProvider);
            stkInNoticeService.RefreshStatus(inNoticeIds);
        }
    }

    /// <summary>
    /// 推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("push")]
    public Task<List<ExecResult>> Push(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = Rep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.Stk1029, entity.BillNo);

            var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
            var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkReceive>(EntityName).GetAwaiter().GetResult()
                .FirstOrDefault(u => u.Item1.Number == entity.BillType);

            if (string.IsNullOrEmpty(billTypeExt.Item2.PushSettingNumberToUse))
                throw Oops.Bah(StkErrorCode.Stk1030, entity.BillType);

            // 查询推送设置
            var pushSettingRep = ServiceProvider.GetService<SqlSugarRepository<EsSyncPushSetting>>();
            var pushSetting = pushSettingRep.GetFirst(u => u.Number == billTypeExt.Item2.PushSettingNumberToUse);

            var esType = pushSetting?.EsType;
            var settingNumber = pushSetting?.Number;

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var externalSyncService = ServiceProvider.GetService<ExternalSyncService>();
            var pushResults = externalSyncService.Push(new EsSyncPushInput2 { BillNo = entity.BillNo, SettingNumber = settingNumber, EsType = esType }).GetAwaiter().GetResult();
            var pushResult = pushResults.First();
            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            // 提交事务
            uow.Commit();

            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            return pushResult.Message;
        });

        return Task.FromResult(execResults);
    }
}