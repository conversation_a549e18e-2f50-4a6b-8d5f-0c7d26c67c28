﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案扩展
/// </summary>
public partial class BarBarcode
{
    /// <summary>
    /// 扩展字符01
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符01", Length = 255)]
    public string? ExtStr01 { get; set; }

    /// <summary>
    /// 扩展字符02
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符02", Length = 255)]
    public string? ExtStr02 { get; set; }

    /// <summary>
    /// 扩展字符03
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符03", Length = 255)]
    public string? ExtStr03 { get; set; }

    /// <summary>
    /// 扩展字符04
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符04", Length = 255)]
    public string? ExtStr04 { get; set; }

    /// <summary>
    /// 扩展字符05
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符05", Length = 255)]
    public string? ExtStr05 { get; set; }

    /// <summary>
    /// 扩展字符06
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符06", Length = 255)]
    public string? ExtStr06 { get; set; }

    /// <summary>
    /// 扩展字符07
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符07", Length = 255)]
    public string? ExtStr07 { get; set; }

    /// <summary>
    /// 扩展字符08
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符08", Length = 255)]
    public string? ExtStr08 { get; set; }

    /// <summary>
    /// 扩展字符09
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符09", Length = 255)]
    public string? ExtStr09 { get; set; }

    /// <summary>
    /// 扩展字符10
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展字符10", Length = 255)]
    public string? ExtStr10 { get; set; }


    /// <summary>
    /// 扩展数字01
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字01")]
    public decimal? ExtNum01 { get; set; }

    /// <summary>
    /// 扩展数字02
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字02")]
    public decimal? ExtNum02 { get; set; }

    /// <summary>
    /// 扩展数字03
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字03")]
    public decimal? ExtNum03 { get; set; }

    /// <summary>
    /// 扩展数字04
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字04")]
    public decimal? ExtNum04 { get; set; }

    /// <summary>
    /// 扩展数字05
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字05")]
    public decimal? ExtNum05 { get; set; }

    /// <summary>
    /// 扩展数字06
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字06")]
    public decimal? ExtNum06 { get; set; }

    /// <summary>
    /// 扩展数字07
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字07")]
    public decimal? ExtNum07 { get; set; }

    /// <summary>
    /// 扩展数字08
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字08")]
    public decimal? ExtNum08 { get; set; }

    /// <summary>
    /// 扩展数字09
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字09")]
    public decimal? ExtNum09 { get; set; }

    /// <summary>
    /// 扩展数字10
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展数字10")]
    public decimal? ExtNum10 { get; set; }


    /// <summary>
    /// 扩展日期01
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期01")]
    public DateTime? ExtDat01 { get; set; }

    /// <summary>
    /// 扩展日期02
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期02")]
    public DateTime? ExtDat02 { get; set; }

    /// <summary>
    /// 扩展日期03
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期03")]
    public DateTime? ExtDat03 { get; set; }

    /// <summary>
    /// 扩展日期04
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期04")]
    public DateTime? ExtDat04 { get; set; }

    /// <summary>
    /// 扩展日期05
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期05")]
    public DateTime? ExtDat05 { get; set; }

    /// <summary>
    /// 扩展日期06
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期06")]
    public DateTime? ExtDat06 { get; set; }

    /// <summary>
    /// 扩展日期07
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期07")]
    public DateTime? ExtDat07 { get; set; }

    /// <summary>
    /// 扩展日期08
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期08")]
    public DateTime? ExtDat08 { get; set; }

    /// <summary>
    /// 扩展日期09
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期09")]
    public DateTime? ExtDat09 { get; set; }

    /// <summary>
    /// 扩展日期10
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展日期10")]
    public DateTime? ExtDat10 { get; set; }


    /// <summary>
    /// 扩展布尔01
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔01")]
    public bool? ExtBool01 { get; set; }

    /// <summary>
    /// 扩展布尔02
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔02")]
    public bool? ExtBool02 { get; set; }

    /// <summary>
    /// 扩展布尔03
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔03")]
    public bool? ExtBool03 { get; set; }

    /// <summary>
    /// 扩展布尔04
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔04")]
    public bool? ExtBool04 { get; set; }

    /// <summary>
    /// 扩展布尔05
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔05")]
    public bool? ExtBool05 { get; set; }

    /// <summary>
    /// 扩展布尔06
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔06")]
    public bool? ExtBool06 { get; set; }

    /// <summary>
    /// 扩展布尔07
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔07")]
    public bool? ExtBool07 { get; set; }

    /// <summary>
    /// 扩展布尔08
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔08")]
    public bool? ExtBool08 { get; set; }

    /// <summary>
    /// 扩展布尔09
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔09")]
    public bool? ExtBool09 { get; set; }

    /// <summary>
    /// 扩展布尔10
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展布尔10")]
    public bool? ExtBool10 { get; set; }

    /// <summary>
    /// 扩展长字符
    /// </summary>
    [SugarColumn(ColumnDescription = "扩展长字符", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? ExtText { get; set; }
}