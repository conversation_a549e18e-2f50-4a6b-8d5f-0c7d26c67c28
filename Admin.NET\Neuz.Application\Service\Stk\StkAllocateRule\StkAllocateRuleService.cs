﻿using Neuz.Application.Model;

namespace Neuz.Application;

/// <summary>
/// 仓储分配规则服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkAllocateRule", Order = 100)]
public class StkAllocateRuleService : BaseBdService<StkAllocateRule, StkAllocateRuleLookupInput, LookupOutput>,
    IDynamicApiController, ITransient
{
    /// <summary>
    /// 仓储分配规则服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkAllocateRuleService(IServiceProvider serviceProvider, SqlSugarRepository<StkAllocateRule> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理实体名称的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "EntityName");
        var entityService = ServiceProvider.GetService<SysEntityService>();
        var entityList = entityService.ListAsync().GetAwaiter().GetResult();
        billTypeColumn.Options = entityList.Select(u => new SelectOption { Value = u.EntityName, Title = $"[{u.EntityName}]{u.Description}" }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "Number", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Name", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EntityName", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "Number",
            "Name",
            "EntityName",
            "Description",
            "IsForbid",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
        ];
    }

    protected override void OnBeforeAdd(StkAllocateRule entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkAllocateRule entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkAllocateRule entity)
    {
    }

    protected override void OnBeforeDelete(StkAllocateRule entity)
    {
        if (Rep.Context.Queryable<StkAllocatePolicyEntry>().Any(u => u.AllocateRuleId == entity.Id))
            throw Oops.Bah(StkErrorCode.StkAllocateRule1001, entity.Number);

        base.OnBeforeDelete(entity);
    }

    public override async Task<SqlSugarPagedList<LookupOutput>> LookupQueryAsync(StkAllocateRuleLookupInput input)
    {
        var entities = await Rep.AsQueryable()
            .Where(u => u.IsForbid == false)
            .Where(u => u.EntityName == input.EntityName) // 实体名称
            .WhereIF(!string.IsNullOrEmpty(input.Keyword), u => u.Number.Contains(input.Keyword) || u.Name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrEmpty(input.Number), u => u.Number.Contains(input.Number))
            .WhereIF(!string.IsNullOrEmpty(input.Name), u => u.Name.Contains(input.Name))
            .OrderBuilder(input)
            .Select(u => new LookupOutput(), true)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }
}