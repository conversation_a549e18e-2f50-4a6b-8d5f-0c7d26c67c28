﻿namespace Neuz.Core.Entity;

/// <summary>
/// 编码规则流水
/// </summary>
[SugarTable(null, "编码规则流水")]
public class SysCodeRuleSerial
{
    /// <summary>
    /// 编码规则Id
    /// </summary>
    [SugarColumn(ColumnDescription = "编码规则Id", IsPrimaryKey = true)]
    public long CodeRuleId { get; set; }

    /// <summary>
    /// 分组依据
    /// </summary>
    [SugarColumn(ColumnDescription = "分组依据", Length = 500, IsPrimaryKey = true)]
    public string ByValue { get; set; }

    /// <summary>
    /// 当前流水号
    /// </summary>
    [SugarColumn(ColumnDescription = "当前流水号")]
    public int CurNumber { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id", IsPrimaryKey = true)]
    public long TenantId { get; set; }
}