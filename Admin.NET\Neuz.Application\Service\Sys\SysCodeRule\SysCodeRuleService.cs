﻿using Neuz.Application.Model;

namespace Neuz.Application;

/// <summary>
/// 编码规则服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysCodeRule", Order = 100)]
public class SysCodeRuleService : BaseBdService<SysCodeRule, SysCodeRuleLookupInput, LookupOutput>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 流水仓储
    /// </summary>
    protected SqlSugarRepository<SysCodeRuleSerial> SerialRep { get; }

    /// <summary>
    /// 获取租户Id
    /// </summary>
    /// <returns></returns>
    public long? GetTenantId()
    {
        if (App.User == null) return null;
        return Convert.ToInt64(App.User.FindFirst(ClaimConst.TenantId)?.Value);
    }

    /// <summary>
    /// 编码规则服务构造函数
    /// </summary>
    public SysCodeRuleService(IServiceProvider serviceProvider,
        SqlSugarRepository<SysCodeRule> rep,
        SqlSugarRepository<SysCodeRuleSerial> serialRep) : base(serviceProvider, rep)
    {
        SerialRep = serialRep;
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理实体名称的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "EntityName");
        var entityService = ServiceProvider.GetService<SysEntityService>();
        var entityList = entityService.ListAsync().GetAwaiter().GetResult();
        billTypeColumn.Options = entityList.Select(u => new SelectOption { Value = u.EntityName, Title = $"[{u.EntityName}]{u.Description}" }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "Number", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EntityName", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Name", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "Number",
            "Name",
            "IsForbid",
            "EntityName",
            "Description",
            "CreateTime",
            "CreateUserName",
        ];
    }

    /// <inheritdoc />
    protected override void OnBeforeAdd(SysCodeRule entity)
    {
        base.OnBeforeAdd(entity);

        EntriesDataValidate(entity.Entries);
    }

    /// <inheritdoc />
    protected override void OnBeforeUpdate(SysCodeRule entity)
    {
        base.OnBeforeUpdate(entity);

        EntriesDataValidate(entity.Entries);
    }

    /// <inheritdoc />
    public override async Task<SqlSugarPagedList<LookupOutput>> LookupQueryAsync(SysCodeRuleLookupInput input)
    {
        var entities = await Rep.AsQueryable()
            .Where(u => u.IsForbid == false)
            .WhereIF(input.IsBarcodeCodeRule, u => u.EntityName == nameof(BarBarcode))
            .WhereIF(!input.IsBarcodeCodeRule && string.IsNullOrEmpty(input.EntityName), u => u.EntityName != nameof(BarBarcode))
            .WhereIF(!input.IsBarcodeCodeRule && !string.IsNullOrEmpty(input.EntityName), u => u.EntityName == input.EntityName)
            .WhereIF(!string.IsNullOrEmpty(input.Keyword), u => u.Number.Contains(input.Keyword) || u.Name.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrEmpty(input.Number), u => u.Number.Contains(input.Number))
            .WhereIF(!string.IsNullOrEmpty(input.Name), u => u.Name.Contains(input.Name))
            .OrderBuilder(input)
            .Select(u => new LookupOutput(), true)
            .ToPagedListAsync(input.Page, input.PageSize);

        return entities;
    }

    /// <summary>
    /// 生成编号示例
    /// </summary>
    /// <returns></returns>
    [HttpPost("generateNoExample")]
    public async Task<string> GenerateNoExampleAsync([FromBody] SysCodeRule codeRule)
    {
        return (await GenerateNoReturnInfoAsync(codeRule, null, true)).GenerateNo;
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRuleId">编码规则Id</param>
    /// <param name="refData">引用数据</param>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GenerateNoAsync(long codeRuleId, object refData)
    {
        return (await GenerateNoReturnInfoAsync(codeRuleId, refData)).GenerateNo;
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRuleNumber">编码规则编码</param>
    /// <param name="refData">引用数据</param>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GenerateNoAsync(string codeRuleNumber, object refData)
    {
        return (await GenerateNoReturnInfoAsync(codeRuleNumber, refData)).GenerateNo;
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRule">编码规则对象</param>
    /// <param name="refData">引用数据</param>
    [NonAction]
    public async Task<string> GenerateNoAsync(SysCodeRule codeRule, object refData)
    {
        return (await GenerateNoReturnInfoAsync(codeRule, refData, false)).GenerateNo;
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRuleId">编码规则Id</param>
    /// <param name="refData">引用数据</param>
    /// <returns></returns>
    [NonAction]
    public async Task<SysCodeRuleGenerateInfo> GenerateNoReturnInfoAsync(long codeRuleId, object refData)
    {
        var codeRule = await Rep.AsQueryable().Includes(u => u.Entries).FirstAsync(u => u.Id == codeRuleId);
        if (codeRule == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1000, codeRuleId);

        return await GenerateNoReturnInfoAsync(codeRule, refData);
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRuleNumber">编码规则编码</param>
    /// <param name="refData">引用数据</param>
    /// <returns></returns>
    [NonAction]
    public async Task<SysCodeRuleGenerateInfo> GenerateNoReturnInfoAsync(string codeRuleNumber, object refData)
    {
        var codeRule = await Rep.AsQueryable().Includes(u => u.Entries).FirstAsync(u => u.Number == codeRuleNumber);
        if (codeRule == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1007, codeRuleNumber);

        return await GenerateNoReturnInfoAsync(codeRule, refData);
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRule">编码规则对象</param>
    /// <param name="refData">引用数据</param>
    [NonAction]
    public Task<SysCodeRuleGenerateInfo> GenerateNoReturnInfoAsync(SysCodeRule codeRule, object refData)
    {
        return GenerateNoReturnInfoAsync(codeRule, refData, false);
    }

    /// <summary>
    /// 生成编号
    /// </summary>
    /// <param name="codeRule">编码规则实例</param>
    /// <param name="refData">引用数据</param>
    /// <param name="isExample">是否生成示例</param>
    /// <returns></returns>
    private Task<SysCodeRuleGenerateInfo> GenerateNoReturnInfoAsync(SysCodeRule codeRule, object refData, bool isExample)
    {
        var generateInfo = new SysCodeRuleGenerateInfo();

        // 仓储中的 Context 对象为 SqlSugarScope，当仓储在一个新线程下，仓储中的 Context 会自动变成新的 Context，来达到线程安全的目的
        // 但在调用方开启事务时，如果用 Task.Run 来异步执行逻辑，就会导致任务里面的 Context 与原来的 Context 不同，任务里的 Context 是在事务之外的
        // 因此这里的实现改为同步执行
        // return Task.Run(() => { });
        var type = refData?.GetType();
        if (refData != null && type.Name != codeRule.EntityName)
            throw Oops.Bah(SysErrorCode.SysCodeRule1001, codeRule.Number, codeRule.EntityName, type.Name);

        var sortEntries = codeRule.Entries.OrderBy(u => u.Seq).ToList();

        //分录设置校验
        EntriesDataValidate(sortEntries);

        //作为分组依据的项
        var codeOnlyByList = sortEntries.Where(u => u.CodeOnlyBy).ToList();
        //作为参与编码的项
        var codeElementList = sortEntries.Where(u => u.CodeElement).ToList();

        var builder = new StringBuilder();
        foreach (var entry in codeElementList)
        {
            var value = GetElementValue(codeRule, entry, refData, codeOnlyByList, isExample, out var objValue);
            builder.Append(value);
            if (entry.ElementType == ElementType.Serial)
            {
                generateInfo.SerialNo = Convert.ToDecimal(objValue);
                generateInfo.FormatSerialNo = value;
            }
        }

        generateInfo.GenerateNo = builder.ToString();
        generateInfo.CodeRule = codeRule;

        return Task.FromResult(generateInfo);
    }

    /// <summary>
    /// 获取元素的值
    /// </summary>
    /// <param name="codeRule">编码规则实例</param>
    /// <param name="entry">编码规则分录实例</param>
    /// <param name="refData">引用数据</param>
    /// <param name="codeOnlyByList">作为分组依据的项的集合</param>
    /// <param name="isExample">是否返回编码示例</param>
    /// <param name="objValue">返回未经格式化的原始值</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private string GetElementValue(SysCodeRule codeRule, SysCodeRuleEntry entry, object refData, IList<SysCodeRuleEntry> codeOnlyByList, bool isExample, out object objValue)
    {
        var refDataType = refData?.GetType();
        if (isExample)
        {
            switch (entry.ElementType)
            {
                case ElementType.Const:
                    objValue = entry.ConstValue;
                    break;
                case ElementType.TextField:
                    objValue = "文Ben值";
                    break;
                case ElementType.DateTimeField:
                    objValue = DateTime.Now;
                    break;
                case ElementType.NumberField:
                    objValue = 666;
                    break;
                case ElementType.EnumField:
                    objValue = "枚举名";
                    break;
                case ElementType.BaseData:
                    if (entry.ElementProperty == "Number")
                        objValue = "BD0001";
                    else if (entry.ElementProperty == "Name")
                        objValue = "资料名称";
                    else
                        objValue = "资料字段";
                    break;
                case ElementType.Serial:
                    objValue = entry.Seed;
                    break;
                default:
                    throw new NotImplementedException();
            }
        }
        else
        {
            switch (entry.ElementType)
            {
                case ElementType.Const:
                    objValue = entry.ConstValue;
                    break;
                case ElementType.TextField:
                case ElementType.DateTimeField:
                case ElementType.NumberField:
                case ElementType.EnumField:
                    objValue = GetRefDataPropValue(codeRule, entry.ElementName, refData, refDataType);
                    break;
                case ElementType.BaseData:
                    objValue = GetRefDataBaseDataPropValue(codeRule, entry.ElementName, entry.ElementProperty, refData, refDataType);
                    break;
                case ElementType.Serial:
                    objValue = GetSerialNumberValue(codeRule, entry.Seed, entry.Increment, refData, codeOnlyByList);
                    break;
                default:
                    throw new NotImplementedException();
            }
        }

        //格式化
        var value = FormatValue(objValue, entry.Format);
        //替代符
        if (string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(entry.ReChar))
            value = entry.ReChar;
        else
        {
            //需要填充
            if (entry.Length > 0 && entry.Length > value!.Length && !string.IsNullOrEmpty(entry.AddChar))
            {
                //右侧填充（True右侧填充 False左侧填充）
                value = entry.AddStyle ? value.PadRight(entry.Length, entry.AddChar[0]) : value.PadLeft(entry.Length, entry.AddChar[0]);
            }

            //右侧截断（True右侧截断 False不截断）
            if (entry.Length > 0 && value!.Length > entry.Length && entry.CutStyle)
                value = value.Substring(0, entry.Length);
        }

        return value;
    }

    /// <summary>
    /// 获取引用数据的属性值
    /// </summary>
    /// <param name="codeRule">编码规则实例</param>
    /// <param name="elementName">引用数据中的属性名</param>
    /// <param name="refData">引用数据</param>
    /// <param name="refDataType">引用数据的类型</param>
    /// <returns></returns>
    private object GetRefDataPropValue(SysCodeRule codeRule, string elementName, object refData, Type refDataType)
    {
        if (refData == null) return null;

        var property = refDataType.GetProperty(elementName);
        if (property == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1002, codeRule.Number, refDataType.Name, elementName);
        var value = property.GetValue(refData);
        if (value == null) return null;

        if (value is Enum)
        {
            //使用枚举的名称
            value = property.PropertyType.GetEnumDescDictionary().First(u => u.Key == (int)value).Value;
        }

        return value;
    }

    /// <summary>
    /// 获取引用数据的基础资料属性值
    /// </summary>
    /// <param name="codeRule">编码规则实例</param>
    /// <param name="elementName">引用数据中的属性名</param>
    /// <param name="elementProperty">引用数据中的属性下的属性名</param>
    /// <param name="refData">引用数据</param>
    /// <param name="refDataType">引用数据的类型</param>
    /// <returns></returns>
    private object GetRefDataBaseDataPropValue(SysCodeRule codeRule, string elementName, string elementProperty, object refData, Type refDataType)
    {
        if (refData == null) return null;

        //type: 如 PrdMo
        //entry.ElementName: 如 Material
        var property = refDataType.GetProperty(elementName);
        if (property == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1002, codeRule.Number, refDataType.Name, elementName);

        var entityInfo = Rep.Context.EntityMaintenance.GetEntityInfo(refDataType);
        if (entityInfo == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1003, codeRule.Number, refDataType.Name);

        var entityNavCol = entityInfo.Columns.FirstOrDefault(u => u.PropertyName == elementName);
        if (entityNavCol == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1004, codeRule.Number, refDataType.Name, elementName);

        var entityNavColType = entityNavCol.PropertyInfo.PropertyType;
        var entityNavColChildProperty = entityNavColType.GetProperty(elementProperty);
        if (entityNavColChildProperty == null)
            throw Oops.Bah(SysErrorCode.SysCodeRule1002, codeRule.Number, refDataType.Name, $"{elementName}.{elementProperty}");

        var value = property.GetValue(refData);
        //refData 指定的导航属性没有值
        if (value == null)
        {
            var entityNavColFkId = (long)entityInfo.Columns.First(u => u.PropertyName == entityNavCol.Navigat.GetName()).PropertyInfo.GetValue(refData)!;
            var navColEntityInfo = Rep.Context.EntityMaintenance.GetEntityInfo(entityNavColType);
            //带有缓存的查询，如果之前有缓存，则从缓存中返回
            //由于是无实体查询，返回的对象是 ExpandoObject 类型，需要重新映射为 entityNavColType
            //ExpandoObject 中的字典为【数据库】字段名，非实体的属性名称，只不过在当前设置（表名和字段名不转换成下划线格式）下
            //【数据库】字段名与实体属性名一致，可以直接映射
            //TODO：编码规则查询引用数据的基础资料时，在【数据库】字段名与实体的属性名称不一致时，会映射不到
            value = Rep.Context.Queryable<object>().AS(navColEntityInfo.DbTableName)
                .WithCache(10)
                .Where($"{nameof(EntityBaseId.Id)}=@id", new { id = entityNavColFkId }).First();
            value = value.Adapt(typeof(ExpandoObject), entityNavColType);
        }

        value = entityNavColChildProperty.GetValue(value);

        return value;
    }

    /// <summary>
    /// 获取流水号值
    /// </summary>
    /// <param name="codeRule">编码规则实例</param>
    /// <param name="seed">起始值</param>
    /// <param name="increment">步长</param>
    /// <param name="refData">引用数据</param>
    /// <param name="codeOnlyByList">作为分组依据的项的集合</param>
    /// <returns></returns>
    private decimal GetSerialNumberValue(SysCodeRule codeRule, int seed, int increment, object refData, IList<SysCodeRuleEntry> codeOnlyByList)
    {
        //分组依据的组合值
        var codeOnlyByValue = string.Join("", codeOnlyByList.Select(u => GetElementValue(codeRule, u, refData, null, false, out _)));
        var tenantId = GetTenantId() ?? 0;

        // 开启事务，如有外部事务，内部事务用外部事务
        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

        SysCodeRuleSerial serialEntity;
        var hasChange = SerialRep.AsUpdateable().SetColumns(u => u.CurNumber == u.CurNumber + increment)
            .Where(u => u.CodeRuleId == codeRule.Id && u.ByValue == codeOnlyByValue && u.TenantId == tenantId).ExecuteCommandHasChange();
        if (hasChange)
        {
            serialEntity = SerialRep.GetFirst(u => u.CodeRuleId == codeRule.Id && u.ByValue == codeOnlyByValue && u.TenantId == tenantId);
        }
        else
        {
            serialEntity = new SysCodeRuleSerial
            {
                CodeRuleId = codeRule.Id,
                ByValue = codeOnlyByValue,
                CurNumber = seed,
                TenantId = tenantId,
            };
            SerialRep.Insert(serialEntity);
        }

        // 提交事务
        uow.Commit();

        return serialEntity.CurNumber;
    }

    /// <summary>
    /// 格式化值
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="format">格式化字符串</param>
    /// <returns></returns>
    private static string FormatValue(object value, string format)
    {
        if (value == null) return "";
        if (string.IsNullOrWhiteSpace(format)) return value + "";

        var match = Regex.Match(format, @"(?<=^\{\d:).+(?=\})");
        if (match.Success)
            format = match.Value;

        /*
         * https://learn.microsoft.com/zh-cn/dotnet/standard/base-types/standard-numeric-format-strings
         * .NET 中数字格式化已定义如下符号，自定义符号时请避开
         * B 或 b，二进制（.net 8+）
         * C 或 c，货币
         * D 或 d，十进制
         * E 或 e，指数
         * F 或 f，定点
         * G 或 g，常规
         * N 或 n，数字
         * P 或 p，百分比
         * R 或 r，往返过程
         */
        format = $"{{0:{format}}}";
        switch (format)
        {
            case "{0:U}": // 转换为大写
                return (value + "").ToUpper();
            case "{0:L}": // 转换为小写
                return (value + "").ToLower();
            case "{0:Z}": // 转换为36进制（大写）
                return long.TryParse(value + "", out var z1) ? To36(z1) : value + "";
            case "{0:z}": // 转换为36进制（小写）
                return long.TryParse(value + "", out var z2) ? To36(z2, false) : value + "";
            default:
                return string.Format(format, value);
        }
    }

    /// <summary>
    /// 编码规则分录数据验证
    /// </summary>
    /// <param name="entries">编码规则分录集合</param>
    private void EntriesDataValidate(IList<SysCodeRuleEntry> entries)
    {
        if (entries.Count(u => u.ElementType == ElementType.Serial) > 1)
            throw Oops.Bah(SysErrorCode.SysCodeRule1012);

        foreach (var entry in entries)
        {
            switch (entry.ElementType)
            {
                case ElementType.Const:
                    if (string.IsNullOrEmpty(entry.ConstValue))
                        throw Oops.Bah(SysErrorCode.SysCodeRule1008, entry.Seq);
                    if (entry.CodeOnlyBy)
                        throw Oops.Bah(SysErrorCode.SysCodeRule1005, entry.Seq);
                    break;
                case ElementType.TextField:
                case ElementType.DateTimeField:
                case ElementType.NumberField:
                case ElementType.EnumField:
                    if (string.IsNullOrEmpty(entry.ElementName))
                        throw Oops.Bah(SysErrorCode.SysCodeRule1009, entry.Seq, entry.ElementType.GetDescription());
                    break;
                case ElementType.BaseData:
                    if (string.IsNullOrEmpty(entry.ElementName) || string.IsNullOrEmpty(entry.ElementProperty))
                        throw Oops.Bah(SysErrorCode.SysCodeRule1010, entry.Seq);
                    break;
                case ElementType.Serial:
                    if (entry.CodeOnlyBy)
                        throw Oops.Bah(SysErrorCode.SysCodeRule1006, entry.Seq);
                    if (entry.Increment <= 0)
                        throw Oops.Bah(SysErrorCode.SysCodeRule1011, entry.Seq);
                    break;
                default:
                    throw new NotImplementedException();
            }
        }
    }

    /// <summary>
    /// 将整形转换为36进制
    /// </summary>
    /// <param name="number">待转换的数字</param>
    /// <param name="isUpper">是否返回大写，true：大写，false：小写</param>
    /// <returns></returns>
    private static string To36(long number, bool isUpper = true)
    {
        var chars = "0123456789abcdefghijklmnopqrstuvwxyz";
        var sb = new StringBuilder();
        do
        {
            sb.Insert(0, chars[(int)(number % 36)]);
            number /= 36;
        } while (number > 0);

        return isUpper ? sb.ToString().ToUpper() : sb.ToString();
    }
}