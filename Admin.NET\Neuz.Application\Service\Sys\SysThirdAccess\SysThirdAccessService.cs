﻿using Furion.Localization;
using Neuz.Application.Model;

namespace Neuz.Application;

/// <summary>
/// 第三方系统登录身份服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysThirdAccess", Order = 100)]
public class SysThirdAccessService : NeuzBaseService<SysThirdAccess>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 第三方系统登录身份服务构造函数
    /// </summary>
    public SysThirdAccessService(IServiceProvider serviceProvider, SqlSugarRepository<SysThirdAccess> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);
        listColumns.Add(new ListColumn { FieldName = "BindTenantName", Title = L.Text["绑定租户名称"] });

        return listColumns;
    }

    protected override List<string> GetCustomQueryPageSelectFieldNames(CustomPageInput input, IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var selectFieldNames = base.GetCustomQueryPageSelectFieldNames(input, columnInfos);
        // 移除 BindTenantName，并添加 BindTenantId
        if (selectFieldNames.Contains("BindTenantName", StringComparer.OrdinalIgnoreCase))
        {
            selectFieldNames.Add("BindTenantId");
            selectFieldNames.Remove(selectFieldNames.Find(fieldName => fieldName.Equals("BindTenantName", StringComparison.OrdinalIgnoreCase)));
        }

        return selectFieldNames;
    }

    protected override void AfterCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, SqlSugarPagedList<Dictionary<string, object>> result)
    {
        base.AfterCustomQueryPage(columnInfos, input, result);

        // 查询绑定租户名称
        if (input.FieldNames.Contains("BindTenantName", StringComparer.OrdinalIgnoreCase))
        {
            var bindTenantIds = result.Items.Select(u => Convert.ToInt64(u["BindTenantId"])).ToList();
            if (bindTenantIds.Any())
            {
                var bindTenants = Rep.Change<SysTenant>().AsQueryable()
                    .LeftJoin<SysOrg>((t, o) => t.OrgId == o.Id)
                    .Where((t, o) => bindTenantIds.Contains(t.Id))
                    .Select((t, o) => new { t.Id, o.Name })
                    .ToList();

                foreach (var item in result.Items)
                {
                    item["BindTenantName"] = bindTenants.FirstOrDefault(o => o.Id == Convert.ToInt64(item["BindTenantId"]))?.Name;
                }
            }
        }
    }

    protected override string GetDeleteSuccessMsg(SysThirdAccess entity)
    {
        return L.Text["应用标识: {0} 删除成功", entity.AppKey];
    }

    protected override void OnBeforeAdd(SysThirdAccess entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSave(entity);
    }

    protected override void OnBeforeUpdate(SysThirdAccess entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSave(entity);
    }

    /// <summary>
    /// 保存前
    /// </summary>
    /// <param name="entity"></param>
    private void BeforeSave(SysThirdAccess entity)
    {
        if (Rep.AsQueryable().Any(u => u.AppKey == entity.AppKey && u.Id != entity.Id))
            throw Oops.Oh(SysErrorCode.SysThirdAccess1000);

        var repeatedAccounts = entity.Entries.GroupBy(u => u.ThirdUserAccount).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
        if (repeatedAccounts.Any())
            throw Oops.Bah(SysErrorCode.SysThirdAccess1001, string.Join(",", repeatedAccounts));
    }

    /// <summary>
    /// 获取第三方登录数据
    /// </summary>
    /// <param name="id">主键Id</param>
    /// <param name="thirdUserAccount">第三方系统用户账号</param>
    /// <returns></returns>
    [HttpGet("getThirdLoginData")]
    public async Task<string> GetThirdLoginData([FromQuery] long id, [FromQuery] string thirdUserAccount)
    {
        var entity = await Rep.GetFirstAsync(u => u.Id == id);
        if (entity == null)
            throw Oops.Bah(BaseErrorCode.Base1000, id);

        // 第三方登录数据
        var loginData = new SysThirdLoginData()
        {
            AppKey = entity.AppKey,
            UserAccount = thirdUserAccount,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
        };

        // 生成签名
        var appSecretByte = Encoding.UTF8.GetBytes(entity.AppSecret);
        var messageForSign = SysThirdAuthService.GetMessageForSign(loginData);
        string sign = SysThirdAuthService.SignData(appSecretByte, messageForSign);

        loginData.Sign = sign;

        // 序列化第三方登录数据
        var jsonStr = JSON.Serialize(loginData);

        // 16进制编码第三方登录数据
        return Convert.ToHexString(Encoding.UTF8.GetBytes(jsonStr));
    }

    /// <summary>
    /// 创建密钥
    /// </summary>
    /// <returns></returns>
    [DisplayName("创建密钥")]
    public Task<string> CreateSecret()
    {
        return Task.FromResult(Convert.ToBase64String(Guid.NewGuid().ToByteArray())[..^2]);
    }

    /// <summary>
    /// 获取应用密钥
    /// </summary>
    /// <param name="appKey">应用标识</param>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GetAppSecret(string appKey)
    {
        var thirdAccess = await Rep.AsQueryable().FirstAsync(u => u.AppKey == appKey);
        return thirdAccess == null ? "" : thirdAccess.AppSecret;
    }

    /// <summary>
    /// 根据 Key 获取应用密钥
    /// </summary>
    /// <param name="appKey">应用标识</param>
    /// <param name="thirdUserAccount">第三方系统用户账号</param>
    /// <returns></returns>
    [NonAction]
    public async Task<SysUser> GetBindUser(string appKey, string thirdUserAccount)
    {
        var userId = await Rep.Change<SysThirdAccessEntry>().AsQueryable()
            .LeftJoin<SysThirdAccess>((te, t) => te.Id == t.Id)
            .Where((te, t) => t.AppKey == appKey && te.ThirdUserAccount == thirdUserAccount)
            .Select((te, t) => te.BindUserId)
            .FirstAsync();

        return await Rep.Change<SysUser>().AsQueryable()
            .IncludesAllFirstLayer()
            .ClearFilter()
            .FirstAsync(u => u.Id == userId);
    }
}