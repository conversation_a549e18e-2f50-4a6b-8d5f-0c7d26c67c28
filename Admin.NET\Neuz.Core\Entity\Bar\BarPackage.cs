﻿namespace Neuz.Core.Entity;

/// <summary>
/// 装箱
/// </summary>
[SugarTable(null, "装箱")]
[SugarIndex("index_{table}_P", nameof(PackageBarcode), OrderByType.Asc)]
public class BarPackage : EntityTenant
{
    /// <summary>
    /// 箱码
    /// </summary>
    [SugarColumn(ColumnDescription = "箱码", Length = 200)]
    public string PackageBarcode { get; set; }

    /// <summary>
    /// 箱码状态
    /// </summary>
    [SugarColumn(ColumnDescription = "箱码状态")]
    public PackageStatus Status { get; set; }

    /// <summary>
    /// 父箱Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父箱Id")]
    public long ParentPackageId { get; set; }

    /// <summary>
    /// 父箱Ids
    /// </summary>
    /// <remarks>
    /// 描述整个父箱关系链
    /// </remarks>
    [SugarColumn(ColumnDescription = "父箱Ids", Length = 255)]
    public string ParentPackageIds { get; set; }

    /// <summary>
    /// 最后打印时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印时间")]
    public DateTime? LastPrintTime { get; set; }

    /// <summary>
    /// 已打印数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已打印数量")]
    public int PrintedQty { get; set; }

    /// <summary>
    /// 最后打印者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者Id")]
    public long? LastPrintUserId { get; set; }

    /// <summary>
    /// 最后打印者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者名称", Length = 20)]
    public string? LastPrintUserName { get; set; }

    /// <summary>
    /// 作废时间
    /// </summary>
    [SugarColumn(ColumnDescription = "作废时间")]
    public DateTime? DisuseTime { get; set; }

    /// <summary>
    /// 作废者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者Id")]
    public long? DisuseUserId { get; set; }

    /// <summary>
    /// 作废者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者名称", Length = 20)]
    public string? DisuseUserName { get; set; }

    /// <summary>
    /// 装箱限制
    /// </summary>
    [SugarColumn(ColumnDescription = "装箱限制")]
    public bool IsPackageLimit { get; set; }

    /// <summary>
    /// 箱内条码
    /// </summary>
    /// <remarks>
    /// 不设置导航属性，调用者自行处理
    /// </remarks>
    [SugarColumn(IsIgnore = true)]
    public IList<BarBarcode> Barcodes { get; set; }

    /// <summary>
    /// 构建事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "构建事务Id")]
    public long BuildTranId { get; set; }
}