﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.Scan.Wise;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Core.Entity.Stk;

namespace Neuz.Application.Pda.StockCount.Data;

public abstract class PdaStockCountModel : IPdaModel
{
    private const string ScanQtyKey = "ScanQty";
    private const string QtyKey = "Qty";

    /// <summary>
    /// 
    /// </summary>
    protected PdaDataCacheService PdaDataCacheService { get; }

    protected readonly IServiceProvider _serviceProvider;
    protected readonly UserManager _userManager;

    /// <summary>
    /// 日志组件
    /// </summary>
    protected ILogger<PdaStockCountModel> Logger => App.GetService<ILogger<PdaStockCountModel>>(_serviceProvider);

    protected PdaStockCountModel(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _userManager = serviceProvider.GetService<UserManager>();
        PdaDataCacheService = serviceProvider.GetService<PdaDataCacheService>();
    }

    /// <summary>
    /// Key
    /// </summary>
    public virtual string Key { get; set; } = "WiseStockCount";

    /// <summary>
    /// ErpKey
    /// </summary>
    public abstract string ErpKey { get; }

    ///// <summary>
    ///// 扫描条码仓库优先类型
    ///// </summary>
    //public virtual FirstStockType FirstStockType => FirstStockType.Barcode;

    /// <summary>
    /// 是否允许超源单数量
    /// </summary>
    public virtual bool IsOverSourceQty => true;
    /// <summary>
    /// 是否允许源单外物料
    /// </summary>
    public virtual bool IsOverSourceItem => true;

    /// <summary>
    /// 是否允许扫条码带源单
    /// </summary>
    public virtual bool IsScanBarcodeFindSource => false;

    /// <summary>
    /// 合并单据汇总字段(提交后合并)
    /// </summary>
    /// 默认只合并物料,批号,生产日期,仓库,计量单位
    public virtual List<string> SummaryBillFields { get; } = new()
    {
        "MaterialId",
        "MaterialNumber",
        "MaterialName",
        "UnitId",
        "UnitNumber",
        "UnitName",
        "BatchNo",
        "ProduceDate",
        "ExpPeriod",
        "AuxPropId",
        "AuxPropNumber",
        "AuxPropName",
        "StockId",
        "StockNumber",
        "StockName",
        "StockLocId",
        "StockLocNumber",
        "StockLocName",
        "SourceBillId",
        "SourceBillEntryId",
        "SourceBillKey"
    };

    /// <summary>
    /// 合并单据汇总字段(PDA显示合并)
    /// </summary>
    /// 默认只合并物料
    public virtual List<string> SummaryOperationFields { get; } = new() { "MaterialId" };

    /// <summary>
    /// 单据架构
    /// </summary>
    public IPdaSchema BillSchema { get; } = new PdaStockCountBillSchema();

    /// <summary>
    /// 扫描操作列表
    /// </summary>
    public List<IPdaScanBarcodeOperation> ScanBarcodeOperations { get; } = new List<IPdaScanBarcodeOperation>();

    /// <summary>
    /// 初始化
    /// </summary>
    public virtual void Initialization()
    {
        ScanBarcodeOperations.Add(App.GetService<PdaWiseStockCountScanStockOperation>(_serviceProvider));
        ScanBarcodeOperations.Add(App.GetService<PdaWiseStockCountScanBarcodeOperation>(_serviceProvider));
    }

    public Type DataType { get; } = typeof(PdaStockCountData);

    public virtual void BillDataInitialization(IPdaData pdaData)
    {

    }

    /// <summary>
    /// 查询Lookup
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="lookupKey"></param>
    /// <param name="lookupValue"></param>
    /// <returns></returns>
    public virtual List<PdaLookupOutput> QueryLookupData(long tranId, string lookupKey, string lookupValue)
    {
        var model = PdaDataCacheService.GetPdaBasicModel(lookupKey);
        if (model is IPdaBasicLookupModel basicModel)
        {
            return basicModel.GetLookupOutput(tranId, basicModel.QueryBasicData(tranId, lookupValue));
        }
        if (model is IPdaBillLookupModel billModel)
        {
            return billModel.GetLookupOutput(tranId, billModel.QuerySourceHead(tranId, lookupValue));
        }

        throw Oops.Bah(L.Text["错误的类型"]);
    }

    /// <summary>
    /// 设置Lookup数据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="lookupKey"></param>
    /// <param name="key"></param>
    /// <param name="valueKey"></param>
    /// <param name="lookupDataKey"></param>
    /// <param name="queryType"></param>
    /// <returns></returns>
    public virtual bool SelectLookupData(long tranId, string key, string lookupKey, string valueKey, string lookupDataKey, QueryType queryType = QueryType.Id)
    {
        var model = PdaDataCacheService.GetPdaBasicModel(lookupKey);
        DataTable lookupData = null;
        if (model is IPdaBasicLookupModel basicModel)
        {
            lookupData = basicModel.GetBasicData(tranId, valueKey);
        }

        if (model is IPdaBillLookupModel billModel)
        {
            if (queryType == QueryType.Id)
                lookupData = billModel.GetSourceHeadForId(tranId, valueKey);
            else
                lookupData = billModel.GetSourceHeadForBillNo(tranId, valueKey);
        }

        if (lookupData == null) throw Oops.Bah(L.Text["错误的类型"]);
        return SetLookupValueAction(tranId, key, lookupData, lookupKey, lookupDataKey);
    }

    public virtual List<BarBarcode> ScanBarcodeString(long tranId, string barcodeStr, string firstStockType, string scanBarcodeType)
    {
        PdaScanBarcodeArgs args = new PdaScanBarcodeArgs()
        {
            TranId = tranId,
            BarcodeString = barcodeStr,
            Barcodes = new List<BarBarcode>(),
            IsResult = false
        };
        args.Properties.Add("ScanBarcodeType", scanBarcodeType);
        args.Properties.Add("FirstStockType", firstStockType);
        foreach (var scanBarcodeOperation in ScanBarcodeOperations)
        {
            if (args.IsResult) break;
            scanBarcodeOperation.Operation(args);
        }
        if (!args.IsResult) throw Oops.Bah(PdaErrorCode.Pda1013, barcodeStr);
        return args.Barcodes;
    }

    public void DeleteBarcode(long tranId, string key, string detailId)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        if (!billData.BarcodeList.Exists(r => r.DetailId == detailId)) throw Oops.Bah(PdaErrorCode.Pda1029, detailId);
        List<PdaBarcode> tmpPdaBarcodes = new List<PdaBarcode>();
        billData.BarcodeList.ForEach(b =>
        {
            if (b.DetailId != detailId)
                tmpPdaBarcodes.Add(b);
        });
        billData.BarcodeList.Clear();
        billData.DetailIncludeBarcodes.Clear();
        //把扫描数量设置为0
        billData.ScanDetails.ForEach(r => r.ScanQty = 0);
        foreach (var barcode in tmpPdaBarcodes)
        {
            MatchingBarcode(tranId, Key, barcode, "", barcode.CalcBarcode.ScanQty);
        }

        RefreshShow(tranId);
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <returns></returns>
    public string Submit(long tranId, string key)
    {
        var sourceBillRelateKeys = new[] { "SourceBillId", "SourceBillNo", "SourceBillEntryId", "SourceBillEntrySeq", "SourceBillKey" };
        
        var stockCountData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var detailData = stockCountData.ScanDetails;
        var headData = stockCountData.ScanHead;
        if (detailData.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1022);
        var detailIncludeBarcodes = stockCountData.DetailIncludeBarcodes;
        if (detailIncludeBarcodes.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1023);

        DataTable submitTable = CreateSubmitTable();

        Dictionary<string, PdaSummaryInfo> summaryDetails = new Dictionary<string, PdaSummaryInfo>(StringComparer.OrdinalIgnoreCase);

        foreach (var detail in detailData)
        {
            var entryId = detail.DetailId;
            if (!detailIncludeBarcodes.ContainsKey(entryId)) continue;
            var pdaBarcodes = detailIncludeBarcodes[entryId];

            //条码的KEY
            foreach (PdaIncludeBarcode pdaBarcode in pdaBarcodes)
            {
                List<string> barcodeSummaryValues = new List<string>();
                //添加分录内码作为分组
                //barcodeSummaryValues.Add(entryId);
                var dic = detail.Adapt<PdaScanDetail>();
                foreach (string summaryBillField in SummaryBillFields)
                {
                    //如果是源单5件套,也需要忽略
                    if (sourceBillRelateKeys.Contains(summaryBillField))
                    {
                        barcodeSummaryValues.Add($"{detail[summaryBillField]}");
                        continue;
                    }
                    
                    barcodeSummaryValues.Add($"{pdaBarcode.PdaBarcode.CalcBarcode[summaryBillField]}");
                }

                string barcodeSummaryKey = string.Join("|", barcodeSummaryValues);
                if (!summaryDetails.ContainsKey(barcodeSummaryKey))
                {
                    summaryDetails.Add(barcodeSummaryKey, new PdaSummaryInfo());
                    foreach (string summaryBillField in SummaryBillFields)
                    {
                        //如果是源单5件套,也需要忽略
                        if (sourceBillRelateKeys.Contains(summaryBillField))
                        {
                            if (detail.IsNew)
                                dic[summaryBillField] = default;
                            continue;
                        }
                        
                        var value = pdaBarcode.PdaBarcode.CalcBarcode[summaryBillField];
                        if (GetIsCalcBarcodeFieldEmpty(summaryBillField, value)) continue;
                        dic[summaryBillField] = value;
                    }
                    dic.ScanQty = 0m;
                    summaryDetails[barcodeSummaryKey].Values = dic;
                }

                summaryDetails[barcodeSummaryKey].ScanQty = summaryDetails[barcodeSummaryKey].ScanQty + Convert.ToDecimal(pdaBarcode.ScanQty);
                summaryDetails[barcodeSummaryKey].PdaBarcodes.Add(pdaBarcode.PdaBarcode);
            }
        }

        foreach (KeyValuePair<string, PdaSummaryInfo> pair in summaryDetails)
        {
            var newRow = submitTable.NewRow();

            SetSubmitRowValue(stockCountData, newRow, headData, pair.Value);

            //大于0的数量才提交
            submitTable.Rows.Add(newRow);
        }

        if (submitTable.Rows.Count <= 0) throw Oops.Bah(L.Text["没有提交的明细!"]);
        Logger.LogError($"{((PdaStockCountBillSchema)BillSchema).BillLink.SourceKey}");
        Logger.LogError($"{((PdaStockCountBillSchema)BillSchema).BillLink.DestKey}");
        Logger.LogError($"{((PdaStockCountBillSchema)BillSchema).BillLink.Rob}");
        Logger.LogError($"{JsonConvert.SerializeObject(submitTable)}");

        var saveInterId = "";
        var saveBillNo = "";
        PdaSubmitMessage result = SaveBill(tranId, ((PdaStockCountBillSchema)BillSchema).BillLink.SourceKey, ((PdaStockCountBillSchema)BillSchema).BillLink.DestKey, ((PdaStockCountBillSchema)BillSchema).BillLink.Rob, submitTable);

        //保存成功
        if (result.ErrCode == 0)
        {
            //保存成功删除billData
            PdaDataCacheService.DelBillData(tranId);
            saveInterId = result.InterId;
            saveBillNo = result.BillNo;
        }
        //保存失败
        else
        {
            throw Oops.Bah(result.Message);
        }

        //保存成功，改条码状态 (1. 入库状态, 2, 首次入库状态， 3. 仓库仓位)
        List<long> barcodeIds = new List<long>();
        foreach (var pdaBarcodes in detailIncludeBarcodes)
        {
            foreach (PdaIncludeBarcode pdaBarcode in pdaBarcodes.Value)
            {
                barcodeIds.Add(pdaBarcode.PdaBarcode.Barcode.Id);
            }
        }

        var barcodeRep = App.GetService<SqlSugarRepository<BarBarcode>>(_serviceProvider);
        var barcodeLogRep = App.GetService<SqlSugarRepository<BarBarcodeLog>>(_serviceProvider);
        var bs = barcodeRep.GetList(b => barcodeIds.Contains(b.Id));
        Dictionary<long, PdaBillModelBase.PdaBarcodeInfo> infos = GetSubmitWriteBackBillInfo(tranId, result, summaryDetails);

        var logList = new List<BarBarcodeLog>();
        bs.ForEach(b =>
        {
            //写日志
            var pd = infos[b.Id];
            //if (pd == null) throw Oops.Bah(PdaErrorCode.PDA_1026, b.Id);
            PdaBarcode pb = null;
            if (pd != null)
            {
                pb = pd.PdaBarcode;
            }
            else
            {
                Logger.LogInformation(L.Text["事务[{0}]GetSubmitWriteBackBillInfo返回的条码Id[{1}]没有找到pdaBarcode", tranId, b.Id]);
                //如果infos没有对应的pdaBarcode,就去detailIncludeBarcodes下面取
                bool isExist = false;
                foreach (var pdaBarcodes in detailIncludeBarcodes)
                {
                    if (isExist) break;
                    foreach (PdaIncludeBarcode pdaBarcode in pdaBarcodes.Value)
                    {
                        if (pdaBarcode.PdaBarcode.Barcode.Id.Equals(b.Id))
                        {
                            pb = pdaBarcode.PdaBarcode;
                            isExist = true;
                            break;
                        }
                    }
                }
            }
            if (pd == null) throw Oops.Bah(PdaErrorCode.Pda1026, b.Id);

            if (b.Qty != pb.CalcBarcode.ScanQty || b.StockId != pb.CalcBarcode.StockId || b.StockLocId != pb.CalcBarcode.StockLocId)
            {
                b.Qty = pb.CalcBarcode.ScanQty;
                b.StockId = pb.CalcBarcode.StockId;
                b.StockNumber = pb.CalcBarcode.StockNumber;
                b.StockName = pb.CalcBarcode.StockName;
                b.StockLocId = pb.CalcBarcode.StockLocId;
                b.StockLocNumber = pb.CalcBarcode.StockNumber;
                b.StockLocName = pb.CalcBarcode.StockLocName;
            }

            var log = new BarBarcodeLog
            {
                Id = YitIdHelper.NextId(),
                OpTranId = tranId,
                OpFuncKey = Key,
                BarcodeId = pb.Barcode.Id,
                Barcode = pb.Barcode.Barcode,
                OriginQty = pb.Barcode.Qty,
                OpQty = pb.CalcBarcode.ScanQty,
                BatchNo = $"{pb.CalcBarcode.BatchNo}",
                ProduceDate = pb.CalcBarcode.ProduceDate,
                ExpiryDate = null,
                OpType = BarOpType.StockCount,
                TargetBillKey = pd?.TargetBillKey,
                TargetBillId = pd?.TargetBillId,
                TargetBillEntryId = pd?.TargetBillEntryId,
                TargetBillNo = pd?.TargetBillNo,
                TargetBillEntrySeq = pd?.TargetBillEntrySeq,
                SourceStockId = pb.Barcode.StockId,
                SourceStockNumber = pb.Barcode.StockNumber,
                SourceStockName = pb.Barcode.StockName,
                SourceStockLocId = pb.Barcode.StockLocId,
                SourceStockLocNumber = pb.Barcode.StockLocNumber,
                SourceStockLocName = pb.Barcode.StockLocName,
                TargetStockId = pb.CalcBarcode.StockId,
                TargetStockNumber = pb.CalcBarcode.StockNumber,
                TargetStockName = pb.CalcBarcode.StockName,
                TargetStockLocId = pb.CalcBarcode.StockLocId,
                TargetStockLocNumber = pb.CalcBarcode.StockLocNumber,
                TargetStockLocName = pb.CalcBarcode.StockLocName,
            };
            logList.Add(log);
        });
        barcodeRep.UpdateRange(bs);
        barcodeLogRep.InsertRange(logList);

        return $"{result.Message}";
    }

    /// <summary>
    /// 获取计算用条码列的值是否为空
    /// </summary>
    /// <param name="summaryBillField"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    protected virtual bool GetIsCalcBarcodeFieldEmpty(string summaryBillField, object value)
    {
        return string.IsNullOrEmpty(value + "");
    }

    protected abstract Dictionary<long, PdaBillModelBase.PdaBarcodeInfo> GetSubmitWriteBackBillInfo(long tranId, PdaSubmitMessage pdaSubmitMessage, Dictionary<string, PdaSummaryInfo> summaryDetails);


    /// <summary>
    /// 重置盘点方案
    /// </summary>
    /// <returns></returns>
    public string ResetStockCount(long tranId, string key)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);

        PdaSubmitMessage result = ResetBill(billData.ScanHead.SourceBillId);

        var saveInterId = "";
        var saveBillNo = "";
        //保存成功
        if (result.ErrCode == 0)
        {
            //保存成功删除billData
            PdaDataCacheService.DelBillData(tranId);
            saveInterId = result.InterId;
            saveBillNo = result.BillNo;
        }
        //保存失败
        else
        {
            throw Oops.Bah(result.Message);
        }

        //写日志
        var logRep = App.GetService<SqlSugarRepository<StkStockCountResetLog>>(_serviceProvider);
        var log = new StkStockCountResetLog
        {
            Id = YitIdHelper.NextId(),
            CreateTime = DateTime.Now,
            UpdateTime = null,
            CreateUserId = _userManager.UserId,
            CreateUserName = _userManager.Account,
            UpdateUserId = null,
            UpdateUserName = null,
            IsDelete = false,
            TenantId = _userManager.TenantId,
            StockCountId = saveInterId,
            BillNo = saveBillNo
        };
        logRep.Insert(log);

        return $"{result.Message}";
    }

    /// <summary>
    /// 保存
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="billLinkSourceKey"></param>
    /// <param name="billLinkDestKey"></param>
    /// <param name="billLinkRob"></param>
    /// <param name="submitTable"></param>
    /// <returns></returns>
    public abstract PdaSubmitMessage SaveBill(long tranId, string billLinkSourceKey, string billLinkDestKey, int billLinkRob, DataTable submitTable);

    /// <summary>
    /// 重置
    /// </summary>
    /// <param name="billId"></param>
    /// <returns></returns>
    public abstract PdaSubmitMessage ResetBill(string billId);

    /// <summary>
    /// 赋值
    /// </summary>
    /// <param name="stockCountData"></param>
    /// <param name="newRow"></param>
    /// <param name="headData"></param>
    /// <param name="summaryInfo"></param>
    public abstract void SetSubmitRowValue(PdaStockCountData stockCountData, DataRow newRow, PdaStockCountScanHead headData, PdaSummaryInfo summaryInfo);

    /// <summary>
    /// 创建table
    /// </summary>
    /// <returns></returns>
    public abstract DataTable CreateSubmitTable();

    /// <summary>
    /// 设置Lookup值
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="table"></param>
    /// <param name="lookupKey"></param>
    /// <param name="lookupDataKey"></param>
    /// <returns></returns>
    public virtual bool SetLookupValueAction(long tranId, string key, DataTable table, string lookupKey, string lookupDataKey)
    {
        if (table == null || table.Rows.Count <= 0) return false;
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        switch (lookupDataKey)
        {
            //回写源单
            case "SourceInfo":
                if (table.Rows.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1006);
                var interId = $"{table.Rows[0]["SourceBillId"]}";
                var lookupModel = PdaDataCacheService.GetPdaBasicModel(lookupKey);
                var billLookupModel = lookupModel as IPdaBillLookupModel;
                if (billLookupModel == null) throw Oops.Bah(PdaErrorCode.Pda1030);
                var sourceList = billLookupModel.GetSourceDetailForIds(tranId, new List<string> { interId });
                if (sourceList.Rows.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1021);
                //这里暂时认为，源单的字段，就是目标单的字段
                SetSourceInfo(tranId, key, table, sourceList);
                return true;
            //回写仓库
            case "StockInfo":
                if (table == null || table.Rows.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1010);
                billData.StockInfo = table.Rows[0].ToDictionary().Adapt<PdaBillStockInfo>();
                RefreshShow(tranId);
                return true;
            //其他Lookup
            default:
                return true;
        }
    }

    public virtual List<PdaStockCountScanDetail> GetTotalShowDetails(List<PdaStockCountScanDetail> scanDetails)
    {
        if (scanDetails.Count > 50)
        {
            return scanDetails.Where(r => r.ScanQty > 0).ToList();
        }

        //盘点不合并
        return scanDetails;

        //按什么条件合并汇总行信息
        //暂时按物料
    }

    public PdaStockCountDetailShowInfo SetDetailValueShow(Dictionary<string, List<PdaIncludeBarcode>> detailIncludeBarcodes, PdaStockCountScanDetail pdaScandDetail)
    {
        PdaStockCountDetailShowInfo info = new PdaStockCountDetailShowInfo
        {
            DetailId = pdaScandDetail.DetailId,
            Title = $"[{pdaScandDetail.MaterialNumber}]{pdaScandDetail.MaterialName}",
            SubTitle = $"{pdaScandDetail.BatchNo} - {pdaScandDetail.StockName}[{pdaScandDetail.ScStockLocName}]",
            ScanQty = pdaScandDetail.ScanQty,
            CheckQty = pdaScandDetail.CheckQty,
            Qty = pdaScandDetail.Qty,
            IsNew = pdaScandDetail.IsNew,
        };
        if (detailIncludeBarcodes.ContainsKey(pdaScandDetail.DetailId))
        {
            var bs = detailIncludeBarcodes[pdaScandDetail.DetailId]?.ToList().Select(r => r.PdaBarcode.DetailId);
            if (bs != null)
            {
                var bList = bs.ToList();
                if (bList.ToList().Any())
                    info.IncludeBarcodes.AddRange(bList);
            }
        }

        return info;
    }

    /// <summary>
    /// 设置目标头字段值
    /// </summary>
    /// <param name="scanDetails"></param>
    /// <param name="sourceHeads"></param>
    /// <param name="sourceDetails"></param>
    /// <param name="scanHead"></param>
    /// <exception cref="NotImplementedException"></exception>
    public PdaStockCountScanHead SetScanHeadValue(PdaStockCountScanHead scanHead, List<PdaStockCountScanDetail> scanDetails, List<PdaStockCountSourceHead> sourceHeads, List<PdaStockCountSourceDetail> sourceDetails)
    {
        //使用第一个源单头信息为准
        var head = new PdaStockCountScanHead();
        if (sourceHeads == null || sourceHeads.Count <= 0) return head;
        head = sourceHeads[0].Adapt<PdaStockCountScanHead>();
        //如果目标单日期字段有值,不更新
        if (scanHead.Date != null) head.Date = scanHead.Date;
        return head;
    }

    #region StockCountData

    /// <summary>
    /// 修改条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="detailId"></param>
    /// <param name="fieldName"></param>
    /// <param name="value"></param>
    public void SetBarcodeValue(long tranId, string key, string detailId, string fieldName, object value)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var barcode = billData.BarcodeList.FirstOrDefault(r => r.DetailId == detailId);
        if (barcode == null) throw Oops.Bah(PdaErrorCode.Pda1028);
        barcode.CalcBarcode[fieldName] = value;
    }

    /// <summary>
    /// 设备表头的值
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="fieldName"></param>
    /// <param name="value"></param>
    public void SetScanHeadValue(long tranId, string key, string fieldName, object value)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        billData.ScanHead[fieldName] = value;
    }

    /// <summary>
    /// 刷新扫描数据
    /// </summary>
    private void RefreshScanDataShow(long tranId, string key)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = PdaDataCacheService.GetPdaModel(billData.ModelKey);
        StringBuilder errBuilder = new StringBuilder();

        ((PdaStockCountBillSchema)((PdaStockCountModel)pdaModel).BillSchema).DestHead.ForEach(b =>
       {
           ((PdaStockCountDataShow)billData.DataShow).DestData.Head[b.Fieldname] = GetFormatTypeValue(b, billData.ScanHead[b.Fieldname]);
       });
        ((PdaStockCountDataShow)billData.DataShow).SourceBill.Clear();
        ((PdaStockCountDataShow)billData.DataShow).DestData.Details.Clear();
        if (billData.ScanDetails.Count > 0)
        {
            //可能需要根据条件,合并明细在前端显示 (比如按物料汇总)
            List<PdaStockCountScanDetail> totalDetails = ((PdaStockCountModel)pdaModel).GetTotalShowDetails(billData.ScanDetails);

            foreach (PdaStockCountScanDetail pdaScanDetail in totalDetails)
            {
                PdaStockCountDetailShowInfo detail = ((PdaStockCountModel)pdaModel).SetDetailValueShow(billData.DetailIncludeBarcodes, pdaScanDetail);
                ((PdaStockCountDataShow)billData.DataShow).DestData.Details.Add(detail);
            }

            //设置SourceInfo,这里只显示单据号,如要特殊处理,需二开
            ((PdaStockCountDataShow)billData.DataShow).SourceBill = billData.SourceHeads.Select(r => r.SourceBillNo).ToList();

            //盘点统计信息
            ((PdaStockCountDataShow)billData.DataShow).Summary.Qty = billData.SourceDetails.Sum(r => r.Qty);
            ((PdaStockCountDataShow)billData.DataShow).Summary.CheckQty = billData.SourceDetails.Sum(r => r.CheckQty);
            ((PdaStockCountDataShow)billData.DataShow).Summary.ProfitQty = billData.ScanDetails.Sum(r => r.CheckQty + r.ScanQty - r.Qty > 0 ? r.CheckQty + r.ScanQty - r.Qty : 0);
            ((PdaStockCountDataShow)billData.DataShow).Summary.LoseQty = billData.ScanDetails.Sum(r => r.Qty - r.CheckQty - r.ScanQty > 0 ? r.Qty - r.ScanQty - r.CheckQty : 0);
            ((PdaStockCountDataShow)billData.DataShow).Summary.ScanQty = billData.ScanDetails.Sum(r => r.ScanQty);
        }

        if (errBuilder.Length > 0) throw Oops.Bah(errBuilder);
    }

    /// <summary>
    /// 刷新PDA显示
    /// </summary>
    public void RefreshShow(long tranId)
    {
        //刷新条码
        RefreshBarcodeShow(tranId, Key);

        //刷新DestData.Details
        RefreshScanDataShow(tranId, Key);
        //刷新仓库
        RefreshStockShow(tranId, Key);

        //保存到Redis
        SaveRedis(tranId, Key);
    }

    private void SaveRedis(long tranId, string key)
    {
        var sysCacheService = App.GetService<SysCacheService>();
        var billData = PdaDataCacheService.GetBillData(key, tranId);
        var jsonStr = JsonConvert.SerializeObject(billData, new JsonSerializerSettings()
        {
            // 首字母小写(驼峰样式)
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            // 时间格式化
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            // 忽略循环引用
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            // 忽略空值
            NullValueHandling = NullValueHandling.Ignore
        });
        sysCacheService.Set($"pda_tranId_{tranId}", Encoding.UTF8.GetBytes(jsonStr), new TimeSpan(7, 0, 0, 0));
        sysCacheService.Set($"pda_billdata_{billData.ModelKey}_{billData.UserId}", tranId + "", new TimeSpan(7 * 2, 0, 0, 0));
    }

    private string GetFormatTypeValue(PdaColumn column, object value)
    {
        if (column == null) return value + "";
        switch (column.Type)
        {
            case "date":
                var date = value == null || value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDateTime(value).ToString("yyyy-MM-dd");
                return date;
            case "number":
                var number = value == null || value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDecimal(value).ToString(PdaHelper.DecimalPrecision);
                return number;
            default:
                return value + "";
        }
    }

    /// <summary>
    /// 刷新条码在PDA显示
    /// </summary>
    public void RefreshBarcodeShow(long tranId, string key)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = PdaDataCacheService.GetPdaModel(billData.ModelKey);
        //刷新条码
        ((PdaStockCountDataShow)billData.DataShow).Barcodes.Clear();
        billData.BarcodeList.ForEach(b =>
        {
            var barcode = new PdaBarcodeShowInfo();
            foreach (PdaColumn column in ((PdaStockCountBillSchema)((PdaStockCountModel)pdaModel).BillSchema).Barcode)
            {
                if (column.Fieldname.Equals(ScanQtyKey) || column.Fieldname.Equals(QtyKey)) continue;
                //这里第一个字要转大写，不然反射不认
                var value = b.CalcBarcode[column.Fieldname];
                //PdaHelper.GetValue(b.BarBarcode, column.Fieldname.Substring(0, 1).ToUpper() + column.Fieldname.Substring(1));
                barcode[column.Fieldname] = GetFormatTypeValue(column, value);
            }

            barcode.ScanQty = $"{b.CalcBarcode.ScanQty.ToString(PdaHelper.DecimalPrecision)}";
            barcode.Qty = $"{b.Barcode.Qty.ToString(PdaHelper.DecimalPrecision)}";
            barcode.BarcodeId = $"{b.Barcode.Id}";
            barcode.DetailId = b.DetailId;
            barcode.Title = b.Barcode.Barcode;
            barcode.SubTitle = b.Barcode.MaterialName;
            barcode.StockKey = $"{b.CalcBarcode.StockId}_{b.CalcBarcode.StockLocId}";
            barcode.StockValue = $"{b.CalcBarcode.StockName}[{b.CalcBarcode.StockLocName}]";
            barcode.Value = L.Text["数量:{0}/{1}", b.CalcBarcode.ScanQty.ToString(PdaHelper.DecimalPrecision), b.Barcode.Qty.ToString(PdaHelper.DecimalPrecision)];
            ((PdaStockCountDataShow)billData.DataShow).Barcodes.Add(barcode);
        });
    }

    /// <summary>
    /// 刷新仓库仓位PDA显示
    /// </summary>
    public void RefreshStockShow(long tranId, string key)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        ((PdaStockCountDataShow)billData.DataShow).Stock = (string.IsNullOrEmpty(billData.StockInfo.StockId) || billData.StockInfo.StockId == "0")
            ? ""
            : $"[{billData.StockInfo.StockNumber}]{billData.StockInfo.StockName} - [{billData.StockInfo.StockLocNumber}]{billData.StockInfo.StockLocName}";
    }

    /// <summary>
    /// 选择源单
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="headTable"></param>
    /// <param name="detailTable"></param>
    public void SetSourceInfo(long tranId, string key, DataTable headTable, DataTable detailTable)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = PdaDataCacheService.GetPdaModel(billData.ModelKey);
        //暂时只支持单个源单，不支持多源单
        var headDics = headTable.ToDictionary().Adapt<List<PdaStockCountSourceHead>>();
        var detailDics = detailTable.ToDictionary().Adapt<List<PdaStockCountSourceDetail>>();
        //TODO: 判断是否存在
        List<PdaStockCountSourceHead> removeHeads = new List<PdaStockCountSourceHead>();
        List<PdaStockCountSourceDetail> removeDetails = new List<PdaStockCountSourceDetail>();
        foreach (PdaStockCountSourceHead headDic in headDics)
        {
            if (billData.SourceHeads.Exists(r => r.SourceBillId == headDic.SourceBillId))
                removeHeads.Add(headDic);
        }
        foreach (PdaStockCountSourceDetail detailDic in detailDics)
        {
            if (billData.SourceDetails.Exists(r => r.SourceBillId == detailDic.SourceBillId && r.SourceBillEntryId == detailDic.SourceBillEntryId && r.SourceBillEntrySeq == detailDic.SourceBillEntrySeq))
                removeDetails.Add(detailDic);
        }

        if (removeHeads.Count > 0)
        {
            foreach (PdaStockCountSourceHead removeHead in removeHeads)
            {
                headDics.Remove(removeHead);
            }
        }
        if (removeDetails.Count > 0)
        {
            foreach (PdaStockCountSourceDetail removeDetail in removeDetails)
            {
                detailDics.Remove(removeDetail);
            }
        }

        //判断是否匹配源单
        if (billData.SourceHeads.Count > 0) throw Oops.Bah(L.Text["已存在盘点方案"]);
        billData.SourceHeads.AddRange(headDics);
        billData.SourceDetails.AddRange(detailDics);
        //设置目标头字段值
        PdaStockCountScanHead head = ((PdaStockCountModel)pdaModel).SetScanHeadValue(billData.ScanHead, billData.ScanDetails, billData.SourceHeads, billData.SourceDetails);
        billData.ScanHead = head;
        var sourceDetails = detailDics.Adapt<List<PdaStockCountScanDetail>>();
        sourceDetails.ForEach(r => r.DetailId = $"{YitIdHelper.NextId()}");
        billData.ScanDetails.AddRange(sourceDetails);

        RefreshShow(tranId);
    }

    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="barcode"></param>
    /// <param name="firstStockType"></param>
    /// <param name="modifyQty"></param>
    public void ScanBarcode(long tranId, string key, BarBarcode barcode, string firstStockType, decimal? modifyQty = null)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = PdaDataCacheService.GetPdaModel(billData.ModelKey);
        //条码只影响 DetailTable
        //现在暂不支持无源单
        //1. 首先检查 DetailTable是否满足合并要求，并且数量不能超, 该条码不能在Barcodes表存在
        //2. 合并数量
        //3. 添加条码表

        //判断条码是否有仓库仓位
        var barcodeHasStock = !(string.IsNullOrEmpty(barcode.StockId) || barcode.StockId == "0");
        var stockInfoHasStock = !(string.IsNullOrEmpty(billData.StockInfo.StockId) || billData.StockInfo.StockId == "0");
        if (!barcodeHasStock && !stockInfoHasStock) throw Oops.Bah(PdaErrorCode.Pda1017);
        if (!stockInfoHasStock && GetFirstStockType(firstStockType) == FirstStockType.Select)
            throw Oops.Bah(PdaErrorCode.Pda1020);

        //有源单,并且源单的KEY为该单据的源单KEY
        if (!string.IsNullOrEmpty(barcode.SourceBillId) && !barcode.SourceBillId.Equals("0") &&
            $"{barcode.SourceBillKey}".Equals(((PdaStockCountBillSchema)((PdaStockCountModel)pdaModel).BillSchema).BillLink.SourceKey) && ((PdaStockCountModel)pdaModel).IsScanBarcodeFindSource)
        {
            var lookupModel = PdaDataCacheService.GetPdaBasicModel($"{((PdaStockCountBillSchema)((PdaStockCountModel)pdaModel).BillSchema).BillLink.SourceKey}_{((PdaStockCountBillSchema)((PdaStockCountModel)pdaModel).BillSchema).BillLink.DestKey}");
            var billModel = lookupModel as IPdaBillLookupModel;
            if (billModel == null) throw Oops.Bah(PdaErrorCode.Pda1030);
            var headTable = billModel.GetSourceHeadForId(tranId, barcode.SourceBillId);
            var detailTable = billModel.GetSourceDetailForIds(tranId, new List<string> { barcode.SourceBillId });

            //查到有源单，先把源单加载
            if (headTable.Rows.Count > 0 && detailTable.Rows.Count > 0)
            {
                SetSourceInfo(tranId, key, headTable, detailTable);
            }
        }

        //查询源单，如果没有源单
        if (billData.ScanDetails.Count <= 0)
        {
            //如果不支持源单外物料 报错
            if (!((PdaStockCountModel)pdaModel).IsOverSourceItem) throw Oops.Bah(PdaErrorCode.Pda1011);
        }

        MatchingBarcode(tranId, Key, new PdaBarcode(barcode, modifyQty ?? barcode.Qty), firstStockType, modifyQty);

        //刷新
        RefreshShow(tranId);
    }

    /// <summary>
    /// 匹配条码到明细行
    /// </summary>
    private void MatchingBarcode(long tranId, string key, PdaBarcode inPutPdaBarcode, string firstStockType, decimal? modifyQty = null)
    {
        var billData = (PdaStockCountData)PdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = PdaDataCacheService.GetPdaModel(billData.ModelKey);

        var pdaBarcode = inPutPdaBarcode;
        pdaBarcode.CalcBarcode.ScanQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;
        //反写仓库(不管源单是否有仓库)
        //如果条码没仓库，以选的仓库为准
        var barcodeHasStock = !(string.IsNullOrEmpty(inPutPdaBarcode.Barcode.StockId) || inPutPdaBarcode.Barcode.StockId == "0");
        if (!barcodeHasStock)
        {
            pdaBarcode.SetStockInfo(billData.StockInfo.Adapt<PdaBillStockInfo>(), false);
        }
        else
        {
            //如果有选仓库，看看仓库和条码的是不是一致，不一致用条码的
            PdaBillStockInfo barcodeStockInfo = billData.StockInfo.Adapt<PdaBillStockInfo>();
            if (!$"{billData.StockInfo.StockId}_{billData.StockInfo.StockLocId}".Equals($"{inPutPdaBarcode.Barcode.StockId}_{inPutPdaBarcode.Barcode.StockLocId}"))
            {
                //条码优先
                if (!string.IsNullOrEmpty(firstStockType) && GetFirstStockType(firstStockType) == FirstStockType.Barcode)
                {

                    barcodeStockInfo = new PdaBillStockInfo();
                    barcodeStockInfo.StockId = inPutPdaBarcode.Barcode.StockId;
                    barcodeStockInfo.StockNumber = inPutPdaBarcode.Barcode.StockNumber;
                    barcodeStockInfo.StockName = inPutPdaBarcode.Barcode.StockName;
                    barcodeStockInfo.StockLocId = inPutPdaBarcode.Barcode.StockLocId;
                    barcodeStockInfo.StockLocNumber = inPutPdaBarcode.Barcode.StockLocNumber;
                    barcodeStockInfo.StockLocName = inPutPdaBarcode.Barcode.StockLocName;
                    if (string.IsNullOrEmpty(billData.StockInfo.StockId) || billData.StockInfo.StockId.Equals("0"))
                        billData.StockInfo = barcodeStockInfo.Adapt<PdaBillStockInfo>();
                }
                //选择优先
                else if (string.IsNullOrEmpty(firstStockType) || GetFirstStockType(firstStockType) == FirstStockType.Select)
                {
                    barcodeStockInfo = billData.StockInfo.Adapt<PdaBillStockInfo>();
                }
            }

            pdaBarcode.SetStockInfo(barcodeStockInfo, false);
            RefreshShow(tranId);
        }

        //查找匹配的行
        List<string> values = new List<string>();
        Dictionary<string, object> summaryOperationFields = new Dictionary<string, object>();
        foreach (var field in ((PdaStockCountModel)pdaModel).SummaryOperationFields)
        {

            var value = PdaHelper.GetValue(pdaBarcode.CalcBarcode, field);
            values.Add($"{value}");
        }
        var keyValue = string.Join("|", values);
        var matchingData = billData.ScanDetails.Where(r =>
        {
            List<string> rKeyValues = new List<string>();
            foreach (string colname in ((PdaStockCountModel)pdaModel).SummaryOperationFields)
            {
                rKeyValues.Add($"{r[colname]}");
            }

            return string.Join("|", rKeyValues.ToArray()) == keyValue;
        }).ToList();

        if (matchingData.Count <= 0)
        {
            //不支持源单外物料 报错
            if (!((PdaStockCountModel)pdaModel).IsOverSourceItem)
            {
                throw Oops.Bah(PdaErrorCode.Pda1016);
            }
            //如果支持源单外物料 (无源单)
            //把条码的值，写到Detail
            var detail = inPutPdaBarcode.Barcode.Adapt<PdaStockCountScanDetail>();
            billData.ScanDetails.Add(detail);
            //忝加明细主键Key值
            detail.DetailId = $"{YitIdHelper.NextId()}";
            detail.IsNew = true;
            //盘点需要把源单ID,写到明细
            detail.SourceBillId = billData.ScanDetails[0].SourceBillId;
            detail.SourceBillNo = billData.ScanDetails[0].SourceBillNo;
            detail.SourceBillKey = billData.ScanDetails[0].SourceBillKey;
            detail.SourceBillEntryId = null;
            detail.SourceBillEntrySeq = null;
            //盘点账存数量,新增行应该也为0
            detail.Qty = 0;
            matchingData.Add(detail);
        }

        var diffQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;
        if (!((PdaStockCountModel)pdaModel).IsOverSourceItem && !((PdaStockCountModel)pdaModel).IsOverSourceQty)
        {
            //查找到匹配的明细，再用QTY判断是否超
            foreach (var data in matchingData)
            {
                var qty = data.Qty - data.ScanQty;
                diffQty = diffQty - qty;
            }

            if (diffQty > 0) throw Oops.Bah(PdaErrorCode.Pda1014);
        }

        //校验通过,写数据
        //这里是扫描，默认带条码数量

        diffQty = pdaBarcode.CalcBarcode.ScanQty;

        foreach (PdaScanDetail detail in matchingData)
        {
            if (diffQty <= 0) break;
            decimal includeQty = diffQty;
            var qty = detail.Qty - detail.ScanQty;
            if (qty <= 0) continue;
            if (diffQty > qty)
            {
                //如果可扣减数量大于源单数量,includeQty为源单数量
                includeQty = qty;
                detail.ScanQty = detail.Qty;
                diffQty = diffQty - qty;
            }
            else
            {
                detail.ScanQty = detail.ScanQty + diffQty;
                diffQty = 0;
            }

            if (!billData.DetailIncludeBarcodes.ContainsKey(detail.DetailId))
                billData.DetailIncludeBarcodes.Add(detail.DetailId, new List<PdaIncludeBarcode>());
            billData.DetailIncludeBarcodes[detail.DetailId].Add(new PdaIncludeBarcode(pdaBarcode, includeQty));
            pdaBarcode.BarcodeIncludeDetails.Add(detail);
        }

        //如果剩余数量大于0
        if (diffQty > 0)
        {
            //如果超源单数量，直接把剩余数量填到匹配的最后一行记录上
            if (((PdaStockCountModel)pdaModel).IsOverSourceQty)
            {
                matchingData[^1].ScanQty = matchingData[^1].ScanQty + diffQty;

                if (!billData.DetailIncludeBarcodes.ContainsKey(matchingData[^1].DetailId))
                {
                    billData.DetailIncludeBarcodes.Add(matchingData[^1].DetailId, new List<PdaIncludeBarcode>());
                    billData.DetailIncludeBarcodes[matchingData[^1].DetailId].Add(new PdaIncludeBarcode(pdaBarcode, diffQty));
                    pdaBarcode.BarcodeIncludeDetails.Add(matchingData[^1]);
                }
                else
                {
                    var pdaIncludeBarcode = billData.DetailIncludeBarcodes[matchingData[^1].DetailId].FirstOrDefault(r => r.PdaBarcode.DetailId == pdaBarcode.DetailId);
                    if (pdaIncludeBarcode != null)
                    {
                        pdaIncludeBarcode.SetScanQty(pdaIncludeBarcode.ScanQty + diffQty);
                    }
                    else
                    {
                        billData.DetailIncludeBarcodes[matchingData[^1].DetailId].Add(new PdaIncludeBarcode(pdaBarcode, diffQty));
                        pdaBarcode.BarcodeIncludeDetails.Add(matchingData[^1]);
                    }
                }
            }
            else
            {
                throw Oops.Bah(PdaErrorCode.Pda1016);
            }
        }
        billData.BarcodeList.Add(pdaBarcode);
    }

    private FirstStockType GetFirstStockType(string firstStockType)
    {
        switch (firstStockType)
        {
            case "1":
                return FirstStockType.Barcode;
            case "2":
                return FirstStockType.Select;
            default:
                throw Oops.Bah(L.Text["不支持的仓库优先类型"]);
        }
    }

    #endregion

    public class PdaStockCountBillSchema : IPdaSchema
    {
        public List<PdaColumn> DestHead => new List<PdaColumn> { };

        public List<PdaColumn> Barcode => new List<PdaColumn>
        {
            new PdaColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaColumn
            {
                Fieldname = "materialNumber",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaColumn
            {
                Fieldname = "materialName",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaColumn
            {
                Fieldname = "stockName",
                Caption = L.Text["仓库名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "BarcodeList.ScanQty"
            },
            new PdaColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink => new PdaBillLink
        {
            SourceKey = "0",
            SourceTitle = "",
            DestKey = "StockCount",
            DestTitle = L.Text["盘点"],
            Rob = 1
        };
    }
}