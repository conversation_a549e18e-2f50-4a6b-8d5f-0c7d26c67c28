﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 系统实体服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysEntity", Order = 100)]
public class SysEntityService : IDynamicApiController, ITransient
{
    private const string ENTITY_INFO_LIST_CACHE_KEY = nameof(SysEntityService) + "_Entity_Info_List";

    /// <summary>
    /// 内存缓存服务
    /// </summary>
    private IMemoryCache MemoryCache { get; }

    /// <summary>
    /// SqlSugar
    /// </summary>
    private ISqlSugarClient SqlSugarClient { get; }

    /// <summary>
    /// 系统实体服务构造函数
    /// </summary>
    public SysEntityService(IMemoryCache memoryCache, ISqlSugarClient sqlSugarClient)
    {
        MemoryCache = memoryCache;
        SqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 实体列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("list")]
    public Task<List<SysEntityOutput>> ListAsync()
    {
        var entityInfoList = GetEntityInfoListCache();

        var list = entityInfoList.Select(u => new SysEntityOutput
        {
            EntityName = u.EntityName,
            Description = string.IsNullOrEmpty(u.TableDescription) ? u.EntityName : u.TableDescription
        }).ToList();

        return Task.FromResult(list);
    }

    /// <summary>
    /// 实体属性列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("propertyList")]
    public Task<List<SysEntityPropertyOutput>> PropertyListAsync([FromQuery] SysEntityPropertyInput input)
    {
        var entityInfoList = GetEntityInfoListCache();
        var entity = entityInfoList.FirstOrDefault(u => u.EntityName == input.EntityName);
        if (entity == null)
            throw Oops.Bah(SysErrorCode.SysEntity1000, input.EntityName);

        var colList = new List<EntityColumnInfo>();

        switch (input.ElementType)
        {
            case ElementType.TextField:
                colList = entity.Columns.Where(u => u.PropertyInfo.PropertyType == typeof(string)).ToList();
                break;
            case ElementType.DateTimeField:
                colList = entity.Columns.Where(u => u.PropertyInfo.PropertyType == typeof(DateTimeOffset) || u.PropertyInfo.PropertyType == typeof(DateTime) ||
                                                    u.PropertyInfo.PropertyType == typeof(DateTimeOffset?) || u.PropertyInfo.PropertyType == typeof(DateTime?)).ToList();
                break;
            case ElementType.NumberField:
                colList = entity.Columns.Where(u =>
                    u.PropertyInfo.PropertyType == typeof(short) || u.PropertyInfo.PropertyType == typeof(int) || u.PropertyInfo.PropertyType == typeof(long) ||
                    u.PropertyInfo.PropertyType == typeof(ushort) || u.PropertyInfo.PropertyType == typeof(uint) || u.PropertyInfo.PropertyType == typeof(ulong) ||
                    u.PropertyInfo.PropertyType == typeof(float) || u.PropertyInfo.PropertyType == typeof(double) || u.PropertyInfo.PropertyType == typeof(decimal) ||
                    u.PropertyInfo.PropertyType == typeof(short?) || u.PropertyInfo.PropertyType == typeof(int?) || u.PropertyInfo.PropertyType == typeof(long?) ||
                    u.PropertyInfo.PropertyType == typeof(ushort?) || u.PropertyInfo.PropertyType == typeof(uint?) || u.PropertyInfo.PropertyType == typeof(ulong?) ||
                    u.PropertyInfo.PropertyType == typeof(float?) || u.PropertyInfo.PropertyType == typeof(double?) || u.PropertyInfo.PropertyType == typeof(decimal?)
                ).ToList();
                break;
            case ElementType.EnumField:
                colList = entity.Columns.Where(u => u.PropertyInfo.PropertyType == typeof(Enum)).ToList();
                break;
            case ElementType.BaseData:
                colList = entity.Columns.Where(u => u.PropertyInfo.PropertyType.IsSubclassOf(typeof(EntityTenant))).ToList();
                break;
            case ElementType.Const:
            case ElementType.Serial:
                break;
            default:
                throw new NotImplementedException();
        }

        var list = colList.Select(u => new SysEntityPropertyOutput
        {
            PropertyName = u.PropertyName,
            Description = string.IsNullOrEmpty(u.ColumnDescription) ? u.PropertyName : u.ColumnDescription,
        }).ToList();

        return Task.FromResult(list);
    }

    /// <summary>
    /// 实体基础资料属性列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("baseDataPropertyList")]
    public Task<List<SysEntityPropertyOutput>> BaseDataPropertyListAsync([FromQuery] SysEntityBaseDataPropertyInput input)
    {
        input.PropertyName ??= "";

        var entityInfoList = GetEntityInfoListCache();
        var entity = entityInfoList.FirstOrDefault(u => u.EntityName == input.EntityName);
        if (entity == null)
            throw Oops.Bah(SysErrorCode.SysEntity1000, input.EntityName);

        var col = entity.Columns.FirstOrDefault(u => u.Navigat != null && u.PropertyName == input.PropertyName);
        if (col == null)
            throw Oops.Bah(SysErrorCode.SysEntity1001, input.EntityName, input.PropertyName);

        var navColEntityInfo = entityInfoList.FirstOrDefault(u => u.EntityName == col.PropertyInfo.PropertyType.Name);
        if (navColEntityInfo == null) return Task.FromResult(new List<SysEntityPropertyOutput>());

        //只返回 Number 或 Name
        var colList = navColEntityInfo.Columns
            .Where(u => u.PropertyName is "Number" or "Name")
            .Select(u => new SysEntityPropertyOutput
            {
                PropertyName = u.PropertyName,
                Description = string.IsNullOrEmpty(u.ColumnDescription) ? u.PropertyName : u.ColumnDescription,
            }).ToList();

        return Task.FromResult(colList);
    }

    /// <summary>
    /// 从缓存中获取 EntityInfo 集合
    /// </summary>
    /// <returns></returns>
    private List<EntityInfo> GetEntityInfoListCache()
    {
        var entityInfoList = MemoryCache.Get<List<EntityInfo>>(ENTITY_INFO_LIST_CACHE_KEY);
        if (entityInfoList != null) return entityInfoList;

        var entityTypes = App.EffectiveTypes.Where(u => !u.IsInterface && !u.IsAbstract && u.IsClass && u.IsDefined(typeof(SugarTable), false)).ToList();
        entityInfoList = entityTypes.Select(entityType => SqlSugarClient.EntityMaintenance.GetEntityInfo(entityType)).ToList();
        MemoryCache.Set(ENTITY_INFO_LIST_CACHE_KEY, entityInfoList, new MemoryCacheEntryOptions { SlidingExpiration = TimeSpan.FromHours(1) });

        return entityInfoList;
    }
}