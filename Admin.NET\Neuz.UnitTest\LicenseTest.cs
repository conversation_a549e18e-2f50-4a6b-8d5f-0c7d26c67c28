﻿using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion;
using System.Diagnostics;
using Xunit;

namespace Neuz.UnitTest;

public class LicenseTest
{
    [Fact(DisplayName = "授权信息拷贝测试")]
    public void LicenseCloneTest()
    {
        LicenseInfo licenseInfo = new LicenseInfo
        {
            BeginDate = DateTime.Now.Date,
            ServerKey = "111"
        };

        var newLicenseInfo = licenseInfo.Clone();
        Assert.Equal(licenseInfo.MaxConnectCount, newLicenseInfo.MaxConnectCount);
        Assert.Equal(licenseInfo.ServerKey, newLicenseInfo.ServerKey);
        //Assert.NotEqual(licenseInfo.ServerKeys.GetHashCode(), newLicenseInfo.ServerKeys.GetHashCode());
    }

    [Fact(DisplayName = "系统授权服务获取授权信息，无授权文件测试")]
    public void SysLicenseServiceGetLicenseInfo_NoLicenseTest()
    {
        var service = App.GetService<SysLicenseService>();
        var licenseInfo = service.GetLicenseInfo();
        if (Debugger.IsAttached)
        {
            Assert.Equal("调试环境无限制", licenseInfo.Description);
            Assert.Equal(0, licenseInfo.MaxConnectCount);
            Assert.Equal(DateTime.MinValue, licenseInfo.BeginDate);
            Assert.Equal(DateTime.MaxValue, licenseInfo.EndDate);
            Assert.Equal(LicenseType.Official, licenseInfo.LicenseType);
        }
        else
        {
            Assert.Equal("无授权信息", licenseInfo.Description);
            Assert.Equal(2, licenseInfo.MaxConnectCount);
            Assert.Equal(DateTime.MinValue, licenseInfo.BeginDate);
            Assert.Equal(DateTime.MinValue, licenseInfo.EndDate);
            Assert.Equal(LicenseType.NoLicense, licenseInfo.LicenseType);
        }
    }

    [Fact(DisplayName = "系统授权服务获取授权信息，有授权文件测试")]
    public void SysLicenseServiceGetLicenseInfo_LicenseFileTest()
    {
        var licenseFilePath = Path.Combine(App.WebHostEnvironment.ContentRootPath, "license.key");
        File.WriteAllText(licenseFilePath, "hdBX3f33ZfqRJWc6m5Seg3bZPYZeL/RfstZVmFmUdy0gK14IQ6tIgNAO1NBF17wnwcXiZdkM04FslWIJW9+4AfB+6WXjCxF3qeIXFoZFdnFcv/jrJHBJo79qm7P056WFkVpMMQp9ftG7aLVy5nIuu6tIHk3pZqHGn3eEXsY3rWJQ4VigSWmBT25ZDQ4iNREgeD79TNdbUfeTz8glnvyWTGe89YHh0dym4QMe7KwAIcFRnmO1kfBhNAUQbhMjRUWzceghEsc75Ehhkwkz9RX0kgFKO6SOt6n0SeJFzY+yKT4SFCDsQMyAynsPnt/fDzoGfgGYQkvJaxlxV2VMaRvETpvxCyuoaYEv8nQPS53gRN9m8A143/rgDWk8YUqHrNjUzaE/TggrXwqmJgkxf49WtOpNM150ogj67iRIqZkZDlrXRFUy2ABNFAIw2aKTVRo2Fnj5ES5lm8hbxzDfqv+Xw2fkpMi2LGzyYaWXP5M1g9FbEllnUvOBLfBQGdr9ja5IHoEVVCPyhTurn94lTItxRCgDbhP8abLvOL8R/jg1m+HzBAv00UC0DKneLxK9f0fAoDLkpvvb6Yzb2LEDFGz3Tvbo/+0IcqJQvF0Ss5JvUXCiQxs/5KMEEg==");

        var service = App.GetService<SysLicenseService>();
        var licenseInfo = service.GetLicenseInfo();

        if (File.Exists(licenseFilePath))
            File.Delete(licenseFilePath);

        Assert.Equal("测试授权", licenseInfo.Description);
    }

    [Fact(DisplayName = "系统授权服务获取机器序列号测试")]
    public void SysLicenseGetMachineSerialKeyTest()
    {
        var service = App.GetService<SysLicenseService>();
        var serialKey = service.GetMachineSerialKey();
        var serialKey2 = service.GetMachineSerialKey();
        Assert.Equal(serialKey, serialKey2);
    }
}