﻿using Furion.DependencyInjection;

namespace Neuz.Core.Enum;

/// <summary>
/// 条码编辑El类型
/// </summary>
[SuppressSniffer]
public enum SchemaElType
{
    /// <summary>
    /// 文本输入类型
    /// </summary>
    [Description("ElInput")]
    ElInput,
    /// <summary>
    /// 选择输入类型
    /// </summary>
    [Description("ElSelect")]
    ElSelect,
    /// <summary>
    /// 数字输入类型
    /// </summary>
    [Description("ElInputNumber")]
    ElInputNumber,
    /// <summary>
    /// 日期输入类型
    /// </summary>
    [Description("ElDatePicker")]
    ElDatePicker
}