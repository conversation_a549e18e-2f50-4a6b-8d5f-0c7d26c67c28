﻿using Furion.DependencyInjection;

namespace Neuz.Core.Enum;

/// <summary>
/// 格式化网络显示
/// </summary>
[SuppressSniffer]
public enum SchemaFormatter
{
    /// <summary>
    /// 不需要格式化
    /// </summary>
    [Description("")]
    None,
    ///// <summary>
    ///// 是/否
    ///// </summary>
    [Description("formatBoolean")]
    FormatBoolean,
    ///// <summary>
    ///// √/''
    ///// </summary>
    [Description("formatBooleanTick")]
    FormatBooleanTick,
    ///// <summary>
    ///// yyyy-MM-dd
    ///// </summary>
    [Description("formatDate")]
    FormatDate,
    ///// <summary>
    ///// yyyy-MM-dd HH:mm:ss
    ///// </summary>
    [Description("formatTime")]
    FormatTime,
    ///// <summary>
    ///// 四舍五入金额，每隔3位逗号分隔，默认2位数
    ///// </summary>
    [Description("formatAmount")]
    FormatAmount,
    ///// <summary>
    ///// 格式化银行卡，默认每4位空格隔开
    ///// </summary>
    [Description("formatBankcard")]
    FormatBankcard,
    ///// <summary>
    ///// 四舍五入,默认两位数
    ///// </summary>
    [Description("formatFixedNumber")]
    FormatFixedNumber,
    ///// <summary>
    ///// 向下舍入,默认两位数
    ///// </summary>
    [Description("formatCutNumber")]
    FormatCutNumber
}