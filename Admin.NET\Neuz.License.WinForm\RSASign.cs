﻿using System.Security.Cryptography;
using System.Text;

namespace Neuz.License.WinForm;

/// <summary>
/// RSA 签名
/// </summary>
// ReSharper disable once InconsistentNaming
public class RSASign
{
    /// <summary>
    /// 签名
    /// </summary>
    /// <param name="name">加密哈希算法的名称</param>
    /// <param name="text">明文内容</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>签名内容</returns>
    public static string Sign(HashAlgorithmName name, string text, string privateKey)
    {
        using var rsa = new RSACryptoServiceProvider();
        rsa.FromXmlString(privateKey);

        var encryptedData = rsa.SignData(Encoding.UTF8.GetBytes(text), name, RSASignaturePadding.Pkcs1);
        var encryptedContent = Convert.ToBase64String(encryptedData);

        return encryptedContent;
    }

    /// <summary>
    /// 校验
    /// </summary>
    /// <param name="name">加密哈希算法的名称</param>
    /// <param name="text">明文内容</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="sign">签名内容</param>
    /// <returns>是否一致</returns>
    public static bool Verify(HashAlgorithmName name, string text, string publicKey, string sign)
    {
        using var rsa = new RSACryptoServiceProvider();
        rsa.FromXmlString(publicKey);

        return rsa.VerifyData(Encoding.UTF8.GetBytes(text), Convert.FromBase64String(sign), name, RSASignaturePadding.Pkcs1);
    }
}