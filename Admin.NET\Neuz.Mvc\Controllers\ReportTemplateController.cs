﻿using Stimulsoft.Report.Dictionary;
using Stimulsoft.Report.Web;
using System.Text;
using Furion.Localization;

namespace Neuz.Mvc.Controllers;

/*
 * 不要在此 Controller 类中标记 [AllowAnonymous]，否则无法进入 JwtHandler 中处理 context.User
 * 在 JwtHandler 中，Viewer 和 Designer 会验证 token，其他方法会跳过验证
 * 达到首次打开时，从 token 中获取用户信息，后续的报表操作则不验证，防止 token 超时而无法继续操作报表的情况
 */
/// <summary>
/// 报表模板控制器
/// </summary>
[NonUnify]
// [Route("{controller}/{action}")]
//[ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
public class ReportTemplateController : Controller
{
    protected IServiceProvider ServiceProvider { get; }
    protected SysReportTemplateService ReportTemplateService { get; }

    public ReportTemplateController(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        ReportTemplateService = serviceProvider.GetService<SysReportTemplateService>();
    }

    public IActionResult Viewer()
    {
        var viewerOptions = new StiNetCoreViewerOptions
        {
            Localization = "stimulsoft/localization/zh-CHS.xml",
            Theme = StiViewerTheme.Office2013DarkGrayBlue,
            Actions =
            {
                GetReport = "GetViewerReport",
                ViewerEvent = "ViewerEvent",
            },
            Appearance =
            {
                BackgroundColor = System.Drawing.Color.FromArgb(241, 241, 241), // 保持与 Designer 显示的颜色一致
                PageBorderColor = System.Drawing.Color.FromArgb(192, 198, 198), // 保持与 Designer 显示的颜色一致
                FullScreenMode = true
            },
            Toolbar =
            {
                AutoHide = true,
                MenuAnimation = false,
                ShowAboutButton = false,
                ShowOpenButton = false,
                ShowBookmarksButton = false,
                ShowParametersButton = false,
                ShowSendEmailButton = false,
                ShowEditorButton = false,
                ShowFindButton = false,
                ViewMode = StiWebViewMode.Continuous,
                ShowFullScreenButton = false,
                DisplayMode = StiToolbarDisplayMode.Separated,

                ShowPrintButton = false, // TODO：StiReport Viewer 隐藏打印按钮，已打印回调实现有解决方案后，再决定是否需要放回出来
            },
        };

        ViewBag.ViewerOptions = viewerOptions;

        return View();
    }

    public IActionResult Designer()
    {
        var designerOptions = new StiNetCoreDesignerOptions
        {
            Localization = "Stimulsoft/Localization/zh-CHS.xml",
            Theme = StiDesignerTheme.Office2013DarkGrayBlue,
            FileMenu =
            {
                ShowNew = false,
                ShowSaveAs = false,
                ShowInfo = false,
                ShowClose = false,
                ShowAbout = false,
            },
            Actions =
            {
                GetReport = "GetDesignerReport",
                DesignerEvent = "DesignerEvent",
                SaveReport = "SaveReport",
                OpenReport = "OpenReport",
                // SaveReportAs = "SaveReportAs",
            },
            Appearance =
            {
                ShowAnimation = false, // 动画
            },
            Behavior =
            {
                UndoMaxLevel = 20, // 撤销操作最大次数，默认 6
            },
            // PropertiesGrid =
            // {
            //     PropertiesGridPosition = StiPropertiesGridPosition.Right, // 属性网格位置
            // },
            Toolbar =
            {
                ShowSaveButton = true, // 显示保存按钮
            },
            PreviewToolbar =
            {
                ShowOpenButton = false,
                ShowBookmarksButton = false,
                ShowParametersButton = false,
                ShowSendEmailButton = false,
                ShowEditorButton = false,
                ShowFindButton = false,
                ViewMode = StiWebViewMode.Continuous,
            }
        };

        ViewBag.DesignerOptions = designerOptions;

        return View();
    }

    #region Viewer

    public async Task<IActionResult> GetViewerReport(long templateId, string typeofIReportData, string ids, string cacheId)
    {
        try
        {
            var report = await GetReport(templateId, typeofIReportData, ids, cacheId);
            return StiNetCoreViewer.GetReportResult(this, report);
        }
        catch (Exception ex)
        {
            return StiNetCoreDesigner.SaveReportResult(this, ex.Message);
        }
    }

    public IActionResult ViewerEvent()
    {
        return StiNetCoreViewer.ViewerEventResult(this);
    }

    #endregion

    #region designer

    public async Task<IActionResult> GetDesignerReport(long templateId, string typeofIReportData, string ids, string cacheId)
    {
        try
        {
            var report = await GetReport(templateId, typeofIReportData, ids, cacheId);
            return StiNetCoreDesigner.GetReportResult(this, report);
        }
        catch (Exception ex)
        {
            return StiNetCoreDesigner.SaveReportResult(this, ex.Message);
        }
    }

    public IActionResult DesignerEvent()
    {
        return StiNetCoreDesigner.DesignerEventResult(this);
    }

    public async Task<IActionResult> SaveReport(long templateId)
    {
        var reportTemplate = await ReportTemplateService.GetAsync(new IdInput { Id = templateId });
        if (reportTemplate == null)
            return StiNetCoreDesigner.SaveReportResult(this, L.Text["报表模板Id：{0} 不存在", templateId]);

        var report = StiNetCoreDesigner.GetReportObject(this);
        if (report == null)
            return StiNetCoreDesigner.SaveReportResult(this, L.Text["保存失败，会话已丢失，请刷新"]);
        var json = report.SaveToJsonString();
        // reportTemplate.Json = json;

        await ReportTemplateService.UpdateReportJsonAsync(new UpdateSysReportTemplateInput2 { Id = templateId, Json = json });

        return StiNetCoreDesigner.SaveReportResult(this);
    }

    public IActionResult OpenReport(long templateId, string typeofIReportData, string ids, string cacheId)
    {
        var report = StiNetCoreDesigner.GetReportObject(this);
        if (report == null)
            return StiNetCoreDesigner.SaveReportResult(this, L.Text["保存失败，会话已丢失，请刷新"]);

        StiRequestParams requestParams = StiNetCoreDesigner.GetRequestParams(this);

        // 打开报表文件
        var data = requestParams.All["stiweb_data"] + "";
        byte[] dataBytes = Convert.FromBase64String(data);
        string reportTemplateStr = Encoding.UTF8.GetString(dataBytes);

        // 备份业务对象字典和业务对象集合
        var dicBusinessObjects = (StiBusinessObjectsCollection)report.Dictionary.BusinessObjects.Clone();
        var businessObjectsStore = new StiBusinessObjectData[report.BusinessObjectsStore.Count];
        report.BusinessObjectsStore.CopyTo(businessObjectsStore);

        if (reportTemplateStr.StartsWith("<"))
        {
            // 从 XML 中重新加载报表
            report.LoadFromString(reportTemplateStr);
        }
        else
        {
            // 从 json 中重新加载报表
            report.LoadFromJson(reportTemplateStr);
        }

        // 清除数据源字典
        report.Dictionary.DataSources.Clear();
        // 清除业务对象字典
        report.Dictionary.BusinessObjects.Clear();
        // 重新添加业务对象字典
        report.Dictionary.BusinessObjects.AddRange(dicBusinessObjects);
        // 清除业务对象集合
        report.BusinessObjectsStore.Clear();
        // 重新添加业务对象集合
        report.BusinessObjectsStore.AddRange(businessObjectsStore);

        return StiNetCoreDesigner.GetReportResult(this, report);
    }

    #endregion

    private async Task<StiReport> GetReport(long templateId, string typeofIReportData, string ids, string cacheId)
    {
        var idList = new List<string>();
        if (!string.IsNullOrEmpty(ids))
            idList = ids.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        else if (!string.IsNullOrEmpty(cacheId))
        {
            var l = await ReportTemplateService.ExchangeFromCacheId(cacheId);
            idList = l ?? throw Oops.Bah(L.Text["cacheId 已失效，请关闭重新打开"]);
        }

        var report = await ReportTemplateService.GetReport(templateId, typeofIReportData, idList);

        return report;
    }
}