﻿using Furion.Localization;
using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Model.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.LocalBill.Link;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.Scan.LocalBill;
using Neuz.Application.Pda.Scan.LocalBill.Proj;
using Neuz.Core.Entity.Pda.Erp;

namespace Neuz.Application.Pda.LocalBill;

public abstract class PdaLocalBillModel<TS, TT, TSE, TTE> : PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>, IPdaLocalBillModel
    where TS : BillEntityBase, new() where TT : BillEntityBase, new() where TSE : EntryEntityBase, new() where TTE : EntryEntityBase, new()
{
    private const string ScanQtyKey = "ScanQty";

    private const string QtyKey = "Qty";

    // 用户管理
    protected readonly UserManager UserManager;

    protected SqlSugarRepository<TS> SourceRep;
    protected SqlSugarRepository<TT> TargetRep;

    protected PdaLocalBillModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
        UserManager = serviceProvider.GetService<UserManager>();
        SourceRep = App.GetService<SqlSugarRepository<TS>>(serviceProvider);
        TargetRep = App.GetService<SqlSugarRepository<TT>>(serviceProvider);
    }

    public virtual ILocalBillLinkParam GetLinkParam(long tranId)
    {
        var key = HandleLookupKey(tranId, Key);
        var link = new LocalBillLinkService(ServiceProvider, key);
        return link.LinkParam;
    }

    /// <summary>
    /// 刷新前端显示数据
    /// </summary>
    /// <param name="tranId"></param>
    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        Config.BillSchema.DestHead.ForEach(b => { pdaShow.DestData.Head[b.Fieldname] = GetFormatTypeValue(b, pdaData.ScanHead[b.Fieldname]); });
        pdaShow.SourceBill.Clear();
        pdaShow.DestData.Details.Clear();
        if (pdaData.ScanDetails.Count > 0)
        {
            //可能需要根据条件,合并明细在前端显示 (比如按物料汇总)
            List<PdaLocalBillScanDetail> totalDetails = GetTotalShowDetails(tranId, pdaData.ScanDetails);

            foreach (PdaLocalBillScanDetail pdaScanDetail in totalDetails)
            {
                PdaLocalBillDetailShowInfo detail = SetDetailValueShow(tranId, pdaData.DetailIncludeBarcodes, pdaScanDetail);
                pdaShow.DestData.Details.Add(detail);
            }
        }

        //设置SourceInfo,这里只显示单据号,如要特殊处理,需二开
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => r.SrcBillNo).ToList();

        //刷新条码
        pdaShow.Barcodes.Clear();
        pdaData.BarcodeList.ForEach(b =>
        {
            var barcode = new PdaBarcodeShowInfo();
            foreach (PdaLocalBillColumn column in Config.BillSchema.Barcode)
            {
                if (column.Fieldname.Equals(ScanQtyKey) || column.Fieldname.Equals(QtyKey)) continue;
                //这里第一个字要转大写，不然反射不认
                var value = GetExtensionObj(b.CalcBarcode, column.Fieldname);
                //PdaHelper.GetValue(b.BarBarcode, column.Fieldname.Substring(0, 1).ToUpper() + column.Fieldname.Substring(1));
                barcode[column.Fieldname] = GetFormatTypeValue(column, value);
            }

            barcode.ScanQty = $"{b.CalcBarcode.ScanQty.ToString(PdaHelper.DecimalPrecision)}";
            barcode.Qty = $"{b.Barcode.Qty.ToString(PdaHelper.DecimalPrecision)}";
            barcode.BarcodeId = $"{b.Barcode.Id}";
            barcode.DetailId = b.DetailId;
            barcode.Title = b.Barcode.Barcode;
            barcode.SubTitle = b.Barcode.Material.Name;
            barcode.StockKey = $"{b.CalcBarcode.WhAreaId}_{b.CalcBarcode.WhLocId}";
            barcode.StockValue = $"{b.CalcBarcode.WhAreaName}[{b.CalcBarcode.WhLocName}]";
            barcode.Value = $"数量:{b.CalcBarcode.ScanQty.ToString(PdaHelper.DecimalPrecision)}/{b.Barcode.Qty.ToString(PdaHelper.DecimalPrecision)}";
            pdaShow.Barcodes.Add(barcode);
        });

        // 刷新库区库位
        pdaShow.Stock = (string.IsNullOrEmpty(pdaData.StockInfo.WhAreaId) || pdaData.StockInfo.WhAreaId == "0")
            ? ""
            : $"[{pdaData.StockInfo.WhAreaNumber}]{pdaData.StockInfo.WhAreaName} - [{pdaData.StockInfo.WhLocNumber}]{pdaData.StockInfo.WhLocName}";

        pdaShow.IsHideFinishScanQty = pdaData.IsHideFinishScanQty;
        pdaShow.ExtendArgs = pdaData.ExtendArgs;
    }

    /// <summary>
    /// 获取平铺字段名的对象,如: material.Number 转成 materialNumber
    /// 暂时只支持2层
    /// </summary>
    /// <returns></returns>
    public object GetExtensionObj(ExtensionObject obj, string fieldName)
    {
        var names = fieldName.Split(".");
        if (names.Length == 1) return obj[fieldName];
        var o = obj[names[0]];
        if (o == null) return null;
        var oProperty = o.GetType().GetProperty(names[1]);
        if (oProperty == null) return null;
        return oProperty.GetValue(o);
    }

    /// <summary>
    /// 合计明细行
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="scanDetails"></param>
    /// <returns></returns>
    public virtual List<PdaLocalBillScanDetail> GetTotalShowDetails(long tranId, List<PdaLocalBillScanDetail> scanDetails)
    {
        //如果是制作单据,直接返回所有明细不汇总
        if (Config.BillParams.IsMakeBill) return scanDetails;
        //按什么条件合并汇总行信息
        //暂时按物料
        List<PdaLocalBillScanDetail> total = new List<PdaLocalBillScanDetail>();
        foreach (PdaLocalBillScanDetail scanDetail in scanDetails)
        {
            if (total.Exists(r => r.MaterialNumber == scanDetail.MaterialNumber))
            {
                var totalDetail = total.FirstOrDefault(r => r.MaterialNumber == scanDetail.MaterialNumber);
                totalDetail.Qty += scanDetail.Qty;
                totalDetail.ScanQty += scanDetail.ScanQty;
                totalDetail.MergeDetailIds ??= [];
                totalDetail.MergeDetailIds.Add(scanDetail.DetailId);
            }
            else
            {
                total.Add(scanDetail.Adapt<PdaLocalBillScanDetail>());
            }
        }

        var pdaData = GetPdaData(tranId);
        if (pdaData.IsHideFinishScanQty)
        {
            // 如果要隐藏已完成扫描,把列隐藏
            var removeDetails = total.Where(detail => detail.ScanQty >= detail.Qty).ToList();
            removeDetails.ForEach(r => total.Remove(r));
        }

        return total;
    }

    /// <summary>
    /// 明细行显示
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="detailIncludeBarcodes"></param>
    /// <param name="pdaScandDetail"></param>
    /// <returns></returns>
    public virtual PdaLocalBillDetailShowInfo SetDetailValueShow(long tranId, Dictionary<string, List<PdaLocalBillIncludeBarcode>> detailIncludeBarcodes, PdaLocalBillScanDetail pdaScandDetail)
    {
        //所有列显示,但我更偏向只显示有配置的列
        //var info = IsMakeBill ? pdaScandDetail.Adapt<PdaDetailShowInfo>() : new PdaDetailShowInfo();
        var info = new PdaLocalBillDetailShowInfo();

        if (Config.BillSchema.BillDetail != null)
            foreach (PdaColumn pdaColumn in Config.BillSchema.BillDetail)
            {
                info[pdaColumn.Fieldname] = GetFormatTypeValue(pdaColumn, pdaScandDetail[pdaColumn.Fieldname]);
            }

        info.DetailId = pdaScandDetail.DetailId;
        info.Title = $"[{pdaScandDetail.MaterialNumber}]{pdaScandDetail.MaterialName}";


        var linkParam = GetLinkParam(tranId);
        info.Contents = new List<PdaLocalBillDetailContent>();
        // 显示明细内容
        foreach (var content in linkParam.DetailContents)
        {
            info.Contents.Add(new PdaLocalBillDetailContent()
            {
                Title = content.Title,
                Value = GetFormatTypeValue(pdaScandDetail[content.ScanFiledName]),
                Col = content.Col
            });
        }

        info.ScanQty = pdaScandDetail.ScanQty;
        info.Qty = pdaScandDetail.Qty;
        info.IsNew = pdaScandDetail.IsNew;

        // 添加明细对应条码
        var totalDetailIds = new List<string> { pdaScandDetail.DetailId };
        if (pdaScandDetail.MergeDetailIds != null) totalDetailIds.AddRange(pdaScandDetail.MergeDetailIds);

        foreach (string detailId in totalDetailIds)
        {
            if (!detailIncludeBarcodes.TryGetValue(detailId, out List<PdaLocalBillIncludeBarcode> barcode)) continue;

            var bs = barcode?.ToList().Select(r => r.PdaBarcode.DetailId);
            if (bs == null) continue;

            var bList = bs.ToList();
            if (!bList.ToList().Any()) continue;

            // 有可能存在拆分,所以需要去重
            foreach (var b in bList.Where(b => !info.IncludeBarcodes.Contains(b)))
            {
                info.IncludeBarcodes.Add(b);
            }
        }

        return info;
    }

    /// <summary>
    /// 格式化数据显示
    /// </summary>
    /// <param name="column"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    protected string GetFormatTypeValue(PdaColumn column, object value)
    {
        if (column == null) return value + "";
        switch (column.Type)
        {
            case "date":
                var date = value == null || value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDateTime(value).ToString("yyyy-MM-dd");
                return date;
            case "number":
                var number = value == null || value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDecimal(value).ToString(PdaHelper.DecimalPrecision);
                return number;
            default:
                return value + "";
        }
    }

    /// <summary>
    /// 格式化数据显示
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    protected string GetFormatTypeValue(object value)
    {
        if (value == null) return "";
        var type = value.GetType();

        if (type == typeof(DateTime) || type == typeof(DateTime?))
        {
            var date = value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDateTime(value).ToString("yyyy-MM-dd");
            return date;
        }

        if (type == typeof(decimal) || type == typeof(decimal?))
        {
            var number = value == DBNull.Value || string.IsNullOrEmpty(value + "") ? "" : Convert.ToDecimal(value).ToString(PdaHelper.DecimalPrecision);
            return number;
        }

        return value + "";
    }

    /// <summary>
    /// 初始化单据数据
    /// </summary>
    /// <param name="pdaData"></param>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        var billData = GetPdaData(pdaData.TranId);
        // 获取配置,并设置值
        var config = GetUserConfig<PdaLocalBillDataUserConfig>().GetAwaiter().GetResult();
        billData.ExtendArgs["LoadedComponent"] = "auxPropConfirm";
        if (config == null) return;
        var configProperties = config.GetType().GetProperties();
        var billDataProperties = billData.GetType().GetProperties();
        foreach (var property in configProperties)
        {
            var billDataProperty = billDataProperties.FirstOrDefault(r => r.Name == property.Name);
            if (billDataProperty == null) continue;
            var configValue = property.GetValue(config);
            billDataProperty.SetValue(billData, Convert.ChangeType(configValue, billDataProperty.PropertyType));
        }
    }

    /// <summary>
    /// 初始化
    /// </summary>
    public override void Initialization()
    {
        Config.BillParams.ScanBarcodeOperations.Add(new ProjPdaLocalBillScanBarcodeOperation(ServiceProvider));
        Config.BillParams.ScanBarcodeOperations.Add(new PdaLocalBillScanBdContainerOperation(ServiceProvider));
        Config.BillParams.ScanBarcodeOperations.Add(new PdaLocalBillScanWhAreaOperation(ServiceProvider));
        Config.BillParams.ScanBarcodeOperations.Add(new PdaLocalBillScanSourceBillOperation(ServiceProvider));
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override async Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        // 获取Link
        // 按配置映射数据
        var bill = await SourceRep.AsQueryable().IncludeNavCol().FirstAsync(r => r.BillNo == input.BillNo);
        if (bill == null) return null;
        return await GetBill(new PdaLocalBillLookSelectInput
        {
            Key = input.Key,
            TranId = input.TranId,
            LookupDataKey = null,
            LookupKey = null,
            Id = bill.Id + "",
            DetailIndex = null,
            IsLocalBill = false
        });
    }

    protected override async Task<dynamic> LocalBillBillSelectGet(PdaLocalBillLookSelectInput input)
    {
        if (new[] { "ScanHead.DestWhAreaLoc", "ScanHead.SrcWhAreaLoc" }.Contains(input.LookupDataKey))
        {
            var keys = input.Id.Split("_");
            if (keys.Length != 2) throw Oops.Bah(L.Text["Id[{0}]格式错误", input.Id]);
            var whAreaId = Convert.ToInt64(keys[0]);
            var whLocId = Convert.ToInt64(keys[1]);
            var whAreaLoc = await Rep.Change<BdWhArea>().AsQueryable()
                .InnerJoin<BdWhLoc>((t1, t2) => t1.Id == t2.WhAreaId)
                .Where((t1, t2) => t1.Id == whAreaId && t2.Id == whLocId)
                .Select((t1, t2) => new
                {
                    WhAreaId = t1.Id,
                    WhAreaNumber = t1.Number,
                    WhAreaName = t1.Name,
                    WhLocId = t2.Id,
                    WhLocNumber = t2.Number,
                    WhLocName = t2.Name,
                    WhAreaLoc = $"{t1.Name}[{t2.Name}]"
                })
                .FirstAsync();
            if (whAreaLoc == null) throw Oops.Bah(L.Text["没有找到库位[{0}_{1}]", whAreaId, whLocId]);
            return whAreaLoc;
        }

        return await base.LocalBillBillSelectGet(input);
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override async Task<dynamic> GetBill(PdaLocalBillLookSelectInput input, bool isException = true)
    {
        var pdaData = GetPdaData(input.TranId);

        var info = await GetBillInfo(input, isException);

        if (info.Item2 is { Count: > 0 }) SetSourceInfo(input.TranId, [info.Item1], info.Item2, null);

        return pdaData;
    }

    public virtual async Task<(PdaLocalBillSourceHead, List<PdaLocalBillSourceDetail>)> GetBillInfo(PdaLocalBillLookSelectInput input, bool isException = true)
    {
        // 获取Link
        // 按配置映射数据
        var pdaData = GetPdaData(input.TranId);

        // 如果源单已经存在,退出
        if (pdaData.SourceHeads.Select(r => r.SrcBillId).ToList().Contains(input.Id)) return (null, null);

        // 如果控制了不允许多源单, 有多源单报错
        if (!Config.BillParams.IsMultiSource)
        {
            if (pdaData.SourceHeads.Count > 0)
                if (isException)
                    throw Oops.Bah(L.Text["不能选择多源单"]);
                else return (null, null);
        }

        var id = Convert.ToInt64(input.Id);
        var bill = await SourceRep.Context
            .AddPdaFilter<TS, TSE>(ServiceProvider, "WarehouseId", "WhAreaId", "OwnerId")
            .Queryable(SourceRep.AsQueryable().Where(r => r.Id == id && r.DocumentStatus == DocumentStatus.Approve))
            .IncludeNavCol()
            .FirstAsync();
        if (bill == null)
            if (isException) throw Oops.Bah(L.Text["没有找到源单, 可能没有仓库,库区或者货主权限"]);
            else return (null, null);
        var linkParam = GetLinkParam(input.TranId);
        List<PdaLocalBillSourceDetail> scanDetails = new List<PdaLocalBillSourceDetail>();
        // 先找到,有没有明细信息
        var entryProperty = bill.GetType().GetProperty(linkParam.SourceEntryName);
        object entries = null;

        if (entryProperty != null)
        {
            entries = entryProperty.GetValue(bill);
            // 获取行数
            var countProperty = typeof(List<TSE>).GetProperty("Count");
            var count = countProperty?.GetValue(entries, null) is int ? (int)countProperty.GetValue(entries, null)! : 0;

            for (int i = 0; i < count; i++)
            {
                var targetObj = new PdaLocalBillSourceDetail();
                // 普通映射
                foreach (LocalBillLinkMapping mapping in linkParam.Mappings.Where(r => r.ScanType == LocalBillLinkMappingScanType.Detail).ToList())
                {
                    object value = string.IsNullOrEmpty(mapping.LocalBillFiledName) ? mapping.DefaultValue : GetLocalBillValue(linkParam, mapping.LocalBillFiledName, bill, entries, i);
                    targetObj[mapping.ScanFiledName] = value;
                }

                // 计算映射
                foreach (var mapping in linkParam.CalcMappings.Where(r => r.ScanType == LocalBillLinkMappingScanType.Detail))
                {
                    decimal calcQty = GetLocalBillCalcQty(linkParam, mapping, bill, entries, i);
                    targetObj[mapping.ScanFiledName] = calcQty;
                }

                var isAdd = true;

                foreach (LocalBillFilter filter in linkParam.Filters)
                {
                    if (isAdd == false) break;
                    // 获取值
                    var scanValue = targetObj[filter.ScanFiledName];
                    if (scanValue == null) break;
                    switch (filter.Condition)
                    {
                        case LocalBillFilterCondition.Equals:
                            if (!scanValue.Equals(filter.Value)) isAdd = false;
                            break;
                        case LocalBillFilterCondition.NotEquals:
                            if (scanValue.Equals(filter.Value)) isAdd = false;
                            break;
                    }
                }

                // 判断该行是否可用 (比如数量为0, 关闭状态)
                if (isAdd) scanDetails.Add(targetObj);
            }
        }

        var scanHead = new PdaLocalBillSourceHead();

        foreach (LocalBillLinkMapping mapping in linkParam.Mappings)
        {
            if (mapping.ScanType == LocalBillLinkMappingScanType.Detail) continue;
            object value = string.IsNullOrEmpty(mapping.LocalBillFiledName) ? mapping.DefaultValue : GetLocalBillValue(linkParam, mapping.LocalBillFiledName, bill, entries, 0);
            scanHead[mapping.ScanFiledName] = value;
        }

        // 表头
        foreach (var mapping in linkParam.CalcMappings.Where(r => r.ScanType == LocalBillLinkMappingScanType.Head))
        {
            decimal calcQty = GetLocalBillCalcQty(linkParam, mapping, bill, entries, 0);
            scanHead[mapping.ScanFiledName] = calcQty;
        }

        // 计算scanDetails,如果有数量为空的行删除
        scanDetails = VerifyScanDetails(input.TranId, scanDetails, isException);

        // 如果没有明细行返回
        return scanDetails.Count == 0 ? (null, null) : (scanHead, scanDetails);
    }

    /// <summary>
    /// 验证扫描明细数据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="scanDetails"></param>
    /// <param name="isException">是否抛错</param>
    /// <returns></returns>
    protected virtual List<PdaLocalBillSourceDetail> VerifyScanDetails(long tranId, List<PdaLocalBillSourceDetail> scanDetails, bool isException = true)
    {
        // 计算scanDetails,如果有数量为空的行删除
        var details = scanDetails.Where(r => r.Qty != 0).ToList();
        // 如果没有明细行,报错
        if (scanDetails.Count != 0) return details;
        if (isException) throw Oops.Bah(L.Text["源单没有找到可下推明细行信息"]);
        return details;
    }

    /// <summary>
    /// 获取计算数量值
    /// </summary>
    /// <param name="linkParam"></param>
    /// <param name="mapping"></param>
    /// <param name="bill"></param>
    /// <param name="entries"></param>
    /// <param name="i"></param>
    /// <returns></returns>
    public decimal GetLocalBillCalcQty(ILocalBillLinkParam linkParam, LocalBillQtyCalcMapping mapping, object bill, object entries, int i)
    {
        decimal calcQty = 0;
        for (int j = 0; j < mapping.CalcFiledNames.Count; j++)
        {
            // 如果是第一个,直接赋值
            if (j == 0)
            {
                calcQty = Convert.ToDecimal(GetLocalBillValue(linkParam, mapping.CalcFiledNames[j].LocalBillFiledName, bill, entries, i));
                continue;
            }

            // 第二个开始需要计算
            var nowQty = Convert.ToDecimal(GetLocalBillValue(linkParam, mapping.CalcFiledNames[j].LocalBillFiledName, bill, entries, i));
            switch (mapping.CalcFiledNames[j].CalcType)
            {
                case LocalBillCalcType.Add:
                    calcQty += nowQty;
                    break;
                case LocalBillCalcType.Sub:
                    calcQty -= nowQty;
                    break;
                case LocalBillCalcType.Mul:
                    calcQty *= nowQty;
                    break;
                case LocalBillCalcType.Div:
                    calcQty /= nowQty;
                    break;
            }
        }

        return calcQty;
    }

    /// <summary>
    /// 获取单据的值
    /// </summary>
    /// <param name="linkParam"></param>
    /// <param name="fieldName"></param>
    /// <param name="bill"></param>
    /// <param name="entries"></param>
    /// <param name="i"></param>
    /// <returns></returns>
    public object GetLocalBillValue(ILocalBillLinkParam linkParam, string fieldName, object bill, object entries, int i)
    {
        // 先获取源单的值
        var names = fieldName.Split(".");
        if (names[0] == linkParam.SourceEntryName)
        {
            names = names.Skip(1).ToArray();
            // 获取索引器（Item 属性）
            PropertyInfo itemProperty = typeof(List<TSE>).GetProperty("Item");
            var itemObj = itemProperty!.GetValue(entries, new object[] { i });
            var sourceObj = GetLocalBillValue(names, itemObj);
            return sourceObj;
        }
        else
        {
            var sourceObj = GetLocalBillValue(names, bill);
            return sourceObj;
        }
    }

    /// <summary>
    /// 获取单据的值
    /// </summary>
    /// <param name="names"></param>
    /// <param name="obj"></param>
    /// <param name="level"></param>
    /// <returns></returns>
    private object GetLocalBillValue(string[] names, object obj, int level = 0)
    {
        if (obj == null) return null;
        if (obj.GetType().IsSubclassOf(typeof(ExtensionObject)))
        {
            var value = ((ExtensionObject)obj)[names[level]];
            return value;
        }

        var property = obj.GetType().GetProperty(names[level]);
        if (property == null) return null;
        var objValue = property.GetValue(obj);
        if (names.Length <= level + 1) return objValue;
        return GetLocalBillValue(names, objValue, level + 1);
    }

    /// <summary>
    /// 设置源单信息
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="hands"></param>
    /// <param name="details"></param>
    /// <param name="barcode"></param>
    /// <param name="isEstimate">预估</param>
    public (List<PdaLocalBillSourceHead>, List<PdaLocalBillSourceDetail>, PdaLocalBillScanHead, List<PdaLocalBillScanDetail>) SetSourceInfo(long tranId, List<PdaLocalBillSourceHead> hands,
        List<PdaLocalBillSourceDetail> details, BdBarcode barcode, bool isEstimate = false)
    {
        var billData = GetPdaData(tranId);
        //暂时只支持单个源单，不支持多源单
        var headDics = hands.Adapt<List<PdaLocalBillSourceHead>>();
        var detailDics = details.Adapt<List<PdaLocalBillSourceDetail>>();
        //TODO: 判断是否存在
        List<PdaLocalBillSourceHead> removeHeads = new List<PdaLocalBillSourceHead>();
        List<PdaLocalBillSourceDetail> removeDetails = new List<PdaLocalBillSourceDetail>();
        foreach (PdaLocalBillSourceHead headDic in headDics)
        {
            if (billData.SourceHeads.Exists(r => r.SrcBillId == headDic.SrcBillId))
                removeHeads.Add(headDic);
        }

        foreach (PdaLocalBillSourceDetail detailDic in detailDics)
        {
            if (billData.SourceDetails.Exists(r =>
                    r.SrcBillId == detailDic.SrcBillId && r.SrcBillEntryId == detailDic.SrcBillEntryId && r.SrcBillEntrySeq == detailDic.SrcBillEntrySeq))
                removeDetails.Add(detailDic);
        }

        if (removeHeads.Count > 0)
        {
            foreach (PdaLocalBillSourceHead removeHead in removeHeads)
            {
                headDics.Remove(removeHead);
            }
        }

        if (removeDetails.Count > 0)
        {
            foreach (PdaLocalBillSourceDetail removeDetail in removeDetails)
            {
                detailDics.Remove(removeDetail);
            }
        }

        BeforeSetSourceInfo(tranId, headDics, detailDics, barcode);

        //判断是否匹配源单
        var isMatching = GetSourceMatching(headDics, billData.SourceHeads, barcode);
        // 如果不能选源单,退出,不抛错
        if (!isMatching) return (null, null, null, null); //throw Oops.Bah(PdaErrorCode.PDA_1002);
        //设置目标头字段值
        PdaLocalBillScanHead head = SetScanHeadValue(billData.ScanHead, billData.ScanDetails, headDics, detailDics);
        var sourceDetails = detailDics.Adapt<List<PdaLocalBillScanDetail>>();
        sourceDetails.ForEach(r => r.DetailId = $"{YitIdHelper.NextId()}");
        //如果是制作单据,需要ScanQty = Qty
        if (Config.BillParams.IsMakeBill)
        {
            sourceDetails.ForEach(r =>
            {
                r["ScanQty"] = r.Qty;

                if (!string.IsNullOrEmpty(billData.StockInfo.WhAreaId) && billData.StockInfo.WhAreaId != "0" && (string.IsNullOrEmpty(r.WhAreaId) || r.WhAreaId == "0"))
                {
                    r.WhAreaId = billData.StockInfo.WhAreaId;
                    r.WhAreaNumber = billData.StockInfo.WhAreaNumber;
                    r.WhAreaName = billData.StockInfo.WhAreaName;
                    r.WhLocId = billData.StockInfo.WhLocId;
                    r.WhLocNumber = billData.StockInfo.WhLocNumber;
                    r.WhLocName = billData.StockInfo.WhLocName;
                }
            });
        }

        if (!isEstimate)
        {
            billData.SourceHeads.AddRange(headDics);
            billData.SourceDetails.AddRange(detailDics);
            billData.ScanHead = head;
            billData.ScanDetails.AddRange(sourceDetails);
        }

        AfterSetSourceInfo(tranId, headDics, detailDics, barcode);

        RefreshShow(tranId);

        return (headDics, detailDics, head, sourceDetails);
    }

    /// <summary>
    /// 判断是否匹配源单
    /// </summary>
    /// <param name="newHeads"></param>
    /// <param name="sourceHeads"></param>
    /// <param name="barcode"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public virtual bool GetSourceMatching(List<PdaLocalBillSourceHead> newHeads, List<PdaLocalBillSourceHead> sourceHeads, BdBarcode barcode)
    {
        //1.判断是否重复
        newHeads.ForEach(m =>
            {
                if (string.IsNullOrEmpty(m.SrcBillId)) throw Oops.Bah(PdaErrorCode.Pda1027);
                sourceHeads.ForEach(r =>
                {
                    if (r.SrcBillId.Equals(m.SrcBillId)) throw Oops.Bah(PdaErrorCode.Pda1018);
                });
            }
        );

        //2.判断是否部门,供应商,客户相同 
        newHeads.ForEach(m =>
        {
            sourceHeads.ForEach(r =>
            {
                if (!PdaHelper.IsEquals(r.CustomerId, m.CustomerId) || !PdaHelper.IsEquals(r.DepartmentId, m.DepartmentId) || !PdaHelper.IsEquals(r.WorkShopId, m.WorkShopId) ||
                    !PdaHelper.IsEquals(r.SupplierId, m.SupplierId))
                    throw Oops.Bah(PdaErrorCode.Pda1024);
            });
        });
        return true;
    }

    /// <summary>
    /// 设置目标头字段值
    /// </summary>
    /// <param name="scanDetails"></param>
    /// <param name="sourceHeads"></param>
    /// <param name="sourceDetails"></param>
    /// <param name="scanHead"></param>
    /// <exception cref="NotImplementedException"></exception>
    public PdaLocalBillScanHead SetScanHeadValue(PdaLocalBillScanHead scanHead, List<PdaLocalBillScanDetail> scanDetails, List<PdaLocalBillSourceHead> sourceHeads,
        List<PdaLocalBillSourceDetail> sourceDetails)
    {
        //使用第一个源单头信息为准
        var head = new PdaLocalBillScanHead();
        if (sourceHeads == null || sourceHeads.Count <= 0) return scanHead;
        head = sourceHeads[0].Adapt<PdaLocalBillScanHead>();
        //如果初始化的时候有值,不应该清了
        foreach (KeyValuePair<string, object> sourceHead in scanHead)
        {
            if (string.IsNullOrEmpty(sourceHead.Value + "")) continue;
            if (!string.IsNullOrEmpty(head[sourceHead.Key] + "")) continue;
            head[sourceHead.Key] = sourceHead.Value;
        }

        //如果目标单日期字段有值,不更新
        if (scanHead.Date != null) head.Date = scanHead.Date;
        return head;
    }

    /// <summary>
    /// 选择源单前处理
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="headDics"></param>
    /// <param name="detailDics"></param>
    /// <param name="barcode"></param>
    public virtual void BeforeSetSourceInfo(long tranId, List<PdaLocalBillSourceHead> headDics, List<PdaLocalBillSourceDetail> detailDics, BdBarcode barcode)
    {
    }

    /// <summary>
    /// 选择源单后处理
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="hands"></param>
    /// <param name="sourceDetails"></param>
    /// <param name="barcode"></param>
    public virtual void AfterSetSourceInfo(long tranId, List<PdaLocalBillSourceHead> hands, List<PdaLocalBillSourceDetail> sourceDetails, BdBarcode barcode)
    {
    }

    /// <summary>
    /// 对 lookupKey 处理，返回新的 lookupKey
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="lookupKey"></param>
    /// <returns></returns>
    protected virtual string HandleLookupKey(long tranId, string lookupKey)
    {
        return lookupKey;
    }

    /// <inheritdoc/>
    public void ScanBarcode(long tranId, BdBarcode barcode, decimal? modifyQty = null, Dictionary<string, object> properties = null)
    {
        var billData = GetPdaData(tranId);

        //条码只影响 DetailTable
        //现在暂不支持无源单
        //1. 首先检查 DetailTable是否满足合并要求，并且数量不能超, 该条码不能在Barcodes表存在
        //2. 合并数量
        //3. 添加条码表

        //判断条码是否有仓库仓位
        var barcodeHasStock = !(barcode.WhAreaId == null || barcode.WhAreaId == 0);
        var stockInfoHasStock = !(string.IsNullOrEmpty(billData.StockInfo.WhAreaId) || billData.StockInfo.WhAreaId == "0");
        if (Config.BillParams.FirstStockType != FirstStockType.NoStock && !barcodeHasStock && !stockInfoHasStock) throw Oops.Bah(PdaErrorCode.Pda1017);
        if (!stockInfoHasStock && Config.BillParams.FirstStockType == FirstStockType.Select)
            throw Oops.Bah(PdaErrorCode.Pda1020);

        // 有可能使用的是LastExec的源单信息
        // 有源单,并且源单的KEY为该单据的源单KEY
        GetBarcodeFindSourceInfo(tranId, barcode);

        //查询源单，如果没有源单
        if (billData.ScanDetails.Count <= 0)
        {
            //如果不支持源单外物料 报错
            if (!Config.BillParams.IsOverSourceItem) throw Oops.Bah(PdaErrorCode.Pda1011);
        }

        BeforeMatchingBarcode(tranId, barcode);

        var pdaBarcode = new PdaLocalBillBarcode(barcode, modifyQty ?? barcode.Qty, properties);
        MatchingBarcode(tranId, pdaBarcode, modifyQty);

        //匹配条码后处理
        AfterMatchingBarcode(tranId, pdaBarcode);
        //刷新
        RefreshShow(tranId);
    }

    protected virtual void BeforeMatchingBarcode(long tranId, BdBarcode barcode)
    {
    }

    protected virtual void AfterMatchingBarcode(long tranId, PdaLocalBillBarcode pdaBarcode)
    {
    }

    /// <summary>
    /// 批量扫描条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcodes"></param>
    /// <param name="container"></param>
    /// <exception cref="NotImplementedException"></exception>
    public void ScanBarcodes(long tranId, List<BdBarcode> barcodes, BdContainer container)
    {
        var billData = GetPdaData(tranId);
        var tmpPdaBarcodes = billData.BarcodeList.Adapt<List<PdaLocalBillBarcode>>();

        try
        {
            var info = new PdaLocalBillMatchingInfo();
            var pdaBarcodes = new List<PdaLocalBillBarcode>();
            //判断条码SourceBillId不为空并且SourceKey是单据SourceKey并且为允许带源单
            foreach (BdBarcode barcode in barcodes)
            {
                var pdaBarcode = new PdaLocalBillBarcode(barcode, barcode.Qty, null, container);
                pdaBarcodes.Add(pdaBarcode);

                //反写仓库(不管源单是否有仓库)
                //如果条码没仓库，以选的仓库为准
                var barcodeHasStock = !(barcode.WhAreaId == null || barcode.WhAreaId == 0);
                if (!barcodeHasStock)
                {
                    info.StockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
                    pdaBarcode.SetStockInfo(info.StockInfo, Config.BillParams.OpType == BarOpType.Transfer);
                }
                else
                {
                    //如果有选仓库，看看仓库和条码的是不是一致，不一致用条码的
                    PdaLocalBillStockInfo barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
                    if (!$"{billData.StockInfo.WhAreaId}_{billData.StockInfo.WhLocId}".Equals($"{barcode.WhAreaId}_{barcode.WhLocId}"))
                    {
                        //条码优先
                        if (Config.BillParams.FirstStockType == FirstStockType.Barcode)
                        {
                            var rep = App.GetService<SqlSugarRepository<BdWhLoc>>(ServiceProvider);
                            // 直接读仓库表
                            var whLocs = rep.AsQueryable()
                                .LeftJoin<BdWhArea>((t1, t2) => t2.Id == t1.WhAreaId)
                                .Where((t1, t2) => t2.Id == barcode.WhAreaId && t1.Id == barcode.WhLocId)
                                .Select((t1, t2) => new
                                {
                                    WhAreaId = t2.Id,
                                    WhAreaNumber = t2.Number,
                                    WhAreaName = t2.Name,
                                    WhLocId = t1.Id,
                                    WhLocNumber = t1.Number,
                                    WhLocName = t1.Name
                                })
                                .ToList();
                            if (whLocs.Count <= 0)
                                throw Oops.Bah(PdaErrorCode.Pda1019, $"{barcode.WhAreaId}_{barcode.WhLocId}");
                            var row = whLocs[0];
                            barcodeStockInfo.WhAreaId = $"{row.WhAreaId}";
                            barcodeStockInfo.WhAreaNumber = $"{row.WhAreaNumber}";
                            barcodeStockInfo.WhAreaName = $"{row.WhAreaName}";
                            barcodeStockInfo.WhLocId = $"{row.WhLocId}";
                            barcodeStockInfo.WhLocNumber = $"{row.WhLocNumber}";
                            barcodeStockInfo.WhLocName = $"{row.WhLocName}";
                            if (string.IsNullOrEmpty(billData.StockInfo.WhAreaId) || billData.StockInfo.WhAreaId.Equals("0"))
                                billData.StockInfo = barcodeStockInfo.Adapt<PdaLocalBillStockInfo>();
                        }
                        //选择优先
                        else if (Config.BillParams.FirstStockType == FirstStockType.Select)
                        {
                            barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
                        }
                        //不带仓库
                        else if (Config.BillParams.FirstStockType == FirstStockType.NoStock)
                        {
                            barcodeStockInfo = new PdaLocalBillStockInfo();
                        }
                    }

                    pdaBarcode.SetStockInfo(barcodeStockInfo, Config.BillParams.OpType == BarOpType.Transfer);
                    info.StockInfo = barcodeStockInfo;
                }

                var scanDetails = billData.ScanDetails;

                if ((pdaBarcode.Barcode.SrcBillId != null && pdaBarcode.Barcode.SrcBillId != 0 &&
                     $"{pdaBarcode.Barcode.SrcBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource) ||
                    (pdaBarcode.Barcode.LastExecBillId != null && pdaBarcode.Barcode.LastExecBillId != 0 &&
                     $"{pdaBarcode.Barcode.LastExecBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource))
                {
                    var srcBillId = pdaBarcode.Barcode.SrcBillId;
                    if (srcBillId is null or 0) srcBillId = pdaBarcode.Barcode.LastExecBillId;
                    var billInfo = GetBillInfo(new PdaLocalBillLookSelectInput
                    {
                        Key = Key,
                        TranId = tranId,
                        LookupDataKey = null,
                        LookupKey = null,
                        Id = srcBillId + "",
                        DetailIndex = null,
                        IsLocalBill = false
                    }, false).Result;
                    if (billInfo.Item2 is { Count: > 0 })
                    {
                        var sourceInfo = SetSourceInfo(tranId, [billInfo.Item1], billInfo.Item2, pdaBarcode.Barcode, true);
                        if (sourceInfo.Item4 != null) scanDetails.AddRange(sourceInfo.Item4);
                    }
                }
            }

            //查询源单，如果没有源单
            if (billData.ScanDetails.Count <= 0)
            {
                //如果不支持源单外物料 报错
                if (!Config.BillParams.IsOverSourceItem) throw Oops.Bah(PdaErrorCode.Pda1011);
            }

            foreach (PdaLocalBillBarcode pdaBarcode in pdaBarcodes)
            {
                MatchingBarcode(tranId, pdaBarcode);
            }

            //刷新
            RefreshShow(tranId);
        }
        catch (System.Exception)
        {
            //出错回滚
            billData.BarcodeList.Clear();
            billData.DetailIncludeBarcodes.Clear();
            //把扫描数量设置为0
            billData.ScanDetails.ForEach(r => r.ScanQty = 0);
            foreach (var barcode in tmpPdaBarcodes)
            {
                MatchingBarcode(tranId, barcode, barcode.CalcBarcode.ScanQty);
            }

            throw;
        }
    }

    /// <summary>
    /// 获取条码关联的源单信息
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <returns></returns>
    public void GetBarcodeFindSourceInfo(long tranId, BdBarcode barcode)
    {
        // 要么用Src, 要么用LastExec
        if ((barcode.SrcBillId != null && barcode.SrcBillId != 0 &&
             $"{barcode.SrcBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource) ||
            (barcode.LastExecBillId != null && barcode.LastExecBillId != 0 &&
             $"{barcode.LastExecBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource))
        {
            var srcBillId = barcode.SrcBillId;
            if (barcode.LastExecBillId != null && barcode.LastExecBillId != 0) srcBillId = barcode.LastExecBillId;
            var bill = GetBill(new PdaLocalBillLookSelectInput
            {
                Key = Key,
                TranId = tranId,
                LookupDataKey = null,
                LookupKey = null,
                Id = srcBillId + "",
                DetailIndex = null,
                IsLocalBill = false
            }, false).Result;
        }
    }

    /// <summary>
    /// 匹配条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="inPutPdaBarcode"></param>
    /// <param name="modifyQty"></param>
    public virtual void MatchingBarcode(long tranId, PdaLocalBillBarcode inPutPdaBarcode, decimal? modifyQty = null)
    {
        var billData = GetPdaData(tranId);

        var pdaBarcode = inPutPdaBarcode;
        pdaBarcode.CalcBarcode.ScanQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;

        //反写仓库(不管源单是否有仓库)
        //如果条码没仓库，以选的仓库为准
        var barcodeHasStock = !(inPutPdaBarcode.Barcode.WhAreaId == null || inPutPdaBarcode.Barcode.WhAreaId == 0);
        if (!barcodeHasStock)
        {
            pdaBarcode.SetStockInfo(billData.StockInfo.Adapt<PdaLocalBillStockInfo>(), Config.BillParams.OpType == BarOpType.Transfer);
        }
        else
        {
            //如果有选仓库，看看仓库和条码的是不是一致，不一致用条码的
            PdaLocalBillStockInfo barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
            if (!$"{billData.StockInfo.WhAreaId}_{billData.StockInfo.WhLocId}".Equals($"{inPutPdaBarcode.Barcode.WhAreaId}_{inPutPdaBarcode.Barcode.WhLocId}"))
            {
                //条码优先
                if (Config.BillParams.FirstStockType == FirstStockType.Barcode)
                {
                    var rep = App.GetService<SqlSugarRepository<BdWhLoc>>(ServiceProvider);
                    // 直接读仓库表
                    var whLocs = rep.AsQueryable()
                        .LeftJoin<BdWhArea>((t1, t2) => t2.Id == t1.WhAreaId)
                        .Where((t1, t2) => t2.Id == inPutPdaBarcode.Barcode.WhAreaId && t1.Id == inPutPdaBarcode.Barcode.WhLocId)
                        .Select((t1, t2) => new
                        {
                            WhAreaId = t2.Id,
                            WhAreaNumber = t2.Number,
                            WhAreaName = t2.Name,
                            WhLocId = t1.Id,
                            WhLocNumber = t1.Number,
                            WhLocName = t1.Name
                        })
                        .ToList();
                    if (whLocs.Count <= 0)
                        throw Oops.Bah(PdaErrorCode.Pda1019, $"{inPutPdaBarcode.Barcode.WhAreaId}_{inPutPdaBarcode.Barcode.WhLocId}");
                    var row = whLocs[0];
                    barcodeStockInfo.WhAreaId = $"{row.WhAreaId}";
                    barcodeStockInfo.WhAreaNumber = $"{row.WhAreaNumber}";
                    barcodeStockInfo.WhAreaName = $"{row.WhAreaName}";
                    barcodeStockInfo.WhLocId = $"{row.WhLocId}";
                    barcodeStockInfo.WhLocNumber = $"{row.WhLocNumber}";
                    barcodeStockInfo.WhLocName = $"{row.WhLocName}";
                    if (string.IsNullOrEmpty(billData.StockInfo.WhAreaId) || billData.StockInfo.WhAreaId.Equals("0"))
                        billData.StockInfo = barcodeStockInfo.Adapt<PdaLocalBillStockInfo>();
                }
                //选择优先
                else if (Config.BillParams.FirstStockType == FirstStockType.Select)
                {
                    barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
                }
                //不带仓库
                else if (Config.BillParams.FirstStockType == FirstStockType.NoStock)
                {
                    barcodeStockInfo = new PdaLocalBillStockInfo();
                }
            }

            pdaBarcode.SetStockInfo(barcodeStockInfo, Config.BillParams.OpType == BarOpType.Transfer);
            RefreshShow(tranId);
        }

        //查找匹配的行
        List<string> values = new List<string>();
        Dictionary<string, object> summaryOperationFields = new Dictionary<string, object>();
        foreach (var field in Config.BillParams.SummaryOperationFields)
        {
            var value = PdaLocalBillHelper.GetValue(pdaBarcode.CalcBarcode, field.FieldName);
            values.Add($"{value}");
        }

        var keyValue = string.Join("|", values);
        var matchingDetails = billData.ScanDetails.Where(r =>
        {
            List<string> rKeyValues = new List<string>();
            foreach (var column in Config.BillParams.SummaryOperationFields)
            {
                rKeyValues.Add($"{r[column.ScanFieldName]}");
            }

            return string.Join("|", rKeyValues.ToArray()) == keyValue;
        }).ToList();

        if (matchingDetails.Count <= 0)
        {
            //不支持源单外物料 报错
            if (!Config.BillParams.IsOverSourceItem)
            {
                throw Oops.Bah(PdaErrorCode.Pda1016);
            }

            //如果支持源单外物料 (无源单)
            //把条码的值，写到Detail
            var detail = inPutPdaBarcode.Barcode.Adapt<PdaLocalBillScanDetail>();
            // 循环BdBarcode属性,如果是类,添加Number,Name
            var bdBarProperties = inPutPdaBarcode.Barcode.GetType().GetProperties();
            foreach (PropertyInfo bdBarProperty in bdBarProperties)
            {
                // 如果继承基础资料
                if (!bdBarProperty.PropertyType.IsSubclassOf(typeof(BdEntityBase))) continue;
                if (bdBarProperty.GetValue(inPutPdaBarcode.Barcode) is not BdEntityBase obj) continue;

                detail[$"{bdBarProperty.Name}Number"] = obj.Number;
                detail[$"{bdBarProperty.Name}Name"] = obj.Name;

                // 如果是物料
                if (bdBarProperty.GetValue(inPutPdaBarcode.Barcode) is not BdMaterial materialObj) continue;
                detail.MaterialSpec = materialObj.Specification;
            }

            billData.ScanDetails.Add(detail);
            //忝加明细主键Key值
            detail.DetailId = $"{YitIdHelper.NextId()}";
            detail.IsNew = true;

            SetDetailValue(inPutPdaBarcode.Barcode, detail);
            matchingDetails.Add(detail);
        }

        var matchingData = matchingDetails.Adapt<List<PdaLocalBillScanDetail>>();
        var matchingIncludes = new List<(PdaLocalBillScanDetail, PdaLocalBillBarcode, decimal)>();

        var diffQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;
        if (!Config.BillParams.IsOverSourceItem && !Config.BillParams.IsOverSourceQty)
        {
            //查找到匹配的明细，再用QTY判断是否超
            foreach (var data in matchingData)
            {
                var qty = data.Qty - data.ScanQty;
                diffQty = diffQty - qty;
            }

            if (diffQty > 0) throw Oops.Bah(PdaErrorCode.Pda1014);
        }

        //校验通过,写数据
        //这里是扫描，默认带条码数量
        diffQty = pdaBarcode.CalcBarcode.ScanQty;

        foreach (PdaLocalBillScanDetail detail in matchingData)
        {
            if (diffQty <= 0) break;
            decimal includeQty = diffQty;
            var qty = detail.Qty - detail.ScanQty;
            if (qty <= 0) continue;
            if (diffQty > qty)
            {
                //如果可扣减数量大于源单数量,includeQty为源单数量
                includeQty = qty;
                detail.ScanQty = detail.Qty;
                diffQty = diffQty - qty;
            }
            else
            {
                detail.ScanQty = detail.ScanQty + diffQty;
                diffQty = 0;
            }

            matchingIncludes.Add((detail, pdaBarcode, includeQty));

            // 转到不报错再添加
            // if (!billData.DetailIncludeBarcodes.ContainsKey(detail.DetailId))
            //     billData.DetailIncludeBarcodes.Add(detail.DetailId, new List<PdaLocalBillIncludeBarcode>());
            // billData.DetailIncludeBarcodes[detail.DetailId].Add(new PdaLocalBillIncludeBarcode(pdaBarcode, includeQty));
            // pdaBarcode.BarcodeIncludeDetails.Add(detail);
        }

        //如果剩余数量大于0
        if (diffQty > 0 && !Config.BillParams.IsOverSourceQty)
        {
            //如果超源单数量，直接把剩余数量填到匹配的最后一行记录上
            if (matchingData.Count <= 0)
                throw Oops.Bah(PdaErrorCode.Pda1016);
            throw Oops.Bah($"条码还有数量[{diffQty}]没能匹配");
        }

        // 更新值
        matchingData.ForEach(r =>
        {
            var data = matchingDetails.FirstOrDefault(n => n.DetailId == r.DetailId);
            if (data == null) return;
            data.ScanQty = r.ScanQty;
        });

        matchingIncludes.ForEach(r =>
        {
            if (!billData.DetailIncludeBarcodes.ContainsKey(r.Item1.DetailId))
                billData.DetailIncludeBarcodes.Add(r.Item1.DetailId, new List<PdaLocalBillIncludeBarcode>());
            billData.DetailIncludeBarcodes[r.Item1.DetailId].Add(new PdaLocalBillIncludeBarcode(r.Item2, r.Item3));
            pdaBarcode.BarcodeIncludeDetails.Add(r.Item1);
        });
        
        if (diffQty > 0)
        {
            var lastMatching = matchingData[^1];
            lastMatching.ScanQty += diffQty;

            if (!billData.DetailIncludeBarcodes.ContainsKey(matchingData[^1].DetailId))
            {
                billData.DetailIncludeBarcodes.Add(matchingData[^1].DetailId, new List<PdaLocalBillIncludeBarcode>());
                billData.DetailIncludeBarcodes[matchingData[^1].DetailId].Add(new PdaLocalBillIncludeBarcode(pdaBarcode, diffQty));
                pdaBarcode.BarcodeIncludeDetails.Add(matchingData[^1]);
            }
            else
            {
                var matchingDetail = billData.DetailIncludeBarcodes[matchingData[^1].DetailId];
                var pdaIncludeBarcode = matchingDetail.FirstOrDefault(r => r.PdaBarcode.DetailId == pdaBarcode.DetailId);
                if (pdaIncludeBarcode != null)
                {
                    pdaIncludeBarcode.SetScanQty(pdaIncludeBarcode.ScanQty + diffQty);
                }
                else
                {
                    matchingDetail.Add(new PdaLocalBillIncludeBarcode(pdaBarcode, diffQty));
                    pdaBarcode.BarcodeIncludeDetails.Add(matchingData[^1]);
                }
            }

            // 如果是新增明细(即非源单带下来的明细)扫描数量大于数量,把数量改成扫描数量
            if (lastMatching.ScanQty > lastMatching.Qty && lastMatching.IsNew) lastMatching.Qty = lastMatching.ScanQty;
        }

        billData.BarcodeList.Add(pdaBarcode);
    }

    /// <summary>
    /// 设置明细值
    /// </summary>
    /// <param name="barcode"></param>
    /// <param name="data"></param>
    public virtual void SetDetailValue(BdBarcode barcode, PdaLocalBillScanDetail data)
    {
    }

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="dataKey"></param>
    /// <param name="valueKey"></param>
    /// <returns></returns>
    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        switch (dataKey)
        {
            case "deleteBarcode":
                return DeleteBarcode(tranId, valueKey);
                break;
            default:
                throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 删除条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="detailId"></param>
    /// <returns></returns>
    public virtual Task DeleteBarcode(long tranId, string detailId)
    {
        var pdaData = GetPdaData(tranId);
        var removeBarcode = pdaData.BarcodeList.Find(r => r.DetailId == detailId);
        if (removeBarcode == null) throw Oops.Bah(PdaErrorCode.Pda1029, detailId);
        List<PdaLocalBillBarcode> tmpPdaBarcodes = new List<PdaLocalBillBarcode>();
        pdaData.BarcodeList.ForEach(b =>
        {
            if (b.DetailId != detailId)
                tmpPdaBarcodes.Add(b);
        });
        pdaData.BarcodeList.Clear();
        pdaData.DetailIncludeBarcodes.Clear();
        //把扫描数量设置为0
        pdaData.ScanDetails.ForEach(r => r.ScanQty = 0);
        // 如果仓库选择优先,需要把仓库改成Calc条码的仓库仓位
        var oldStockInfo = pdaData.StockInfo;
        foreach (var barcode in tmpPdaBarcodes)
        {
            if (Config.BillParams.FirstStockType == FirstStockType.Select)
                pdaData.StockInfo = barcode.CalcBarcode.Adapt<PdaLocalBillStockInfo>();
            MatchingBarcode(tranId, barcode, barcode.CalcBarcode.ScanQty);
        }

        pdaData.StockInfo = oldStockInfo;
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 修改条码 
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="detailId"></param>
    /// <param name="json"></param>
    /// <returns></returns>
    public Task ModifyBarcode(long tranId, string json)
    {
        var vs = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
        var detailId = vs["detailId"] + "";
        var value = vs["value"] + "";
        var fieldName = vs["fieldName"] + "";
        var pdaData = GetPdaData(tranId);
        var pdaBarcode = pdaData.BarcodeList.FirstOrDefault(r => r.DetailId == detailId);
        if (pdaBarcode == null) throw Oops.Bah(L.Text["没有找到条码记录"]);
        ResetBarcodeOldValue(tranId);
        SetObjectValue(pdaBarcode.CalcBarcode, value, fieldName);
        if (fieldName == "ScanQty") ResetBarcodeScanQty(tranId);
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 写入旧的数量值
    /// </summary>
    /// <param name="tranId"></param>
    public void ResetBarcodeOldValue(long tranId)
    {
        var billData = GetPdaData(tranId);
        billData.BarcodeList.ForEach(b => b.OldQty = b.CalcBarcode.ScanQty);
    }

    /// <summary>
    /// 重置条码
    /// </summary>
    /// <param name="tranId"></param>
    public void ResetBarcodeScanQty(long tranId)
    {
        var billData = GetPdaData(tranId);
        List<PdaLocalBillBarcode> tmpPdaBarcodes = new List<PdaLocalBillBarcode>();
        billData.BarcodeList.ForEach(b => tmpPdaBarcodes.Add(b));
        billData.BarcodeList.Clear();
        billData.DetailIncludeBarcodes.Clear();
        //1. 把数量设置为0
        billData.ScanDetails.ForEach(r => r.ScanQty = 0);

        try
        {
            foreach (var barcode in tmpPdaBarcodes)
            {
                MatchingBarcode(tranId, barcode, barcode.CalcBarcode.ScanQty);
            }
        }
        catch (System.Exception)
        {
            //出错回滚数量
            tmpPdaBarcodes.ForEach(b => b.CalcBarcode.ScanQty = b.OldQty);
            billData.BarcodeList.Clear();
            billData.DetailIncludeBarcodes.Clear();
            //1. 把数量设置为0
            billData.ScanDetails.ForEach(r => r.ScanQty = 0);
            foreach (var barcode in tmpPdaBarcodes)
            {
                MatchingBarcode(tranId, barcode, barcode.CalcBarcode.ScanQty);
            }

            throw;
        }

        RefreshShow(tranId);
    }

    public override async Task<dynamic> LocalBillSelectFieldData(PdaLocalBillSelectDataInput input)
    {
        switch (input.FieldKey)
        {
            case "modifyBarcode":
                await ModifyBarcode(input.TranId, input.FieldValue);
                return GetPdaData(input.TranId);
            case "HideFinishScanQtyChange":
                await HideFinishScanQtyDetail(input.TranId, input.FieldValue);
                return GetPdaData(input.TranId);
            default:
                return await base.LocalBillSelectFieldData(input);
        }
    }

    public override async Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "modifyAuxQty":
                return await ModifyAuxQty(tranId, submitData + "");
        }

        throw new NotImplementedException();
    }

    /// <summary>
    /// 修改辅助数量
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    private Task<dynamic> ModifyAuxQty(long tranId, string value)
    {
        var modifyAuxQtyInfo = JsonConvert.DeserializeObject<ModifyAuxQtyInfo>(value);
        var k3CloudInterface = App.GetService<K3CloudInterface>(ServiceProvider);
        var client = k3CloudInterface.GetK3CloudClient();
        var modifyQty = 0m;
        switch (modifyAuxQtyInfo.Field)
        {
            case "qty":
                modifyQty = client.DoUnitConvert(modifyAuxQtyInfo.MaterialId, modifyAuxQtyInfo.UnitId, modifyAuxQtyInfo.AuxUnitId, modifyAuxQtyInfo.Qty);
                break;
            case "auxQty":
                modifyQty = client.DoUnitConvert(modifyAuxQtyInfo.MaterialId, modifyAuxQtyInfo.AuxUnitId, modifyAuxQtyInfo.UnitId, modifyAuxQtyInfo.Qty);
                break;
            default:
                throw Oops.Bah($"没有实现Field: {modifyAuxQtyInfo.Field}");
        }

        return Task.FromResult<dynamic>(modifyQty);
    }

    public virtual Task HideFinishScanQtyDetail(long tranId, string value)
    {
        var billData = GetPdaData(tranId);
        billData.IsHideFinishScanQty = Convert.ToBoolean(value);
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 设置表头值
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldName"></param>
    /// <param name="value"></param>
    public void SetScanHeadValue(long tranId, string fieldName, object value)
    {
        var billData = GetPdaData(tranId);
        billData.ScanHead[fieldName] = value;
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="submitKey"></param>
    /// <param name="submitData"></param>
    /// <param name="isRepeat"></param>
    /// <returns></returns>
    public override async Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        // 校验提交的数据合法性
        VerifySubmit(tranId);

        var billData = GetPdaData(tranId);

        var sourceBillRelateKeys = new[] { "SrcBillId", "SrcBillNo", "SrcBillEntryId", "SrcBillEntrySeq", "SrcBillKey" };
        var detailData = billData.ScanDetails;
        if (detailData.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1022);
        var detailIncludeBarcodes = billData.DetailIncludeBarcodes;
        if (detailIncludeBarcodes.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1023);

        // var isFifoCheck = FifoCheck(tranId, isRepeat, properties);
        // if (!isFifoCheck) return;

        Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails = new Dictionary<string, PdaLocalBillSummaryInfo>(StringComparer.OrdinalIgnoreCase);

        if (billData.ScanDetails.Count <= 0) throw Oops.Bah(L.Text["没有提交的明细内容!"]);

        // 获取单据类型
        var billType = await GetBillType(tranId);
        billData.ScanHead.DestBillType = billType;

        // 填仓库信息
        var warehouseIdStr = GetWarehouseId(tranId);
        if (string.IsNullOrEmpty(warehouseIdStr)) throw Oops.Bah(L.Text["请先选择仓库（组织）"]);
        var warehouseId = Convert.ToInt64(warehouseIdStr);
        var warehouse = await SourceRep.Change<BdWarehouse>().GetFirstAsync(r => r.Id == warehouseId);
        if (warehouse == null) throw Oops.Bah(L.Text["仓库（组织）不存在，请重新选择"]);
        billData.ScanHead.WareHouseId = warehouse.Id;
        billData.ScanHead.WareHouseNumber = warehouse.Number;
        billData.ScanHead.WareHouseName = warehouse.Name;

        foreach (var detail in detailData)
        {
            var entryId = detail.DetailId;
            if (!detailIncludeBarcodes.ContainsKey(entryId)) continue;
            var pdaBarcodes = detailIncludeBarcodes[entryId];

            //条码的KEY
            foreach (PdaLocalBillIncludeBarcode pdaBarcode in pdaBarcodes)
            {
                List<string> barcodeSummaryValues = new List<string>();
                //添加分录内码作为分组
                barcodeSummaryValues.Add(entryId);

                foreach (var summaryBillField in Config.BillParams.SummaryBillFields)
                {
                    if (sourceBillRelateKeys.Contains(summaryBillField.FieldName))
                    {
                        barcodeSummaryValues.Add($"{detail[summaryBillField.FieldName]}");
                        continue;
                    }

                    var value = PdaLocalBillHelper.GetValue(pdaBarcode.PdaBarcode.CalcBarcode, summaryBillField.FieldName);
                    barcodeSummaryValues.Add($"{value}");
                }

                string barcodeSummaryKey = string.Join("|", barcodeSummaryValues);
                if (!summaryDetails.ContainsKey(barcodeSummaryKey))
                {
                    summaryDetails.Add(barcodeSummaryKey, new PdaLocalBillSummaryInfo());
                    var dic = detail.Adapt<PdaLocalBillScanDetail>();
                    foreach (var summaryBillField in Config.BillParams.SummaryBillFields)
                    {
                        //如果是源单5件套,也需要忽略
                        if (sourceBillRelateKeys.Contains(summaryBillField.FieldName))
                        {
                            if (detail.IsNew)
                                dic[summaryBillField.FieldName] = default;
                            continue;
                        }

                        var value = PdaLocalBillHelper.GetValue(pdaBarcode.PdaBarcode.CalcBarcode, summaryBillField.FieldName);
                        if (!summaryBillField.IsSummaryNull)
                        {
                            if (string.IsNullOrEmpty($"{value}")) continue;
                            if (value != null && (value is int || value is decimal || value is float || value is double))
                            {
                                if (Convert.ToDecimal(value) == 0) continue;
                            }
                        }

                        dic[summaryBillField.FieldName] = value;
                    }

                    dic.ScanQty = 0m;

                    // 自定义合计汇总字段
                    foreach (var summaryBillQtyField in Config.BillParams.SummaryBillQtyFields)
                    {
                        dic[summaryBillQtyField.FieldName] = 0;
                    }

                    // 添加单据类型
                    dic.DestBillType = billType;

                    summaryDetails[barcodeSummaryKey].Values = dic;
                }

                //在盘点测试这里有数量问题,等有项目有超源单数量和源单外物料后测试
                //summaryDetails[barcodeSummaryKey].ScanQty = summaryDetails[barcodeSummaryKey].ScanQty + Convert.ToDecimal(pdaBarcode.PdaBarcode.CalcBarcode.ScanQty);
                summaryDetails[barcodeSummaryKey].ScanQty = summaryDetails[barcodeSummaryKey].ScanQty + Convert.ToDecimal(pdaBarcode.ScanQty);
                // 自定义合计汇总字段
                foreach (var summaryBillQtyField in Config.BillParams.SummaryBillQtyFields)
                {
                    if (!pdaBarcode.PdaBarcode.CalcBarcode.Contains(summaryBillQtyField.FieldName)) continue;
                    summaryDetails[barcodeSummaryKey].Values[summaryBillQtyField.FieldName] =
                        Convert.ToDecimal(summaryDetails[barcodeSummaryKey].Values[summaryBillQtyField.FieldName]) +
                        Convert.ToDecimal(pdaBarcode.PdaBarcode.CalcBarcode[summaryBillQtyField.FieldName]);
                }

                summaryDetails[barcodeSummaryKey].PdaBarcodes.Add(pdaBarcode.PdaBarcode);
            }
        }

        // 把扫描数最填到Values中
        foreach (KeyValuePair<string, PdaLocalBillSummaryInfo> prir in summaryDetails)
        {
            prir.Value.Values.ScanQty = prir.Value.ScanQty;
        }

        if (summaryDetails.Count <= 0) throw Oops.Bah(L.Text["没有提交的明细!"]);
        var linkParam = GetLinkParam(tranId);

        // 设置提交的对象
        var submitObj = SetSubmitObj(tranId, linkParam, summaryDetails);

        if (linkParam.SubmitBillType == null) throw Oops.Bah(L.Text["未指定[SubmitBillType]提交单据类型"]);

        //开启事务，如有外部事务，内部事务用外部事务
        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());
        var paramLogRep = App.GetService<SqlSugarRepository<PdaSaveBillParamLog>>(ServiceProvider);
        PdaSaveBillParamLog paramLog = new PdaSaveBillParamLog
        {
            Id = YitIdHelper.NextId(),
            TranId = tranId,
            Param = JsonConvert.SerializeObject(submitObj),
            IsSuccess = false,
            Result = "",
            SourceKey = Config.BillSchema.BillLink.SourceKey,
            TargetKey = Config.BillSchema.BillLink.DestKey,
            TargetBillId = "",
            TargetBillNo = "",
            Extra1 = null,
            Extra2 = null,
            Extra3 = null,
            CreateTime = DateTime.Now,
            CreateUserId = UserManager.UserId,
            CreateUserName = UserManager.Account
        };
        PdaSubmitMessage saveResult = new PdaSubmitMessage();
        try
        {
            saveResult = await OnHandleSave(tranId, linkParam, submitObj, summaryDetails);
            // 更新打条状态,写日志 :TODO 暂时不写日志
            // WriteBarcodeLog(tranId, detailIncludeBarcodes, saveResult, summaryDetails);
            //保存成功
            if (saveResult.ErrCode == 0)
            {
                // 保存配置
                var pdaData = GetPdaData(tranId);
                var config = new PdaBillDataUserConfig();
                var configProperties = config.GetType().GetProperties();
                var billDataProperties = pdaData.GetType().GetProperties();
                foreach (PropertyInfo property in configProperties)
                {
                    var billDataProperty = billDataProperties.FirstOrDefault(r => r.Name == property.Name);
                    if (billDataProperty == null) continue;
                    var billDataValue = billDataProperty.GetValue(pdaData);
                    property.SetValue(config, Convert.ChangeType(billDataValue, property.PropertyType));
                }

                SetUserConfig(config).GetAwaiter().GetResult();

                PdaSaveBillLog saveLog = new PdaSaveBillLog
                {
                    CreateTime = DateTime.Now,
                    UpdateTime = null,
                    CreateUserId = UserManager.UserId,
                    CreateUserName = UserManager.Account,
                    UpdateUserId = null,
                    UpdateUserName = null,
                    IsDelete = false,
                    TranId = tranId,
                    SourceKey = Config.BillSchema.BillLink.SourceKey,
                    TargetKey = Config.BillSchema.BillLink.DestKey,
                    TargetBillId = saveResult.InterId,
                    TargetBillNo = saveResult.BillNo ?? "",
                    Result = saveResult.Message ?? "",
                    Extra1 = paramLog.Extra1,
                    Extra2 = paramLog.Extra2,
                    Extra3 = paramLog.Extra3
                };
                // 日志不能回滚, 用新的CopyNow来保存
                await Rep.Change<PdaSaveBillLog>().CopyNew().InsertAsync(saveLog);

                paramLog.TargetBillId = saveResult.InterId;
                paramLog.TargetBillNo = saveResult.BillNo ?? "";
                paramLog.Result = saveResult.Message ?? "";
                paramLog.IsSuccess = true;
                // 日志不能回滚, 用新的CopyNow来保存
                await paramLogRep.CopyNew().InsertAsync(paramLog);
            }
            //保存失败
            else
            {
                paramLog.IsSuccess = false;
                paramLog.Result = saveResult.Message;
                // 日志不能回滚, 用新的CopyNow来保存
                await paramLogRep.CopyNew().InsertAsync(paramLog);
                throw Oops.Bah(saveResult.Message);
            }

            if (Config.BillParams.IsAutoAudit)
            {
                var checkResult = await HandleAudit(tranId, linkParam, saveResult);
                saveResult.Message = saveResult.Message + ";" + checkResult.Message + L.Text["单据Id:{0}", saveResult.InterId];
                // 如果开始了审核成功才算成功并且审核失败
                if (Config.BillParams.IsAutoAuditSuccess && checkResult.ErrCode != 0)
                {
                    saveResult.ErrCode = checkResult.ErrCode;
                }
                // 如果审核失败,也要记录日志
                if (checkResult.ErrCode != 0)
                {
                    paramLog.IsSuccess = saveResult.ErrCode == 0;
                    paramLog.Result = saveResult.Message;
                    // 日志不能回滚, 用新的CopyNow来保存
                    await paramLogRep.CopyNew().UpdateAsync(paramLog);
                }
            }
        }
        catch (System.Exception ex)
        {
            saveResult.ErrCode = -1;
            saveResult.Message = ex.Message;
            paramLog.IsSuccess = false;
            paramLog.Result = ex.Message;
            // 日志不能回滚, 用新的CopyNow来保存
            await paramLogRep.CopyNew().UpdateAsync(paramLog);
            throw;
        }
        finally
        {
            // 如果保存成功, 删除缓存, 后面审核出错也当成功
            if (saveResult != null && saveResult.ErrCode == 0)
            {
                // 如果保存成功, 提交事务
                uow.Commit();

                var cacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
                //保存成功删除billData
                cacheService.DelBillData(tranId);
                //删除暂存
                cacheService.DeleteTempStore(tranId);
            }
        }

        // 返回结果
        PdaRestfulCode restfulCode = PdaRestfulCode.P803;
        PdaExtrasRestfulResult<string> result = new PdaExtrasRestfulResult<string>
        {
            Code = (int)restfulCode,
            Message = null,
            Data = saveResult?.Message,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            IsSuccess = saveResult is { ErrCode: 0 }
        };
        UnifyContext.Fill(result);

        // uow.Commit();
    }

    /// <summary>
    /// 获取PDA传过来的仓库Id
    /// </summary>
    /// <param name="tranId"></param>
    /// <returns></returns>
    public string GetWarehouseId(long tranId)
    {
        // 查询把仓库维度加上
        var httpContext = App.GetService<IHttpContextAccessor>(ServiceProvider);
        // 在本地调试,是全小写的,服务器是驼峰,以下是为了不区分大小写
        Dictionary<string, string> headers = new(StringComparer.OrdinalIgnoreCase);
        foreach (string key in httpContext.HttpContext.Request.Headers.Keys)
        {
            headers[key] = httpContext.HttpContext.Request.Headers[key];
        }

        var warehouseId = headers.ContainsKey("localwarehouse") ? headers["localwarehouse"] : "";
        return warehouseId;
    }

    protected virtual async Task<string> GetBillType(long tranId)
    {
        var billData = GetPdaData(tranId);
        // 通过单据类型获取目标编码
        // 假设只能有一种单据类型暂取第一个
        var billType = await Rep.Change<StkBillType>().AsQueryable()
            .LeftJoin<StkBillTypeNextEntry>((t1, t2) => t1.Id == t2.Id)
            .Where((t1, t2) => t1.Number == billData.ScanDetails[0].SrcBillKey && t1.EntityName == typeof(TS).Name && t2.NextEntityName == typeof(TT).Name)
            .Select((t1, t2) => t2.NextBillTypeNumber)
            .FirstAsync();
        if (string.IsNullOrEmpty(billType))
            throw Oops.Bah(L.Text["没有找到单据类型配置: 源单类型[{0}], 目标单类型[{1}], 源单单据类型[{2}]", typeof(TS).Name, typeof(TT).Name, billData.ScanDetails[0].SrcBillKey]);
        return billType;
    }

    /// <summary>
    /// 设置提交对象
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="linkParam"></param>
    /// <param name="summaryDetails"></param>
    /// <returns></returns>
    protected virtual TT SetSubmitObj(long tranId, ILocalBillLinkParam linkParam, Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails)
    {
        var pdaData = GetPdaData(tranId);
        // 创建提交表
        var submitObj = Activator.CreateInstance<TT>();
        foreach (LocalBillLinkMapping mapping in linkParam.SubmitMappings.Where(r => r.ScanType == LocalBillLinkMappingScanType.Head).ToList())
        {
            object value = string.IsNullOrEmpty(mapping.ScanFiledName)
                ? mapping.DefaultValue
                :
                // 取表头字段
                IsEmptyValue(pdaData.ScanHead[mapping.ScanFiledName])
                    ? summaryDetails.Count <= 0 ? null : summaryDetails.First().Value.Values[mapping.ScanFiledName]
                    : pdaData.ScanHead[mapping.ScanFiledName];
            SetObjectValue(submitObj, value, mapping.LocalBillFiledName);
        }

        // 获取明细列的值
        var entryProperty = submitObj.GetType().GetProperty(linkParam.TargetEntryName);
        if (entryProperty == null) throw Oops.Bah(L.Text["明细字段[{0}]没找到", linkParam.TargetEntryName]);
        var entries = Activator.CreateInstance(entryProperty.PropertyType);
        entryProperty.SetValue(submitObj, entries);
        // 获取 Add 方法
        MethodInfo addMethod = entryProperty.PropertyType.GetMethod("Add");

        // 插入行号
        int seq = 0;
        foreach (KeyValuePair<string, PdaLocalBillSummaryInfo> pair in summaryDetails)
        {
            seq++;
            // 创建一个 List<>
            var listType = typeof(TTE);
            var listObj = Activator.CreateInstance(listType);

            // 保存行号
            var seqProperty = listType.GetProperty("Seq");
            if (seqProperty != null)
            {
                seqProperty.SetValue(listObj, seq);
            }

            // 保存明细
            foreach (LocalBillLinkMapping mapping in linkParam.SubmitMappings.Where(r => r.ScanType == LocalBillLinkMappingScanType.Detail).ToList())
            {
                object value = string.IsNullOrEmpty(mapping.ScanFiledName)
                    ? mapping.DefaultValue
                    :
                    // 取明细字段,如果明细字段不存在,取表头
                    pair.Value.Values.Contains(mapping.ScanFiledName, true)
                        ? IsEmptyValue(pair.Value.Values[mapping.ScanFiledName]) && !IsEmptyValue(pdaData.ScanHead[mapping.ScanFiledName])
                            ? pdaData.ScanHead[mapping.ScanFiledName]
                            : pair.Value.Values[mapping.ScanFiledName]
                        : pdaData.ScanHead[mapping.ScanFiledName];

                // TODO： 暂时只支持2层
                var fileName = mapping.LocalBillFiledName.Split(".")[^1];
                SetObjectValue(listObj, value, fileName);
            }

            addMethod?.Invoke(entries, new object[] { listObj });

            MethodInfo barcodeAddMethod = null;
            var barcodeProperty = listObj.GetType().GetProperty(linkParam.BarcodeEntryName);
            // 如果单据不需要保存条码 TODO: 处理条码
            if (barcodeProperty == null) continue;
            var barcodeListType = barcodeProperty.PropertyType;
            Type barcodeType = barcodeListType.GetGenericArguments()[0];
            barcodeAddMethod = barcodeProperty.PropertyType.GetMethod("Add");

            // 插入条码
            // 如果有明细条码集合
            object barcodes = null;
            if (barcodeAddMethod != null)
            {
                barcodes = Activator.CreateInstance(barcodeProperty.PropertyType);
                barcodeProperty.SetValue(listObj, barcodes);
            }

            // 保存条码明细
            foreach (PdaLocalBillBarcode pdaBarcode in pair.Value.PdaBarcodes)
            {
                var barcodeObj = Activator.CreateInstance(barcodeType);
                foreach (LocalBillLinkBarcodeMapping mapping in linkParam.BarcodeMappings)
                {
                    object value = string.IsNullOrEmpty(mapping.ScanBarcodeName)
                        ? mapping.DefaultValue
                        :
                        // 取明细字段
                        GetLocalBillValue(mapping.ScanBarcodeName.Split("."), pdaBarcode.CalcBarcode);

                    // TODO： 暂时只支持2层
                    var fileName = mapping.SaveBarcodeName.Split(".")[^1];
                    SetObjectValue(barcodeObj, value, fileName);
                }

                if (barcodeAddMethod != null && barcodes != null) barcodeAddMethod.Invoke(barcodes, new object[] { barcodeObj });
            }
        }

        return submitObj;
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="linkParam"></param>
    /// <param name="submitObj"></param>
    /// <param name="summaryDetails"></param>
    /// <returns></returns>
    protected virtual async Task<PdaSubmitMessage> OnHandleSave(long tranId, ILocalBillLinkParam linkParam, TT submitObj, Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails)
    {
        var service = (IBillService)App.GetService(linkParam.SubmitBillType, ServiceProvider);

        PdaSubmitMessage result = new PdaSubmitMessage();
        try
        {
            var task = service.AddAsync(submitObj);
            var interId = await task!;
            result.ErrCode = 0;
            result.InterId = interId + "";
        }
        catch (System.Exception ex)
        {
            result.ErrCode = -1;
            result.Message = ex.Message;
            throw;
        }

        //保存成功
        if (result.ErrCode == 0)
        {
            // 保存配置
            var pdaData = GetPdaData(tranId);
            var config = new PdaBillDataUserConfig();
            var configProperties = config.GetType().GetProperties();
            var billDataProperties = pdaData.GetType().GetProperties();
            foreach (PropertyInfo property in configProperties)
            {
                var billDataProperty = billDataProperties.FirstOrDefault(r => r.Name == property.Name);
                if (billDataProperty == null) continue;
                var billDataValue = billDataProperty.GetValue(pdaData);
                property.SetValue(config, Convert.ChangeType(billDataValue, property.PropertyType));
            }

            SetUserConfig(config).GetAwaiter().GetResult();

            // 获取单据,取编号
            var getMethod = service.GetType().GetMethod("GetAsync");
            var task = (Task<TT>)getMethod!.Invoke(service, new object[] { new IdInput() { Id = Convert.ToInt64(result.InterId) } });
            var billObj = await task!;
            // 获取单据编号
            var billNoProperty = billObj.GetType().GetProperty("BillNo");
            if (billNoProperty == null)
            {
                result.BillNo = "";
            }
            else
            {
                result.BillNo = billNoProperty.GetValue(billObj) + "";
                result.Message = L.Text["单据编号:[{0}]", result.BillNo];
            }
        }
        //保存失败
        else
        {
            throw Oops.Bah(result.Message);
        }

        return result;
    }

    /// <summary>
    /// 处理审核并写日志
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="linkParam"></param>
    /// <param name="saveResult"></param>
    /// <returns></returns>
    protected virtual async Task<PdaSubmitMessage> HandleAudit(long tranId, ILocalBillLinkParam linkParam, PdaSubmitMessage saveResult)
    {
        var checkResult = new PdaSubmitMessage
        {
            Properties = null,
            ErrCode = 0,
            Message = "",
            InterId = saveResult.InterId,
            BillNo = saveResult.BillNo
        };

        //审核
        try
        {
            var service = (IBillService)App.GetService(linkParam.SubmitBillType, ServiceProvider);
            var task = service.AuditAsync(new IdsInput() { Ids = [Convert.ToInt64(saveResult.InterId)] });
            var result = await task!;
            checkResult.ErrCode = result.Any(r => !r.IsSuccess) ? -1 : 0;
            checkResult.Message = string.Join("\r\n", result.Select(r => r.Message));
            var message = string.Join(",", result.Select(r => r.Message).ToList());
            var auditLogRep = App.GetService<SqlSugarRepository<PdaAuditBillLog>>(ServiceProvider);
            var log = new PdaAuditBillLog
            {
                CreateTime = DateTime.Now,
                UpdateTime = null,
                CreateUserId = UserManager.UserId,
                CreateUserName = UserManager.Account,
                UpdateUserId = null,
                UpdateUserName = null,
                IsDelete = false,
                TranId = tranId,
                TargetKey = Config.BillSchema.BillLink.DestKey,
                AuditBillId = saveResult.InterId,
                Result = message,
                Extra1 = null,
                Extra2 = null,
                Extra3 = null
            };
            await auditLogRep.InsertAsync(log);
        }
        catch (System.Exception ex)
        {
            checkResult.ErrCode = -1;
            checkResult.Message = ex.Message;
        }

        return checkResult;
    }

    /// <summary>
    /// 校验提交数据
    /// </summary>
    /// <param name="tranId"></param>
    protected virtual void VerifySubmit(long tranId)
    {
        var billData = GetPdaData(tranId);
        // 校验单据头
        foreach (PdaColumn column in Config.BillSchema.DestHead)
        {
            // 暂时先只校验必填
            if (column.IsRequired)
            {
                if (string.IsNullOrEmpty(billData.ScanHead[column.Fieldname] + ""))
                    throw Oops.Bah(PdaErrorCode.Pda1036, column.Caption);
            }
        }

        // 检查条码
        foreach (PdaColumn column in Config.BillSchema.Barcode)
        {
            // 暂时先只校验必填
            if (column.IsRequired)
            {
                foreach (PdaLocalBillBarcode barcode in billData.BarcodeList)
                {
                    if (string.IsNullOrEmpty(barcode.CalcBarcode[column.Fieldname] + ""))
                        throw Oops.Bah(PdaErrorCode.Pda1037, column.Caption);
                }
            }
        }
    }

    /// <inheritdoc/>
    protected override Task<dynamic> LookupBdQuery(PdaLocalBillPageInput input)
    {
        var warehouseId = GetWarehouseId(input.TranId);
        if (!string.IsNullOrEmpty(warehouseId))
        {
            input["WarehouseId"] = warehouseId;
        }

        return base.LookupBdQuery(input);
    }

    /// <summary>
    /// 检查能匹配的数量
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="inPutPdaBarcode"></param>
    /// <param name="modifyQty"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public virtual PdaLocalBillMatchingInfo CheckMatchingQty(long tranId, PdaLocalBillBarcode inPutPdaBarcode, decimal? modifyQty = null)
    {
        var billData = GetPdaData(tranId);

        var pdaBarcode = inPutPdaBarcode;
        pdaBarcode.CalcBarcode.ScanQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;

        var info = new PdaLocalBillMatchingInfo();

        //反写仓库(不管源单是否有仓库)
        //如果条码没仓库，以选的仓库为准
        var barcodeHasStock = !(inPutPdaBarcode.Barcode.WhAreaId == null || inPutPdaBarcode.Barcode.WhAreaId == 0);
        if (!barcodeHasStock)
        {
            info.StockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
            pdaBarcode.SetStockInfo(info.StockInfo, Config.BillParams.OpType == BarOpType.Transfer);
        }
        else
        {
            //如果有选仓库，看看仓库和条码的是不是一致，不一致用条码的
            PdaLocalBillStockInfo barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
            if (!$"{billData.StockInfo.WhAreaId}_{billData.StockInfo.WhLocId}".Equals($"{inPutPdaBarcode.Barcode.WhAreaId}_{inPutPdaBarcode.Barcode.WhLocId}"))
            {
                //条码优先
                if (Config.BillParams.FirstStockType == FirstStockType.Barcode)
                {
                    var rep = App.GetService<SqlSugarRepository<BdWhLoc>>(ServiceProvider);
                    // 直接读仓库表
                    var whLocs = rep.AsQueryable()
                        .LeftJoin<BdWhArea>((t1, t2) => t2.Id == t1.WhAreaId)
                        .Where((t1, t2) => t2.Id == inPutPdaBarcode.Barcode.WhAreaId && t1.Id == inPutPdaBarcode.Barcode.WhLocId)
                        .Select((t1, t2) => new
                        {
                            WhAreaId = t2.Id,
                            WhAreaNumber = t2.Number,
                            WhAreaName = t2.Name,
                            WhLocId = t1.Id,
                            WhLocNumber = t1.Number,
                            WhLocName = t1.Name
                        })
                        .ToList();
                    if (whLocs.Count <= 0)
                        throw Oops.Bah(PdaErrorCode.Pda1019, $"{inPutPdaBarcode.Barcode.WhAreaId}_{inPutPdaBarcode.Barcode.WhLocId}");
                    var row = whLocs[0];
                    barcodeStockInfo.WhAreaId = $"{row.WhAreaId}";
                    barcodeStockInfo.WhAreaNumber = $"{row.WhAreaNumber}";
                    barcodeStockInfo.WhAreaName = $"{row.WhAreaName}";
                    barcodeStockInfo.WhLocId = $"{row.WhLocId}";
                    barcodeStockInfo.WhLocNumber = $"{row.WhLocNumber}";
                    barcodeStockInfo.WhLocName = $"{row.WhLocName}";
                    if (string.IsNullOrEmpty(billData.StockInfo.WhAreaId) || billData.StockInfo.WhAreaId.Equals("0"))
                        billData.StockInfo = barcodeStockInfo.Adapt<PdaLocalBillStockInfo>();
                }
                //选择优先
                else if (Config.BillParams.FirstStockType == FirstStockType.Select)
                {
                    barcodeStockInfo = billData.StockInfo.Adapt<PdaLocalBillStockInfo>();
                }
                //不带仓库
                else if (Config.BillParams.FirstStockType == FirstStockType.NoStock)
                {
                    barcodeStockInfo = new PdaLocalBillStockInfo();
                }
            }

            pdaBarcode.SetStockInfo(barcodeStockInfo, Config.BillParams.OpType == BarOpType.Transfer);
            info.StockInfo = barcodeStockInfo;
        }

        //查找匹配的行
        List<string> values = new List<string>();
        Dictionary<string, object> summaryOperationFields = new Dictionary<string, object>();
        foreach (var field in Config.BillParams.SummaryOperationFields)
        {
            var value = PdaLocalBillHelper.GetValue(pdaBarcode.CalcBarcode, field.FieldName);
            values.Add($"{value}");
        }

        var scanDetails = billData.ScanDetails;

        if ((pdaBarcode.Barcode.SrcBillId != null && pdaBarcode.Barcode.SrcBillId != 0 &&
             $"{pdaBarcode.Barcode.SrcBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource) ||
            (pdaBarcode.Barcode.LastExecBillId != null && pdaBarcode.Barcode.LastExecBillId != 0 &&
             $"{pdaBarcode.Barcode.LastExecBillKey}".Equals(Config.BillSchema.BillLink.SourceKey) && Config.BillParams.IsScanBarcodeFindSource))
        {
            var srcBillId = pdaBarcode.Barcode.SrcBillId;
            if (srcBillId is null or 0) srcBillId = pdaBarcode.Barcode.LastExecBillId;
            var billInfo = GetBillInfo(new PdaLocalBillLookSelectInput
            {
                Key = Key,
                TranId = tranId,
                LookupDataKey = null,
                LookupKey = null,
                Id = srcBillId + "",
                DetailIndex = null,
                IsLocalBill = false
            }, false).Result;
            if (billInfo.Item2 is { Count: > 0 })
            {
                var sourceInfo = SetSourceInfo(tranId, [billInfo.Item1], billInfo.Item2, pdaBarcode.Barcode, true);
                if (sourceInfo.Item4 != null) scanDetails.AddRange(sourceInfo.Item4);
            }
        }

        var keyValue = string.Join("|", values);
        var matchingData = scanDetails.Where(r =>
        {
            List<string> rKeyValues = new List<string>();
            foreach (var colname in Config.BillParams.SummaryOperationFields)
            {
                rKeyValues.Add($"{r[colname.ScanFieldName]}");
            }

            return string.Join("|", rKeyValues.ToArray()) == keyValue;
        }).ToList();

        if (matchingData.Count <= 0)
        {
            //不支持源单外物料 报错
            if (!Config.BillParams.IsOverSourceItem)
            {
                return info;
            }

            //如果支持源单外物料 (无源单)
            //把条码的值，写到Detail
            var detail = inPutPdaBarcode.Barcode.Adapt<PdaLocalBillScanDetail>();
            // 循环BdBarcode属性,如果是类,添加Number,Name
            var bdBarProperties = inPutPdaBarcode.Barcode.GetType().GetProperties();
            foreach (PropertyInfo bdBarProperty in bdBarProperties)
            {
                // 如果继承基础资料
                if (!bdBarProperty.PropertyType.IsSubclassOf(typeof(BdEntityBase))) continue;
                if (bdBarProperty.GetValue(inPutPdaBarcode.Barcode) is not BdEntityBase obj) continue;

                detail[$"{bdBarProperty.Name}Number"] = obj.Number;
                detail[$"{bdBarProperty.Name}Name"] = obj.Name;
            }

            // billData.ScanDetails.Add(detail);
            // 忝加明细主键Key值
            detail.DetailId = $"{YitIdHelper.NextId()}";
            detail.IsNew = true;

            // SetDetailValue(inPutPdaBarcode.Barcode, detail);
            matchingData.Add(detail);
        }

        var diffQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;

        //查找到匹配的明细，再用QTY判断是否超
        foreach (var data in matchingData)
        {
            if (diffQty <= 0) break;
            var qty = data.Qty - data.ScanQty;
            if (qty <= 0) continue;
            info.Infos.Add(new PdaLocalBillMatchingDetailInfo(data, Math.Min(diffQty, qty)));
            diffQty = diffQty - qty;
        }

        // 如果还有剩余数量, 并且允许超源单数量
        if (diffQty > 0 && Config.BillParams.IsOverSourceQty)
        {
            // 如果没有匹配行
            if (info.Infos.Count == 0)
            {
                info.Infos.Add(new PdaLocalBillMatchingDetailInfo(matchingData[^1], diffQty));
            }
            else
            {
                // 如果能超源单数量, 把剩余数量加到最后一行匹配行上
                info.Infos[^1].Qty += diffQty;
            }

            diffQty = 0;
        }

        if (diffQty > 0)
        {
            pdaBarcode.CalcBarcode.ScanQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty - diffQty;
            return info;
        }

        pdaBarcode.CalcBarcode.ScanQty = modifyQty ?? inPutPdaBarcode.Barcode.Qty;
        return info;
    }
}

/// <summary>
/// 条码匹配信息
/// </summary>
public class PdaLocalBillMatchingDetailInfo
{
    public PdaLocalBillMatchingDetailInfo(PdaLocalBillScanDetail detail, decimal qty)
    {
        Detail = detail;
        Qty = qty;
    }

    /// <summary>
    /// 匹配到的明细行
    /// </summary>
    public PdaLocalBillScanDetail Detail { get; set; }

    /// <summary>
    /// 匹配的数量
    /// </summary>
    public decimal Qty { get; set; }
}

/// <summary>
/// 匹配项
/// </summary>
public class PdaLocalBillMatchingInfo
{
    /// <summary>
    /// 匹配的仓库仓位
    /// </summary>
    public PdaLocalBillStockInfo StockInfo { get; set; }

    /// <summary>
    /// 条码匹配信息
    /// </summary>
    public List<PdaLocalBillMatchingDetailInfo> Infos { get; set; } = new List<PdaLocalBillMatchingDetailInfo>();
}

/// <summary>
/// 修改数量类
/// </summary>
public class ModifyAuxQtyInfo
{
    public long MaterialId { get; set; }
    public long UnitId { get; set; }
    public long AuxUnitId { get; set; }
    public string Field { get; set; }
    public decimal Qty { get; set; }
}