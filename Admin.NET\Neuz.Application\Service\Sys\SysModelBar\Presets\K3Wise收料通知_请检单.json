{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "t6.FBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t2.F<PERSON><PERSON>ber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t2.F<PERSON>ame", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t6.FDate", "title": "单据日期", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "t12.F<PERSON><PERSON>ber", "title": "供应商编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t12.FName", "title": "供应商名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "t1.FSourceBillNo", "title": "源单编号", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "日期", "sortable": true, "format": "Date"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "FSourceBillNo", "title": "源单编码"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "<PERSON><PERSON><PERSON>", "title": "已制作数量"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "SupplierNumber", "title": "供应商编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "SupplierName", "title": "供应商名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillDate", "title": "单据日期", "sortable": true, "format": "Date"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Wise", "idFieldName": "t1.FInterID", "entryIdFieldName": "t1.FEntryID", "content": "SELECT\r\n    t1.FInterID AS _id,\r\n    t1.FEntryID AS _entryId,\r\n    t6.FTranType AS SourceBillKey,\r\n    t1.FInterID AS SourceBillId,\r\n    t1.FEntryID AS SourceBillEntryId,\r\n    t6.FBillNo AS SourceBillNo,\r\n    t6.FDate AS SourceBillDate,\r\n    t1.FItemID AS MaterialId,\r\n    t2.FNumber AS MaterialNumber,\r\n    t2.FName AS MaterialName,\r\n    t2.FShortNumber AS MaterialShortNumber,\r\n    t2.FModel AS MaterialSpec,\r\n    t1.FAuxQty AS Qty,\r\n    t1.FUnitID AS UnitId,\r\n    t3.FNumber AS UnitNumber,\r\n    t3.FName AS UnitName,\r\n    t1.FBatchNo AS BatchNo,\r\n    t2.FBatchManager AS IsBatchManage,\r\n    t2.FIsSnManage AS IsSnManage,\r\n    t2.FISKFPeriod AS IsKfPeriod,\r\n    t1.FKFPeriod AS ExpPeriod,\r\n    t1.FKFDate AS ProduceDate,\r\n    t1.FPeriodDate AS ExpiryDate,\r\n    t6.FSupplyID AS SupplierId,\r\n    t12.FNumber AS SupplierNumber,\r\n    t12.FName AS SupplierName,\r\n    t1.FStockID AS StockId,\r\n    t14.FNumber AS StockNumber,\r\n    t14.FName AS StockName,\r\n    t1.FDCSPID AS StockLocId,\r\n    t4.FNumber AS StockLocNumber,\r\n    t4.FName AS StockLocName,\r\n    t1.FAuxPropID AS AuxPropId,\r\n    t5.FNumber AS AuxPropNumber,\r\n    t5.FName AS AuxPropName,\r\n    t1.FSourceBillNo\r\n    -- t1.FQty,\r\n    -- t1.FNote,\r\n    -- t2.FKFPeriod AS FItemKfPeriod,\r\n    -- t2.FAuxClassID AS FItemAuxClassID,\r\n    -- t2.FUnitGroupID AS FItemUnitGroupID,\r\n    -- t3.FCoefficient AS FUnitCoefficient,\r\n    -- t3.FUnitGroupID,\r\n    -- t5.FItemClassID AS FAuxPropClassID,\r\n    -- t6.FExplanation,\r\n    -- t6.FDeptID,\r\n    -- t6.FEmpID,\r\n    -- t6.FFManagerID,\r\n    -- t6.FRelateBrID,\r\n    -- t6.FBillerID,\r\n    -- t6.FCheckerID,\r\n    -- t6.FCheckDate,\r\n    -- t6.FStatus AS FBillStatus,\r\n    -- t6.FCancellation,\r\n    -- t7.FName AS FBillerName,\r\n    -- t8.FNumber AS FRelateBrNumber,\r\n    -- t8.FName AS FRelateBrName,\r\n    -- t9.FName AS FCheckerName,\r\n    -- t10.FNumber AS FFManagerNumber,\r\n    -- t10.FName AS FFManagerName,\r\n    -- t11.FNumber AS FDeptNumber,\r\n    -- t11.FName AS FDeptName,\r\n    -- t13.FNumber AS FEmpNumber,\r\n    -- t13.FName AS FEmpName,\r\nFROM\r\n    POInStockEntry t1\r\n    LEFT JOIN t_ICItem t2 ON (t2.FItemID = t1.FItemID)\r\n    LEFT JOIN t_MeasureUnit t3 ON (t3.FMeasureUnitID = t1.FUnitID)\r\n    LEFT JOIN t_StockPlace t4 ON (t4.FSPID = t1.FDCSPID)\r\n    LEFT JOIN t_AuxItem t5 ON (\r\n        t5.FItemID = t1.FAuxPropID\r\n        AND t5.FItemClassID = t2.FAuxClassID\r\n    )\r\n    LEFT JOIN POInStock t6 ON (t6.FInterID = t1.FInterID)\r\n    LEFT JOIN t_User t7 ON (t7.FUserID = t6.FBillerID)\r\n    LEFT JOIN t_SonCompany t8 ON (t8.FItemID = t6.FRelateBrID)\r\n    LEFT JOIN t_User t9 ON (t9.FUserID = t6.FCheckerID)\r\n    LEFT JOIN t_Emp t10 ON (t10.FItemID = t6.FFManagerID)\r\n    LEFT JOIN t_Department t11 ON (t11.FItemID = t6.FDeptID)\r\n    LEFT JOIN t_Supplier t12 ON (t12.FItemID = t6.FSupplyID)\r\n    LEFT JOIN t_Emp t13 ON (t13.FItemID = t6.FEmpID)\r\n    LEFT JOIN t_Stock t14 ON (t14.FItemID = t1.FStockID)\r\nWHERE\r\n    (t6.FTranType = 72)"}}