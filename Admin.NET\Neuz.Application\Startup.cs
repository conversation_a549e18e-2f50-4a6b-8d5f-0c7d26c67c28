﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.SeedData;

namespace Neuz.Application;

public class Startup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 自定义数据种子
        services.AddCustomSeedData();

        // Stk自定义数据种子
        services.AddStkCustomSeedData();

        // 设置表单数据大小限制，Stimulsoft 报表导入时，报表内容作为 FormValue 项传入
        services.Configure<FormOptions>(options =>
        {
            options.ValueLengthLimit = 50 * 1024 * 1024; // 50 MB
        });
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // 设置 StiReport
        app.UseStiReportSetting();

        // 注册 Harmony 拦截（for stimulsoft patch）
        app.UseHarmony();

        // 注册Pda Model,Data
        app.UsePdaProxy();

        // 初始化类型名称映射
        SqlSugarExtension.InitEntityTypeNames();
    }
}