﻿namespace Neuz.Core.Entity;

/// <summary>
/// 容器
/// </summary>
[SugarTable(null, "容器")]
[SugarIndex("index_{table}_N", nameof(Number), OrderByType.Asc)]
public class BdContainer : EntityTenant
{
    /// <summary>
    /// 容器编码
    /// </summary>
    [SugarColumn(ColumnDescription = "容器编码", Length = 200)]
    public string Number { get; set; }

    /// <summary>
    /// 容器状态
    /// </summary>
    [SugarColumn(ColumnDescription = "容器状态")]
    public BdContainerStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 255)]
    public string? Memo { get; set; }

    /// <summary>
    /// 父容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父容器Id")]
    public long? ParentContainerId { get; set; }

    /// <summary>
    /// 父容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ParentContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer ParentContainer { get; set; }

    /// <summary>
    /// 父容器Ids
    /// </summary>
    /// <remarks>
    /// 描述整个父容器关系链
    /// </remarks>
    [SugarColumn(ColumnDescription = "父容器Ids", Length = 2000)]
    public string? ParentContainerIds { get; set; }

    /// <summary>
    /// 最后打印时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印时间")]
    public DateTime? LastPrintTime { get; set; }

    /// <summary>
    /// 已打印数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已打印数量", DefaultValue = "0")]
    public int PrintedQty { get; set; }

    /// <summary>
    /// 最后打印者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者Id")]
    public long? LastPrintUserId { get; set; }

    /// <summary>
    /// 最后打印者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者名称", Length = 20)]
    public string? LastPrintUserName { get; set; }

    /// <summary>
    /// 作废时间
    /// </summary>
    [SugarColumn(ColumnDescription = "作废时间")]
    public DateTime? DisuseTime { get; set; }

    /// <summary>
    /// 作废者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者Id")]
    public long? DisuseUserId { get; set; }

    /// <summary>
    /// 作废者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "作废者名称", Length = 20)]
    public string? DisuseUserName { get; set; }

    /// <summary>
    /// 构建事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "构建事务Id", DefaultValue = "0")]
    public long BuildTranId { get; set; }

    /// <summary>
    /// 容器内条码
    /// </summary>
    /// <remarks>
    /// 不设置导航属性，调用者自行处理
    /// </remarks>
    [SugarColumn(IsIgnore = true)]
    public IList<BdBarcode> Barcodes { get; set; }
}