﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存锁定事务日志
/// </summary>
[SugarTable(null, "库存锁定事务日志")]
[SugarIndex("index_{table}_BKBIG", nameof(RelBillKey), OrderByType.Asc, nameof(RelBillId), OrderByType.Asc, nameof(GroupName), OrderByType.Asc)]
[SugarIndex("index_{table}_BNG", nameof(RelBillNo), OrderByType.Asc, nameof(GroupName), OrderByType.Asc)]
[SugarIndex("index_{table}_TI", nameof(RelTaskId), OrderByType.Asc)]
public class StkInventoryLockLog : EntityTenant
{
    /// <summary>
    /// 锁定事务类型
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定事务类型")]
    public StkInvLockLogType InvLockLogType { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    /// <remarks>
    /// （增加正数，扣减负数）
    /// </remarks>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库位Id")]
    public long WhLocId { get; set; }

    /// <summary>
    /// 库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhLocId))]
    [CustomSerializeFields]
    public BdWhLoc WhLoc { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer Container { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 关联单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "关联单据标识", Length = 200)]
    public string? RelBillKey { get; set; }

    /// <summary>
    /// 关联单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据编号", Length = 80)]
    public string? RelBillNo { get; set; }

    /// <summary>
    /// 关联单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据类型", Length = 80)]
    public string? RelBillType { get; set; }

    /// <summary>
    /// 关联序号
    /// </summary>
    /// <remarks>
    /// （单据分录的序号）
    /// </remarks>
    [SugarColumn(ColumnDescription = "关联序号")]
    public int? RelSeq { get; set; }

    /// <summary>
    /// 关联单据主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据主键Id")]
    public long? RelBillId { get; set; }

    /// <summary>
    /// 关联单据分录主键Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据分录主键Id")]
    public long? RelBillEntryId { get; set; }

    /// <summary>
    /// 关联源单单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "关联源单单据编号", Length = 80)]
    public string? RelSourceBillNo { get; set; }

    /// <summary>
    /// 关联任务Id
    /// </summary>
    /// <remarks>
    /// 分配时产生的日志需要记录任务Id，用于任务的取消分配操作
    /// </remarks>
    [SugarColumn(ColumnDescription = "关联任务Id")]
    public long? RelTaskId { get; set; }

    /// <summary>
    /// 分组名称
    /// </summary>
    /// <remarks>
    /// 给定一个名称，描述同一关联单据同一个操作的日志集合，用于同一关联单据的不同操作的日志回滚
    /// </remarks>
    [SugarColumn(ColumnDescription = "分组名称", Length = 50)]
    public string? GroupName { get; set; }
}