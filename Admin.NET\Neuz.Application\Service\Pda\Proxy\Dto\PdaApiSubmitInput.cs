﻿namespace Neuz.Application.Pda.Proxy.Dto;

public class PdaApiSubmitInput
{
    /// <summary>
    /// Model的Key
    /// </summary>
    public string Key { get; set; }
    /// <summary>
    /// 事务Id
    /// </summary>
    public long TranId { get; set; }
    /// <summary>
    /// 提交Key(可能会有多种提交方式,用Key区分,如果没有,不传)
    /// </summary>
    public string SubmitKey { get; set; }
    /// <summary>
    /// 是否确认提交(比如校验库存不足,是否前端确认需要提交)
    /// </summary>
    public bool IsRepeat { get; set; }
    /// <summary>
    /// 提交附加数据 
    /// </summary>
    public object SubmitData { get; set; }
}