﻿using Furion.Localization;
using Neuz.Application.Pda.Barcode.Dto;
using Neuz.Application.Pda.Barcode;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.PackageEx.Dto;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Application.Service.Pda.SplitBarcode.Dto;
using Stimulsoft.Report.Export;
using Stimulsoft.Report;
using static Neuz.Application.Pda.Package.Data.PdaPackageData;
using Neuz.Application.Service.Contract;

namespace Neuz.Application.Pda.PackageEx;

/// <summary>
/// 装箱模型(为旧版装箱扩展)
/// </summary>
public class PdaPackageExModel : PdaModelBillBase<PdaPackageExShow, PdaPackageExData>
{
    private BarPackageService PackageService => App.GetService<BarPackageService>(ServiceProvider);
    private BarBarcodeService BarcodeService => App.GetService<BarBarcodeService>(ServiceProvider);
    private SqlSugarRepository<BarBarcode> BarcodeRep => App.GetService<SqlSugarRepository<BarBarcode>>(ServiceProvider);

    public PdaPackageExModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = "PackageEx";
    public override IPdaSchema BillSchema { get; } = new PdaPackageExSchema();

    public override void Initialization()
    {
        var pdaSchemaEx = (PdaPackageExSchema)BillSchema;
        // 装箱规则
        var codeRules = BarcodeRep.Change<SysCodeRule>().GetList(u => u.EntityName == "BarPackage" && u.IsForbid == false);
        var codeRuleCell = pdaSchemaEx.BillCells.Find(r => r.Fieldname == "codeRule");
        codeRuleCell.Options.Clear();
        codeRules.ForEach(r => { codeRuleCell.Options.Add(new PdaColumnOption(r.Id + "", r.Name)); });

        // 打印模板
        var templates = BarcodeRep.Change<SysReportTemplate>().GetList(u => u.FuncKey == "barPackage");
        var templateCell = pdaSchemaEx.BillCells.Find(r => r.Fieldname == "template");
        templateCell.Options.Clear();
        templates.ForEach(r => { templateCell.Options.Add(new PdaColumnOption(r.Id + "", r.Name)); });
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.IsNew = pdaData.IsNew;
        pdaShow.Status = pdaData.Status == PdaPackageExStatus.In ? "1" : pdaData.Status == PdaPackageExStatus.Out ? "2" : "0";
        pdaShow.PackageNo = pdaData.Package?.PackageBarcode;
        pdaShow.Barcodes.Clear();
        pdaData.Barcodes.ForEach(r =>
        {
            pdaShow.Barcodes.Add(new PdaPackageDataBarcodeInfo
            {
                BarcodeId = r.Id,
                Title = r.Barcode,
                Label = r.MaterialNumber,
                Value = r.MaterialName,
                Qty = r.Qty
            });
        });
        pdaShow.BillCells.Clear();
        foreach (KeyValuePair<string, string> billCell in pdaData.BillCells)
        {
            pdaShow.BillCells.Add(billCell.Key, billCell.Value);
        }
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
        var pdaDataEx = GetPdaData(pdaData.TranId);
        // 读配置
        var config = GetUserConfig<PdaPackageExModelUserConfig>().Result;
        if (config != null)
        {
            pdaDataEx.IsNew = config.Data.IsNew;
            pdaDataEx.Status = config.Data.Status;
            pdaDataEx.BillCells.Clear();
            foreach (KeyValuePair<string, string> billCell in config.Data.BillCells)
            {
                pdaDataEx.BillCells.Add(billCell.Key, billCell.Value);
            }
        }
        RefreshShow(pdaData.TranId);
    }

    /// <summary>
    /// 改变装/拆箱状态
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private Task SelectFieldDataStatus(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        switch (fieldValue)
        {
            case "1":
                pdaData.Status = PdaPackageExStatus.In;
                break;
            case "2":
                pdaData.Status = PdaPackageExStatus.Out;
                break;
            default:
                throw new NotImplementedException();
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 改变是否新箱状态
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private Task SelectFieldDataIsNew(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        if (pdaData.Barcodes.Count > 0) throw Oops.Bah(PdaErrorCode.Pda1039);
        if (pdaData.Package != null) throw Oops.Bah(PdaErrorCode.Pda1042);
        if (!bool.TryParse(fieldValue, out bool isNew)) throw Oops.Bah(PdaErrorCode.Pda1038);
        pdaData.IsNew = isNew;
        return Task.CompletedTask;
    }

    /// <summary>
    /// 删除条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private Task SelectFieldDataDelBarcode(long tranId, string fieldValue)
    {
        var pdaData = GetPdaData(tranId);
        var barcodeId = long.Parse(fieldValue);
        var bar = pdaData.Barcodes.FirstOrDefault(r => r.Id == barcodeId);
        if (bar == null) throw Oops.Bah(BarErrorCode.BarBarcode1008);
        pdaData.Barcodes.Remove(bar);
        return Task.CompletedTask;
    }

    public override async Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        var pdaData = GetPdaData(tranId);
        if (!pdaData.IsNew && pdaData.Package == null)
        {
            // 如果不是生成新箱并且没有扫描箱码, 先扫箱码
            //扫箱
            var package = await PackageService.GetPackageByPackageBarcode(new GetPackageByPackageBarcodeInput() { PackageBarcode = barcode });
            if (package == null) throw Oops.Bah(BarErrorCode.BarPackage1005, barcode);
            pdaData.Package = package;
            //带出已装箱条码
            var barcodes = await BarcodeRep.GetListAsync(r => r.ParentPackageId == package.Id && r.Status != BarcodeStatus.Disuse && !r.IsDelete);
            foreach (var barBarcode in barcodes)
                pdaData.Barcodes.Add(barBarcode);
        }
        else
        {
            if (pdaData.Status == PdaPackageExStatus.In)
            {
                // 扫描条码
                var barBarcode = await BarcodeService.GetBarcodeAsync(barcode);
                if (barBarcode == null) throw Oops.Bah(BarErrorCode.BarBarcode1005, barcode);
                //判断条码是否存在
                var isExist = pdaData.Barcodes.Exists(r => r.Id == barBarcode.Id);
                if (isExist) throw Oops.Bah(BarErrorCode.BarBarcode1006);
                //判断条码是否已装在其它箱
                if (barBarcode.ParentPackageId != null && barBarcode.ParentPackageId != 0)
                {
                    var package = await PackageService.GetAsync(new IdInput() { Id = barBarcode.ParentPackageId });
                    throw Oops.Bah(BarErrorCode.BarBarcode1007, package.PackageBarcode);
                }

                pdaData.Barcodes.Add(barBarcode);
            }
            else if (pdaData.Status == PdaPackageExStatus.Out)
            {
                var bar = pdaData.Barcodes.FirstOrDefault(r => r.Barcode == barcode);
                if (bar == null) throw Oops.Bah(BarErrorCode.BarBarcode1005, barcode);
                await SelectFieldDataDelBarcode(tranId, bar.Id + "");
            }
        }

        RefreshShow(tranId);
    }

    public override Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        throw new NotImplementedException();
    }

    public override Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        switch (fieldKey)
        {
            case "Status":
                await SelectFieldDataStatus(tranId, fieldValue);
                break;
            case "IsNew":
                await SelectFieldDataIsNew(tranId, fieldValue);
                break;
            case "DelBarcode":
                await SelectFieldDataDelBarcode(tranId, fieldValue);
                break;
            case "codeRule":
            case "template":
                var pdaData = GetPdaData(tranId);
                pdaData.BillCells[fieldKey] = fieldValue;
                break;
        }

        RefreshShow(tranId);
    }

    public override Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        throw new NotImplementedException();
    }

    public override async Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        var pdaData = GetPdaData(tranId);
        var packageService = App.GetService<BarPackageService>(ServiceProvider);
        // 如果不是创建新箱码,并且没有扫描箱码
        if (!pdaData.IsNew && pdaData.Package == null) throw Oops.Bah(PdaErrorCode.Pda1040);
        // 开启事务，如有外部事务，内部事务用外部事务
        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());
        // 如果生成箱码
        if (pdaData.Package == null && pdaData.IsNew)
        {
            var codeRuleId = GetDictionaryValue("codeRule", pdaData.BillCells);
            if (codeRuleId == "") throw Oops.Bah(PdaErrorCode.Pda1041);
            var buildTranId = await PackageService.BuildEmptyAsync(new BuildEmptyInput
            {
                CodeRuleId = long.Parse(codeRuleId),
                PackageCount = 1
            });
            // 只会生成一个箱
            var package = await Rep.Change<BarPackage>().GetFirstAsync(r => r.BuildTranId == buildTranId);
            pdaData.Package = package;
        }

        try
        {
            if (pdaData.Package == null) throw Oops.Bah(PdaErrorCode.Pda1040);

            await packageService.BindBarcodeAsync(new BarPackageBindBarcodeInput
            {
                BarcodeIds = pdaData.Barcodes.Select(r => r.Id).ToList(),
                PackageId = pdaData.Package.Id
            });
            pdaData.IsSubmit = true;

            uow.Commit();
        }
        catch (System.Exception ex)
        {
            if (pdaData.IsNew) pdaData.Package = null;
            throw;
        }
        finally
        {
            RefreshShow(tranId);
        }


        // 写配置
        await SetUserConfig(new PdaPackageExModelUserConfig() { Data = pdaData });
    }

    private string GetDictionaryValue(string key, Dictionary<string, string> dictionary)
    {
        if (!dictionary.TryGetValue(key, out string value)) return "";
        return value + "";
    }

    public override async Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        if (submitKey != "print") return new List<string>();
        var pdaData = GetPdaData(tranId);
        var service = App.GetService<SysReportTemplateService>(ServiceProvider);
        var templateId = GetDictionaryValue("template", pdaData.BillCells);
        if (string.IsNullOrEmpty(templateId)) throw Oops.Bah(L.Text["请先选择模板"]);

        // 这里打印有三种方式,
        // 1. 蓝牙打印 
        // 2. 服务器端打印
        // 3. 打印服务器打印
        var printInfo = JsonConvert.DeserializeObject<PdaPrintInfo>(submitData + "");
        var report = await service.GetReport(Convert.ToInt64(templateId), typeof(BarPackageService).AssemblyQualifiedName, new List<string>() { pdaData.Package.Id + "" });
        switch (printInfo.PrintType)
        {
            case PdaPrintType.Ble:
                return await PrintBle(tranId, report, printInfo);
            case PdaPrintType.Server:
                await PrintService(tranId, report, printInfo);
                break;
            case PdaPrintType.PrintService:
                break;
            default:
                throw new NotImplementedException();
        }

        return L.Text["打印成功"];
    }

    /// <summary>
    /// 蓝牙打印
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="report"></param>
    /// <param name="printInfo"></param>
    /// <returns></returns>
    private async Task<object> PrintBle(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        await report.RenderAsync();
        List<string> base64Strings = new();
        StiPngExportService stiPngExportService = new();
        for (int i = 0; i < report.RenderedPages.Count; i++)
        {
            var ms = new MemoryStream();
            //report.ExportDocument(StiExportFormat.ImagePng, ms, new StiEmfExportSettings() { PageRange = new StiPagesRange(i + 1) });
            StiImageExportSettings settings = new StiPngExportSettings
            {
                //加了下面这个就会报错,应该是用了默认格式保存
                //settings.ImageType = StiImageType.Png;
                PageRange = new StiPagesRange(i + 1),
                ImageResolution = printInfo.Dpi ?? 0
            };
            stiPngExportService.ExportImage(report, ms, settings);
            ms.Seek(0, SeekOrigin.Begin);

            //服务器端处理返回的图像指令
            var resultBytes = PdaHelper.GetBitmapData(ms.ToArray());
            var base64String = Convert.ToBase64String(resultBytes);
            base64Strings.Add(base64String);
        }

        return base64Strings;
    }


    /// <summary>
    /// 服务器端打印
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="report"></param>
    /// <param name="printInfo"></param>
    /// <returns></returns>
    private Task PrintService(long tranId, StiReport report, PdaPrintInfo printInfo)
    {
        //打印
        var printService = App.GetService<PdaServerPrintService>(ServiceProvider);
        printService.Prints(new List<PdaReportPrintInfo>()
        {
            new()
            {
                Report = report,
                PrintName = printInfo.PrintName,
                Count = 1
            }
        });

        return Task.CompletedTask;
    }

    public class PdaPackageExSchema : IPdaSchema
    {
        public List<PdaColumn> BillCells { get; } = new List<PdaColumn>
        {
            new PdaColumn()
            {
                Fieldname = "codeRule",
                Caption = L.Text["箱码生成规则"],
                Type = "select",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                Options = new List<PdaColumnOption>(),
                FieldDataKey = "codeRule"
            },
            new PdaColumn()
            {
                Fieldname = "template",
                Caption = L.Text["选择打印模板"],
                Type = "select",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                Options = new List<PdaColumnOption>(),
                FieldDataKey = "template"
            }
        };
    }

    public class PdaPackageExModelUserConfig : IModelUserConfig
    {
        public PdaPackageExData Data = new PdaPackageExData();
    }
}