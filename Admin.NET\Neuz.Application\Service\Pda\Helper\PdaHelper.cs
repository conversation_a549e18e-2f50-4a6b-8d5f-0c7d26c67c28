﻿using Neuz.Application.Pda.Contract;
using System.Drawing;
using System.Drawing.Imaging;
using Image = System.Drawing.Image;
using Rectangle = System.Drawing.Rectangle;

namespace Neuz.Application.Pda.Helper;

public class PdaHelper
{
    /// <summary>
    /// 显示精度
    /// </summary>
    public static string DecimalPrecision { get; set; } = "0.###";

    public static T SetValue<T>(T t, string propertyName, object value)
    {
        var property = typeof(T).GetProperty(propertyName);
        if (property == null) throw Oops.Bah(PdaErrorCode.Pda1008, propertyName);
        property.SetValue(t, Convert.ChangeType(value, property.PropertyType));
        return t;
    }

    public static object GetValue<T>(T t, string propertyName)
    {
        if (t is ExtensionObject extensionObject)
        {
            return extensionObject[propertyName];
        }

        var property = typeof(T).GetProperty(propertyName);
        if (property == null) throw Oops.Bah(PdaErrorCode.Pda1008, propertyName);
        return property.GetValue(t);
    }

    /// <summary>
    /// 判断是否相等
    /// </summary>
    /// <param name="a"></param>
    /// <param name="b"></param>
    /// <returns></returns>
    public static bool IsEquals(object a, object b)
    {
        var ax = $"{a}";
        var bx = $"{b}";
        return ax.Equals(bx, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 对象转换为字典
    /// </summary>
    /// <param name="obj">待转化的对象</param>
    /// <param name="isIgnoreNull">是否忽略NULL 这里我不需要转化NULL的值，正常使用可以不穿参数 默认全转换</param>
    /// <returns></returns>
    public static Dictionary<string, object> ObjectToMap(object obj, bool isIgnoreNull = false)
    {
        Dictionary<string, object> map = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

        Type t = obj.GetType(); // 获取对象对应的类， 对应的类型

        PropertyInfo[] pi = t.GetProperties(BindingFlags.Public | BindingFlags.Instance); // 获取当前type公共属性

        foreach (PropertyInfo p in pi)
        {
            MethodInfo m = p.GetGetMethod();

            if (m != null && m.IsPublic)
            {
                // 进行判NULL处理 
                if (m.Invoke(obj, new object[] { }) != null || !isIgnoreNull)
                {
                    map.Add(p.Name, m.Invoke(obj, new object[] { })); // 向字典添加元素
                }
            }
        }

        return map;
    }

    /// <summary>
    /// 返回二值化图片
    /// </summary>
    /// <param name="bitmapBytes"></param>
    /// <returns></returns>
    public static byte[] GetBitmapData(byte[] bitmapBytes)
    {
        var graphBuffer = bitmapBytes;
        MemoryStream memoryStream = new MemoryStream();
        MemoryStream memoryStream2 = new MemoryStream();
        Bitmap bitmap = null;
        Bitmap bitmap2 = null;
        try
        {
            memoryStream = new MemoryStream(graphBuffer);
            bitmap = Image.FromStream(memoryStream) as Bitmap;
            memoryStream.ToArray();
            var graphWidth = bitmap.Width;
            var graphHeight = bitmap.Height;
            bitmap2 = bitmap.Clone(new Rectangle(0, 0, bitmap.Width, bitmap.Height), PixelFormat.Format1bppIndexed);
            bitmap2.Save(memoryStream2, ImageFormat.Bmp);
            byte[] array2 = memoryStream2.ToArray();
            return array2;
        }
        finally
        {
            memoryStream.Dispose();
            memoryStream2.Dispose();
            if (bitmap != null)
            {
                bitmap.Dispose();
                bitmap = null;
            }

            if (bitmap2 != null)
            {
                bitmap2.Dispose();
                bitmap2 = null;
            }
        }
    }

    #region 保留部分算法,如果后面二值化需要优化,作为参考
    ///// <summary>
    ///// 二值化操作 迭代法 (效率很差)
    ///// </summary>
    ///// <param name="bmp"></param>
    ///// <returns></returns>
    //public static Bitmap ConvertTo2Bpp2(Bitmap bmp)
    //{
    //    int[] histogram = new int[256];
    //    int minGrayValue = 255, maxGrayValue = 0;
    //    //求取直方图
    //    for (int i = 0; i < bmp.Width; i++)
    //    {
    //        for (int j = 0; j < bmp.Height; j++)
    //        {
    //            Color pixelColor = bmp.GetPixel(i, j);
    //            histogram[pixelColor.R]++;
    //            if (pixelColor.R > maxGrayValue) maxGrayValue = pixelColor.R;
    //            if (pixelColor.R < minGrayValue) minGrayValue = pixelColor.R;
    //        }
    //    }

    //    //迭代计算阀值
    //    int threshold = -1;
    //    int newThreshold = (minGrayValue + maxGrayValue) / 2;
    //    for (int iterationTimes = 0; threshold != newThreshold && iterationTimes < 100; iterationTimes++)
    //    {
    //        threshold = newThreshold;
    //        int lP1 = 0;
    //        int lP2 = 0;
    //        int lS1 = 0;
    //        int lS2 = 0;
    //        //求两个区域的灰度的平均值
    //        for (int i = minGrayValue; i < threshold; i++)
    //        {
    //            lP1 += histogram[i] * i;
    //            lS1 += histogram[i];
    //        }

    //        int mean1GrayValue = (lP1 / lS1);
    //        for (int i = threshold + 1; i < maxGrayValue; i++)
    //        {
    //            lP2 += histogram[i] * i;
    //            lS2 += histogram[i];
    //        }

    //        int mean2GrayValue = (lP2 / lS2);
    //        newThreshold = (mean1GrayValue + mean2GrayValue) / 2;
    //    }

    //    //计算二值化
    //    for (int i = 0; i < bmp.Width; i++)
    //    {
    //        for (int j = 0; j < bmp.Height; j++)
    //        {
    //            Color pixelColor = bmp.GetPixel(i, j);
    //            if (pixelColor.R > threshold) bmp.SetPixel(i, j, Color.FromArgb(255, 255, 255));
    //            else bmp.SetPixel(i, j, Color.FromArgb(0, 0, 0));
    //        }
    //    }

    //    return bmp;
    //}

    ///// <summary>
    ///// 二值化otsu (效率一般)
    ///// </summary>
    ///// <param name="bitmap"></param>
    ///// <returns></returns>
    //public static byte[] OtsuThreshold(Bitmap bitmap)
    //{
    //    // 图像灰度化   
    //    // b = Gray(b);   
    //    int width = bitmap.Width;
    //    int height = bitmap.Height;
    //    byte threshold = 0;
    //    int[] hist = new int[256];

    //    int AllPixelNumber = 0, PixelNumberSmall = 0, PixelNumberBig = 0;
    //    double MaxValue, AllSum = 0, SumSmall = 0, SumBig, ProbabilitySmall, ProbabilityBig, Probability;
    //    BitmapData bmpData = bitmap.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, PixelFormat.Format32bppArgb);
    //    unsafe
    //    {
    //        byte* p = (byte*)bmpData.Scan0;
    //        int offset = bmpData.Stride - width * 4;
    //        for (int j = 0; j < height; j++)
    //        {
    //            for (int i = 0; i < width; i++)
    //            {
    //                hist[p[0]]++;
    //                p += 4;
    //            }

    //            p += offset;
    //        }

    //        bitmap.UnlockBits(bmpData);
    //    }

    //    //计算灰度为I的像素出现的概率   
    //    for (int i = 0; i < 256; i++)
    //    {
    //        AllSum += i * hist[i]; //   质量矩   
    //        AllPixelNumber += hist[i]; //  质量       
    //    }

    //    MaxValue = -1.0;
    //    for (int i = 0; i < 256; i++)
    //    {
    //        PixelNumberSmall += hist[i];
    //        PixelNumberBig = AllPixelNumber - PixelNumberSmall;
    //        if (PixelNumberBig == 0)
    //        {
    //            break;
    //        }

    //        SumSmall += i * hist[i];
    //        SumBig = AllSum - SumSmall;
    //        ProbabilitySmall = SumSmall / PixelNumberSmall;
    //        ProbabilityBig = SumBig / PixelNumberBig;
    //        Probability = PixelNumberSmall * ProbabilitySmall * ProbabilitySmall + PixelNumberBig * ProbabilityBig * ProbabilityBig;
    //        if (Probability > MaxValue)
    //        {
    //            MaxValue = Probability;
    //            threshold = (byte)i;
    //        }
    //    }

    //    Threshoding(bitmap, threshold);
    //    return ToBit(bitmap);

    //    //bitmap = twoBit(bitmap);
    //    //return bitmap;
    //}

    //public static byte[] ToBit(Bitmap srcBitmap)
    //{
    //    int midrgb = Color.FromArgb(128, 128, 128).ToArgb();
    //    int stride; //简单公式((width/8)+3)&(~3)
    //    stride = (srcBitmap.Width % 8) == 0 ? (srcBitmap.Width / 8) : (srcBitmap.Width / 8) + 1;
    //    stride = (stride % 4) == 0 ? stride : ((stride / 4) + 1) * 4;
    //    int k = srcBitmap.Height * stride;
    //    byte[] buf = new byte[k];
    //    int x = 0, ab = 0;
    //    for (int j = 0; j < srcBitmap.Height; j++)
    //    {
    //        k = j * stride; //因图像宽度不同、有的可能有填充字节需要跳越
    //        x = 0;
    //        ab = 0;
    //        for (int i = 0; i < srcBitmap.Width; i++)
    //        {
    //            //从灰度变单色（下法如果直接从彩色变单色效果不太好，不过反相也可以在这里控制）
    //            if ((srcBitmap.GetPixel(i, j)).ToArgb() > midrgb)
    //            {
    //                ab = ab * 2 + 1;
    //            }
    //            else
    //            {
    //                ab = ab * 2;
    //            }

    //            x++;
    //            if (x == 8)
    //            {
    //                buf[k++] = (byte)ab; //每字节赋值一次，数组buf中存储的是十进制。
    //                ab = 0;
    //                x = 0;
    //            }
    //        }

    //        if (x > 0)
    //        {
    //            //循环实现：剩余有效数据不满1字节的情况下须把它们移往字节的高位部分
    //            for (int t = x; t < 8; t++) ab = ab * 2;
    //            buf[k++] = (byte)ab;
    //        }
    //    }

    //    int width = srcBitmap.Width;
    //    int height = srcBitmap.Height;
    //    Bitmap dstBitmap = new Bitmap(width, height, PixelFormat.Format1bppIndexed);
    //    BitmapData dt = dstBitmap.LockBits(new Rectangle(0, 0, dstBitmap.Width, dstBitmap.Height), ImageLockMode.ReadWrite, dstBitmap.PixelFormat);
    //    Marshal.Copy(buf, 0, dt.Scan0, buf.Length);
    //    dstBitmap.UnlockBits(dt);
    //    return BitmapToByte(dstBitmap);
    //}

    //public static Bitmap twoBit(Bitmap srcBitmap)
    //{
    //    int midrgb = Color.FromArgb(128, 128, 128).ToArgb();
    //    int stride; //简单公式((width/8)+3)&(~3)
    //    stride = (srcBitmap.Width % 8) == 0 ? (srcBitmap.Width / 8) : (srcBitmap.Width / 8) + 1;
    //    stride = (stride % 4) == 0 ? stride : ((stride / 4) + 1) * 4;
    //    int k = srcBitmap.Height * stride;
    //    byte[] buf = new byte[k];
    //    int x = 0, ab = 0;
    //    for (int j = 0; j < srcBitmap.Height; j++)
    //    {
    //        k = j * stride; //因图像宽度不同、有的可能有填充字节需要跳越
    //        x = 0;
    //        ab = 0;
    //        for (int i = 0; i < srcBitmap.Width; i++)
    //        {
    //            //从灰度变单色（下法如果直接从彩色变单色效果不太好，不过反相也可以在这里控制）
    //            if ((srcBitmap.GetPixel(i, j)).ToArgb() > midrgb)
    //            {
    //                ab = ab * 2 + 1;
    //            }
    //            else
    //            {
    //                ab = ab * 2;
    //            }

    //            x++;
    //            if (x == 8)
    //            {
    //                buf[k++] = (byte)ab; //每字节赋值一次，数组buf中存储的是十进制。
    //                ab = 0;
    //                x = 0;
    //            }
    //        }

    //        if (x > 0)
    //        {
    //            //循环实现：剩余有效数据不满1字节的情况下须把它们移往字节的高位部分
    //            for (int t = x; t < 8; t++) ab = ab * 2;
    //            buf[k++] = (byte)ab;
    //        }
    //    }

    //    int width = srcBitmap.Width;
    //    int height = srcBitmap.Height;
    //    Bitmap dstBitmap = new Bitmap(width, height, PixelFormat.Format1bppIndexed);
    //    BitmapData dt = dstBitmap.LockBits(new Rectangle(0, 0, dstBitmap.Width, dstBitmap.Height), ImageLockMode.ReadWrite, dstBitmap.PixelFormat);
    //    Marshal.Copy(buf, 0, dt.Scan0, buf.Length);
    //    dstBitmap.UnlockBits(dt);
    //    return dstBitmap;
    //}

    //public static Bitmap Threshoding(Bitmap b, byte threshold)
    //{
    //    int width = b.Width;
    //    int height = b.Height;
    //    BitmapData data = b.LockBits(new Rectangle(0, 0, width, height), ImageLockMode.ReadWrite, PixelFormat.Format32bppArgb);
    //    unsafe
    //    {
    //        byte* p = (byte*)data.Scan0;
    //        int offset = data.Stride - width * 4;
    //        byte R, G, B, gray;
    //        for (int y = 0; y < height; y++)
    //        {
    //            for (int x = 0; x < width; x++)
    //            {
    //                R = p[2];
    //                G = p[1];
    //                B = p[0];
    //                gray = (byte)((R * 19595 + G * 38469 + B * 7472) >> 16);
    //                if (gray >= threshold)
    //                {
    //                    p[0] = p[1] = p[2] = 255;
    //                }
    //                else
    //                {
    //                    p[0] = p[1] = p[2] = 0;
    //                }

    //                p += 4;
    //            }

    //            p += offset;
    //        }

    //        b.UnlockBits(data);
    //        return b;

    //    }

    //}

    ///// <summary>
    ///// 将BitMap转换成bytes数组
    ///// </summary>
    ///// <param name="bitmap">要转换的图像</param>
    ///// <returns></returns>
    //private static byte[] BitmapToByte(System.Drawing.Bitmap bitmap)
    //{
    //    // 1.先将BitMap转成内存流
    //    System.IO.MemoryStream ms = new System.IO.MemoryStream();
    //    bitmap.Save(ms, System.Drawing.Imaging.ImageFormat.Bmp);
    //    ms.Seek(0, System.IO.SeekOrigin.Begin);
    //    // 2.再将内存流转成byte[]并返回
    //    byte[] bytes = new byte[ms.Length];
    //    ms.Read(bytes, 0, bytes.Length);
    //    ms.Dispose();
    //    return bytes;
    //}
    #endregion
}