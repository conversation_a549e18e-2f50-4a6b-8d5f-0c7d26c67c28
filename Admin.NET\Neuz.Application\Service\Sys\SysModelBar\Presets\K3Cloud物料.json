{"$schema": "http://barModelSchema.json", "modelServiceName": "K3CloudBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "FNumber", "title": "编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FName", "title": "名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FSpecification", "title": "规格型号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FIsBatchManage", "title": "启用批号管理", "inputCtrl": "Select", "op": "Equals", "options": [{"value": 0, "title": "否"}, {"value": 1, "title": "是"}]}], "billListColumns": [{"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "IsKfPeriod", "title": "启用保质期管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "_ExpUnit_", "title": "保质期单位"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Cloud", "idFieldName": "FMATERIALID", "entryIdFieldName": "", "content": "{\r\n    \"$schema\": \"http://k3CloudDataQuerySchema.json\",\r\n    \"formId\": \"BD_MATERIAL\",\r\n    \"fieldKeys\": [\r\n        \"FMATERIALID AS _id\",\r\n        \"FMATERIALID AS MaterialId\",\r\n        \"FNumber AS MaterialNumber\",\r\n        \"FName AS MaterialName\",\r\n        \"FSpecification AS MaterialSpec\",\r\n        \"1 AS Qty\",\r\n        \"FStoreUnitID AS UnitId\",\r\n        \"FStoreUnitID.FNumber AS UnitNumber\",\r\n        \"FStoreUnitID.FName AS UnitName\",\r\n        \"FIsBatchManage AS IsBatchManage\",\r\n        \"FIsSNManage AS IsSnManage\",\r\n        \"FIsKFPeriod AS IsKfPeriod\",\r\n        \"FExpPeriod AS ExpPeriod\",\r\n        \"FExpUnit AS _ExpUnit_\"\r\n    ],\r\n    \"filters\": [\r\n        {\r\n            \"fieldKey\": \"FMATERIALID\",\r\n            \"op\": \"Equals\",\r\n            \"value\": \"#FMasterId\"\r\n        }\r\n    ],\r\n    \"customFilter\": \"\"\r\n}"}}