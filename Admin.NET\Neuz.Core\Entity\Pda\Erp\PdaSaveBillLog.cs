﻿namespace Neuz.Core.Entity.Pda.Erp;

/// <summary>
/// Pda保存单据日志
/// </summary>
[SugarTable(null, "Pda保存单据日志")]
[SugarIndex("index_{table}_T", nameof(TranId), OrderByType.Asc)]
public class PdaSaveBillLog : EntityTenant
{
    /// <summary>
    /// Pda提交事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Pda保存事务Id")]
    public long TranId { get; set; }

    /// <summary>
    /// 源单Key
    /// </summary>
    [SugarColumn(ColumnDescription = "源单Key", Length = 50)]
    public string? SourceKey { get; set; }

    /// <summary>
    /// 目标单Key
    /// </summary>
    [SugarColumn(ColumnDescription = "目标单Key", Length = 50)]
    public string? TargetKey { get; set; }

    /// <summary>
    /// 保存单据内码
    /// </summary>
    [SugarColumn(ColumnDescription = "保存单据内码", Length = 500)]
    public string? TargetBillId { get; set; }

    /// <summary>
    /// 保存单据编码
    /// </summary>
    [SugarColumn(ColumnDescription = "保存单据编码", Length = 500)]
    public string? TargetBillNo { get; set; }

    /// <summary>
    /// 返回结果
    /// </summary>
    [SugarColumn(ColumnDescription = "返回结果", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Result { get; set; }

    /// <summary>
    /// 附加信息1 Wise-红蓝单 Cloud-转换规则Id
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息1", Length = 255)]
    public string? Extra1 { get; set; }

    /// <summary>
    /// 附加信息2
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息2", Length = 255)]
    public string? Extra2 { get; set; }

    /// <summary>
    /// 附加信息3
    /// </summary>
    [SugarColumn(ColumnDescription = "附加信息3", Length = 255)]
    public string? Extra3 { get; set; }
}