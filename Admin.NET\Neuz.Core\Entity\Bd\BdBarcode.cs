﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案
/// </summary>
[SugarTable(null, "条码档案")]
[SugarIndex("index_{table}_B", nameof(Barcode), OrderByType.Asc)]
[SugarIndex("index_{table}_F", nameof(FuncKey), OrderByType.Asc)]
[SugarIndex("index_{table}_B2", nameof(BuildTranId), OrderByType.Asc)]
[SugarIndex("index_{table}_C", nameof(ContainerId), OrderByType.Asc)]
public class BdBarcode : EntityTenant
{
    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 100)]
    public string? FuncKey { get; set; }

    /// <summary>
    /// 来源单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源单据标识", Length = 200)]
    public string? SrcBillKey { get; set; }

    /// <summary>
    /// 来源单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据编号", Length = 80)]
    public string? SrcBillNo { get; set; }

    /// <summary>
    /// 来源单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据类型", Length = 80)]
    public string? SrcBillType { get; set; }

    /// <summary>
    /// 来源单据分录序号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录序号")]
    public int? SrcBillEntrySeq { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id")]
    public long? SrcBillId { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id")]
    public long? SrcBillEntryId { get; set; }

    /// <summary>
    /// 条码状态
    /// </summary>
    [SugarColumn(ColumnDescription = "条码状态")]
    public BdBarcodeStatus Status { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer Container { get; set; }

    /// <summary>
    /// 父容器Ids
    /// </summary>
    /// <remarks>
    /// 与 <see cref="BdContainer"/> 的 ParentPackageId 存放的数据结构一样，描述整个父容器关系链
    /// </remarks>
    [SugarColumn(ColumnDescription = "父容器Ids", Length = 2000)]
    public string? ParentContainerIds { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long? WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long? WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库位Id")]
    public long? WhLocId { get; set; }

    /// <summary>
    /// 库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhLocId))]
    [CustomSerializeFields]
    public BdWhLoc WhLoc { get; set; }

    /// <summary>
    /// 最后打印时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印时间")]
    public DateTime? LastPrintTime { get; set; }

    /// <summary>
    /// 已打印数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已打印数量")]
    public int PrintedQty { get; set; }

    /// <summary>
    /// 最后打印者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者Id")]
    public long? LastPrintUserId { get; set; }

    /// <summary>
    /// 最后打印者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "最后打印者名称", Length = 20)]
    public string? LastPrintUserName { get; set; }

    /// <summary>
    /// 构建事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "构建事务Id")]
    public long BuildTranId { get; set; }

    /// <summary>
    /// 最后执行单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "最后执行单据标识", Length = 200)]
    public string? LastExecBillKey { get; set; }

    /// <summary>
    /// 最后执行单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "最后执行单据编号", Length = 80)]
    public string? LastExecBillNo { get; set; }

    /// <summary>
    /// 最后执行单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "最后执行单据类型", Length = 80)]
    public string? LastExecBillType { get; set; }

    /// <summary>
    /// 最后执行单据分录序号
    /// </summary>
    [SugarColumn(ColumnDescription = "最后执行单据分录序号")]
    public int? LastExecBillEntrySeq { get; set; }

    /// <summary>
    /// 最后执行单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后执行单据Id")]
    public long? LastExecBillId { get; set; }

    /// <summary>
    /// 最后执行单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "最后执行单据分录Id")]
    public long? LastExecBillEntryId { get; set; }

    /// <summary>
    /// 使用的编码规则
    /// </summary>
    [SugarColumn(ColumnDescription = "使用的编码规则", Length = 2000)]
    public string? UsedCodeRule { get; set; }

    /// <summary>
    /// 辅助数量
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助数量", DefaultValue = "0")]
    public decimal AuxQty { get; set; }

    /// <summary>
    /// 辅助单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助单位Id")]
    public long? AuxUnitId { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxUnitId))]
    [CustomSerializeFields]
    public BdUnit AuxUnit { get; set; }
}