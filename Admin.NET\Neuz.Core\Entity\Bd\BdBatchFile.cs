﻿namespace Neuz.Core.Entity;

/// <summary>
/// 批号档案
/// </summary>
[SugarTable(null, "批号档案")]
[SugarIndex("index_{table}_TBM", nameof(TenantId), OrderByType.Asc, nameof(BatchNo), OrderByType.Asc, nameof(MaterialId), OrderByType.Asc, IsUnique = true)]
public class BdBatchFile : EntityTenant
{
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    [SugarColumn(ColumnDescription = "供应商Id")]
    public long? SupplierId { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
    [CustomSerializeFields]
    public BdSupplier Supplier { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注")]
    public string? Memo { get; set; }

    /// <summary>
    /// 批号状态
    /// </summary>
    [SugarColumn(ColumnDescription = "批号状态")]
    public BdBatchStatus BatchStatus { get; set; }
}