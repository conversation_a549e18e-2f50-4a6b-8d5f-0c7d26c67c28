﻿namespace Neuz.Application.Pda.Contract;

/// <summary>
/// 扩展实体类
/// </summary>
[Serializable]
public class ExtensionObject : DynamicObject, IDictionary<string, object>
{
    object _instance;

    Type _instanceType;
    PropertyInfo[] _cacheInstancePropertyInfos;

    IEnumerable<PropertyInfo> _instancePropertyInfos
    {
        get
        {
            if (_cacheInstancePropertyInfos == null && _instance != null)
            {
                var properties = new List<PropertyInfo>(8);
                var instanceProperties = _instance.GetType()
                    .GetProperties(BindingFlags.Instance | BindingFlags.Public).ToList();
                //移除索引参数类型的属性
                properties.AddRange(instanceProperties
                    .Where(instanceProperty => instanceProperty.GetIndexParameters().Length == 0));
                _cacheInstancePropertyInfos = properties.ToArray();
            }

            return _cacheInstancePropertyInfos;
        }
    }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    /// 
    /// </summary>
    public ExtensionObject()
    {
        Initialize(this);
    }

    /// <remarks>
    /// You can pass in null here if you don't want to 
    /// check native properties and only check the Dictionary!
    /// </remarks>
    /// <param name="instance"></param>
    public ExtensionObject(object instance)
    {
        Initialize(instance);
    }


    private void Initialize(object instance)
    {
        _instance = instance;
        if (instance != null)
            _instanceType = instance.GetType();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public override IEnumerable<string> GetDynamicMemberNames()
    {
        return GetDynamicMemberNames(true);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="includeInstanceMemberName"></param>
    /// <returns></returns>
    public IEnumerable<string> GetDynamicMemberNames(bool includeInstanceMemberName)
    {
        return GetProperties(includeInstanceMemberName).Select(prop => prop.Key);
    }

    /// <param name="binder"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public override bool TryGetMember(GetMemberBinder binder, out object result)
    {
        result = null;

        if (Properties.Keys.Contains(binder.Name))
        {
            result = Properties[binder.Name];
            return true;
        }

        if (_instance != null)
        {
            try
            {
                return GetProperty(_instance, binder.Name, out result);
            }
            catch (System.Exception)
            {
            }
        }

        return false;
    }

    /// <param name="binder"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public override bool TrySetMember(SetMemberBinder binder, object value)
    {
        if (_instance != null)
        {
            try
            {
                bool result = SetProperty(_instance, binder.Name, value);
                if (result)
                    return true;
            }
            catch
            {
            }
        }

        Properties[binder.Name] = value;
        return true;
    }

    /// <param name="binder"></param>
    /// <param name="args"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    public override bool TryInvokeMember(InvokeMemberBinder binder, object[] args, out object result)
    {
        if (_instance != null)
        {
            try
            {
                // check instance passed in for methods to invoke
                if (InvokeMethod(_instance, binder.Name, args, out result))
                    return true;
            }
            catch (System.Exception)
            {
            }
        }

        result = null;
        return false;
    }

    /// <param name="instance"></param>
    /// <param name="name"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    private bool GetProperty(object instance, string name, out object result)
    {
        if (instance == null)
            instance = this;

        var miArray = _instanceType.GetMember(name, BindingFlags.Public | BindingFlags.GetProperty | BindingFlags.Instance | BindingFlags.IgnoreCase);
        if (miArray.Length > 0)
        {
            var mi = miArray[0];
            if (mi.MemberType == MemberTypes.Property)
            {
                result = ((PropertyInfo)mi).GetValue(instance, null);
                return true;
            }
        }

        result = null;
        return false;
    }

    /// <param name="instance"></param>
    /// <param name="name"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    private bool SetProperty(object instance, string name, object value)
    {
        if (instance == null)
            instance = _instance;

        var miArray = _instanceType.GetMember(name, BindingFlags.Public | BindingFlags.SetProperty | BindingFlags.Instance | BindingFlags.IgnoreCase);
        if (miArray.Length > 0)
        {
            var pi = miArray[0] as PropertyInfo;
            if (pi != null)
            {
                if (pi.PropertyType == typeof(string))
                {
                    if (value != null) value += "";
                    pi.SetValue(instance, value, null);
                }
                else if (value == null || string.IsNullOrEmpty(value.ToString()))
                {
                    pi.SetValue(instance, null, null);
                }
                else if (pi.PropertyType.IsGenericType)
                {
                    var ptype = pi.PropertyType.GetGenericArguments()[0];
                    if (ptype == typeof(DateTime))
                    {
                        pi.SetValue(instance, DateTime.Parse(value + ""));
                    }
                    else
                    {
                        if (typeof(IConvertible).IsAssignableFrom(ptype))
                        {
                            //如果是空的数组(序列化的时候)
                            if (value is JArray)
                            {
                                var value2 = JsonConvert.DeserializeObject(value + "", pi.PropertyType);
                                pi.SetValue(instance, Convert.ChangeType(value2, pi.PropertyType), null);
                            }
                            else
                            {
                                pi.SetValue(instance, Convert.ChangeType(value, ptype), null);
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    if (pi.PropertyType == typeof(DateTime))
                    {
                        pi.SetValue(instance, DateTime.Parse(value + ""));
                    }
                    else
                    {
                        if (pi.PropertyType.IsEnum)
                        {
                            var t = System.Enum.ToObject(pi.PropertyType, 0);
                            pi.SetValue(instance, Convert.ChangeType(t, pi.PropertyType), null);
                        }
                        else
                        {
                            // 这个转换BdMaterial失败了
                            // var obj = Convert.ChangeType(value, pi.PropertyType);
                            var obj = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(value), pi.PropertyType);
                            pi.SetValue(instance, obj, null);
                        }
                    }
                }

                return true;
            }
        }

        return false;
    }

    /// <param name="instance"></param>
    /// <param name="name"></param>
    /// <param name="args"></param>
    /// <param name="result"></param>
    /// <returns></returns>
    private bool InvokeMethod(object instance, string name, object[] args, out object result)
    {
        if (instance == null)
            instance = this;

        // Look at the instanceType
        var miArray = _instanceType.GetMember(name,
            BindingFlags.InvokeMethod |
            BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);

        if (miArray != null && miArray.Length > 0)
        {
            var mi = miArray[0] as MethodInfo;
            result = mi.Invoke(_instance, args);
            return true;
        }

        result = null;
        return false;
    }

    /// <summary>
    /// 索引器
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public object this[string key]
    {
        get
        {
            if (Properties.ContainsKey(key))
            {
                return Properties[key];
            }

            GetProperty(_instance, key, out var result);
            return result;
        }
        set
        {
            if (Properties.ContainsKey(key))
            {
                Properties[key] = value;
                return;
            }

            var miArray = _instanceType.GetMember(key, BindingFlags.Public | BindingFlags.GetProperty | BindingFlags.Instance | BindingFlags.IgnoreCase);
            if (miArray.Length > 0)
                SetProperty(_instance, key, value);
            else
                Properties[key] = value;
        }
    }

    /// <param name="includeInstanceProperties">是否包含对象实例属性</param>
    /// <returns></returns>
    public IEnumerable<KeyValuePair<string, object>> GetProperties(bool includeInstanceProperties = false)
    {
        if (includeInstanceProperties && _instance != null)
        {
            foreach (var prop in _instancePropertyInfos)
                yield return new KeyValuePair<string, object>(prop.Name, prop.GetValue(_instance, null));
        }

        if (this.Properties == null) yield break;

        foreach (var key in this.Properties.Keys)
            yield return new KeyValuePair<string, object>(key, this.Properties[key]);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public IEnumerable<KeyValuePair<string, object>> GetInstanceProperties()
    {
        if (_instance != null)
        {
            foreach (var prop in _instancePropertyInfos)
                yield return new KeyValuePair<string, object>(prop.Name, prop.GetValue(_instance, null));
        }
    }

    /// <param name="item"></param>
    /// <param name="includeInstanceProperties">是否包含对象实例属性</param>
    /// <returns></returns>
    public bool Contains(KeyValuePair<string, object> item, bool includeInstanceProperties = false)
    {
        bool res = Properties.ContainsKey(item.Key);
        if (res)
            return true;

        if (includeInstanceProperties && _instance != null)
        {
            foreach (var prop in _instancePropertyInfos)
            {
                if (prop.Name == item.Key)
                    return true;
            }
        }

        return false;
    }

    /// <param name="key"></param>
    /// <param name="includeInstanceProperties">是否包含对象实例属性</param>
    /// <returns></returns>
    public bool Contains(string key, bool includeInstanceProperties = false)
    {
        bool res = Properties.ContainsKey(key);
        if (res)
            return true;

        if (includeInstanceProperties && _instance != null)
        {
            foreach (var prop in this._instancePropertyInfos)
            {
                if (prop.Name == key)
                    return true;
            }
        }

        return false;
    }

    void IDictionary<string, object>.Add(string key, object value)
    {
        Properties.Add(key, value);
    }

    bool IDictionary<string, object>.ContainsKey(string key)
    {
        return this.Contains(key, true);
    }

    ICollection<string> IDictionary<string, object>.Keys => (ICollection<string>)GetDynamicMemberNames();

    bool IDictionary<string, object>.Remove(string key)
    {
        return Properties.Remove(key);
    }

    bool IDictionary<string, object>.TryGetValue(string key, out object value)
    {
        value = null;
        var list = GetProperties(true);
        foreach (var valuePair in list)
        {
            if (valuePair.Key == key)
            {
                value = valuePair.Value;
                return true;
            }
        }

        return false;
    }

    ICollection<object> IDictionary<string, object>.Values
    {
        get { return (ICollection<object>)GetProperties(true).Select(c => c.Value); }
    }

    object IDictionary<string, object>.this[string key]
    {
        get => this[key];
        set => this[key] = value;
    }

    void ICollection<KeyValuePair<string, object>>.Add(KeyValuePair<string, object> item)
    {
        ((ICollection<KeyValuePair<string, object>>)Properties).Add(item);
    }

    void ICollection<KeyValuePair<string, object>>.Clear()
    {
        ((ICollection<KeyValuePair<string, object>>)Properties).Clear();
    }

    bool ICollection<KeyValuePair<string, object>>.Contains(KeyValuePair<string, object> item)
    {
        return ((ICollection<KeyValuePair<string, object>>)GetProperties(true)).Contains(item);
    }

    void ICollection<KeyValuePair<string, object>>.CopyTo(KeyValuePair<string, object>[] array, int arrayIndex)
    {
        ((ICollection<KeyValuePair<string, object>>)Properties).CopyTo(array, arrayIndex);
    }

    int ICollection<KeyValuePair<string, object>>.Count
    {
        get
        {
            var properties = GetProperties(true);
            if (properties is ICollection<KeyValuePair<string, object>>)
                return ((ICollection<KeyValuePair<string, object>>)GetProperties(true)).Count;
            return 0;
        }
    }

    bool ICollection<KeyValuePair<string, object>>.IsReadOnly
    {
        get
        {
            var properties = GetProperties(true);
            if (properties is ICollection<KeyValuePair<string, object>>)
                return ((ICollection<KeyValuePair<string, object>>)GetProperties(true)).IsReadOnly;
            return false;
        }
    }

    bool ICollection<KeyValuePair<string, object>>.Remove(KeyValuePair<string, object> item)
    {
        return ((ICollection<KeyValuePair<string, object>>)Properties).Remove(item);
    }

    IEnumerator<KeyValuePair<string, object>> IEnumerable<KeyValuePair<string, object>>.GetEnumerator()
    {
        return GetProperties(true).GetEnumerator();
    }

    System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
    {
        return ((System.Collections.IEnumerable)GetProperties(true)).GetEnumerator();
    }
}