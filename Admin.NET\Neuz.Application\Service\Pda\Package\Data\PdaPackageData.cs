﻿using Neuz.Application.Pda.Base;

namespace Neuz.Application.Pda.Package.Data;

/// <summary>
/// 装箱数据
/// </summary>
public class PdaPackageData : IPdaData
{
    /// <inheritdoc />
    public string ModelKey { get; set; }
    /// <inheritdoc />
    public long TranId { get; set; }
    /// <inheritdoc />
    public long UserId { get; set; }

    /// <inheritdoc />
    public PdaShow DataShow { get; set; } = new PdaDataShow();

    /// <summary>
    /// 箱号
    /// </summary>
    public BarPackage Package { get; set; }

    /// <summary>
    /// 已装条码号
    /// </summary>
    public List<BarBarcode> Barcodes { get; } = new List<BarBarcode>();

    /// <inheritdoc />
    public bool IsEmptyData()
    {
        return Barcodes.Count <= 0;
    }

    public class PdaDataShow : PdaShow
    {
        /// <summary>
        /// 箱号
        /// </summary>
        public string PackageNo { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public List<PdaPackageDataBarcodeInfo> Barcodes { get; } = new List<PdaPackageDataBarcodeInfo>();
    }

    /// <summary>
    /// 装箱条码信息
    /// </summary>
    public class PdaPackageDataBarcodeInfo
    {
        /// <summary>
        /// 条码Id
        /// </summary>
        public long BarcodeId { get; set; }
        /// <summary>
        /// 标题(左上 大号字)
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 副标题(左下 小号字)
        /// </summary>
        public string Label { get; set; }
        /// <summary>
        /// 值(右)
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 条码数量
        /// </summary>
        public decimal Qty { get; set; }
    }
}