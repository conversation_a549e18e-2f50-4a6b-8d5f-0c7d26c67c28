﻿using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.LocalBill.LocalBillDto;

/// <summary>
/// 全局分页查询输入参数
/// </summary>
public class PdaBasePageInput : ExtensionObject
{
    /// <summary>
    /// 当前页码
    /// </summary>
    [DataValidation(ValidationTypes.Numeric)]
    public virtual int Page { get; set; } = 1;

    /// <summary>
    /// 页码容量
    /// </summary>
    //[Range(0, 100, ErrorMessage = "页码容量超过最大限制")]
    [DataValidation(ValidationTypes.Numeric)]
    public virtual int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    public virtual string Field { get; set; }

    /// <summary>
    /// 排序方向
    /// </summary>
    public virtual string Order { get; set; }

    /// <summary>
    /// 降序排序
    /// </summary>
    public virtual string DescStr { get; set; } = "descending";
}

/// <summary>
/// PDA 能过单号获取源单
/// </summary>
public class PdaLocalBillGetBillForBillNoInput : ExtensionObject
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; } = "";

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    public string BillNo { get; set; }
}

/// <summary>
/// PDA LocalBill的Lookup查询
/// </summary>
public class PdaLocalBillPageInput : PdaBasePageInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; } = "";

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// LookupKey
    /// </summary>
    public string LookupKey { get; set; } = "";

    /// <summary>
    /// LookupDataKey
    /// </summary>
    public string LookupDataKey { get; set; } = "";

    /// <summary>
    /// 关键字
    /// </summary>
    public string Keyword { get; set; } = "";

    /// <summary>
    /// 编码
    /// </summary>
    public string Number { get; set; } = "";

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = "";

    /// <summary>
    /// 是否单据
    /// </summary>
    public bool IsLocalBill { get; set; } = false;
}

/// <summary>
/// PDA LocalBill的Lookup选择
/// </summary>
public class PdaLocalBillLookSelectInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string LookupDataKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string LookupKey { get; set; }

    /// <summary>
    /// 返回的Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 如果要保存的是列表,需要传DetailId,没有值就当是实体,不是列表
    /// </summary>
    public int? DetailIndex { get; set; }

    /// <summary>
    /// 是否单据
    /// </summary>
    public bool IsLocalBill { get; set; } = false;
}

/// <summary>
/// PDA LocalBill的Input,Select,Date,Number等,确认后输入参数
/// </summary>
public class PdaLocalBillSelectDataInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string FieldKey { get; set; }

    /// <summary>
    /// 返回的值
    /// </summary>
    public string FieldValue { get; set; }

    /// <summary>
    /// 如果要保存的是列表,需要传DetailId,没有值就当是实体,不是列表
    /// </summary>
    public int? DetailIndex { get; set; }
}

/// <summary>
/// PDA LocalBill的Input,Select,Date,Number等,确认后输入参数
/// </summary>
public class PdaLocalBillListAddInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// List的Key值,为了方便,暂时用PdaElColumn第一列的Key的.分隔后第一个值作为搜索
    /// </summary>
    public string ListKey { get; set; }

    /// <summary>
    /// 暂留,以后可能用作其它特殊判断
    /// </summary>
    public string CustomKey { get; set; }

    /// <summary>
    /// 如果要保存的是列表,需要传DetailId,没有值就当是实体,不是列表
    /// </summary>
    public int? DetailIndex { get; set; }
}

/// <summary>
/// PDA LocalBill的Input,Select,Date,Number等,确认后输入参数
/// </summary>
public class PdaLocalBillListDeleteInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// List的Key值,为了方便,暂时用PdaElColumn第一列的Key的.分隔后第一个值作为搜索
    /// </summary>
    public string ListKey { get; set; }

    /// <summary>
    /// 暂留,以后可能用作其它特殊判断
    /// </summary>
    public string CustomKey { get; set; }

    /// <summary>
    /// 如果要保存的是列表,需要传DetailId,没有值就当是实体,不是列表
    /// </summary>
    public int? DetailIndex { get; set; }
}

/// <summary>
/// Pda查询输入参数
/// </summary>
public class PdaIdInput
{
    /// <summary>
    /// Id主键
    /// </summary>
    [Required(ErrorMessage = "Id主键不能为空")]
    public string Id { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// Key
    /// </summary>
    public string Key { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string LookupDataKey { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string LookupKey { get; set; }

    /// <summary>
    /// 如果要保存的是列表,需要传DetailId,没有值就当是实体,不是列表
    /// </summary>
    public int? DetailIndex { get; set; }
}

/// <summary>
/// PDA扫描的条码入参
/// </summary>
public class PdaScanBarcodeInput
{
    /// <summary>
    /// ModelKey
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// TranId
    /// </summary>
    public long TranId { get; set; }

    /// <summary>
    /// 扫描条码值
    /// </summary>
    public string Barcode { get; set; }

    /// <summary>
    /// 是否确认扫描
    /// </summary>
    public bool IsRepeat { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public ExtensionObject Ext { get; set; }
}