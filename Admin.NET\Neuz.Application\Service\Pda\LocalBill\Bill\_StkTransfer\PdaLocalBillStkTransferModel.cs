﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.LocalBill.LocalBillDto;

namespace Neuz.Application.Pda.LocalBill.Bill._StkTransfer;

/// <summary>
/// 无源调拨单
/// </summary>
public class PdaLocalBillStkTransferModel : PdaLocalBillModel<StkTask, StkTransfer, StkTaskEntry, StkTransferEntry>
{
    public PdaLocalBillStkTransferModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "_StkTransfer";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destBillTypeName",
                Caption = L.Text["单据类型"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdBillTypeModel",
                    LookupDataKey = "ScanHead.DestBillType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestBillType", "Number"),
                        new PdaLookupMapping("DestBillTypeName", "Name"),
                    },
                    Properties = { ["EntityName"] = "StkTransfer" }
                },
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destWhAreaLoc",
                Caption = L.Text["调入库区库位"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdWhAreaLocModel",
                    LookupDataKey = "ScanHead.DestWhAreaLoc",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestWhAreaId", "WhAreaId"),
                        new PdaLookupMapping("DestWhAreaNumber", "WhAreaNumber"),
                        new PdaLookupMapping("DestWhAreaName", "WhAreaName"),
                        new PdaLookupMapping("DestWhLocId", "WhLocId"),
                        new PdaLookupMapping("DestWhLocNumber", "WhLocNumber"),
                        new PdaLookupMapping("DestWhLocName", "WhLocName"),
                        new PdaLookupMapping("DestWhAreaLoc", "WhAreaLoc"),
                    }
                }
            }
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "batchNo",
                Caption = L.Text["批号"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "produceDate",
                Caption = L.Text["生产日期"],
                Type = "date",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhAreaName",
                Caption = L.Text["调入库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhLocName",
                Caption = L.Text["调入库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "",
            SourceTitle = "",
            DestKey = "StkTransfer",
            DestTitle = L.Text["库存调拨单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceItem = true,
            IsOverSourceQty = true,
            SummaryBillFields = new List<PdaLocalBillSummaryBillField>()
            {
                new() { FieldName = "MaterialId" },
                new() { FieldName = "MaterialNumber" },
                new() { FieldName = "MaterialName" },
                new() { FieldName = "UnitId" },
                new() { FieldName = "UnitNumber" },
                new() { FieldName = "UnitName" },
                new() { FieldName = "BatchNo" },
                new() { FieldName = "ProduceDate" },
                new() { FieldName = "ExpPeriod" },
                new() { FieldName = "ExpUnit" },
                new() { FieldName = "ExpiryDate" },
                new() { FieldName = "WhAreaId" },
                new() { FieldName = "WhAreaNumber" },
                new() { FieldName = "WhAreaName" },
                new() { FieldName = "WhLocId" },
                new() { FieldName = "WhLocNumber" },
                new() { FieldName = "WhLocName" },
                new() { FieldName = "DestWhAreaId" },
                new() { FieldName = "DestWhAreaNumber" },
                new() { FieldName = "DestWhAreaName" },
                new() { FieldName = "DestWhLocId" },
                new() { FieldName = "DestWhLocNumber" },
                new() { FieldName = "DestWhLocName" },
                new() { FieldName = "SrcBillId" },
                new() { FieldName = "SourceBillEntryId" },
                new() { FieldName = "SrcBillKey" },
                new() { FieldName = "AuxUnitId" },
                new() { FieldName = "AuxPropValueId" },
            }
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        throw new NotImplementedException();
    }

    public override void RefreshShow(long tranId)
    {
        base.RefreshShow(tranId);
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => $"{r.SrcBillNo} - {r["EsSrcBillNo"]}").ToList();
    }

    /// <inheritdoc>
    ///     <cref></cref>
    /// </inheritdoc>
    public override Task<dynamic> GetBillForBillNo(PdaLocalBillGetBillForBillNoInput input)
    {
        return Task.FromResult<dynamic>(null);
    }

    /// <inheritdoc/>
    protected override Task<string> GetBillType(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        return Task.FromResult(pdaData.ScanHead.DestBillType);
    }

    /// <inheritdoc/>
    protected override void VerifySubmit(long tranId)
    {
        base.VerifySubmit(tranId);
        var pdaData = GetPdaData(tranId);

        foreach (KeyValuePair<string, List<PdaLocalBillIncludeBarcode>> pair in pdaData.DetailIncludeBarcodes)
        {
            foreach (PdaLocalBillIncludeBarcode includeBarcode in pair.Value)
            {
                if (includeBarcode.PdaBarcode.CalcBarcode.WhAreaId == includeBarcode.PdaBarcode.CalcBarcode.DestWhAreaId &&
                    includeBarcode.PdaBarcode.CalcBarcode.WhLocId == includeBarcode.PdaBarcode.CalcBarcode.DestWhLocId)
                    throw Oops.Bah(L.Text["条码[{0}]调拨单调入调出库位相同,请重新选择调入库位", includeBarcode.PdaBarcode.Barcode.Barcode]);
            }
        }
    }

    /// <inheritdoc/>
    protected override void BeforeMatchingBarcode(long tranId, BdBarcode barcode)
    {
        base.BeforeMatchingBarcode(tranId, barcode);
        var pdaData = GetPdaData(tranId);

        if (IsEmptyValue(pdaData.ScanHead["DestWhAreaId"])) throw Oops.Bah(L.Text["请先选择调入库区"]);

        if (pdaData.ScanHead["DestWhAreaId"] + "" == pdaData.StockInfo.WhAreaId && pdaData.ScanHead["DestWhLocId"] + "" == pdaData.StockInfo.WhLocId)
            throw Oops.Bah(L.Text["调拨单调入调出库位相同,请重新选择调入库位"]);
    }

    /// <inheritdoc/>
    protected override void AfterMatchingBarcode(long tranId, PdaLocalBillBarcode pdaBarcode)
    {
        base.AfterMatchingBarcode(tranId, pdaBarcode);
        var pdaData = GetPdaData(tranId);
        var curBarcode = pdaData.BarcodeList.First(r => r.DetailId == pdaBarcode.DetailId);
        var includeBarcodes = new List<PdaLocalBillBarcode>();
        foreach (KeyValuePair<string, List<PdaLocalBillIncludeBarcode>> pair in pdaData.DetailIncludeBarcodes)
        {
            foreach (PdaLocalBillIncludeBarcode includeBarcode in pair.Value)
            {
                if (includeBarcode.PdaBarcode.DetailId == pdaBarcode.DetailId) includeBarcodes.Add(includeBarcode.PdaBarcode);
            }
        }

        foreach (PdaLocalBillBarcode includeBarcode in includeBarcodes)
        {
            includeBarcode.CalcBarcode.DestWhAreaId = pdaData.ScanHead["DestWhAreaId"] + "";
            includeBarcode.CalcBarcode.DestWhAreaNumber = pdaData.ScanHead["DestWhAreaNumber"] + "";
            includeBarcode.CalcBarcode.DestWhAreaName = pdaData.ScanHead["DestWhAreaName"] + "";
            includeBarcode.CalcBarcode.DestWhLocId = pdaData.ScanHead["DestWhLocId"] + "";
            includeBarcode.CalcBarcode.DestWhLocNumber = pdaData.ScanHead["DestWhLocNumber"] + "";
            includeBarcode.CalcBarcode.DestWhLocName = pdaData.ScanHead["DestWhLocName"] + "";
        }

        curBarcode.CalcBarcode.DestWhAreaId = pdaData.ScanHead["DestWhAreaId"] + "";
        curBarcode.CalcBarcode.DestWhAreaNumber = pdaData.ScanHead["DestWhAreaNumber"] + "";
        curBarcode.CalcBarcode.DestWhAreaName = pdaData.ScanHead["DestWhAreaName"] + "";
        curBarcode.CalcBarcode.DestWhLocId = pdaData.ScanHead["DestWhLocId"] + "";
        curBarcode.CalcBarcode.DestWhLocNumber = pdaData.ScanHead["DestWhLocNumber"] + "";
        curBarcode.CalcBarcode.DestWhLocName = pdaData.ScanHead["DestWhLocName"] + "";
    }
}