﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.PdaStkStockCount.Dto;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.PdaStkStockCount.Scan;

public class PdaStkStockCountBarcodeOperation : PdaLocalBillScanBarcodeOperationBase
{
    private SqlSugarRepository<BdBarcode> Rep => App.GetService<SqlSugarRepository<BdBarcode>>(_serviceProvider);

    /// <summary>
    /// 数据缓存服务
    /// </summary>
    protected PdaDataCacheService DataCacheService => App.GetService<PdaDataCacheService>(_serviceProvider);

    /// <summary>
    /// 条码
    /// </summary>
    /// <param name="serviceProvider"></param>
    public PdaStkStockCountBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var barStr = (args.BarcodeString + "").Split(';')[0];

        //查询条码
        //1. 按条码号查询,默认条码不重复,只取第一条,如有重复条码号,后面再想
        var barcode = Rep.AsQueryable().IncludeNavCol().First(r => r.Barcode == barStr && r.Status != BdBarcodeStatus.Disuse);
        //没有条码,下一个扫描操作
        if (barcode == null) return;

        var pdaData = (PdaStkStockCountData)DataCacheService.GetBillData(args.Key, args.TranId);
        var pdaModel = (PdaStkStockCountModel)DataCacheService.GetPdaModel(args.Key);
        var input = (PdaStkStockCountScanBarcodeInput)args.Properties["ScanBarcodeType"];

        //检证条码是否能扫描
        var verified = VerifyBarcode(pdaData, pdaModel, input, barcode);
        if (!verified)
        {
            args.IsResult = true;
            return;
        }

        string funcKey = $"bar_{barcode.FuncKey}~barcode";
        var templates = Rep.Change<SysReportTemplate>().AsQueryable().Where(r => r.FuncKey == funcKey).OrderBy(r => r.Number).ToList();
        List<SysReportTemplateInfo> templateInfos = new List<SysReportTemplateInfo>();
        foreach (var item in templates)
        {
            templateInfos.Add(new SysReportTemplateInfo
            {
                Id = item.Id,
                Number = item.Number,
            });
        }

        //如果扫描条码策略,选的是弹窗修改,并且不是确认后
        if (input.IsRepeat == false && input.ScanBarcodeType == ScanBarcodeType.Modify)
        {
            var pdaBdBarcode = barcode.Adapt<PdaBdBarcode>();
            pdaBdBarcode.SysReports.AddRange(templateInfos);
            PdaRestfulCode restfulCode = PdaRestfulCode.P999;
            //如果返回只有一个条码,并且为一物一码
            PdaExtrasRestfulResult<PdaBdBarcode> result = new PdaExtrasRestfulResult<PdaBdBarcode>
            {
                Code = (int)restfulCode,
                Message = L.Text["修改数量"],
                Data = pdaBdBarcode,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);
            args.Barcodes.Add(barcode);
            args.IsResult = true;
            return;
        }

        //扫描条码
        ScanBarcode(pdaData, pdaModel, input, barcode, input.ModifyQty ?? barcode.Qty);

        //只要查到有条码,认为此次扫描为条码扫描
        args.IsResult = true;
    }

    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="pdaData"></param>
    /// <param name="pdaModel"></param>
    /// <param name="input"></param>
    /// <param name="barcode"></param>
    /// <param name="modifyQty"></param>
    private void ScanBarcode(PdaStkStockCountData pdaData, PdaStkStockCountModel pdaModel, PdaStkStockCountScanBarcodeInput input, BdBarcode barcode, decimal modifyQty)
    {
        //1. 判断仓库优先
        PdaLocalBillBarcode pdaBarcode = new PdaLocalBillBarcode(barcode, modifyQty);
        // TODO: 宏芯宇特殊处理
        // //这里要处理物料,可能不是基础资料的内码
        // var material = Rep.Change<BdMaterial>().GetFirst(r => r.Number == barcode.Material.Number);
        // pdaBarcode.CalcBarcode.MaterialId = material.Id;

        if (input.FirstStockType == FirstStockType.Barcode)
        {
            //条码优先
            //如果仓库为空,取选择的
            if (barcode.WhAreaId is null or 0)
            {
                if (pdaData.StockInfo == null || string.IsNullOrEmpty(pdaData.StockInfo.WhAreaId)) throw Oops.Bah(L.Text["条码没有仓库,请先选择仓库"]);
                pdaBarcode.SetStockInfo(pdaData.StockInfo, false);
            }
            //如果条码上有仓库,直接取条码的,不用赋值仓库
        }
        else
        {
            //选择优先
            if (pdaData.StockInfo == null || string.IsNullOrEmpty(pdaData.StockInfo.WhAreaId)) throw Oops.Bah(L.Text["条码没有仓库,请先选择仓库"]);
            pdaBarcode.SetStockInfo(pdaData.StockInfo, false);
        }

        //匹配条码
        pdaModel.MatchingBarcode(pdaData.TranId, pdaBarcode);
    }

    /// <summary>
    /// 检证条码是否能扫描
    /// </summary>
    /// <param name="pdaData"></param>
    /// <param name="pdaModel"></param>
    /// <param name="input"></param>
    /// <param name="barcode"></param>
    private bool VerifyBarcode(PdaStkStockCountData pdaData, PdaStkStockCountModel pdaModel, PdaStkStockCountScanBarcodeInput input, BdBarcode barcode)
    {
        bool verified = true;
        if (pdaData.StkStockCount == null) throw Oops.Bah(L.Text["请先选择盘点单"]);
        if (pdaData.Barcodes.Any(r => r.Barcode.Id == barcode.Id)) throw Oops.Bah(L.Text["条码[{0}]已扫描", barcode.Barcode]);
        //找条码日志
        // 20241217 项目允许多次扫描进行覆盖
        // if (Rep.Change<StkStockCountBarcodeLog>().AsQueryable().Any(r => r.BarcodeId == barcode.Id && r.StockCountId == pdaData.StkStockCount.Id)) throw Oops.Bah($"条码[{barcode.Barcode}]已在其它盘点扫描");

        // 250114不允许跨库位
        var stockIds = pdaData.StkStockCountEntries.Select(f => $"{f.Entry.WhAreaId}|{f.Entry.WhLocId}").ToList().Distinct();
        if (!stockIds.Contains($"{barcode.WhAreaId}|{barcode.WhLocId}"))
        {
            // 当前扫描的条码是否已扫描过待确认
            var IsWaitingConfirmBarcodesIncludeCurrentBarcode = pdaData.WaitingConfirmBarcodes.Any(a => a.TranId == pdaData.TranId && a.Barcode.Id == barcode.Id);

            if (!IsWaitingConfirmBarcodesIncludeCurrentBarcode)
            {
                throw Oops.Bah(L.Text["条码库区与当前盘点库区库位不一致"]);
                // PdaRestfulCode restfulCode = PdaRestfulCode.P888;
                // pdaData.WaitingConfirmBarcodes.Add(new WaitingConfirmBarcode
                // {
                //     TranId = pdaData.TranId,
                //     Barcode = barcode
                // });
                // PdaExtrasRestfulResult<BarBarcode> result = new()
                // {
                //     Code = (int)restfulCode,
                //     Message = "条码库区与当前盘点库区不一致，是否确认扫描?",
                //     Data = null,
                //     Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                // };
                // UnifyContext.Fill(result);
                // verified = false;
            }
        }

        return verified;
    }
}

public class PdaBdBarcode : BdBarcode
{
    public List<SysReportTemplateInfo> SysReports { get; set; } = new List<SysReportTemplateInfo>();
}

public class SysReportTemplateInfo
{
    public long Id { get; set; }

    public string Number { get; set; }
}