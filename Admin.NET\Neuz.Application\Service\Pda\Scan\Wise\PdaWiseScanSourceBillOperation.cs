﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.Wise;

public class PdaWiseScanSourceBillOperation : PdaScanBarcodeOperationBase
{
    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaCacheService = App.GetService<PdaCacheService>(_serviceProvider);
        var billData = pdaCacheService.GetBillData(args.TranId);
        var billModel = pdaCacheService.GetPdaBillModel(billData.BillModelKey);
        var billSchema = billModel.Config.BillSchema;
        string lookupKey = $"{billSchema.BillLink.SourceKey}_{billSchema.BillLink.DestKey}";
        if (pdaCacheService.IsExistPdaBasicModel(lookupKey))
        {
            //如果barcode为空,尝试找源单
            var isSourceBarcode = billModel.SelectLookupData(args.TranId, lookupKey, args.BarcodeString, "SourceInfo", QueryType.Key);
            if (isSourceBarcode) args.IsResult = true;
        }
    }

    public PdaWiseScanSourceBillOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}