﻿namespace Neuz.Core.Enum;

/// <summary>
/// 元素类型
/// </summary>
public enum ElementType
{
    /// <summary>
    /// 常量
    /// </summary>
    [Description("常量")]
    Const = 0,

    /// <summary>
    /// 日期字段
    /// </summary>
    [Description("日期字段")]
    DateTimeField = 1,

    /// <summary>
    /// 文本字段
    /// </summary>
    [Description("文本字段")]
    TextField = 2,

    /// <summary>
    /// 基础资料
    /// </summary>
    [Description("基础资料")]
    BaseData = 3,

    /// <summary>
    /// 流水号
    /// </summary>
    [Description("流水号")]
    Serial = 4,

    /// <summary>
    /// 枚举字段
    /// </summary>
    [Description("枚举字段")]
    EnumField = 5,

    /// <summary>
    /// 数值字段
    /// </summary>
    [Description("数值字段")]
    NumberField = 6,
}