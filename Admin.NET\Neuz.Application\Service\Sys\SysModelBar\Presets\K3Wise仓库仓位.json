{"$schema": "http://barModelSchema.json", "modelServiceName": "K3WiseBarModelService", "modelParams": {"billTypeofIReportData": "Neuz.Application.Erp.Barcode.K3Wise.K3WiseStockService, Neuz.Application", "billDataMaterialNumberFieldName": "", "billDataQtyFieldName": "", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "StockNumber", "title": "仓库编码", "sortable": true}, {"fieldName": "StockName", "title": "仓库名称", "sortable": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "sortable": true}, {"fieldName": "StockLocName", "title": "仓位名称", "sortable": true}], "barcodeSearchColumns": [], "barcodeListColumns": [], "barcodeEditColumns": []}, "billDataQuery": {"type": "K3Wise", "idFieldName": "_id", "entryIdFieldName": "", "content": "SELECT\r\n    *\r\nFROM\r\n    (\r\n        SELECT\r\n            CAST(t2.FItemID AS VARCHAR(50)) + '|' + CAST(t1.FSPID AS VARCHAR(50)) AS _id,\r\n            t1.FSPID AS StockLocId,\r\n            t1.FNumber AS StockLocNumber,\r\n            t1.FName AS StockLocName,\r\n            t2.FItemID AS StockId,\r\n            t2.FNumber AS StockNumber,\r\n            t2.FName AS StockName,\r\n            t1.FSPGroupID AS FSpGroupID,\r\n            t2.FIsStockMgr,\r\n            t2.FTypeID\r\n        FROM\r\n            t_StockPlace t1\r\n            LEFT JOIN t_Stock t2 ON (t2.FSPGroupID = t1.FSPGroupID)\r\n        WHERE\r\n            t1.FDeleted = 0\r\n            AND t2.FDeleted = 0\r\n    ) AS table1\r\nWHERE\r\n    1 = 1"}}