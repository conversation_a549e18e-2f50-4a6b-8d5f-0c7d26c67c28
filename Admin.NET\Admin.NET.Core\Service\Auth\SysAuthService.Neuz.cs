﻿namespace Admin.NET.Core.Service;

/// <summary>
/// 系统登录授权服务
/// </summary>
public partial class SysAuthService
{
    /// <summary>
    /// 检查当前用户是否已在其它Pda登录
    /// </summary>
    /// <returns></returns>
    [HttpGet("checkPdaUserLogin")]
    [AllowAnonymous]
    public async Task<bool> CheckPdaUserLogin([FromQuery] string account, [FromQuery] string password)
    {
        // 账号是否存在
        var user = await _sysUserRep.AsQueryable().Includes(t => t.SysOrg).Filter(null, true).FirstAsync(u => u.Account.Equals(account));
        _ = user ?? throw Oops.Oh(ErrorCodeEnum.D0009);

        // 账号是否被冻结
        if (user.Status == StatusEnum.Disable)
            throw Oops.Oh(ErrorCodeEnum.D1017);

        // 租户是否被禁用
        var tenant = await _sysUserRep.ChangeRepository<SqlSugarRepository<SysTenant>>().GetFirstAsync(u => u.Id == user.TenantId);
        if (tenant != null && tenant.Status == StatusEnum.Disable)
            throw Oops.Oh(ErrorCodeEnum.Z1003);

        // 国密SM2解密（前端密码传输SM2加密后的）
        password = CryptogramUtil.SM2Decrypt(password);

        // 是否开启域登录验证
        if (await _sysConfigService.GetConfigValue<bool>(CommonConst.SysDomainLogin))
        {
            var userLdap = await _sysUserLdap.GetFirstAsync(u => u.UserId == user.Id && u.TenantId == tenant.Id);
            if (userLdap == null)
            {
                if (CryptogramUtil.CryptoType == CryptogramEnum.MD5.ToString())
                {
                    if (!user.Password.Equals(MD5Encryption.Encrypt(password)))
                        throw Oops.Oh(ErrorCodeEnum.D1000);
                }
                else
                {
                    if (!CryptogramUtil.Decrypt(user.Password).Equals(password))
                        throw Oops.Oh(ErrorCodeEnum.D1000);
                }
            }
            else if (!await _sysLdapService.AuthAccount(tenant.Id, userLdap.Account, password))
            {
                throw Oops.Oh(ErrorCodeEnum.D1000);
            }
        }
        else
        {
            if (CryptogramUtil.CryptoType == CryptogramEnum.MD5.ToString())
            {
                if (!user.Password.Equals(MD5Encryption.Encrypt(password)))
                    throw Oops.Oh(ErrorCodeEnum.D1000);
            }
            else
            {
                if (!CryptogramUtil.Decrypt(user.Password).Equals(password))
                    throw Oops.Oh(ErrorCodeEnum.D1000);
            }
        }

        //Pda只支持单点登录
        var httpContext = _httpContextAccessor.HttpContext;
        var headers = httpContext?.Request.Headers;
        if ((headers != null && headers.ContainsKey(ClaimConst.ClientType) &&
             LoginDeviceType.Pda.ToString().Equals(headers[ClaimConst.ClientType], StringComparison.OrdinalIgnoreCase)))
        {
            //var sysOnlineUerRep = App.GetService<SqlSugarRepository<SysOnlineUser>>();
            //var onlineUsers = await sysOnlineUerRep.AsQueryable().Where(u => u.UserId == user.Id && u.LoginDeviceType == LoginDeviceType.Pda).ToListAsync();
            //if (onlineUsers.Count > 0)
            //{
            //    return false;
            //}
            if (_sysOnlineUserService.GetUserPdaOnline(user.Id))
                return false;
        }

        return true;
    }

    /// <summary>
    /// 验证用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CheckPassword"), HttpPost]
    [DisplayName("验证用户密码")]
    public async Task<bool> CheckPassword(CheckPasswordInput input)
    {
        var userId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == userId);
        _ = user ?? throw Oops.Oh(ErrorCodeEnum.D0009);

        // 国密SM2解密（前端密码传输SM2加密后的）
        try
        {
            input.Password = CryptogramUtil.SM2Decrypt(input.Password);
        }
        catch
        {
            throw Oops.Oh(ErrorCodeEnum.D0010);
        }
        if (CryptogramUtil.CryptoType == CryptogramEnum.MD5.ToString())
        {
            if (!user.Password.Equals(MD5Encryption.Encrypt(input.Password)))
            {
                throw Oops.Oh(ErrorCodeEnum.D1000);
            }
        }
        else
        {
            if (!CryptogramUtil.Decrypt(user.Password).Equals(input.Password))
            {
                throw Oops.Oh(ErrorCodeEnum.D1000);
            }
        }
        return true;
    }
}