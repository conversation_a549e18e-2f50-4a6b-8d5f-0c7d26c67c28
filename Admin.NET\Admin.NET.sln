﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Application", "Admin.NET.Application\Admin.NET.Application.csproj", "{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Core", "Admin.NET.Core\Admin.NET.Core.csproj", "{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Web.Core", "Admin.NET.Web.Core\Admin.NET.Web.Core.csproj", "{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Web.Entry", "Admin.NET.Web.Entry\Admin.NET.Web.Entry.csproj", "{11EA630B-4600-4236-A117-CE6C6CD67586}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{662E0B8E-F23E-4C7D-80BD-CAA5707503CC}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{76F70D22-8D53-468E-A3B6-1704666A1D71}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.GoView", "Plugins\Admin.NET.Plugin.GoView\Admin.NET.Plugin.GoView.csproj", "{C4A288D5-0FAA-4F43-9072-B97635D7871D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.DingTalk", "Plugins\Admin.NET.Plugin.DingTalk\Admin.NET.Plugin.DingTalk.csproj", "{F6A002AD-CF7F-4771-8597-F12A50A93DAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.ReZero", "Plugins\Admin.NET.Plugin.ReZero\Admin.NET.Plugin.ReZero.csproj", "{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.ApprovalFlow", "Plugins\Admin.NET.Plugin.ApprovalFlow\Admin.NET.Plugin.ApprovalFlow.csproj", "{4124E31B-EA94-4EE3-9EC6-A565F1420AEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.K3Cloud", "Plugins\Admin.NET.Plugin.K3Cloud\Admin.NET.Plugin.K3Cloud.csproj", "{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.PaddleOCR", "Plugins\Admin.NET.Plugin.PaddleOCR\Admin.NET.Plugin.PaddleOCR.csproj", "{1B106C11-E5BF-44AB-A283-1E948A8BD8C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Admin.NET.Plugin.WorkWeixin", "Plugins\Admin.NET.Plugin.WorkWeixin\Admin.NET.Plugin.WorkWeixin.csproj", "{12998618-A875-4580-B5B1-0CC50CE85F27}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Neuz.Application", "Neuz.Application\Neuz.Application.csproj", "{2A557076-9657-4FFA-9E50-5D51C1D2E73E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Neuz.Core", "Neuz.Core\Neuz.Core.csproj", "{810278E7-DD18-4383-901E-85D5E6BAE112}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Neuz.Mvc", "Neuz.Mvc\Neuz.Mvc.csproj", "{8CF63136-CE78-4C86-8BF3-DB24813F4C88}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Neuz.UnitTest", "Neuz.UnitTest\Neuz.UnitTest.csproj", "{7BD5D380-858A-4249-BA3E-F63718B550E7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Release|Any CPU.Build.0 = Release|Any CPU
		{4124E31B-EA94-4EE3-9EC6-A565F1420AEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4124E31B-EA94-4EE3-9EC6-A565F1420AEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4124E31B-EA94-4EE3-9EC6-A565F1420AEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4124E31B-EA94-4EE3-9EC6-A565F1420AEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B106C11-E5BF-44AB-A283-1E948A8BD8C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B106C11-E5BF-44AB-A283-1E948A8BD8C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B106C11-E5BF-44AB-A283-1E948A8BD8C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B106C11-E5BF-44AB-A283-1E948A8BD8C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{12998618-A875-4580-B5B1-0CC50CE85F27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12998618-A875-4580-B5B1-0CC50CE85F27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12998618-A875-4580-B5B1-0CC50CE85F27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12998618-A875-4580-B5B1-0CC50CE85F27}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A557076-9657-4FFA-9E50-5D51C1D2E73E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A557076-9657-4FFA-9E50-5D51C1D2E73E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A557076-9657-4FFA-9E50-5D51C1D2E73E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A557076-9657-4FFA-9E50-5D51C1D2E73E}.Release|Any CPU.Build.0 = Release|Any CPU
		{810278E7-DD18-4383-901E-85D5E6BAE112}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{810278E7-DD18-4383-901E-85D5E6BAE112}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{810278E7-DD18-4383-901E-85D5E6BAE112}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{810278E7-DD18-4383-901E-85D5E6BAE112}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CF63136-CE78-4C86-8BF3-DB24813F4C88}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CF63136-CE78-4C86-8BF3-DB24813F4C88}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CF63136-CE78-4C86-8BF3-DB24813F4C88}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CF63136-CE78-4C86-8BF3-DB24813F4C88}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BD5D380-858A-4249-BA3E-F63718B550E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BD5D380-858A-4249-BA3E-F63718B550E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BD5D380-858A-4249-BA3E-F63718B550E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BD5D380-858A-4249-BA3E-F63718B550E7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C4A288D5-0FAA-4F43-9072-B97635D7871D} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{4124E31B-EA94-4EE3-9EC6-A565F1420AEA} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{9EB9C39E-E14F-443E-9AA3-EE417ABCBC1D} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{1B106C11-E5BF-44AB-A283-1E948A8BD8C2} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{12998618-A875-4580-B5B1-0CC50CE85F27} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5CD801D7-984A-4F5C-8FA2-211B7A5EA9F3}
	EndGlobalSection
EndGlobal
