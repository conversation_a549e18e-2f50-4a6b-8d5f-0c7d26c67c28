﻿using Furion.Localization;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.StockCount;

/// <summary>
/// Pda盘点服务
/// </summary>
[ApiDescriptionSettings("PDA", Name = "StockCount", Order = 300)]
public class PdaStockCountService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly UserManager _userManager;

    public PdaStockCountService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _userManager = serviceProvider.GetService<UserManager>();
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaModel")]
    public async Task<dynamic> GetPdaModel([FromQuery] string key)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var model = pdaCacheService.GetPdaModel(key);
        return await Task.FromResult(model);
    }

    /// <summary>
    /// 创建新的单据数据
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("createBillData")]
    public async Task<dynamic> CreateBillData([FromQuery] string key)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var userId = _userManager.UserId;
        var billData = pdaCacheService.CreateBillData(key, userId);
        var pdaModel = (PdaStockCountModel)pdaCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.BillDataInitialization(billData);
        pdaModel.RefreshShow(billData.TranId);
        billData.DataShow.Properties["tranId"] = billData.TranId;
        billData.DataShow.Properties["userId"] = billData.UserId;
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShow")]
    public async Task<dynamic> GetPdaBillDataShow([FromQuery] string key)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var userId = _userManager.UserId;
        var billData = pdaDataCacheService.GetBillDataForKey(key, userId);
        if (billData != null)
        {
            billData.DataShow.Properties["tranId"] = billData.TranId;
            billData.DataShow.Properties["userId"] = billData.UserId;
        }
        return await Task.FromResult(billData?.DataShow);
    }

    /// <summary>
    /// 获取单据模型
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("getPdaBillDataShowForTranId")]
    public async Task<dynamic> GetPdaBillDataShowForTranId([FromQuery] long tranId, [FromQuery] string key)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.RefreshShow(tranId);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 删除单据模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("delPdaBillData")]
    public async Task<dynamic> DelPdaBillData([FromBody] PdaDelPdaBillDataInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        pdaDataCacheService.DelBillData(input.TranId);
        return await Task.FromResult("");
    }


    /// <summary>
    /// 查询Lookup数据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="lookupKey"></param>
    /// <param name="lookupValue"></param>
    /// <returns></returns>
    [HttpGet("queryLookupData")]
    public async Task<dynamic> QueryLookupData([FromQuery] long tranId, [FromQuery] string key, [FromQuery] string lookupKey, [FromQuery] string lookupValue)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(key, tranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        var lookupOutpust = pdaModel.QueryLookupData(tranId, lookupKey, lookupValue);
        return await Task.FromResult(lookupOutpust);
    }

    /// <summary>
    /// 选择Lookup数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("selectLookupData")]
    public async Task<dynamic> SelectLookupData([FromBody] PdaSelectLookupDataInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.SelectLookupData(input.TranId, input.Key, input.LookupKey, input.ValueKey, input.LookupDataKey);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 扫描条码
    /// 对于可能PDA端会有不同的处理：
    /// 1. 普通条码,直接返回
    /// 2. 可扣减,用返回码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="key"></param>
    /// <param name="barcode"></param>
    /// <param name="firstStockType"></param>
    /// <param name="scanBarcodeType"></param>
    /// <returns></returns>
    [HttpGet("scanBarcode")]
    public async Task<dynamic> ScanBarcode([FromQuery] long tranId, [FromQuery] string key, [FromQuery] string barcode, [FromQuery] string firstStockType, [FromQuery] string scanBarcodeType)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        if (string.IsNullOrEmpty(barcode)) throw Oops.Bah(PdaErrorCode.Pda1012);
        var billData = pdaCacheService.GetBillData(key, tranId);
        var billModel = (PdaStockCountModel)pdaCacheService.GetPdaModel(billData.ModelKey);
        billModel.ScanBarcodeString(tranId, barcode, firstStockType, scanBarcodeType);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 针对可扣减条码PDA确认后返回
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("confirmScanBarcode")]
    public async Task<dynamic> ConfirmScanBarcode([FromBody] PdaStockCountConfirmScanBarcodeInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var dataJObject = input.Data.Value<JObject>();
        if (dataJObject == null) throw Oops.Bah(L.Text["没有修改后的条码信息!"]);
        var barcode = JsonConvert.DeserializeObject<BarBarcode>(dataJObject.ToString());
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = (PdaStockCountModel)pdaCacheService.GetPdaModel(billData.ModelKey);
        billModel.ScanBarcode(input.TranId, input.Key, barcode, input.FirstStockType, input.ModifyQty);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 删除条码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("deleteBarcode")]
    public async Task<dynamic> DeleteBarcode([FromBody] PdaDelPdaBillDataInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        pdaModel.DeleteBarcode(input.TranId, input.Key, input.DetailId);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("billSubmit")]
    public async Task<dynamic> BillSubmit([FromBody] PdaBillSubmitInput input)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaDataCacheService.GetBillData(input.Key, input.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        var message = pdaModel.Submit(input.TranId, input.Key);
        return await Task.FromResult(message);
    }
}