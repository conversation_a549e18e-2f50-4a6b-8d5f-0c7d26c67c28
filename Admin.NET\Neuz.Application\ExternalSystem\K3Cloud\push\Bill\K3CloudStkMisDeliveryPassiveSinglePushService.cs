using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.ExternalSystem.Dto;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Neuz.Application.ExternalSystem.K3Cloud.push.Bill;


/// <summary>
/// K3Cloud 其他出库单（无源单）
/// </summary>
/// <remarks>
/// 金蝶FormId: STK_MisDelivery
/// </remarks>
[Injection(Named = "K3Cloud:StkMisDeliveryPassiveSingle")]
public class K3CloudStkMisDeliveryPassiveSinglePushService : K3CloudBasePushService<StkAdjustmentPushData>
{
    public K3CloudStkMisDeliveryPassiveSinglePushService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider) : base(scopeFactory, serviceProvider)
    {
    }

    protected override string RuleId => "";

    protected override string SourceFormId => ""; // 无源单

    protected override bool IsSumQtyToBeforePush => false; // 项目需求，不能汇总，数据需要按单据的行和金蝶生成的单据行一一匹配

    protected override async Task AfterSetPushObject(EsSyncPushSetting pushSetting, StkAdjustmentPushData localObject,
        Dictionary<string, object> pushObject)
    {
        await base.AfterSetPushObject(pushSetting, localObject, pushObject);
        pushObject["[FEntity].FQty"] = Math.Abs(localObject.Entry.Qty);
        //pushObject["[FEntity].F_WCBW_Combo_qtr1"] = "0";//TODO: 先写死明细扩展字段的其他出库类型，后期开发后在删除
    }

    protected override BasePushHandle<StkAdjustmentPushData> GetPushQuery()
    {
        return new InnerPushQuery(ServiceProvider, Rep.Context);
    }


    /// <summary>
    /// 推送查询
    /// </summary>
    private class InnerPushQuery : StkAdjustmentPushHandle
    {
        /// <summary>
        /// K3Cloud 接口
        /// </summary>
        protected K3CloudInterface K3CloudInterface { get; }


        public InnerPushQuery(IServiceProvider serviceProvider, ISqlSugarClient context) : base(serviceProvider,
            context)
        {
            K3CloudInterface = serviceProvider.GetService<K3CloudInterface>();
        }

        protected override List<string> QueryBillTypes => new() { "QTCKD01_SYS","003","004", "QTCKD06_SYS", "QTCKD02_SYS", "QTCKD07_SYS" };

        public override async Task UpdateFlag(List<StkAdjustmentPushData> localObjects, EsSyncPushResult esSyncPushResult,
            bool isByLastPushTime)
        {
            await base.UpdateFlag(localObjects, esSyncPushResult, isByLastPushTime);

            if (!esSyncPushResult.IsSuccess) return;

            // 项目需求，提交成功后，重新查询一次生成的单据，
            var firstLocalObject = localObjects.First();

            var client = K3CloudInterface.GetK3CloudClient();
            var billList = await client.QueryBillData(new QueryBillParam
            {
                FormId = "STK_MisDelivery",
                FieldKeys = new List<string>
                {
                    "FID",
                    "FBillNo",
                    "FEntity_FEntryID AS FEntryID",
                    "FEntity_FSEQ AS FEntrySeq",
                },
                Filters = new List<QueryFilter> { new("FBillNo", QueryType.Equals, esSyncPushResult.TargetBillNo) }
            });

            // 库存调整单
            var StkAdjustment = await Context.Queryable<StkAdjustment>().Includes(u => u.Entries)
                .FirstAsync(u => u.Id == firstLocalObject.Bill.Id);
            StkAdjustment.EsId = esSyncPushResult.TargetId;
            StkAdjustment.EsBillNo = esSyncPushResult.TargetBillNo;
            // 保存库存调整单变更
            await Context.Updateable(StkAdjustment).ExecuteCommandAsync();

            foreach (var entry in StkAdjustment.Entries)
            {
                var record = billList.FirstOrDefault(u => Convert.ToInt32(u["FEntrySeq"]) == entry.Seq);
                if (record == null)
                    continue;

                entry.EsEntryId = record["FEntryID"] + "";
            }

            // 保存库存调整单细变更
            await Context.Updateable(StkAdjustment.Entries).ExecuteCommandAsync();
        }
    }
}
