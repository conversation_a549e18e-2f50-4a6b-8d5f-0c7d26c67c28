﻿using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.Scan.Wise;

public class PdaWiseStockCountScanStockOperation : PdaScanBarcodeOperationBase
{
    public PdaWiseStockCountScanStockOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaDataCacheService = App.GetService<PdaDataCacheService>();
        var billData = (PdaStockCountData)pdaDataCacheService.GetBillData(args.Key, args.TranId);
        var pdaModel = (PdaStockCountModel)pdaDataCacheService.GetPdaModel(billData.ModelKey);
        string regexStr = "CK(?<CK>.+)\\|CW(?<CW>.+)";
        Regex regex = new Regex(regexStr, RegexOptions.IgnoreCase);
        var match = regex.Match(args.BarcodeString);
        //仓库仓位条码
        if (match.Success)
        {
            string ck = match.Groups["CK"].Value;
            string cw = match.Groups["CW"].Value;
            var lookupModel = pdaDataCacheService.GetPdaBasicModel("t_stock");
            var stockModel = lookupModel as IPdaBasicLookupModel;
            var table = stockModel.QueryCustomData(args.TranId, $"{ck}_{cw}");
            if (table == null || table.Rows.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1025, ck, cw);
            var row = table.Rows[0];
            billData.StockInfo = row.ToDictionary().Adapt<PdaBillStockInfo>();
            billData.StockInfo.StockLocId = $"{row["StockLocId"]}";
            billData.StockInfo.StockLocNumber = $"{row["StockLocNumber"]}";
            billData.StockInfo.StockName = $"{row["StockName"]}";
            billData.StockInfo.StockId = $"{row["StockId"]}";
            billData.StockInfo.StockNumber = $"{row["StockNumber"]}";
            billData.StockInfo.StockName = $"{row["StockName"]}";
            pdaModel.RefreshShow(args.TranId);
            args.IsResult = true;
        }
    }
}