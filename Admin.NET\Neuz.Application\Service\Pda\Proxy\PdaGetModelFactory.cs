using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.LocalBill;

namespace Neuz.Application.Pda.Proxy;

public class PdaGetModelFactory : ITransient
{
    private IServiceProvider ServiceProvider { get; }

    public PdaGetModelFactory(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
    }

    public IPdaModel GetPdaModel(IPdaModel model)
    {
        var type = model.GetType();
        if (type.IsClass && !type.IsAbstract && typeof(PdaLocalBillModelBillBase<PdaLocalBillShow, PdaLocalBillData>).IsAssignableFrom(type))
        {
            var settingService = App.GetService<PdaLocalBillConfigService>(ServiceProvider);
            return settingService.GetPdaModelExplain(model).Result;
        }

        return model;
    }
}