﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储单据类型
/// </summary>
[SugarTable(null, "仓储单据类型")]
public class StkBillType : BdEntityBase
{
    /// <summary>
    /// 实体名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实体名称", Length = 50)]
    public string EntityName { get; set; }

    /// <summary>
    /// 下级明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkBillTypeNextEntry.Id))]
    public List<StkBillTypeNextEntry> NextEntries { get; set; }
}