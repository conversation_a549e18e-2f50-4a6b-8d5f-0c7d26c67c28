﻿using System.Reflection;
using Furion;
using Furion.DependencyInjection;
using Neuz.Application;
using Neuz.Core.Entity;
using Neuz.Core.Enum;
using SqlSugar;
using Xunit;
using Xunit.Abstractions;

namespace Neuz.UnitTest;

public class StkInventoryTest
{
    private readonly ITestOutputHelper _output;

    public StkInventoryTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;
    }

    [Fact(DisplayName = "库位库存相同库存并发增加测试")]
    public async Task SameAddTest()
    {
        var client = App.GetService<ISqlSugarClient>();
        var unit = await client.Queryable<BdUnit>().Where(u => u.Number == "testUnitId1").FirstAsync() ??
                   await client.Insertable(new BdUnit { Number = "testUnitId1", Name = "testUnitId1" }).ExecuteReturnEntityAsync();
        var material = await client.Queryable<BdMaterial>().Where(u => u.Number == "testMaterial1").FirstAsync() ??
                       await client.Insertable(new BdMaterial { Number = "testMaterial1", Name = "testMaterial1", UnitId = unit.Id }).ExecuteReturnEntityAsync();
        var owner = await client.Queryable<BdOwner>().Where(u => u.Number == "testOwner1").FirstAsync() ??
                    await client.Insertable(new BdOwner { Number = "testOwner1", Name = "testOwner1" }).ExecuteReturnEntityAsync();
        var warehouse = await client.Queryable<BdWarehouse>().Where(u => u.Number == "testWarehouse1").FirstAsync() ??
                        await client.Insertable(new BdWarehouse { Number = "testWarehouse1", Name = "testWarehouse1" }).ExecuteReturnEntityAsync();
        var whArea = await client.Queryable<BdWhArea>().Where(u => u.Number == "testWhArea1").FirstAsync() ??
                     await client.Insertable(new BdWhArea { Number = "testWhArea1", Name = "testWhArea1", WarehouseId = warehouse.Id }).ExecuteReturnEntityAsync();
        var whLoc = await client.Queryable<BdWhLoc>().Where(u => u.Number == "testWhLoc1").FirstAsync() ??
                    await client.Insertable(new BdWhLoc { Number = "testWhLoc1", Name = "testWhLoc1", WhAreaId = whArea.Id, WarehouseId = whArea.WarehouseId })
                        .ExecuteReturnEntityAsync();

        var tasks = new List<Task>();
        for (int i = 0; i < 20; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                Scoped.Create((_, s) =>
                {
                    // 重置 SqlSugar 上下文
                    SqlSugarExtension.ResetContext();

                    var inventoryService = App.GetService<StkInventoryService>(s.ServiceProvider);
                    inventoryService.UpdateInventory(new StkInvChange
                    {
                        MaterialId = material.Id,
                        UnitId = unit.Id,
                        OwnerId = owner.Id,
                        WhLocId = whLoc.Id,
                        InvLogType = StkInvLogType.AdjustmentPlus,
                        Qty = 1,
                    }, "审核");
                });
            }));
        }

        foreach (var task in tasks)
            await task;

        var invList = await client.Queryable<StkInventory>().Where(u => u.MaterialId == material.Id && u.WhLocId == whLoc.Id).ToListAsync();

        await client.Deleteable(invList).ExecuteCommandAsync();

        await client.Deleteable(unit).ExecuteCommandAsync();
        await client.Deleteable(material).ExecuteCommandAsync();
        await client.Deleteable(owner).ExecuteCommandAsync();
        await client.Deleteable(warehouse).ExecuteCommandAsync();
        await client.Deleteable(whArea).ExecuteCommandAsync();
        await client.Deleteable(whLoc).ExecuteCommandAsync();

        Assert.Equal(20, invList.Sum(u => u.Qty));
        Assert.Single(invList);
    }
}