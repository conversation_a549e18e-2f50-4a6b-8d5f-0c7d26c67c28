﻿using Furion.Localization;
using Magicodes.ExporterAndImporter.Excel;

namespace Neuz.Application;

/// <summary>
/// 入库报表服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkInReport", Order = 100)]
public class StkInReportService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly SqlSugarRepository<StkInventoryLog> _rep;

    /// <summary>
    /// 入库报表服务
    /// </summary>
    public StkInReportService(IServiceProvider serviceProvider, SqlSugarRepository<StkInventoryLog> rep)
    {
        _serviceProvider = serviceProvider;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("page")]
    public async Task<SqlSugarPagedList<StkInReportOutput>> PageAsync(StkInReportInput input)
    {
        var entities = await _rep.Context
            .AddWarehouseFilter<StkInventoryLog>(_serviceProvider, u => u.WarehouseId) // 仓库权限
            .AddWhAreaFilter<StkInventoryLog>(_serviceProvider, u => u.WhAreaId) // 库区权限
            .AddOwnerFilter<StkInventoryLog>(_serviceProvider, u => u.OwnerId) // 货主权限
            .Queryable(
                _rep.AsQueryable()
                    .InnerJoin<BdMaterial>((t1, t2) => t1.MaterialId == t2.Id)
                    .LeftJoin<BdWarehouse>((t1, t2, t3) => t1.WarehouseId == t3.Id)
                    .LeftJoin<BdOwner>((t1, t2, t3, t4) => t1.OwnerId == t4.Id)
                    .LeftJoin<BdWhArea>((t1, t2, t3, t4, t5) => t1.WhAreaId == t5.Id)
                    .LeftJoin<BdWhLoc>((t1, t2, t3, t4, t5, t6) => t1.WhLocId == t6.Id)
                    .LeftJoin<BdUnit>((t1, t2, t3, t4, t5, t6, t7) => t1.UnitId == t7.Id)
                    .LeftJoin<BdAuxPropValue>((t1, t2, t3, t4, t5, t6, t7, t8) => t1.AuxPropValueId == t8.Id)
                    .LeftJoin<BdContainer>((t1, t2, t3, t4, t5, t6, t7, t8, t9) => t1.ContainerId == t9.Id)
                    .Where((t1, t2, t3, t4, t5, t6, t7, t8, t9) =>
                        (t1.InvLogType == StkInvLogType.ReceivePlus || t1.InvLogType == StkInvLogType.PutAwayPlus || t1.InvLogType == StkInvLogType.PickPlus ||
                         t1.InvLogType == StkInvLogType.AdjustmentPlus || t1.InvLogType == StkInvLogType.BatchAdjustPlus) && t5.WhAreaType == BdWhAreaType.Storage)
                    .WhereIF(!string.IsNullOrEmpty(input.AuxPropValueNumber), (t1, t2, t3, t4, t5, t6, t7, t8, t9) => t8.Number.Contains(input.AuxPropValueNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.AuxPropValueName), (t1, t2, t3, t4, t5, t6, t7, t8, t9) => t8.Name.Contains(input.AuxPropValueName))
                    .Select((t1, t2, t3, t4, t5, t6, t7, t8, t9) => new
                    {
                        t1.Id,
                        t1.InvLogType,
                        MaterialId = t2.Id,
                        MaterialNumber = t2.Number,
                        MaterialName = t2.Name,
                        t1.BatchNo,
                        t1.ProduceDate,
                        t1.ExpiryDate,
                        t1.Qty,
                        UnitId = t7.Id,
                        UnitNumber = t7.Number,
                        UnitName = t7.Name,
                        OwnerId = t4.Id,
                        OwnerNumber = t4.Number,
                        OwnerName = t4.Name,
                        AuxPropValueId = t8.Id,
                        AuxPropValueNumber = t8.Number,
                        AuxPropValueName = t8.Name,
                        WarehouseId = t3.Id,
                        WarehouseNumber = t3.Number,
                        WarehouseName = t3.Name,
                        WhAreaId = t5.Id,
                        WhAreaNumber = t5.Number,
                        WhAreaName = t5.Name,
                        WhLocId = t6.Id,
                        WhLocNumber = t6.Number,
                        WhLocName = t6.Name,
                        ContainerId = t9.Id,
                        ContainerNumber = t9.Number,
                        t1.RelBillId,
                        t1.RelBillNo,
                        t1.RelBillType,
                        t1.RelBillKey,
                        t1.RelBillEntryId,
                        t1.RelSeq,
                        t1.CreateTime,
                        t1.CreateUserName,
                        t1.GroupName,
                    })
                    .MergeTable()
                    .LeftJoin<StkInStock>((t, ti1) => t.RelBillId == ti1.Id)
                    .LeftJoin<BdSupplier>((t, ti1, tis1) => ti1.SupplierId == tis1.Id)
                    .LeftJoin<StkReceive>((t, ti1, tis1, tr2) => t.RelBillId == tr2.Id)
                    .LeftJoin<BdSupplier>((t, ti1, tis1, tr2, trs2) => tr2.SupplierId == trs2.Id)
                    .LeftJoin<StkOutStock>((t, ti1, tis1, tr2, trs2, ts3) => t.RelBillId == ts3.Id)
                    .LeftJoin<BdSupplier>((t, ti1, tis1, tr2, trs2, ts3, trs3) => ts3.SupplierId == trs3.Id)
                    .LeftJoin<StkAdjustment>((t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.RelBillId == ta4.Id)
                    .WhereIF(!string.IsNullOrEmpty(input.BillNo), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.RelBillNo.Contains(input.BillNo))
                    .WhereIF(input.InDateBegin != null, (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.CreateTime >= input.InDateBegin)
                    .WhereIF(input.InDateEnd != null, (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.CreateTime <= input.InDateEnd)
                    .WhereIF(!string.IsNullOrEmpty(input.BillType), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.RelBillType.Contains(input.BillType))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.MaterialNumber.Contains(input.MaterialNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialName), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.MaterialName.Contains(input.MaterialName))
                    .WhereIF(!string.IsNullOrEmpty(input.BatchNo), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.BatchNo.Contains(input.BatchNo))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WarehouseNumber.Contains(input.WarehouseNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseName), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WarehouseName.Contains(input.WarehouseName))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.OwnerNumber.Contains(input.OwnerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerName), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.OwnerName.Contains(input.OwnerName))
                    .WhereIF(!string.IsNullOrEmpty(input.WhAreaNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WhAreaNumber.Contains(input.WhAreaNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WhAreaName), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WhAreaName.Contains(input.WhAreaName))
                    .WhereIF(!string.IsNullOrEmpty(input.WhLocNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WhLocNumber.Contains(input.WhLocNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WhLocName), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.WhLocName.Contains(input.WhLocName))
                    .WhereIF(input.ProduceDate != null, (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.ProduceDate == input.ProduceDate)
                    .WhereIF(!string.IsNullOrEmpty(input.ContainerNumber), (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => t.ContainerNumber.Contains(input.ContainerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.EsBillNo),
                        (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => (ti1.EsBillNo.Contains(input.EsBillNo) || tr2.EsBillNo.Contains(input.EsBillNo) ||
                                                                      ts3.EsBillNo.Contains(input.EsBillNo) || ta4.EsBillNo.Contains(input.EsBillNo)))
                    .WhereIF(input.BillDateBegin != null,
                        (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => (ti1.Date >= input.BillDateBegin || tr2.Date >= input.BillDateBegin || ts3.Date >= input.BillDateBegin ||
                                                                      ta4.Date >= input.BillDateBegin))
                    .WhereIF(input.BillDateEnd != null,
                        (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) =>
                            (ti1.Date <= input.BillDateEnd || tr2.Date <= input.BillDateEnd || ts3.Date <= input.BillDateEnd || ta4.Date <= input.BillDateEnd))
                    .WhereIF(!string.IsNullOrEmpty(input.SupplierNumber),
                        (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => (tis1.Number.Contains(input.SupplierNumber) || trs2.Number.Contains(input.SupplierNumber) ||
                                                                      trs3.Number.Contains(input.SupplierNumber)))
                    .WhereIF(!string.IsNullOrEmpty(input.SupplierName),
                        (t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) =>
                            (tis1.Name.Contains(input.SupplierName) || trs2.Name.Contains(input.SupplierName) || trs3.Name.Contains(input.SupplierName)))
                    .Select((t, ti1, tis1, tr2, trs2, ts3, trs3, ta4) => new StkInReportOutput
                    {
                        Id = t.Id,
                        BillNo = t.RelBillNo,
                        BillType = t.RelBillType,
                        WarehouseId = t.WarehouseId,
                        WarehouseNumber = t.WarehouseNumber,
                        WarehouseName = t.WarehouseName,
                        OwnerId = t.OwnerId,
                        OwnerNumber = t.OwnerNumber,
                        OwnerName = t.OwnerName,
                        Seq = (int)t.RelSeq,
                        MaterialId = t.MaterialId,
                        MaterialNumber = t.MaterialNumber,
                        MaterialName = t.MaterialName,
                        UnitId = t.Id,
                        UnitNumber = t.UnitNumber,
                        UnitName = t.UnitName,
                        BatchNo = t.BatchNo,
                        ProduceDate = t.ProduceDate,
                        ExpiryDate = t.ExpiryDate,
                        Qty = t.Qty,
                        WhAreaId = t.WhAreaId,
                        WhAreaNumber = t.WhAreaNumber,
                        WhAreaName = t.WhAreaName,
                        WhLocId = t.WhLocId,
                        WhLocNumber = t.WhLocNumber,
                        WhLocName = t.WhLocName,
                        ContainerId = t.ContainerId,
                        ContainerNumber = t.ContainerNumber,
                        InDate = t.CreateTime,
                        AuxPropValueId = t.AuxPropValueId,
                        AuxPropValueNumber = t.AuxPropValueNumber,
                        AuxPropValueName = t.AuxPropValueName,
                        EsBillNo = t.RelBillKey == "StkInStock" ? ti1.EsBillNo :
                            t.RelBillKey == "StkReceive" ? tr2.EsBillNo :
                            t.RelBillKey == "StkOutStock" ? ts3.EsBillNo :
                            t.RelBillKey == "StkAdjustment" ? ta4.EsBillNo : "",
                        BillDate = t.RelBillKey == "StkInStock" ? ti1.Date : t.RelBillKey == "StkReceive" ? tr2.Date : t.RelBillKey == "StkOutStock" ? ts3.Date : ta4.Date,
                        PushFlag = t.RelBillKey == "StkInStock" ? ti1.PushFlag :
                            t.RelBillKey == "StkReceive" ? tr2.PushFlag :
                            t.RelBillKey == "StkOutStock" ? ts3.PushFlag : ta4.PushFlag,
                        SupplierId = t.RelBillKey == "StkInStock" ? tis1.Id : t.RelBillKey == "StkReceive" ? trs2.Id : t.RelBillKey == "StkOutStock" ? trs3.Id : 0,
                        SupplierNumber = t.RelBillKey == "StkInStock" ? tis1.Number : t.RelBillKey == "StkReceive" ? trs2.Number : t.RelBillKey == "StkOutStock" ? trs3.Number : "",
                        SupplierName = t.RelBillKey == "StkInStock" ? tis1.Name : t.RelBillKey == "StkReceive" ? trs2.Name : t.RelBillKey == "StkOutStock" ? trs3.Name : "",
                    })
            )
            .Distinct()
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);
        return entities;
    }

    /// <summary>
    /// 按查询条件导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("exportByQuery")]
    public async Task<FileContentResult> ExportByQuery(StkInReportInput input)
    {
        input.Page = 1;
        input.PageSize = 50000;
        var output = await PageAsync(input);
        var exporter = new ExcelExporter();
        byte[] bytes = await exporter.ExportAsByteArray(output.Items.ToList());
        var fileName = L.Text["入库报表_{0}.xlsx", DateTimeOffset.Now.ToString("yyyyMMddHHmmss")];
        return new FileContentResult(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") { FileDownloadName = fileName };
    }
}