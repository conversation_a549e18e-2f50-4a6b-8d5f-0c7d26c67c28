﻿namespace Neuz.Core.EqualityComparers;

/// <summary>
/// <see cref="EntryEntityBase"/> 相等比较器
/// </summary>
/// <typeparam name="T"></typeparam>
public class EntryEntityEqualityComparer<T> : EqualityComparer<T> where T : EntryEntityBase
{
    private static EntryEntityEqualityComparer<T> _instance;

    /// <inheritdoc />
    public override bool Equals(T x, T y)
    {
        if (x == null && y == null) return true;
        if (x == null || y == null) return false;
        if (x.EntryId == 0 && y.EntryId == 0) return false;
        return x.EntryId == y.EntryId && x.Id == y.Id;
    }

    /// <inheritdoc />
    public override int GetHashCode(T obj)
    {
        return $"{obj.EntryId}_{obj.Id}".GetHashCode();
    }

    /// <summary>
    /// <see cref="EntryEntityBase"/> 相等比较器实例
    /// </summary>
    public static EntryEntityEqualityComparer<T> Instance => _instance ??= new();
}