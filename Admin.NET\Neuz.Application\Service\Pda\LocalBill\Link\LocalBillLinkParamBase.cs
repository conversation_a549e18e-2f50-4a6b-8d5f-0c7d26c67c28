﻿namespace Neuz.Application.Pda.LocalBill.Link;

public class LocalBillLinkParamBase : ILocalBillLinkParam
{
    /// <inheritdoc/>
    public virtual string Key { get; set; }

    /// <inheritdoc/>
    public virtual List<string> SourceBillTypes { get; set; }

    /// <inheritdoc/>
    public virtual List<LocalBillLinkMapping> Mappings { get; set; }

    /// <inheritdoc/>
    public virtual List<LocalBillQtyCalcMapping> CalcMappings { get; set; }

    /// <inheritdoc/>
    public virtual List<LocalBillFilter> Filters { get; set; } = new List<LocalBillFilter>();

    /// <inheritdoc/>
    public virtual List<LocalBillContentInfo> DetailContents { get; set; }

    /// <inheritdoc/>
    public virtual List<LocalBillLinkBarcodeMapping> BarcodeMappings { get; set; }

    /// <inheritdoc/>
    public virtual Type SubmitBillType { get; set; }

    /// <inheritdoc/>
    public virtual List<LocalBillLinkMapping> SubmitMappings { get; set; }

    /// <inheritdoc/>
    public virtual string SourceEntryName { get; set; }

    /// <inheritdoc/>
    public virtual string TargetEntryName { get; set; }

    /// <inheritdoc/>

    public virtual string BarcodeEntryName { get; set; }
}