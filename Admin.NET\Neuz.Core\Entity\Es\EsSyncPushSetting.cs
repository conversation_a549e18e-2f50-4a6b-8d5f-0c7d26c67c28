﻿namespace Neuz.Core.Entity;

/// <summary>
/// 外部系统同步推送设置
/// </summary>
[SugarTable(null, "外部系统同步推送设置")]
public class EsSyncPushSetting : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 255)]
    public string Number { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 255)]
    public string? Name { get; set; }

    /// <summary>
    /// 外部系统类型
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统类型", Length = 255)]
    public string EsType { get; set; }

    /// <summary>
    /// 最后推送时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后推送时间")]
    public DateTime LastPushTime { get; set; }

    /// <summary>
    /// 外部业务对象Key
    /// </summary>
    [SugarColumn(ColumnDescription = "外部业务对象Key", Length = 255)]
    public string EsBizKey { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500)]
    public string? Memo { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(EsSyncPushSettingEntry.Id))]
    public List<EsSyncPushSettingEntry> Entries { get; set; }
}