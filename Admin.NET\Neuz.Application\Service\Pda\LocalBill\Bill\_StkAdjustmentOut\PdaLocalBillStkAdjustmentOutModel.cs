﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.Bill._StkAdjustment;
using Neuz.Application.Pda.LocalBill.Link;
using Neuz.Application.Pda.LocalBill.LocalBillDto;

namespace Neuz.Application.Pda.LocalBill.Bill._StkAdjustmentOut;

/// <summary>
/// 其它出库
/// </summary>
public class PdaLocalBillStkAdjustmentOutModel : PdaLocalBillStkAdjustmentModel
{
    public PdaLocalBillStkAdjustmentOutModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "_StkAdjustmentOut";

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceItem = true,
            IsOverSourceQty = true
        }
    };

    /// <inheritdoc/>
    protected override StkAdjustment SetSubmitObj(long tranId, ILocalBillLinkParam linkParam, Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails)
    {
        var submitObj = base.SetSubmitObj(tranId, linkParam, summaryDetails);
        // 由于这里是出库, 需要传负数
        submitObj.Entries.ForEach(r => { r.Qty = -r.Qty; });
        return submitObj;
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destBillTypeName",
                Caption = L.Text["单据类型"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdAdjustmentBillTypeModel",
                    LookupDataKey = "ScanHead.DestBillType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestBillType", "Number"),
                        new PdaLookupMapping("DestBillTypeName", "Name"),
                    },
                    Properties = { ["EntityName"] = "StkAdjustment", ["Direction"] = "Decrease" }
                },
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "departmentName",
                Caption = L.Text["部门"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdDepartmentModel",
                    LookupDataKey = "ScanHead.Department",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DepartmentId", "Id"),
                        new PdaLookupMapping("DepartmentNumber", "Number"),
                        new PdaLookupMapping("DepartmentName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "outTypeName",
                Caption = L.Text["出库类型"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictOutTypeModel",
                    LookupDataKey = "ScanHead.OutType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("OutTypeId", "Id"),
                        new PdaLookupMapping("OutTypeNumber", "Number"),
                        new PdaLookupMapping("OutTypeName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "expenseName",
                Caption = L.Text["费用项目"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictExpenseModel",
                    LookupDataKey = "ScanHead.Expense",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("ExpenseId", "Id"),
                        new PdaLookupMapping("ExpenseNumber", "Number"),
                        new PdaLookupMapping("ExpenseName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "contractProjectName",
                Caption = L.Text["合同项目名称"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictContractProjectModel",
                    LookupDataKey = "ScanHead.ContractProject",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("ContractProjectId", "Id"),
                        new PdaLookupMapping("ContractProjectNumber", "Number"),
                        new PdaLookupMapping("ContractProjectName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "projectName",
                Caption = L.Text["项目名称"],
                Type = "string",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.ProjectName"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "customerName",
                Caption = L.Text["客户"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdCustomerModel",
                    LookupDataKey = "ScanHead.Customer",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("CustomerId", "Id"),
                        new PdaLookupMapping("CustomerNumber", "Number"),
                        new PdaLookupMapping("CustomerName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "assetCategoryName",
                Caption = L.Text["资产出库类别"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictAssetCategoryModel",
                    LookupDataKey = "ScanHead.AssetCategory",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("AssetCategoryId", "Id"),
                        new PdaLookupMapping("AssetCategoryNumber", "Number"),
                        new PdaLookupMapping("AssetCategoryName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "employeeName",
                Caption = L.Text["领料人"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                IsRequired = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdEmployeeModel",
                    LookupDataKey = "ScanHead.Employee",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("EmployeeId", "Id"),
                        new PdaLookupMapping("EmployeeNumber", "Number"),
                        new PdaLookupMapping("EmployeeName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "memo",
                Caption = L.Text["备注"],
                Type = "string",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Memo"
            },
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "batchNo",
                Caption = L.Text["批号"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "produceDate",
                Caption = L.Text["生产日期"],
                Type = "date",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhAreaName",
                Caption = L.Text["调入库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhLocName",
                Caption = L.Text["调入库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "",
            SourceTitle = "",
            DestKey = "StkAdjustment",
            DestTitle = L.Text["库存调整单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }
}