﻿using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 批号调整单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkBatchAdjust", Order = 100)]
public class StkBatchAdjustService : StkBaseBillService<StkBatchAdjust>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkBatchAdjust);

    /// <summary>
    /// 批号调整单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkBatchAdjustService(IServiceProvider serviceProvider, SqlSugarRepository<StkBatchAdjust> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestBatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "WarehouseNumber",
            "WarehouseName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_SrcBatchNo",
            "Entries_SrcProduceDate",
            "Entries_SrcExpiryDate",
            "Entries_DestBatchNo",
            "Entries_DestProduceDate",
            "Entries_DestExpiryDate",
            "Entries_WhAreaNumber",
            "Entries_WhAreaName",
            "Entries_WhLocNumber",
            "Entries_WhLocName",
            "Entries_Qty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkBatchAdjust entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkBatchAdjust entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkBatchAdjust entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        // // 构建查询条件
        // var exp = new Expressionable<StkInventory>();
        // foreach (var entry in entity.Entries)
        //     exp.Or(u => u.MaterialId == entry.MaterialId && u.BatchNo == entry.DestBatchNo);
        // // 目标批号库存集合
        // var destBatchNoInvList = Rep.Context.Queryable<StkInventory>().Where(exp.ToExpression()).ToList();

        foreach (var entry in entity.Entries)
        {
            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);
            // 判断库区是否已填
            if (entry.WhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1005);
            // 判断库位是否已填
            if (entry.WhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1006);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);

            // 是否启用批号管理
            if (!materialInfo.IsBatchManage) throw Oops.Bah(StkErrorCode.StkBatchAdjust1001, materialInfo.Number);

            // 批号判断
            if (string.IsNullOrEmpty(entry.SrcBatchNo)) throw Oops.Bah(StkErrorCode.StkBatchAdjust1002, materialInfo.Number);
            if (string.IsNullOrEmpty(entry.DestBatchNo)) throw Oops.Bah(StkErrorCode.StkBatchAdjust1003, materialInfo.Number);

            // // 检验目标批号是否在库
            // if (!destBatchNoInvList.Any(u => u.MaterialId == entry.MaterialId && u.BatchNo == entry.DestBatchNo))
            //     throw Oops.Bah(StkErrorCode.StkBatchAdjust1008, materialInfo.Number, entry.DestBatchNo);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.SrcProduceDate == null) throw Oops.Bah(StkErrorCode.StkBatchAdjust1004, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.DestProduceDate == null) throw Oops.Bah(StkErrorCode.StkBatchAdjust1005, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.SrcExpiryDate == null) throw Oops.Bah(StkErrorCode.StkBatchAdjust1006, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.DestExpiryDate == null) throw Oops.Bah(StkErrorCode.StkBatchAdjust1007, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (entry.Qty != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }
    }

    protected override void OnAfterAudit(StkBatchAdjust entity)
    {
        base.OnAfterAudit(entity);

        // 更新条码档案
        UpdateBarcodeInfo(entity);

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);
    }

    protected override void OnBeforeUnAudit(StkBatchAdjust entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkBatchAdjust entity)
    {
        base.OnAfterUnAudit(entity);

        // 回滚条码档案
        if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
            BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

        // TODO：批号档案处理

        // 回滚库存更新
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkBatchAdjust entity)
    {
        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = OpQtyType.Replace,
                CurStatus = barcodeEntry.Barcode.Status,
                BatchNo = entry.DestBatchNo, // 目标批号
                ProduceDate = entry.DestProduceDate, // 目标生产日期
                ExpiryDate = entry.DestExpiryDate, // 目标有效期至
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = null,
                SrcWhLocId = null,
                DestWhAreaId = entry.WhAreaId,
                DestWhLocId = entry.WhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkBatchAdjust entity)
    {
        var changes = entity.Entries.SelectMany(u =>
        {
            var innerChanges = new List<StkInvChange>
            {
                new StkInvChange
                {
                    InvLogType = StkInvLogType.BatchAdjustMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.SrcBatchNo,
                    ProduceDate = u.SrcProduceDate,
                    ExpiryDate = u.SrcExpiryDate,
                    Qty = -u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                },
                new StkInvChange
                {
                    InvLogType = StkInvLogType.BatchAdjustPlus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.DestBatchNo,
                    ProduceDate = u.DestProduceDate,
                    ExpiryDate = u.DestExpiryDate,
                    Qty = u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.WhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                },
            };

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }
}