﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Service.Pda.SplitBarcode;

namespace Neuz.Application.Proj.ProjModifyBarcode;

public class ProjPdaModifyBarcodeData : IPdaData
{
    public string ModelKey { get; set; }
    public long TranId { get; set; }
    public long UserId { get; set; }
    public PdaShow DataShow { get; set; } = new ProjPdaModifyBarcodeShow();
    public bool IsEmptyData()
    {
        return true;
    }

    // 源单数据
    public Dictionary<string, string> BarcodeCells { get; } = new Dictionary<string, string>();

    // 扫描的条码
    public BdBarcode CurBarcode { get; set; }

    /// <summary>
    /// 扫描条码后,能选择的模板
    /// </summary>
    public List<SysReportTemplate> Templates { get; set; } = new List<SysReportTemplate>();

    /// <summary>
    /// 打印模板
    /// </summary>
    public List<PdaColumn> TemplateCells { get; } = new List<PdaColumn>
    {
        new PdaColumn
        {
            Fieldname = "template",
            Caption = L.Text["选择模板"],
            Type = "select",
            Readonly = false,
            IsShow = true,
            FieldDataKey = "template.template",
            Options = new List<PdaColumnOption>()
        }
    };

    // 模板数据
    public Dictionary<string, string> TemplateValues = new Dictionary<string, string>();

    // 选择打印的模板
    public string CurTemplateId { get; set; }

    /// <summary>
    /// 是否已提交(生成条码)
    /// </summary>
    public bool IsSubmit { get; set; }
    
    /// <summary>
    /// 用户默认配置
    /// </summary>
    public ProjPdaModifyBarcodeModelUserConfig UserConfig { get; set; }
}