using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Service.Pda.Enum;

namespace Neuz.Application.Pda.LocalBill;

public class PdaLocalBillModelParams
{
    /// <summary>
    /// ErpKey
    /// </summary>
    public string ErpKey { get; set; }

    /// <summary>
    /// 自动审核
    /// </summary>
    public bool IsAutoAudit { get; set; } = true;

    /// <summary>
    /// 扫描条码仓库优先级
    /// </summary>
    public FirstStockType FirstStockType { get; set; } = FirstStockType.Barcode;

    /// <summary>
    /// 是否允许超源单数量
    /// </summary>
    public bool IsOverSourceQty { get; set; } = false;

    /// <summary>
    /// 是否支持源单外物料
    /// </summary>
    public bool IsOverSourceItem { get; set; } = false;

    /// <summary>
    /// 是否允许扫条码带源单
    /// </summary>
    public bool IsScanBarcodeFindSource { get; set; } = true;

    /// <summary>
    /// 可扣减条码是否弹窗修改数量
    /// </summary>
    public bool IsDeductShowModifyQty { get; set; } = false;

    /// <summary>
    /// 条码操作类型(出入库类型)限制出入库
    /// </summary>
    public bool IsBarcodeOpTypeInOutLimit { get; set; } = false;

    /// <summary>
    /// 是否制作单据 (默认否 - 扫描单据)
    /// </summary>
    public bool IsMakeBill { get; set; } = false;

    /// <summary>
    /// 条码操作类型
    /// </summary>
    public BarOpType OpType { get; set; }

    /// <summary>
    /// 合并单据汇总字段(提交后合并)
    /// </summary>
    public List<PdaLocalBillSummaryBillField> SummaryBillFields { get; set; } = new()
    {
        new() { FieldName = "MaterialId" },
        new() { FieldName = "MaterialNumber" },
        new() { FieldName = "MaterialName" },
        new() { FieldName = "UnitId" },
        new() { FieldName = "UnitNumber" },
        new() { FieldName = "UnitName" },
        new() { FieldName = "BatchNo" },
        new() { FieldName = "ProduceDate" },
        new() { FieldName = "ExpPeriod" },
        new() { FieldName = "ExpUnit" },
        new() { FieldName = "ExpiryDate" },
        new() { FieldName = "WhAreaId" },
        new() { FieldName = "WhAreaNumber" },
        new() { FieldName = "WhAreaName" },
        new() { FieldName = "WhLocId" },
        new() { FieldName = "WhLocNumber" },
        new() { FieldName = "WhLocName" },
        new() { FieldName = "SrcBillId" },
        new() { FieldName = "SourceBillEntryId" },
        new() { FieldName = "SrcBillKey" },
        new() { FieldName = "AuxUnitId" },
        new() { FieldName = "AuxPropValueId" },
    };

    /// <summary>
    /// 合并单据汇总字段(PDA显示合并)
    /// </summary>
    public List<PdaLocalBillSummaryOperationField> SummaryOperationFields { get; set; } = [new() { FieldName = "Material.Number", ScanFieldName = "MaterialNumber" }];

    /// <summary>
    /// 合并单据汇总数量字段(提交后合并)
    /// </summary>
    public List<PdaLocalBillSummaryBillField> SummaryBillQtyFields { get; set; } = new List<PdaLocalBillSummaryBillField>() { new() { FieldName = "AuxQty" } };

    /// <summary>
    /// 扫描操作列表
    /// </summary>
    public List<IPdaLocalBillScanBarcodeOperation> ScanBarcodeOperations { get; set; } = new List<IPdaLocalBillScanBarcodeOperation>();

    /// <summary>
    /// 是否需要先进先出控制
    /// </summary>
    public bool IsFifo { get; set; } = false;

    /// <summary>
    /// 先进先出控制
    /// </summary>
    public PdaFifoHintType FifoHintType { get; set; } = PdaFifoHintType.Error;

    /// <summary>
    /// 是否可扣减标签需要控制匹配源单数量
    /// </summary>
    public bool IsDeductMatchSourceQty { get; set; } = false;

    /// <summary>
    /// 是否允许选择多源单
    /// </summary>
    public bool IsMultiSource { get; set; } = true;

    /// <summary>
    /// 审核成功才算成功, 否则审核失败回滚 (必须开启自动审核才生效)
    /// </summary>
    public bool IsAutoAuditSuccess { get; set; } = true;
}