# Admin.NET PaddleOCR 插件 🔍

这是一个基于PaddleOCR的图像文字识别插件，支持多种OCR模型，提供身份证识别和通用文字识别功能。

## 功能特性 ✨

- 🎯 **多模型支持**: 支持4种不同的OCR模型
- 🔖 **身份证识别**: 专门优化的身份证信息提取
- 📝 **通用文字识别**: 支持各种图片的文字识别
- 📥 **模型管理**: 提供模型下载和状态检查功能
- 🌐 **API接口**: RESTful API接口，易于集成

## 支持的模型 📋

| 模型名称 | 描述 | 适用场景 |
|---------|------|----------|
| `ch_PP-OCRv4` | 中英文模型V4 (轻量版) | 一般场景，速度快 |
| `ch_ppocr_server_v2.0` | 服务器中英文模型v2 | 高精度要求场景 |
| `en_PP-OCRv3` | 英文和数字模型v3 | 英文文档识别 |
| `ch_PP-OCRv4_server` | 服务器中英文模型V4 | 最新高精度版本 |

## API接口 🚀

### 1. 获取可用模型列表
```http
GET /api/PaddleOCR/GetAvailableModels
```

### 2. 身份证识别
```http
POST /api/PaddleOCR/IDCardOCR
Content-Type: multipart/form-data

file: [图片文件]
modelName: [可选，模型名称]
```

### 3. 通用文字识别
```http
POST /api/PaddleOCR/GeneralOCR
Content-Type: multipart/form-data

file: [图片文件]
modelName: [可选，模型名称]
```

### 4. 模型管理接口

#### 获取模型配置信息
```http
GET /api/OCRModelDownload/GetModelConfigs
```

#### 检查模型状态
```http
GET /api/OCRModelDownload/CheckModelStatus?modelKey=ch_PP-OCRv4
```

#### 获取下载链接
```http
GET /api/OCRModelDownload/GetDownloadLinks?modelKey=ch_PP-OCRv4
```

#### 清理模型文件
```http
POST /api/OCRModelDownload/CleanModel?modelKey=ch_PP-OCRv4
```

## 模型安装 📥

### 方法1: 使用API获取下载链接

1. 调用 `/api/OCRModelDownload/GetDownloadLinks` 获取下载链接
2. 手动下载模型文件
3. 解压到指定目录

### 方法2: 手动下载安装

#### 中英文模型V4 (轻量版)
```bash
# 创建目录
mkdir -p "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_PP-OCRv4"

# 下载并解压模型文件
wget https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar
wget https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar
wget https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar
wget https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.7/ppocr/utils/ppocr_keys_v1.txt

# 解压到对应目录
tar -xf ch_PP-OCRv4_det_infer.tar -C "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_PP-OCRv4/"
tar -xf ch_PP-OCRv4_rec_infer.tar -C "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_PP-OCRv4/"
tar -xf ch_ppocr_mobile_v2.0_cls_infer.tar -C "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_PP-OCRv4/"
mv ppocr_keys_v1.txt "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_PP-OCRv4/ppocr_keys.txt"
```

#### 服务器中英文模型v2
```bash
# 创建目录
mkdir -p "Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ch_ppocr_server_v2.0"

# 下载模型文件
wget https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_server_v2.0_det_infer.tar
wget https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_server_v2.0_rec_infer.tar
# ... 其他文件类似
```

## 目录结构 📁

```
Plugins/Admin.NET.Plugin.PaddleOCR/
├── Service/
│   ├── PaddleOCRService.cs          # 主要OCR服务
│   └── OCRModelDownloadService.cs   # 模型下载管理服务
├── Config/
│   └── OCRModelConfig.json          # 模型配置文件
├── OcrModel/                        # 模型文件目录
│   ├── ch_PP-OCRv4/                # 轻量版模型
│   │   ├── ch_PP-OCRv4_det_infer/
│   │   ├── ch_PP-OCRv4_rec_infer/
│   │   ├── ch_ppocr_mobile_v2.0_cls_infer/
│   │   └── ppocr_keys.txt
│   ├── ch_ppocr_server_v2.0/       # 服务器版v2模型
│   ├── en_PP-OCRv3/                # 英文模型v3
│   └── ch_PP-OCRv4_server/         # 服务器版v4模型
└── README.md
```

## 使用示例 💡

### C# 调用示例

```csharp
// 使用HttpClient调用API
using var client = new HttpClient();
using var form = new MultipartFormDataContent();
using var fileContent = new ByteArrayContent(imageBytes);
fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("image/jpeg");
form.Add(fileContent, "file", "image.jpg");
form.Add(new StringContent("ch_PP-OCRv4"), "modelName");

var response = await client.PostAsync("http://localhost:5000/api/PaddleOCR/GeneralOCR", form);
var result = await response.Content.ReadAsStringAsync();
```

### JavaScript 调用示例

```javascript
// 使用fetch调用API
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('modelName', 'ch_PP-OCRv4');

fetch('/api/PaddleOCR/GeneralOCR', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('识别结果:', data);
});
```

## 响应格式 📄

### 成功响应示例
```json
{
    "success": true,
    "usedModel": "ch_PP-OCRv4",
    "modelDescription": "中英文模型V4 - 轻量版，适合一般场景",
    "textBlocks": [
        {
            "text": "识别的文字内容",
            "score": 0.95,
            "boundingBox": {...}
        }
    ],
    "fullText": "完整的识别文本"
}
```

### 错误响应示例
```json
{
    "success": false,
    "message": "指定的模型 'invalid_model' 不可用，可用模型：ch_PP-OCRv4, ch_ppocr_server_v2.0",
    "availableModels": ["ch_PP-OCRv4", "ch_ppocr_server_v2.0"]
}
```

## 注意事项 ⚠️

1. **模型文件大小**: 每个模型包约50-200MB，请确保有足够的磁盘空间
2. **内存要求**: 建议至少4GB可用内存
3. **首次加载**: 模型首次加载可能需要几秒钟时间
4. **并发处理**: 支持多个模型同时加载，但会占用更多内存
5. **文件格式**: 支持常见图片格式（JPG、PNG、BMP等）

## 故障排除 🔧

### 常见问题

1. **模型加载失败**
   - 检查模型文件是否完整下载
   - 确认目录结构是否正确
   - 查看应用程序日志

2. **识别精度不高**
   - 尝试使用服务器版模型
   - 确保图片清晰度足够
   - 考虑图片预处理

3. **内存不足**
   - 减少同时加载的模型数量
   - 增加系统内存
   - 优化图片大小

## 更新日志 📝

### v1.0.0
- ✅ 支持多种OCR模型
- ✅ 身份证识别功能
- ✅ 通用文字识别功能
- ✅ 模型管理API
- ✅ 完整的文档和示例

## 许可证 📄

本插件基于 MIT 许可证开源。 