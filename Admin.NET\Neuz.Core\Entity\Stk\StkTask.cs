﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存任务
/// </summary>
[SugarTable(null, "库存任务")]
[SugarIndex("index_{table}_SBSI", nameof(SrcBillKey), OrderByType.Asc, nameof(SrcBillId), OrderByType.Asc)]
[SugarIndex("index_{table}_SB", nameof(SrcBillNo), OrderByType.Asc)]
public class StkTask : BillEntityBase
{
    /// <summary>
    /// 单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "单据日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    [SugarColumn(ColumnDescription = "任务类型", Length = 80)]
    public StkTaskType TaskType { get; set; }

    /// <summary>
    /// 业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "业务状态")]
    public StkTaskStatus Status { get; set; }

    /// <summary>
    /// 来源单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据类型", Length = 80)]
    public string SrcBillType { get; set; }

    /// <summary>
    /// 来源单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源单据标识", Length = 200)]
    public string SrcBillKey { get; set; }

    /// <summary>
    /// 来源单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据编号", Length = 80)]
    public string SrcBillNo { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id")]
    public long SrcBillId { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkTaskEntry.Id))]
    public List<StkTaskEntry> Entries { get; set; }
}