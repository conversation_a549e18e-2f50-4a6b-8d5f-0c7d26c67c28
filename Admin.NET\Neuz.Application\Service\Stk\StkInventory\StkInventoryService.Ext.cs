﻿namespace Neuz.Application;

/// <summary>
/// 库存服务
/// </summary>
public partial class StkInventoryService
{
    /// <summary>
    /// 获取锁定数量的单据信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("getLockQtyBillInfo")]
    public virtual async Task<List<Dictionary<string, object>>> GetLockQtyBillInfo([FromQuery] IdInput input)
    {
        return await Rep.Context.Queryable<StkTaskEntry>()
            .LeftJoin<StkTask>((te, t) => te.Id == t.Id)
            .Where((te, t) => te.RelLockInvId == input.Id && (te.EntryStatus == StkTaskEntryStatus.UnHandle || te.EntryStatus == StkTaskEntryStatus.Handling))
            .Select((te, t) => new
            {
                te.Id,
                te.EntryId,
                t.<PERSON>,
                te.Seq,
                t.<PERSON>,
                te.Src<PERSON>illEntrySeq,
                LockQty = te.RemainExecQty,
            }).ToDictionaryListAsync();
    }
}