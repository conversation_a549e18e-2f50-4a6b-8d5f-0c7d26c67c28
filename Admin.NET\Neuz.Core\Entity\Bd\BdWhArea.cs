﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库区
/// </summary>
[SugarTable(null, "库区")]
public class BdWhArea : EsBdEntityBase
{
    /// <summary>
    /// 库区类型
    /// </summary>
    [SugarColumn(ColumnDescription = "库区类型")]
    public BdWhAreaType WhAreaType { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }
}