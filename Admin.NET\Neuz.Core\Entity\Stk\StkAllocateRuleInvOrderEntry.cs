﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储分配规则库存排序明细
/// </summary>
[SugarTable(null, "仓储分配规则库存排序明细")]
public class StkAllocateRuleInvOrderEntry : EntryEntityBase
{
    /// <summary>
    /// 字段名
    /// </summary>
    [SugarColumn(ColumnDescription = "字段名", Length = 100)]
    public string FieldName { get; set; }

    /// <summary>
    /// 排序类型
    /// </summary>
    [SugarColumn(ColumnDescription = "排序类型")]
    public string SortType { get; set; }
}