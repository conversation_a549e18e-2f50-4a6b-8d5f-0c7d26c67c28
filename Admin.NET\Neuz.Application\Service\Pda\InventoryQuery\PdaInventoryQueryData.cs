﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy.Dto;

namespace Neuz.Application.Pda.InventoryQuery;

public class PdaInventoryQueryData : IPdaData
{
    public string ModelKey { get; set; }
    public long TranId { get; set; }
    public long UserId { get; set; }
    public PdaShow DataShow { get; set; } = new PdaInventoryQueryShow();
    public bool IsEmptyData()
    {
        return true;
    }

    //源单列值
    public ExtensionObject SourceCells = new ExtensionObject();
    /// <summary>
    /// 源单分页
    /// </summary>
    public PdaApiPagination SourcePage = new PdaApiPagination();

    //源单
    public List<Dictionary<string, object>> Sources = new List<Dictionary<string, object>>();
}