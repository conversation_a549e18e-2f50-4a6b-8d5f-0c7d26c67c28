﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Proxy.Dto;

namespace Neuz.Application.Pda.Proxy;

/// <summary>
/// PDA 通用单据Api
/// </summary>
[ApiDescriptionSettings("PDA", Name = "pdaBillBaseService", Order = 300)]
public class PdaApiBillBaseService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="serviceProvider"></param>
    public PdaApiBillBaseService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("scanBarcode")]
    public async Task<dynamic> ScanBarcode([FromBody] PdaScanBarcodeInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        //if (string.IsNullOrEmpty(barcode)) throw Oops.Bah(PdaErrorCode.PDA_1012);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        if (billModel == null) throw Oops.Bah("Model需继承接口IPdaModelBill");
        await billModel.ScanBarcode(input.TranId, input.Barcode, input.IsRepeat, input.Ext);
        return billData.DataShow;
    }

    /// <summary>
    /// Lookup
    /// </summary>
    /// <returns></returns>
    [HttpPost("lookupQuery")]
    public async Task<List<PdaLookupOutput>> LookupQuery(PdaApiLookupQueryInput input)
    {
        // IdInput 的Id 是明细Id
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        var data = await billModel.LookupQuery(input.TranId, input.LookupKey, input.LookupValue);
        return data;
    }

    /// <summary>
    /// 选择Lookup
    /// </summary>
    /// <returns></returns>
    [HttpPost("selectLookupData")]
    public async Task<dynamic> SelectLookupData(PdaApiSelectLookupDataInput input)
    {
        // IdInput 的Id 是明细Id
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        await billModel.SelectLookupData(input.TranId, input.LookupKey, input.ValueKey);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        return billData.DataShow;
    }

    /// <summary>
    /// 更新值
    /// </summary>
    /// <returns></returns>
    [HttpPost("selectFieldData")]
    public async Task<dynamic> SelectFieldData(PdaApiSelectFieldDataInput input)
    {
        // IdInput 的Id 是明细Id
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        await billModel.SelectFieldData(input.TranId, input.FieldKey, input.FieldValue);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        return billData.DataShow;
    }

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <returns></returns>
    [HttpPost("deleteData")]
    public async Task<dynamic> DeleteData(PdaApiDeleteDataInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        await billModel.DeleteData(input.TranId, input.DataKey, input.ValueKey);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        return billData.DataShow;
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <returns></returns>
    /// 在需要确认返回,添加
    //PdaExtrasRestfulResult<BarBarcode> result = new PdaExtrasRestfulResult<BarBarcode>
    //{
    //    Code = (int)PdaRestfulCode.P999,
    //    Message = null,
    //    Data = barcode,
    //    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
    //};
    //UnifyContext.Fill(result);
    [HttpPost("submit")]
    public async Task<dynamic> Submit([FromBody] PdaApiSubmitInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        await billModel.Submit(input.TranId, input.SubmitKey, input.SubmitData, input.IsRepeat);
        return billData.DataShow;
    }

    /// <summary>
    /// 提交返回数据 (不刷新BillData)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("submitReturnData")]
    public async Task<dynamic> SubmitReturnData([FromBody] PdaApiSubmitInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        return await billModel.SubmitReturnData(input.TranId, input.SubmitKey, input.SubmitData, input.IsRepeat);
    }

    /// <summary>
    /// Lb查询Lookup数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("localBillLookupQuery")]
    public async Task<dynamic> LocalBillLookupQuery([FromBody] PdaLocalBillPageInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        bool inheritsFromPdaElModelBillBase = InheritsFromGenericType(billModel.GetType(), typeof(PdaLocalBillModelBillBase<,>));
        if (!inheritsFromPdaElModelBillBase)
            throw Oops.Bah("Model必须继承PdaLockBillModelBillBase");
        var method = billModel.GetType().GetMethod("LocalBillLookupQuery");
        var lookupOutpust = await ((dynamic)method.Invoke(billModel, new object[] { input })).ConfigureAwait(false);
        return await Task.FromResult(lookupOutpust);
    }

    /// <summary>
    /// LocalBill Lookup选择
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("localBillLookupSelect")]
    public async Task<dynamic> LocalBillLookupSelect([FromBody] PdaLocalBillLookSelectInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        bool inheritsFromPdaElModelBillBase = InheritsFromGenericType(billModel.GetType(), typeof(PdaLocalBillModelBillBase<,>));
        if (!inheritsFromPdaElModelBillBase)
            throw Oops.Bah("Model必须继承PdaLocalBillModelBillBase");
        var method = billModel.GetType().GetMethod("LocalBillLookupSelect");
        var billData = await ((dynamic)method.Invoke(billModel, new object[] { input })).ConfigureAwait(false);
        return billData.DataShow;
    }

    /// <summary>
    /// LocalBill 编辑数据后
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("localBillSelectFieldData")]
    public async Task<dynamic> LocalBillSelectFieldData([FromBody] PdaLocalBillSelectDataInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        bool inheritsFromPdaElModelBillBase = InheritsFromGenericType(billModel.GetType(), typeof(PdaLocalBillModelBillBase<,>));
        if (!inheritsFromPdaElModelBillBase)
            throw Oops.Bah("Model必须继承PdaLocalBillModelBillBase");
        var method = billModel.GetType().GetMethod("LocalBillSelectFieldData");
        var billData = await ((dynamic)method.Invoke(billModel, new object[] { input })).ConfigureAwait(false);
        return billData.DataShow;
    }

    /// <summary>
    /// LocalBill 编辑数据后
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("localBillListAdd")]
    public async Task<dynamic> LocalBillListAdd([FromBody] PdaLocalBillListAddInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        bool inheritsFromPdaElModelBillBase = InheritsFromGenericType(billModel.GetType(), typeof(PdaLocalBillModelBillBase<,>));
        if (!inheritsFromPdaElModelBillBase)
            throw Oops.Bah("Model必须继承PdaLocalBillModelBillBase");
        var method = billModel.GetType().GetMethod("LocalBillListAdd");
        var billData = await ((dynamic)method.Invoke(billModel, new object[] { input })).ConfigureAwait(false);
        return billData.DataShow;
    }

    /// <summary>
    /// LocalBill 编辑数据后
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("localBillListDelete")]
    public async Task<dynamic> LocalBillListDelete([FromBody] PdaLocalBillListDeleteInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billModel = pdaCacheService.GetPdaModelBill(input.Key);
        bool inheritsFromPdaElModelBillBase = InheritsFromGenericType(billModel.GetType(), typeof(PdaLocalBillModelBillBase<,>));
        if (!inheritsFromPdaElModelBillBase)
            throw Oops.Bah("Model必须继承PdaLocalBillModelBillBase");
        var method = billModel.GetType().GetMethod("LocalBillListDelete");
        var billData = await ((dynamic)method.Invoke(billModel, new object[] { input })).ConfigureAwait(false);
        return billData.DataShow;
    }

    /// <summary>
    /// 判断是否继承PdaElModelBillBase
    /// </summary>
    /// <param name="currentType"></param>
    /// <param name="genericTypeDefinition"></param>
    /// <returns></returns>
    private bool InheritsFromGenericType(Type currentType, Type genericTypeDefinition)
    {
        // 直接继承
        if (currentType.IsGenericType && currentType.GetGenericTypeDefinition() == genericTypeDefinition)
        {
            return true;
        }

        // 检查基类
        if (currentType.BaseType != null)
        {
            if (InheritsFromGenericType(currentType.BaseType, genericTypeDefinition))
            {
                return true;
            }
        }

        // 检查接口
        foreach (var interfaceType in currentType.GetInterfaces())
        {
            if (interfaceType.IsGenericType && interfaceType.GetGenericTypeDefinition() == genericTypeDefinition)
            {
                return true;
            }
        }

        // 没有找到继承关系
        return false;
    }
}