﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案日志
/// </summary>
[SugarTable(null, "条码档案日志")]
[SugarIndex("index_{table}_B", nameof(Barcode), OrderByType.Asc)]
public class BdBarcodeLog : EntityTenant
{
    /// <summary>
    /// 操作事务Id
    /// </summary>
    /// <remarks>
    /// （Pda事务Id）
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作事务Id")]
    public long? OpTranId { get; set; }

    /// <summary>
    /// 条码档案Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码档案Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 条码原数量
    /// </summary>
    [SugarColumn(ColumnDescription = "条码原数量")]
    public decimal OriginQty { get; set; }

    /// <summary>
    /// 操作数量
    /// </summary>
    /// <remarks>
    /// 表示操作的结果数量
    /// </remarks>
    [SugarColumn(ColumnDescription = "操作数量")]
    public decimal OpQty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 原状态
    /// </summary>
    [SugarColumn(ColumnDescription = "原状态")]
    public BdBarcodeStatus OriginStatus { get; set; }

    /// <summary>
    /// 当前状态
    /// </summary>
    [SugarColumn(ColumnDescription = "当前状态")]
    public BdBarcodeStatus CurStatus { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 关联单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "关联单据标识", Length = 200)]
    public string? RelBillKey { get; set; }

    /// <summary>
    /// 关联单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据Id")]
    public long? RelBillId { get; set; }

    /// <summary>
    /// 关联单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据分录Id")]
    public long? RelBillEntryId { get; set; }

    /// <summary>
    /// 关联单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据编号", Length = 80)]
    public string? RelBillNo { get; set; }

    /// <summary>
    /// 关联单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据类型", Length = 80)]
    public string? RelBillType { get; set; }

    /// <summary>
    /// 关联单据分录行号
    /// </summary>
    [SugarColumn(ColumnDescription = "关联单据分录行号")]
    public int? RelBillEntrySeq { get; set; }

    /// <summary>
    /// 来源库区Id
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调出库位的库区，否则通常为 null
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源库区Id")]
    public long? SrcWhAreaId { get; set; }

    /// <summary>
    /// 来源库区
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调出库位的库区，否则通常为 null
    /// </remarks>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea SrcWhArea { get; set; }

    /// <summary>
    /// 来源库位Id
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调出库位，否则通常为 null
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源库位Id")]
    public long? SrcWhLocId { get; set; }

    /// <summary>
    /// 来源库位
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调出库位，否则通常为 null
    /// </remarks>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc SrcWhLoc { get; set; }

    /// <summary>
    /// 目标库区Id
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调入库位的库区，否则记录业务发生的 库位的库区
    /// </remarks>
    [SugarColumn(ColumnDescription = "目标库区Id")]
    public long? DestWhAreaId { get; set; }

    /// <summary>
    /// 目标库区
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调入库位的库区，否则记录业务发生的 库位的库区
    /// </remarks>
    [Navigate(NavigateType.OneToOne, nameof(DestWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea DestWhArea { get; set; }

    /// <summary>
    /// 目标库位Id
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调入库位，否则记录业务发生的 库位
    /// </remarks>
    [SugarColumn(ColumnDescription = "目标库位Id")]
    public long? DestWhLocId { get; set; }

    /// <summary>
    /// 目标库位
    /// </summary>
    /// <remarks>
    /// 调拨业务时，此字段记录 调入库位，否则记录业务发生的 库位
    /// </remarks>
    [Navigate(NavigateType.OneToOne, nameof(DestWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc DestWhLoc { get; set; }

    /// <summary>
    /// 原条码档案数据Json
    /// </summary>
    [SugarColumn(ColumnDescription = "原条码档案数据Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string OriginBarcodeJson { get; set; }
}