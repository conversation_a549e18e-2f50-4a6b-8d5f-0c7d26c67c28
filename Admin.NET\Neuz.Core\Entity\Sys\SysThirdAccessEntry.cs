﻿namespace Neuz.Core.Entity;

/// <summary>
/// 第三方系统登录身份表明细
/// </summary>
[SugarTable(null, "第三方系统登录身份表明细")]
[SysTable]
public class SysThirdAccessEntry : EntryEntityBase
{
    /// <summary>
    /// 第三方系统用户账号
    /// </summary>
    [SugarColumn(ColumnDescription = "第三方系统用户账号", Length = 128)]
    public string ThirdUserAccount { get; set; }

    /// <summary>
    /// 绑定用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "绑定用户Id")]
    public long BindUserId { get; set; }

    /// <summary>
    /// 绑定用户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(BindUserId))]
    public SysUser BindUser { get; set; }
}