﻿using Neuz.Application.Model;
using Newtonsoft.Json.Converters;

namespace Neuz.Application;

/// <summary>
/// Web端表格默认配置输入参数
/// </summary>
public class StgWebTableDefaultInput
{
    /// <summary>
    /// 表格Id
    /// </summary>
    public string TableId { get; set; }

    /// <summary>
    /// 默认列表列配置集合
    /// </summary>
    public List<DefaultListColumn> ListColumns { get; set; } = new();

    /// <summary>
    /// 默认搜索列配置集合
    /// </summary>
    public List<LiteSearchColumn> SearchColumns { get; set; } = new();
}

/// <summary>
/// 默认的列配置信息
/// </summary>
public class DefaultListColumn
{
    /// <summary>
    /// 字段名
    /// </summary>
    [JsonConverter(typeof(CamelCaseValueConverter))]
    public string FieldName { get; set; }

    /// <summary>
    /// 字段标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 列宽度
    /// </summary>
    public int? Width { get; set; }

    /// <summary>
    /// 格式化类型
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public FormatType? Format { get; set; }

    /// <summary>
    /// 选项类型
    /// </summary>
    /// <remarks>
    /// 后端定义的枚举类型，如：<see cref="BarcodeType"/>，则值为枚举名称 BarcodeType
    /// </remarks>
    public string OptionType { get; set; }

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool? Visible { get; set; }

    /// <summary>
    /// 可排序
    /// </summary>
    public bool? Sortable { get; set; }
}