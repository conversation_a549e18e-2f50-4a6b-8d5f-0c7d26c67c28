﻿namespace Neuz.Core.Entity;

/// <summary>
/// 出库单条码明细
/// </summary>
[SugarTable(null, "出库单条码明细")]
public class StkOutStockBarcodeEntry : EntryEntityBase
{
    /// <summary>
    /// 条码档案Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码档案Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码档案
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(BarcodeId))]
    [CustomSerializeFields(false, "Id", "Barcode", nameof(BdBarcode.Status))]
    public BdBarcode Barcode { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "容器Id")]
    public long? ContainerId { get; set; }

    /// <summary>
    /// 容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer? Container { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 辅助数量
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助数量", DefaultValue = "(0)")]
    public decimal AuxQty { get; set; }

    /// <summary>
    /// 关联明细Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联明细Id")]
    public long RelEntryId { get; set; }

    /// <summary>
    /// 关联明细序号
    /// </summary>
    [SugarColumn(ColumnDescription = "关联明细序号")]
    public int RelEntrySeq { get; set; }
}