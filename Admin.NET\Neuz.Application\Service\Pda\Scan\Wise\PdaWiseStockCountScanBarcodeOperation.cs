﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Proxy;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.Scan.Wise;

public class PdaWiseStockCountScanBarcodeOperation : PdaScanBarcodeOperationBase
{
    public PdaWiseStockCountScanBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>();
        var billData = (PdaStockCountData)pdaCacheService.GetBillData(args.Key, args.TranId);
        var pdaModel = (PdaStockCountModel)pdaCacheService.GetPdaModel(billData.ModelKey);
        if (billData.ScanDetails.Count <= 0) throw Oops.Bah(L.Text["请先选择盘点方案"]);
        var barcodeService = App.GetService<BarBarcodeService>(_serviceProvider);
        var barcode = barcodeService.GetBarcodeAsync(args.BarcodeString).Result;
        if (barcode == null) return;
        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        args.Barcodes.Add(barcode);
        if (!args.Properties.ContainsKey("FirstStockType")) throw Oops.Bah(PdaErrorCode.Pda1031);
        if (!args.Properties.ContainsKey("ScanBarcodeType")) throw Oops.Bah(PdaErrorCode.Pda1034);
        var scanBarcodeType = GetScanBarcodeType(args.Properties["ScanBarcodeType"] + "");
        //可扣减条码不应直接扫描,前端确认后才扫描
        //if (barcode.BarcodeType == BarcodeType.Deduct)
        //{
        //    //如果返回只有一个条码,并且为一物一码
        //    PdaExtrasRestfulResult<BarBarcode> result = new PdaExtrasRestfulResult<BarBarcode>
        //    {
        //        Code = (int)PdaRestfulCode.P101,
        //        Message = null,
        //        Data = barcode,
        //        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        //    };
        //    UnifyContext.Fill(result);
        //}
        //如果是选择弹窗修改
        if (scanBarcodeType == ScanBarcodeType.Modify)
        {
            PdaExtrasRestfulResult<BarBarcode> result = new PdaExtrasRestfulResult<BarBarcode>
            {
                Code = (int)PdaRestfulCode.P101,
                Message = null,
                Data = barcode,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);

        }
        else
        {
            pdaModel.ScanBarcode(args.TranId, Key, barcode, args.Properties["FirstStockType"] + "");
        }
        args.IsResult = true;
    }

    private ScanBarcodeType GetScanBarcodeType(string scanBarcodeType)
    {
        switch (scanBarcodeType)
        {
            case "1":
                return ScanBarcodeType.Standard;
            case "2":
                return ScanBarcodeType.Modify;
            default:
                throw Oops.Bah(L.Text["不支持的仓库优先类型"]);
        }
    }
}