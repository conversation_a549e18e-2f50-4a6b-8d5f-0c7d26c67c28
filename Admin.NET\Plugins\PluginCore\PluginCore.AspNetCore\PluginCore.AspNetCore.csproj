﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
		<PackageId>PluginCore.AspNetCore</PackageId>
		<Version>1.4.3</Version>
		<Company>yiyun</Company>
		<Authors>yiyun</Authors>
		<Description>ASP.NET Core lightweight plugin framework</Description>
		<Copyright>Copyright (c) 2021-present yiyun</Copyright>
		<RepositoryUrl>https://github.com/yiyungent/PluginCore</RepositoryUrl>
		<PackageLicenseUrl>https://github.com/yiyungent/PluginCore/blob/main/LICENSE</PackageLicenseUrl>
		<PackageTags>PluginCore PluginCore.AspNetCore</PackageTags>
		<PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<!-- 方便开发debug,与发布到nuget -->
	<ItemGroup Condition="'$(Configuration)' == 'Release'">
		<PackageReference Include="PluginCore" Version="2.2.5" />
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)' == 'Debug'">
		<ProjectReference Include="..\PluginCore\PluginCore.csproj" />
	</ItemGroup>

	<ItemGroup Condition="'$(Configuration)' == 'Release'">
		<PackageReference Include="PluginCore.IPlugins.AspNetCore" Version="0.1.1" />
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)' == 'Debug'">
		<ProjectReference Include="..\PluginCore.IPlugins.AspNetCore\PluginCore.IPlugins.AspNetCore.csproj" />
	</ItemGroup>

	<!--<ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="5.0.0" />
    <PackageReference Include="System.Text.Json" Version="5.0.2" />
  </ItemGroup>-->

	<!-- 生成注释xml -->
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|netcoreapp3.1|AnyCPU'">
		<DocumentationFile>bin\Release\netcoreapp3.1\PluginCore.AspNetCore.xml</DocumentationFile>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net5.0|AnyCPU'">
		<DocumentationFile>bin\Release\net5.0\PluginCore.AspNetCore.xml</DocumentationFile>
	</PropertyGroup>

	<!-- 0.4.0 : 支持嵌入式 前端 (打包进dll) -->
	<ItemGroup>
		<EmbeddedResource Include="node_modules/plugincore-admin-frontend/dist/**/**/*" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="node_modules\**" />
	  <EmbeddedResource Remove="node_modules\**" />
	  <None Remove="node_modules\**" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Controllers忽略\AppCenterController.cs" />
	  <Compile Remove="Controllers忽略\DebugController.cs" />
	  <Compile Remove="Controllers忽略\HomeController.cs" />
	  <Compile Remove="Controllers忽略\PluginsController.cs" />
	  <Compile Remove="Controllers忽略\PluginWidgetController.cs" />
	  <Compile Remove="Controllers忽略\UserController.cs" />
	</ItemGroup>
	<ItemGroup>
	  <None Remove="package-lock.json" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Controllers忽略\" />
	</ItemGroup>

	<!-- 将 PluginCoreAdmin\*\* 与 readme.txt 复制到build后文件夹，且加入 nupkg -->
	<!-- 从 0.2.0 开始, 支持远程前端，本地前端不再必要 -->
	<!--<ItemGroup>
    <Content Include="PluginCoreAdmin\*">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <Content Include="PluginCoreAdmin\*\*">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <Content Include="PluginCoreAdmin\*\*\*">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
    <Content Include="readme.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>-->

	<!-- Use Visual Studio npm package if it is installed. -->
	<PropertyGroup Condition="Exists('$(VsInstallRoot)\Web\External\npm.cmd')">
		<Path>$(Path)$(VsInstallRoot)\Web\External\;</Path>
	</PropertyGroup>

	<Target Name="NpmInstall" BeforeTargets="Build">
		<Exec Command="npm install" EnvironmentVariables="PATH=$(Path.Replace(';', '%3B'))" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js/npm is required to build this project. To continue, please install Node.js from https://nodejs.org/ or Visual Studio Installer, and then restart your command prompt or IDE." />
	</Target>

</Project>
