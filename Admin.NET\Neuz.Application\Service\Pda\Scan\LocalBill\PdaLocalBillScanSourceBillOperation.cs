﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill;

/// <summary>
/// 扫描带源单
/// </summary>
public class PdaLocalBillScanSourceBillOperation : PdaScanBarcodeOperationBase
{
    public PdaLocalBillScanSourceBillOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var dataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
        var billData = (PdaLocalBillData)dataCacheService.GetBillData(args.Key, args.TranId);
        var billModel = (IPdaLocalBillModel)dataCacheService.GetPdaModel(billData.ModelKey);
        var bill = billModel.GetBillForBillNo(new PdaLocalBillGetBillForBillNoInput
        {
            Key = args.Key,
            TranId = args.TranId,
            BillNo = args.BarcodeString
        }).Result;
        if (bill == null) return;

        args.IsResult = true;
    }
}