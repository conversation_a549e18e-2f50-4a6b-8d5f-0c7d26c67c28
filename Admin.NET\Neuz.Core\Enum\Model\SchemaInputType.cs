﻿using Furion.DependencyInjection;

namespace Neuz.Core.Enum;

/// <summary>
/// 条码编辑类型
/// </summary>
[SuppressSniffer]
public enum SchemaInputType
{
    ///// <summary>
    ///// 字符串
    ///// </summary>
    [Description("input")]
    Input,

    ///// <summary>
    ///// 日期类型(未支持,日期类型都用dateRange)
    ///// </summary>
    [Description("date")]
    Date,

    ///// <summary>
    ///// 日期范围
    ///// </summary>
    [Description("daterange")] // 前端的类型为全小写
    DateRange,

    ///// <summary>
    ///// 下拉(未支持)
    ///// </summary>
    [Description("select")]
    Select,

    ///// <summary>
    ///// 未知类型
    ///// </summary>
    [Description("cascader")]
    Cascader
}