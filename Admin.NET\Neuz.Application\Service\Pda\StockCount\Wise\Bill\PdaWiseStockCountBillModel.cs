﻿using Furion.Localization;
using Neuz.Application.Adapter.K3Wise;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.StockCount.Data;

namespace Neuz.Application.Pda.StockCount.Wise.Bill;

public class PdaWiseStockCountBillModel : PdaStockCountModel
{
    private ErpInterface ErpInterface => App.GetService<ErpInterface>(_serviceProvider);

    public override string Key { get; set; } = "WiseStockCount";

    public override string ErpKey => "K3Wise";

    public override List<PdaLookupOutput> QueryLookupData(long tranId, string lookupKey, string lookupValue)
    {
        var key = lookupKey;
        if (lookupKey == "StockCount")
            key = "WiseStockCount";
        return base.QueryLookupData(tranId, key, lookupValue);
    }

    public override bool SelectLookupData(long tranId, string key, string lookupKey, string valueKey, string lookupDataKey, QueryType queryType = QueryType.Id)
    {
        var lk = lookupKey;
        if (lookupKey == "StockCount")
            lk = "WiseStockCount";
        return base.SelectLookupData(tranId, key, lk, valueKey, lookupDataKey, queryType);
    }

    public override List<string> SummaryBillFields => new List<string>() { "MaterialId", "UnitId", "BatchNo", "FMTONo", "ProduceDate", "ExpPeriod", "AuxPropId", "FSupplyId", "StockId", "StockLocId" };

    //由于条码没有计划跟踪单号和供应商,所以 FMTONo, FSupplyId暂时忽略,以后再想, 辅助属性AuxPropId在条码是'',而单据是0也先忽略
    public override List<string> SummaryOperationFields => new List<string>() { "MaterialId", "UnitId", "BatchNo", "ProduceDate", "ExpPeriod", "StockId", "StockLocId" };

    protected override Dictionary<long, PdaBillModelBase.PdaBarcodeInfo> GetSubmitWriteBackBillInfo(long tranId, PdaSubmitMessage pdaSubmitMessage, Dictionary<string, PdaSummaryInfo> summaryDetails)
    {
        Dictionary<long, PdaBillModelBase.PdaBarcodeInfo> infos = new Dictionary<long, PdaBillModelBase.PdaBarcodeInfo>();
        //这里主要是为了取提交的分录序号,接提交顺序获取
        int index = 0;
        foreach (PdaSummaryInfo summaryInfo in summaryDetails.Values)
        {
            index++;
            foreach (PdaBarcode pdaBarcode in summaryInfo.PdaBarcodes)
            {
                if (!infos.ContainsKey(pdaBarcode.Barcode.Id))
                {
                    infos.Add(pdaBarcode.Barcode.Id, new PdaBillModelBase.PdaBarcodeInfo()
                    {
                        PdaBarcode = pdaBarcode,
                        TargetBillKey = ((PdaStockCountBillSchema)BillSchema).BillLink.DestKey,
                        TargetBillId = pdaSubmitMessage.InterId,
                        TargetBillEntryId = index + "",
                        TargetBillNo = pdaSubmitMessage.BillNo,
                        TargetBillEntrySeq = null
                    });
                }
                else
                {
                    infos[pdaBarcode.Barcode.Id].PdaBarcode.CalcBarcode.ScanQty += pdaBarcode.CalcBarcode.ScanQty;
                }
            }
        }

        return infos;
    }

    public override PdaSubmitMessage SaveBill(long tranId, string billLinkSourceKey, string billLinkDestKey, int billLinkRob, DataTable submitTable)
    {
        var interIds = submitTable.AsEnumerable().Where(r => !string.IsNullOrEmpty(r["FInterId"] + "")).Select(r => $"{r["FInterId"]}").Distinct().ToList();
        if (interIds.Count <= 0) throw Oops.Bah(L.Text["没有明细行"]);
        if (interIds.Count > 1) throw Oops.Bah(L.Text["盘点只能提交一个盘点单"]);
        var interId = interIds.First();
        var logRep = App.GetService<SqlSugarRepository<BarBarcodeLog>>(_serviceProvider);
        var log = logRep.GetFirst(r => r.TargetBillId == interId && r.OpType == BarOpType.StockCount);
        var result = ErpInterface.SaveStockCount(log == null ? true : false, submitTable);
        var submitMessage = result.Adapt<PdaSubmitMessage>();
        return submitMessage;
    }

    public override PdaSubmitMessage ResetBill(string billId)
    {
        var result = ErpInterface.ResetStockCount(billId);
        var submitMessage = result.Adapt<PdaSubmitMessage>();
        return submitMessage;
    }

    public override void SetSubmitRowValue(PdaStockCountData stockCountData, DataRow newRow, PdaStockCountScanHead headData, PdaSummaryInfo summaryInfo)
    {
        newRow["FInterId"] = summaryInfo.Values.SourceBillId;
        newRow["FItemId"] = summaryInfo.Values.MaterialId;
        newRow["FBrNo"] = summaryInfo.Values["FBrNo"];
        newRow["FBatchNo"] = summaryInfo.Values.BatchNo ?? "";
        newRow["FMTONo"] = summaryInfo.Values["FMTONo"] ?? "";
        newRow["FStockId"] = summaryInfo.Values.StockId;
        newRow["FStockPlaceId"] = summaryInfo.Values.StockLocId;
        newRow["FKFDate"] = summaryInfo.Values.ProduceDate == null ? "" : summaryInfo.Values.ProduceDate.Value.Date.ToString("yyyy-MM-dd");
        newRow["FKFPeriod"] = summaryInfo.Values.ExpPeriod;
        newRow["FUnitId"] = summaryInfo.Values.UnitId;
        newRow["FSecUnitId"] = summaryInfo.Values["FSecUnitId"] == null ? 0 : summaryInfo.Values["FSecUnitId"];
        newRow["FSupplyId"] = summaryInfo.Values["FSupplyId"] == null ? 0 : summaryInfo.Values["FSupplyId"];
        newRow["FAuxQty"] = summaryInfo.Values.Qty;
        newRow["FAuxCheckQty"] = summaryInfo.ScanQty;
        newRow["FTranType"] = summaryInfo.Values["FTranType"];
        newRow["FCoefficient"] = summaryInfo.Values["FCoefficient"] == null ? 1 : summaryInfo.Values["FCoefficient"];
        newRow["FSecCoefficient"] = summaryInfo.Values["FSecCoefficient"] == null ? 1 : summaryInfo.Values["FSecCoefficient"];
        newRow["FProcessID"] = summaryInfo.Values["SourceBillNo"];
    }

    public override DataTable CreateSubmitTable()
    {
        DataTable submitTable = new DataTable();
        submitTable.Columns.Add("FInterId");
        submitTable.Columns.Add("FItemId");
        submitTable.Columns.Add("FBrNo");
        submitTable.Columns.Add("FBatchNo");
        submitTable.Columns.Add("FMTONo");
        submitTable.Columns.Add("FStockId");
        submitTable.Columns.Add("FStockPlaceId");
        submitTable.Columns.Add("FKFPeriod");
        submitTable.Columns.Add("FKFDate");
        submitTable.Columns.Add("FUnitId");
        submitTable.Columns.Add("FSecUnitId");
        submitTable.Columns.Add("FSupplyId");
        submitTable.Columns.Add("FAuxQty");
        submitTable.Columns.Add("FAuxCheckQty");
        submitTable.Columns.Add("FTranType");
        submitTable.Columns.Add("FCoefficient");
        submitTable.Columns.Add("FSecCoefficient");
        submitTable.Columns.Add("FProcessID");
        return submitTable;
    }

    public PdaWiseStockCountBillModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc />
    protected override bool GetIsCalcBarcodeFieldEmpty(string summaryBillField, object value)
    {
        var valueStr = value + "";
        return string.IsNullOrEmpty(valueStr) || (decimal.TryParse(valueStr, out var id) && id == 0);
    }
}