﻿using Furion;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Adapter.K3Cloud.ApiClient;
using Neuz.Application.Adapter.K3Cloud.ApiClient.Param;
using Xunit;
using Xunit.Abstractions;

namespace Neuz.UnitTest;

public class K3CloudClientTest
{
    private readonly ITestOutputHelper _output;
    private readonly ILogger<K3CloudClient> _logger;

    //private const string URL = "http://116.228.101.98:8088/K3Cloud";
    //private const string ACCOUNT_NUMBER = "BLJ0106";
    //private const string USER_NAME = "WMS";
    //private const string PASSWORD = "123456";
    private const string URL = "http://171.115.221.107:10008/k3cloud/";
    private const string ACCOUNT_NUMBER = "001";
    private const string USER_NAME = "demo";
    private const string PASSWORD = "********";

    private readonly LoginInfo _loginInfo = new() { Url = URL, AccountNumber = ACCOUNT_NUMBER, UserName = USER_NAME, Password = PASSWORD };

    public K3CloudClientTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;
        _logger = App.GetService<ILogger<K3CloudClient>>();
    }

    [Fact]
    public async Task QueryBillDataTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "STK_InStock",
            FieldKeys = new List<string> { "FMaterialId.FNumber as aa", "FDate", "FRealQty", "FGiveAway" },
            Filters = new List<QueryFilter>
            {
                new("FMaterialId.FNumber", QueryType.Contains, "20"),
                new("FDate", QueryType.LessThan, DateTime.Now)
            },
            PageSize = 10
        });
    }

    [Fact]
    public async Task QueryBillData2Test()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "STK_InStock",
            FieldKeys = new List<string> { "FBillNo", "FMaterialId.FNumber as FMaterialIdNumber", "FDate", "FStockId", "FStockId.FNumber", "FStockId.FName", "FStockLocId.FNumber", "FStockLocId.FName" },
            Filters = new List<QueryFilter>
            {
                new("FStockLocId", QueryType.NotEquals, 0),
                new("FBillNo", QueryType.Equals, "CGRK00009"),
            },
            PageSize = 10
        });
    }

    [Fact]
    public async Task QueryStkInventoryTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "STK_Inventory",
            FieldKeys = new List<string>
            {
                "FMaterialId", "FMaterialId.FNumber as FMaterialIdNumber", "FMaterialId.FName as FMaterialIdName", "FStockId", "FStockId.FNumber", "FStockId.FName",
                "FProduceDate", "FExpiryDate", "FBaseQty", "FQty", "FStockUnitId", "FBaseUnitId",
            },
            Filters = new List<QueryFilter>
            {
                new("FBaseQty", QueryType.GreaterThan, 0),
            },
            Orders = new List<QueryOrder>
            {
                new("FProduceDate"), new("FExpiryDate")
            },
            PageSize = 10
        });
    }

    [Fact]
    public async Task QueryMaterialTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "BD_MATERIAL",
            FieldKeys = new List<string> { "FMaterialId", "FNumber", },
            Filters = new List<QueryFilter>
            {
                new("FMaterialId", QueryType.Equals, "#FMasterId"),
            },
            Orders = new List<QueryOrder> { new("FNumber") },
            PageNo = 2,
            PageSize = 10
        });
    }

    [Fact]
    public async Task QueryStkStockStockLocTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "BD_StockStockLoc",
            FieldKeys = new List<string>
            {
                "FStockId", "FStockIdNumber", "FStockIdName",
                "FStockLocId", "FStockLocIdNumber", "FStockLocIdName",
            },
            Filters = new List<QueryFilter>
            {
                new("FStockId", QueryType.Equals, "#FStockId"),
                new("FStockIdNumber", QueryType.Contains, 10),
            },
            Orders = new List<QueryOrder>
            {
                new("FStockIdNumber"), new("FStockIdName")
            },
            PageSize = 10
        });
    }

    [Fact]
    public async Task QueryStkMiscellaneousTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QueryBillData(new QueryBillParam
        {
            FormId = "STK_MISCELLANEOUS",
            FieldKeys = new List<string>
            {
                "FBillNo", "FMATERIALID.FNumber AS FMATERIALIDNumber", "FMATERIALID.FName AS FMATERIALIDName",
                "FAuxPropId", "FAuxPropId.FNumber AS FAuxPropIdNumber", "FAuxPropId.FName AS FAuxPropIdName",
            },
            Filters = new List<QueryFilter>
            {
                new("FBillNo", QueryType.Equals, "QTRK000115"),
            },
            PageSize = 10
        });
    }

    [Fact]
    public void DataCenterTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = client.DataCenter;
    }

    [Fact]
    public async Task PUR_PurchaseOrder_STK_InStockTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QuerySourceBill(new QuerySourceBillParam
        {
            FormId = "PUR_PurchaseOrder",
            TargetFormId = "STK_InStock",
            RuleId = "PUR_PurchaseOrder-STK_InStock",
            FieldKeys = new List<string>
            {
                "FID", "FBillNo", "FMaterialId", "FQty", "FBaseUnitQty","_BaseUnitQty_","_Qty_"
            },
            Filters = new List<QueryFilter>
            {
            },
            Orders = new List<QueryOrder>
            {
            },
            PageSize = 20,
        });
    }

    [Fact]
    public async Task STK_OutStockApply_STK_MisDeliveryTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QuerySourceBill(new QuerySourceBillParam
        {
            FormId = "STK_OutStockApply",
            TargetFormId = "STK_MisDelivery",
            RuleId = "STK_OutStockApplyToSTK_MisDelivery",
            FieldKeys = new List<string>
            {
                "FID", "FBillNo", "FMaterialId", "FQty", "FBaseQty",
            },
            Filters = new List<QueryFilter>
            {
            },
            Orders = new List<QueryOrder>
            {
            },
            PageSize = 20,
        });
    }

    [Fact]
    public async Task SAL_SaleOrder_SAL_OUTSTOCKTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.QuerySourceBill(new QuerySourceBillParam
        {
            FormId = "SAL_SaleOrder",
            TargetFormId = "SAL_OUTSTOCK",
            RuleId = "SaleOrder-OutStock",
            FieldKeys = new List<string>
            {
                "FID", "FBillNo", "FMaterialId", "FQty", "FBaseUnitQty","_BaseUnitQty_","_Qty_"
            },
            Filters = new List<QueryFilter>
            {
            },
            Orders = new List<QueryOrder>
            {
            },
            PageSize = 20,
        });
    }

    [Fact]
    public async Task STK_OutStockApply_STK_MisDeliveryCommitTest()
    {
        var client = new K3CloudClient(_loginInfo, _logger);
        var result = await client.SaveBill(new SaveBillParam
        {
            SourceFormId = "STK_OutStockApply",
            TargetFormId = "STK_MisDelivery",
            RuleId = "STK_OutStockApplyToSTK_MisDelivery",
            Data = new List<Dictionary<string, object>>
            {
                //CKSQD000694
                new()
                {
                    ["F_BLJ_Assistant.FNumber"] = "101",//收发类别
                    ["[FEntity].FMaterialId.FNumber"] = "1111000002",//物料编码
                    ["[FEntity].FQty"] = 3,//实发数量
                    ["[FEntity].FStockId.FNumber"] = "51010",//发货仓库
                    ["[FEntity].FStockLocId"] = 109074,//仓位
                    ["[FEntity].FLot.FNumber"] = "111",//批号
                    ["[FEntity].FProduceDate"] = DateTime.Now.Date,//生产日期
                    ["[FEntity]._SourceBillId_"] = 216001,//源单Id
                    ["[FEntity]._SourceBillEntryId_"] = 190001,//源单分录Id
                },
                new()
                {
                    ["F_BLJ_Assistant.FNumber"] = "101",//收发类别
                    ["[FEntity].FMaterialId.FNumber"] = "1111000002",//物料编码
                    ["[FEntity].FQty"] = 4,//实发数量
                    ["[FEntity].FStockId.FNumber"] = "51010",//发货仓库
                    ["[FEntity].FStockLocId"] = 109074,//仓位
                    ["[FEntity].FLot.FNumber"] = "222",//批号
                    ["[FEntity].FProduceDate"] = DateTime.Now.Date,//生产日期
                    ["[FEntity]._SourceBillId_"] = 216001,//源单Id
                    ["[FEntity]._SourceBillEntryId_"] = 190001,//源单分录Id
                },
                new()
                {
                    ["F_BLJ_Assistant.FNumber"] = "101",//收发类别
                    ["[FEntity].FMaterialId.FNumber"] = "1111000004",//物料编码
                    ["[FEntity].FQty"] = 5,//实发数量
                    ["[FEntity].FStockId.FNumber"] = "51013",//发货仓库
                    ["[FEntity].FStockLocId"] = 109062,//仓位
                    ["[FEntity].FLot.FNumber"] = "333",//批号
                    ["[FEntity].FProduceDate"] = DateTime.Now.Date,//生产日期
                    ["[FEntity]._SourceBillId_"] = null,//源单Id
                    ["[FEntity]._SourceBillEntryId_"] = null,//源单分录Id
                },
                new()
                {
                    ["F_BLJ_Assistant.FNumber"] = "101",//收发类别
                    ["[FEntity].FMaterialId.FNumber"] = "1111000004",//物料编码
                    ["[FEntity].FQty"] = 6,//实发数量
                    ["[FEntity].FStockId.FNumber"] = "51013",//发货仓库
                    ["[FEntity].FStockLocId"] = 109062,//仓位
                    ["[FEntity].FLot.FNumber"] = "444",//批号
                    ["[FEntity].FProduceDate"] = DateTime.Now.Date,//生产日期
                },
            }
        });
    }
}