using Neuz.Application.Service.Contract;

namespace Neuz.Application.Service.Setting.Config;

/// <summary>
/// 功能配置
/// </summary>
public class SysModelPdaBarService : ITransient
{
    /// <summary>
    /// 当前用户Id
    /// </summary>
    protected long CurUserId => long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 仓储服务
    /// </summary>
    protected SqlSugarRepository<SysModelPdaBarUc> Rep { get; }

    /// <summary>
    /// 功能配置服务构造函数
    /// </summary>
    /// <param name="serviceProvider"></param>
    public SysModelPdaBarService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        Rep = App.GetRequiredService<SqlSugarRepository<SysModelPdaBarUc>>(serviceProvider);
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    /// <param name="modelKey"></param>
    /// <param name="otherKey"></param>
    public async Task<T> GetUserConfig<T>(string modelKey, string otherKey = null) where T : IModelUserConfig
    {
        var config = await Rep.AsQueryable().Where(r => r.ModelKey.Equals(modelKey, StringComparison.OrdinalIgnoreCase) && r.UserId == CurUserId)
            .WhereIF(!string.IsNullOrEmpty(otherKey), r => r.OtherKey.Equals(otherKey, StringComparison.OrdinalIgnoreCase))
            .FirstAsync();
        return config == null ? default : JsonConvert.DeserializeObject<T>(config.ModelContent);
    }

    /// <summary>
    /// 保存设置
    /// </summary>
    /// <param name="modelKey"></param>
    /// <param name="userConfig"></param>
    /// <param name="otherKey"></param>
    public async Task SetUserConfig(string modelKey, IModelUserConfig userConfig, string otherKey = null)
    {
        var config = await Rep.AsQueryable().Where(r => r.ModelKey.Equals(modelKey, StringComparison.OrdinalIgnoreCase) && r.UserId == CurUserId)
            .WhereIF(!string.IsNullOrEmpty(otherKey), r => r.OtherKey.Equals(otherKey, StringComparison.OrdinalIgnoreCase))
            .FirstAsync();
        var json = JsonConvert.SerializeObject(userConfig);
        if (config == null)
        {
            config = new SysModelPdaBarUc
            {
                ModelKey = modelKey,
                ModelContent = json,
                UserId = CurUserId,
                OtherKey = otherKey,
            };
            await Rep.InsertAsync(config);
        }
        else
        {
            config.ModelContent = json;
            await Rep.UpdateAsync(config);
        }
    }
}