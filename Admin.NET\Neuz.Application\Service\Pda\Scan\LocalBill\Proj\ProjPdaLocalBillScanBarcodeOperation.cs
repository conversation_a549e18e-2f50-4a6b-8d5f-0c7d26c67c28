﻿using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill.Proj;

/// <summary>
/// 本地单据条码扫描
/// 需要修改数量和辅助数量
/// </summary>
public class ProjPdaLocalBillScanBarcodeOperation : PdaScanBarcodeOperationBase
{
    public ProjPdaLocalBillScanBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var dataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
        var billData = (PdaLocalBillData)dataCacheService.GetBillData(args.Key, args.TranId);
        var billModel = (IPdaLocalBillModel)dataCacheService.GetPdaModel(billData.ModelKey);
        var rep = App.GetService<SqlSugarRepository<BdBarcode>>(ServiceProvider);
        var barcode = rep.AsQueryable().IncludeNavCol()
            .Includes(r => r.Material, r => r.Unit)
            .Includes(r => r.Material, r => r.AuxUnit)
            .First(r => r.Barcode == args.BarcodeString);
        if (barcode == null)
        {
            if (new string[] { "StkInNotice_StkReceive" }.Contains(args.Key))
            {
                // 如果是收货, 上架, 下架, 需要解释条码
                var bs = args.BarcodeString.Split(";");
                // 如果不是 唯一码;合同号;物料码;批次;采购单位数量;
                // 格式,退出
                if (bs.Length < 4) return;
                var 合同号 = bs[1];
                var 物料编码 = bs[2];
                var 批号 = bs[3];
                var 采购数量 = bs[4];
                var isConvert = decimal.TryParse(采购数量, out decimal qty);
                if (!isConvert) throw Oops.Bah($"条码数量不能解释,请确认条码[{args.BarcodeString}]");
                // 找订单
                var bills = rep.Change<StkInNotice>().AsQueryable()
                    .LeftJoin<StkInNoticeEntry>((t1, t2) => t1.Id == t2.Id)
                    .LeftJoin<BdMaterial>((t1, t2, t3) => t2.MaterialId == t3.Id)
                    .Where((t1, t2, t3) => t1.ProjectNumber == 合同号 && t3.EsNumber == 物料编码)
                    // .WhereIF(!string.IsNullOrEmpty(批号), (t1, t2, t3) => t2.BatchNo == 批号)
                    .Select((t1, t2, t3) => new { t1.Id, t2.EntryId })
                    .ToList();
                if (bills.Count == 0) throw Oops.Bah($"WMS没有找到合同号[{合同号}],物料[{物料编码}],批号[{批号}]的单据");
                if (bills.Count > 1) throw Oops.Bah($"WMS找到多个合同号[{合同号}],物料[{物料编码}],批号[{批号}]的单据");
                var bill = bills[0];

                var bdBarcodeService = App.GetService<BdBarcodeService>(ServiceProvider);
                var bdBarcode = bdBarcodeService.NewBarcode("stkInNotice", new NewBarcodeInput
                {
                    ComboIds = new List<ComboId>()
                    {
                        new ComboId() { EntryId = bill.EntryId, Id = bill.Id }
                    },
                    PerQty = 0,
                    TotalQty = 0
                }).Result;
                if (bdBarcode.Count == 0) throw Oops.Bah($"没有找到相关单据生成条码");
                if (bdBarcode.Count > 1) throw Oops.Bah($"找到相关多个单据生成条码");
                bdBarcode.ForEach(r =>
                {
                    r.Barcode = args.BarcodeString;
                    r.Qty = qty;
                    r.BatchNo = 批号;
                });
                var buildTranId = bdBarcodeService.BuildAsync(new BdBarcodeBuildInput
                {
                    Barcodes = bdBarcode
                }).Result;
                barcode = rep.AsQueryable().IncludeNavCol().First(r => r.BuildTranId == buildTranId);
            }
            else
            {
                return;
            }
        }

        if (barcode == null) return;

        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        // 检查条码状态
        var pdaLocalBillCheckBarcodeStatusService = App.GetService<PdaLocalBillCheckStatusService>(ServiceProvider);
        pdaLocalBillCheckBarcodeStatusService.CheckBarcodeStatus(args.TranId, args, barcode);

        // 张老师要求,没有辅助单位的,也要弹窗
        // if (barcode.Material.AuxUnitId == null || barcode.Material.AuxUnitId == 0)
        // {
        //     // 如果没有开启辅助数量, 直接扫描
        //     // 扫描条码
        //     billModel.ScanBarcode(args.TranId, barcode);
        //     args.Barcodes.Add(barcode);
        //     args.IsResult = true;
        //     return;
        // }

        // 张老师又要求, 上架不弹窗
        if (new string[] { "StkReceive_StkInStock", "_StkTransfer" }.Contains(billData.ModelKey))
        {
            // 如果没有开启辅助数量, 直接扫描
            // 扫描条码
            billModel.ScanBarcode(args.TranId, barcode);
            args.Barcodes.Add(barcode);
            args.IsResult = true;
            return;
        }

        if (args.IsRepeat)
        {
            var qty = Convert.ToDecimal(args.Ext["qty"]);
            if (barcode.Material.AuxUnitId == null || barcode.Material.AuxUnitId == 0)
            {
                // 如果没有辅助属性直接扫描条码
                billModel.ScanBarcode(args.TranId, barcode, qty);
            }
            else
            {
                // 如果有辅助属性,需要更新辅助数量和辅助单位
                var auxQty = Convert.ToDecimal(args.Ext["auxQty"]);
                var auxUnitId = Convert.ToDecimal(args.Ext["auxUnitId"]);
                billModel.ScanBarcode(args.TranId, barcode, qty, new Dictionary<string, object>() { { "AuxQty", auxQty }, { "AuxUnitId", auxUnitId } });
            }
        }
        else
        {
            var projBdBarcode = barcode.Adapt<ProjBdBarcode>();
            if (projBdBarcode.Material.EsId == null) throw Oops.Bah($"物料[{projBdBarcode.Material.Name}]EsId为空转换失败");
            //if (projBdBarcode.Material.AuxUnit == null || projBdBarcode.Material.AuxUnit.EsId == null || projBdBarcode.Material.AuxUnit.EsId == "0") throw Oops.Bah($"物料[{projBdBarcode.Material.Name}]辅助单位为空或EsId为空转换失败");
            if (projBdBarcode.Material.Unit == null || projBdBarcode.Material.Unit.EsId == null || projBdBarcode.Material.Unit.EsId == "0") throw Oops.Bah($"物料[{projBdBarcode.Material.Name}]单位为空或EsId为空转换失败");

            projBdBarcode.EsMaterialId = long.Parse(projBdBarcode.Material.EsId);
            projBdBarcode.EsUnitId = long.Parse(projBdBarcode.Material.Unit.EsId);

            if (barcode.Material.AuxUnitId != null && barcode.Material.AuxUnitId != 0)
            {
                // 计算辅助数量
                var k3CloudInterface = App.GetService<K3CloudInterface>(ServiceProvider);
                var client = k3CloudInterface.GetK3CloudClient();
                var auxQty = client.DoUnitConvert(long.Parse(projBdBarcode.Material.EsId), long.Parse(projBdBarcode.Material.Unit.EsId), long.Parse(projBdBarcode.Material.AuxUnit.EsId), projBdBarcode.Qty);
                var auxUnitId = projBdBarcode.Material.AuxUnitId ?? 0;
                var auxUnitName = projBdBarcode.Material.AuxUnit.Name;
                var esAuxUnitId = long.Parse(projBdBarcode.Material.AuxUnit.EsId);

                // PDA收货、下架弹窗的辅助数量取条码的辅助数量
                if (new string[] { "StkInNotice_StkReceive", "StkTask_StkOutStock" }.Contains(billData.ModelKey))
                {
                    auxQty = projBdBarcode.AuxQty;
                    auxUnitId = projBdBarcode.AuxUnitId;
                    auxUnitName = projBdBarcode.AuxUnitName;
                }

                var qty = projBdBarcode.Qty;
                // 如果是下架,数量取 任务数量/条码数量 最少值
                if (new string[] { "StkTask_StkOutStock" }.Contains(billData.ModelKey))
                {
                    var oldIsOverSourceQty = billModel.Config.BillParams.IsOverSourceQty;
                    billModel.Config.BillParams.IsOverSourceQty = false;
                    try
                    {
                        var info = billModel.CheckMatchingQty(args.TranId, new PdaLocalBillBarcode(barcode, barcode.Qty));
                        qty = info.Infos.Sum(r => r.Qty);
                        if (projBdBarcode.AuxUnit != null)
                        {
                            auxQty = client.DoUnitConvert(long.Parse(projBdBarcode.Material.EsId), long.Parse(projBdBarcode.Material.Unit.EsId), long.Parse(projBdBarcode.AuxUnit.EsId), qty);   
                        }
                    }
                    finally
                    {
                        billModel.Config.BillParams.IsOverSourceQty = oldIsOverSourceQty;
                    }
                }

                projBdBarcode.Qty = qty;
                projBdBarcode.AuxQty = auxQty;
                projBdBarcode.AuxUnitName = auxUnitName;
                projBdBarcode.AuxUnitId = auxUnitId;
                projBdBarcode.EsAuxUnitId = esAuxUnitId;
                projBdBarcode.Specification = projBdBarcode.Material.Specification;
            }

            // 自定义弹窗
            PdaRestfulCode restfulCode = PdaRestfulCode.P105;
            PdaExtrasRestfulResult<ProjBdBarcode> result = new PdaExtrasRestfulResult<ProjBdBarcode>
            {
                Code = (int)restfulCode,
                Message = null,
                Data = projBdBarcode,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);
        }

        args.Barcodes.Add(barcode);
        args.IsResult = true;
    }
}

/// <summary>
/// 返回参数
/// </summary>
public class ProjBdBarcode : BdBarcode
{
    public decimal AuxQty { get; set; }

    public string AuxUnitName { get; set; }

    public long AuxUnitId { get; set; }

    public long EsMaterialId { get; set; }

    public long EsUnitId { get; set; }

    public long EsAuxUnitId { get; set; }

    public string? Specification { get; set; }
}