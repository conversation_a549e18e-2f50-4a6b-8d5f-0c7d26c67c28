﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill.Link;

namespace Neuz.Application.Pda.LocalBill.Bill.StkInNotice_StkInNotice
{
    /// <summary>
    /// 收料通知单->收料通知单
    /// </summary>
    public class LocalBillLinkStkInNotice_StkInNotice : LocalBillLinkParamBase
    {
        /// <inheritdoc/>
        public override string Key { get; set; } = "StkInNotice_StkInNotice";

        public override List<string> SourceBillTypes { get; set; } = new List<string> { };

        /// <inheritdoc/>
        public override List<LocalBillLinkMapping> Mappings { get; set; } = new()
        {
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.BillNo), ScanFiledName = nameof(PdaLocalBillScanHead.SrcBillNo), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.Id), ScanFiledName = nameof(PdaLocalBillScanHead.SrcBillId), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = "StkInNotice.Supplier.Id", ScanFiledName = nameof(PdaLocalBillScanHead.SupplierId), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = "StkInNotice.Supplier.Number", ScanFiledName = nameof(PdaLocalBillScanHead.SupplierNumber), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = "StkInNotice.Supplier.Name", ScanFiledName = nameof(PdaLocalBillScanHead.SupplierName), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialNumber), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Material.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialName), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.WhArea.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.WhArea.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaNumber), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.WhArea.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaName), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.BatchNo", ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = $"{nameof(StkInNotice.Entries)}.{nameof(StkInNoticeEntry.ProduceDate)}", ScanFiledName = nameof(PdaLocalBillScanDetail.ProduceDate), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Id", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitNumber), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Unit.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitName), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.BillType), ScanFiledName = "SrcBillKey", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.BillNo), ScanFiledName = "SrcBillNo", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Seq", ScanFiledName = "SrcBillEntrySeq", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.Id), ScanFiledName = "SrcBillId", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.EntryId", ScanFiledName = "SrcBillEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.OwnerId", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Owner.Number", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerNumber), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Owner.Name", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerName), ScanType = LocalBillLinkMappingScanType.Detail },
        };

        /// <inheritdoc/>
        public override List<LocalBillQtyCalcMapping> CalcMappings { get; set; } = new List<LocalBillQtyCalcMapping>()
        {
            new LocalBillQtyCalcMapping()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.Qty),
                ScanType = LocalBillLinkMappingScanType.Detail,
                CalcFiledNames = new List<LocalBillCalcFieldMapping>()
                {
                    new LocalBillCalcFieldMapping()
                    {
                        LocalBillFiledName = "Entries.Qty",
                        CalcType = LocalBillCalcType.Sub
                    },
                    new LocalBillCalcFieldMapping()
                    {
                        LocalBillFiledName = "Entries.ReceiveQty",
                        CalcType = LocalBillCalcType.Sub
                    },
                    new LocalBillCalcFieldMapping()
                    {
                        LocalBillFiledName = "Entries.ReturnQty",
                        CalcType = LocalBillCalcType.Sub
                    },
                }
            }
        };

        /// <inheritdoc/>
        public override List<LocalBillContentInfo> DetailContents { get; set; } = new List<LocalBillContentInfo>()
        {
            new LocalBillContentInfo()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo),
                Title = L.Text["批号"],
                Col = 1
            },
            new LocalBillContentInfo()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaName),
                Title = L.Text["库区"],
                Col = 1
            },
            new LocalBillContentInfo()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocName),
                Title = L.Text["库位"],
                Col = 1
            },
            new LocalBillContentInfo()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.Qty),
                Title = L.Text["通知数量"],
                Col = 2
            },
            new LocalBillContentInfo()
            {
                ScanFiledName = nameof(PdaLocalBillScanDetail.ScanQty),
                Title = L.Text["上架数量"],
                Col = 2
            },
        };

        /// <inheritdoc/>
        public override List<LocalBillLinkBarcodeMapping> BarcodeMappings { get; set; } = new()
        {
            new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "Id", SaveBarcodeName = "BarcodeId" },
            new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "MaterialId", SaveBarcodeName = "MaterialId" },
            new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "BatchNo", SaveBarcodeName = "BatchNo" },
            new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "ScanQty", SaveBarcodeName = "Qty" },
            new LocalBillLinkBarcodeMapping() { ScanBarcodeName = "AuxQty", SaveBarcodeName = "AuxQty" },
        };

        /// <inheritdoc/>
        public override Type SubmitBillType { get; set; } = typeof(StkInNoticeService);

        /// <inheritdoc/>
        public override List<LocalBillLinkMapping> SubmitMappings { get; set; } = new()
        {
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.Date), ScanFiledName = nameof(PdaLocalBillScanHead.Date), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.SupplierId), ScanFiledName = nameof(PdaLocalBillScanHead.SupplierId), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.BillType), ScanFiledName = "", ScanType = LocalBillLinkMappingScanType.Head, DefaultValue = "SLTZD" },
            new LocalBillLinkMapping() { LocalBillFiledName = nameof(StkInNotice.WarehouseId), ScanFiledName = nameof(PdaLocalBillScanHead.WareHouseId), ScanType = LocalBillLinkMappingScanType.Head },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.MaterialId", ScanFiledName = nameof(PdaLocalBillScanDetail.MaterialId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.WhAreaId", ScanFiledName = nameof(PdaLocalBillScanDetail.WhAreaId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.WhLocId", ScanFiledName = nameof(PdaLocalBillScanDetail.WhLocId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.BatchNo", ScanFiledName = nameof(PdaLocalBillScanDetail.BatchNo), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.ProduceDate", ScanFiledName = nameof(PdaLocalBillScanDetail.ProduceDate), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.ExpiryDate", ScanFiledName = nameof(PdaLocalBillScanDetail.ExpiryDate), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.Qty", ScanFiledName = nameof(PdaLocalBillScanDetail.ScanQty), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.UnitId", ScanFiledName = nameof(PdaLocalBillScanDetail.UnitId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillKey", ScanFiledName = "", ScanType = LocalBillLinkMappingScanType.Detail, DefaultValue = nameof(StkInNotice) },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillNo", ScanFiledName = "SrcBillNo", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntrySeq", ScanFiledName = "SrcBillEntrySeq", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillId", ScanFiledName = "SrcBillId", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.SrcBillEntryId", ScanFiledName = "SrcBillEntryId", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.OwnerId", ScanFiledName = nameof(PdaLocalBillScanDetail.OwnerId), ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxQty", ScanFiledName = "AuxQty", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxUnitId", ScanFiledName = "AuxUnitId", ScanType = LocalBillLinkMappingScanType.Detail },
            new LocalBillLinkMapping() { LocalBillFiledName = "Entries.AuxPropValueId", ScanFiledName = "AuxPropValueId", ScanType = LocalBillLinkMappingScanType.Detail },
        };

        /// <inheritdoc/>
        public override string SourceEntryName { get; set; } = "Entries";

        /// <inheritdoc/>
        public override string TargetEntryName { get; set; } = "Entries";

        /// <inheritdoc/>
        public override string BarcodeEntryName { get; set; } = "BarcodeEntries";
    }
}