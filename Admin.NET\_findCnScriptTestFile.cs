using System.ComponentModel.DataAnnotations;
using Furion.FriendlyException;
using Furion.Localization;
using Furion.Logging;
using Furion.Schedule;

/// <summary>
/// 枚举转字典
/// </summary>
[JobDetail("job_EnumToDictJob", Description = "枚举转字典", GroupName = "default", Concurrent = false)]
[PeriodSeconds(1, TriggerId = "trigger_EnumToDictJob", Description = "枚举转字典", MaxNumberOfRuns = 1, RunOnStart = true)]
public class Test
{
    public Test()
    {
        try
        {
            #region 测试文本

            var dic = new Dictionary<string, string>
            {
                ["物料编码"] = "2.1111"
            };

            dic["物料名称"] = "material";

            var name = "测试3";
            var number = "aa";
            var number2 = $"姓名：{name}，年龄";
            var number2 = $@"姓  名：{name}";
            var number3 = $@"性别：{name}
dd";

            #endregion

            Debug.WriteLine("测试文本Debug");
            var number4 = string.Format("test {0} {1} d", number, number2);
            var t = L.Text["编码: {0} 操作成功。", number];
            var t2 = L.Text[$"名称: {number} 保存成功!!!"];
            var t3 = L.Text[$@"编码: {number} 
删除成功"];
            throw Oops.Bah($"条码[{barcode.Barcode}]已在库,不能入库");
        }
        catch (Exception error)
        {
            Log.Error($"系统枚举转换字典操作错误：{error.Message}\n堆栈跟踪：{error.StackTrace}", error);
        }
    }
}

/// <summary>
/// 系统操作日志表
/// </summary>
[SugarTable(null, "系统操作日志表")]
//[SysTable]
[LogTable]
public partial class SysLogOp : SysLogVis
{
    /// <summary>
    /// 请求方式
    /// </summary>
    [SugarColumn(ColumnDescription = "请求方式", Length = 32)]
    [MaxLength(32)]
    public string? HttpMethod { get; set; }

    /// <summary>
    /// 请求地址
    /// </summary>
    [SugarColumn(ColumnDescription = "请求地址", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? RequestUrl { get; set; }

    /// <summary>
    /// 请求参数
    /// </summary>
    [SugarColumn(ColumnDescription = "请求参数", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? RequestParam { get; set; }

    /// <summary>
    /// 返回结果
    /// </summary>
    [SugarColumn(ColumnDescription = "返回结果", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? ReturnResult { get; set; }

    /// <summary>
    /// 事件Id
    /// </summary>
    [SugarColumn(ColumnDescription = "事件Id")]
    public int? EventId { get; set; }

    /// <summary>
    /// 线程Id
    /// </summary>
    [SugarColumn(ColumnDescription = "线程Id")]
    public int? ThreadId { get; set; }

    /// <summary>
    /// 请求跟踪Id
    /// </summary>
    [SugarColumn(ColumnDescription = "请求跟踪Id", Length = 128)]
    [MaxLength(128)]
    public string? TraceId { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    [SugarColumn(ColumnDescription = "异常信息", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Exception { get; set; }

    /// <summary>
    /// 日志消息Json
    /// </summary>
    [SugarColumn(ColumnDescription = "日志消息Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Message { get; set; }
}

/// <summary>
/// 性别枚举
/// </summary>
[Description("性别枚举")]
public enum GenderEnum
{
    /// <summary>
    /// 男
    /// </summary>
    [Description("男")]
    Male = 1,

    /// <summary>
    /// 女
    /// </summary>
    [Description("女")]
    Female = 2,

    /// <summary>
    /// 其他
    /// </summary>
    [Description("其他")]
    Other = 3
}

/// <summary>
/// 系统错误码
/// </summary>
[ErrorCodeType]
[Description("系统错误码")]
public enum ErrorCodeEnum
{
    /// <summary>
    /// 验证码错误
    /// </summary>
    [ErrorCodeItemMetadata("验证码错误")]
    D0008,

    /// <summary>
    /// 账号不存在
    /// </summary>
    [ErrorCodeItemMetadata("账号不存在")]
    D0009,
}