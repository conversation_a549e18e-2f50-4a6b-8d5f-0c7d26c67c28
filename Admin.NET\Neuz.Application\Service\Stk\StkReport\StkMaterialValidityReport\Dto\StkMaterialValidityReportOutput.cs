﻿using Magicodes.ExporterAndImporter.Core;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 物料有效期报表输出参数
/// </summary>
public class StkMaterialValidityReportOutput
{
    /// <summary>
    /// 库存物料Id
    /// </summary>
    [ExporterHeader(DisplayName = "库存物料Id", IsIgnore = true)]
    public long Id { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [ExporterHeader(DisplayName = "物料Id", IsIgnore = true)]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [ExporterHeader(DisplayName = "物料编码")]
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [ExporterHeader(DisplayName = "物料名称")]
    public string MaterialName { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [ExporterHeader(DisplayName = "批号", IsIgnore = true)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [ExporterHeader(DisplayName = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [ExporterHeader(DisplayName = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [ExporterHeader(DisplayName = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 到期时间
    /// </summary>
    [ExporterHeader(DisplayName = "到期时间")]
    public int ExpirationDate { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [ExporterHeader(DisplayName = "仓库Id", IsIgnore = true)]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    [ExporterHeader(DisplayName = "仓库编码")]
    public string WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    [ExporterHeader(DisplayName = "仓库名称", IsIgnore = true)]
    public string WarehouseName { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [ExporterHeader(DisplayName = "货主Id", IsIgnore = true)]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主编码
    /// </summary>
    [ExporterHeader(DisplayName = "货主编码")]
    public string OwnerNumber { get; set; }

    /// <summary>
    /// 货主名称
    /// </summary>
    [ExporterHeader(DisplayName = "货主名称")]
    public string OwnerName { get; set; }


}