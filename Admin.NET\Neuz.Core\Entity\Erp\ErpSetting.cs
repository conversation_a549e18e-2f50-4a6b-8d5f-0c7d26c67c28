﻿namespace Neuz.Core.Entity
{
    /// <summary>
    /// Erp设置
    /// </summary>
    [SugarTable(null, "Erp设置")]
    public class ErpSetting : EntityTenantId
    {
        /// <summary>
        /// 服务器地址
        /// </summary>
        [SugarColumn(ColumnDescription = "服务器地址", Length = 255)]
        public string? ServerUrl { get; set; }

        /// <summary>
        /// 账套Id
        /// </summary>
        [SugarColumn(ColumnDescription = "账套Id", Length = 50)]
        public string? AccountId { get; set; }

        /// <summary>
        /// 默认用户名
        /// </summary>
        [SugarColumn(ColumnDescription = "默认用户名", Length = 50)]
        public string? DefUserName { get; set; }

        /// <summary>
        /// 默认密码
        /// </summary>
        [SugarColumn(ColumnDescription = "默认密码", Length = 500)]
        public string? DefPassword { get; set; }

        /// <summary>
        /// 附加信息1
        /// </summary>
        [SugarColumn(ColumnDescription = "附加信息1", Length = 255)]
        public string? Extra1 { get; set; }

        /// <summary>
        /// 附加信息2
        /// </summary>
        [SugarColumn(ColumnDescription = "附加信息2", Length = 255)]
        public string? Extra2 { get; set; }

        /// <summary>
        /// 附加信息3
        /// </summary>
        [SugarColumn(ColumnDescription = "附加信息3", Length = 255)]
        public string? Extra3 { get; set; }

        /// <summary>
        /// 附加信息4
        /// </summary>
        [SugarColumn(ColumnDescription = "附加信息4", Length = 255)]
        public string? Extra4 { get; set; }

        /// <summary>
        /// 附加信息5
        /// </summary>
        [SugarColumn(ColumnDescription = "附加信息5", Length = 255)]
        public string? Extra5 { get; set; }
    }
}