﻿namespace Neuz.Core.Entity;

/// <summary>
/// 职员
/// </summary>
[SugarTable(null, "职员")]
public class BdEmployee : EsBdEntityBase
{
    /// <summary>
    /// 所属部门Id
    /// </summary>
    [SugarColumn(ColumnDescription = "所属部门Id")]
    public long DepartmentId { get; set; }

    /// <summary>
    /// 所属部门
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DepartmentId))]
    [CustomSerializeFields]
    public BdDepartment Department { get; set; }
}