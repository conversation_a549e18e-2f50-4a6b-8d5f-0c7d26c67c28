using Furion.DependencyInjection;
using System.Text.Json;

namespace Admin.NET.Plugin.PaddleOCR.Service;

/// <summary>
/// OCR模型下载助手服务 📥
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Description = "OCR模型下载助手")]
public class OCRModelDownloadService : IDynamicApiController, ISingleton
{
    private readonly string _modelBasePath;
    private readonly string _configPath;

    public OCRModelDownloadService()
    {
        _modelBasePath = Path.Combine(AppContext.BaseDirectory, "OcrModel");
        _configPath = Path.Combine(AppContext.BaseDirectory, "Configuration", "OCRModelConfig.json");

        // 确保目录存在
        Directory.CreateDirectory(_modelBasePath);
    }

    /// <summary>
    /// 获取模型配置信息 📋
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("获取模型配置信息")]
    public async Task<dynamic> GetModelConfigs()
    {
        try
        {
            if (!File.Exists(_configPath))
            {
                return await Task.FromResult(new
                {
                    Success = false,
                    Message = "配置文件不存在",
                    ConfigPath = _configPath
                });
            }

            var configJson = await File.ReadAllTextAsync(_configPath);
            var config = JsonSerializer.Deserialize<JsonElement>(configJson);

            var models = new List<object>();
            if (config.TryGetProperty("OCRModels", out var ocrModels))
            {
                foreach (var model in ocrModels.EnumerateObject())
                {
                    var modelInfo = model.Value;
                    var modelPath = Path.Combine(_modelBasePath, modelInfo.GetProperty("ModelPath").GetString());
                    var isInstalled = CheckModelInstalled(modelPath, modelInfo);

                    models.Add(new
                    {
                        ModelKey = model.Name,
                        Name = modelInfo.GetProperty("Name").GetString(),
                        Description = modelInfo.GetProperty("Description").GetString(),
                        ModelPath = modelInfo.GetProperty("ModelPath").GetString(),
                        IsInstalled = isInstalled,
                        InstallPath = modelPath,
                        Files = GetModelFiles(modelInfo)
                    });
                }
            }

            return await Task.FromResult(new
            {
                Success = true,
                Models = models,
                BasePath = _modelBasePath
            });
        }
        catch (Exception ex)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"读取配置失败: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// 检查模型状态 🔍
    /// </summary>
    /// <param name="modelKey">模型键名</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("检查模型状态")]
    public async Task<dynamic> CheckModelStatus(string modelKey)
    {
        try
        {
            var configJson = await File.ReadAllTextAsync(_configPath);
            var config = JsonSerializer.Deserialize<JsonElement>(configJson);

            if (!config.TryGetProperty("OCRModels", out var ocrModels) ||
                !ocrModels.TryGetProperty(modelKey, out var modelInfo))
            {
                return await Task.FromResult(new
                {
                    Success = false,
                    Message = $"模型 '{modelKey}' 不存在"
                });
            }

            var modelPath = Path.Combine(_modelBasePath, modelInfo.GetProperty("ModelPath").GetString());
            var isInstalled = CheckModelInstalled(modelPath, modelInfo);
            var files = GetModelFiles(modelInfo);
            var fileStatus = new List<object>();

            foreach (var file in files)
            {
                var filePath = Path.Combine(modelPath, file.Name);
                var exists = false;
                var size = 0L;

                if (file.Name.EndsWith(".txt"))
                {
                    exists = File.Exists(filePath);
                    if (exists) size = new FileInfo(filePath).Length;
                }
                else
                {
                    exists = Directory.Exists(filePath);
                    if (exists)
                    {
                        var dirInfo = new DirectoryInfo(filePath);
                        size = dirInfo.GetFiles("*", SearchOption.AllDirectories).Sum(f => f.Length);
                    }
                }

                fileStatus.Add(new
                {
                    file.Name,
                    file.Description,
                    Exists = exists,
                    Size = FormatFileSize(size),
                    Path = filePath
                });
            }

            return await Task.FromResult(new
            {
                Success = true,
                ModelKey = modelKey,
                Name = modelInfo.GetProperty("Name").GetString(),
                IsInstalled = isInstalled,
                ModelPath = modelPath,
                FileStatus = fileStatus
            });
        }
        catch (Exception ex)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"检查模型状态失败: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// 获取下载链接 🔗
    /// </summary>
    /// <param name="modelKey">模型键名</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("获取下载链接")]
    public async Task<dynamic> GetDownloadLinks(string modelKey)
    {
        try
        {
            var configJson = await File.ReadAllTextAsync(_configPath);
            var config = JsonSerializer.Deserialize<JsonElement>(configJson);

            if (!config.TryGetProperty("OCRModels", out var ocrModels) ||
                !ocrModels.TryGetProperty(modelKey, out var modelInfo))
            {
                return await Task.FromResult(new
                {
                    Success = false,
                    Message = $"模型 '{modelKey}' 不存在"
                });
            }

            var files = GetModelFiles(modelInfo);
            var downloadLinks = files.Select(f => new
            {
                f.Name,
                f.Description,
                f.DownloadUrl,
                FileName = f.DownloadUrl.Split('/').Last()
            }).ToList();

            var instructions = new List<string>();
            if (config.TryGetProperty("DownloadInstructions", out var instructionsObj))
            {
                if (instructionsObj.TryGetProperty("Steps", out var steps))
                {
                    instructions.AddRange(steps.EnumerateArray().Select(s => s.GetString()));
                }
            }

            return await Task.FromResult(new
            {
                Success = true,
                ModelKey = modelKey,
                Name = modelInfo.GetProperty("Name").GetString(),
                Description = modelInfo.GetProperty("Description").GetString(),
                DownloadLinks = downloadLinks,
                Instructions = instructions,
                TargetPath = Path.Combine(_modelBasePath, modelInfo.GetProperty("ModelPath").GetString())
            });
        }
        catch (Exception ex)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"获取下载链接失败: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// 清理模型文件 🗑️
    /// </summary>
    /// <param name="modelKey">模型键名</param>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("清理模型文件")]
    public async Task<dynamic> CleanModel(string modelKey)
    {
        try
        {
            var configJson = await File.ReadAllTextAsync(_configPath);
            var config = JsonSerializer.Deserialize<JsonElement>(configJson);

            if (!config.TryGetProperty("OCRModels", out var ocrModels) ||
                !ocrModels.TryGetProperty(modelKey, out var modelInfo))
            {
                return await Task.FromResult(new
                {
                    Success = false,
                    Message = $"模型 '{modelKey}' 不存在"
                });
            }

            var modelPath = Path.Combine(_modelBasePath, modelInfo.GetProperty("ModelPath").GetString());

            if (Directory.Exists(modelPath))
            {
                Directory.Delete(modelPath, true);
                return await Task.FromResult(new
                {
                    Success = true,
                    Message = $"模型 '{modelKey}' 已清理",
                    CleanedPath = modelPath
                });
            }
            else
            {
                return await Task.FromResult(new
                {
                    Success = true,
                    Message = $"模型 '{modelKey}' 目录不存在，无需清理"
                });
            }
        }
        catch (Exception ex)
        {
            return await Task.FromResult(new
            {
                Success = false,
                Message = $"清理模型失败: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// 检查模型是否已安装
    /// </summary>
    private bool CheckModelInstalled(string modelPath, JsonElement modelInfo)
    {
        if (!Directory.Exists(modelPath)) return false;

        var files = GetModelFiles(modelInfo);
        foreach (var file in files)
        {
            var filePath = Path.Combine(modelPath, file.Name);
            if (file.Name.EndsWith(".txt"))
            {
                if (!File.Exists(filePath)) return false;
            }
            else
            {
                if (!Directory.Exists(filePath)) return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 获取模型文件信息
    /// </summary>
    private List<(string Name, string Description, string DownloadUrl)> GetModelFiles(JsonElement modelInfo)
    {
        var files = new List<(string, string, string)>();

        if (modelInfo.TryGetProperty("Files", out var filesObj))
        {
            foreach (var file in filesObj.EnumerateObject())
            {
                var fileInfo = file.Value;
                files.Add((
                    fileInfo.GetProperty("Name").GetString(),
                    fileInfo.GetProperty("Description").GetString(),
                    fileInfo.GetProperty("DownloadUrl").GetString()
                ));
            }
        }

        return files;
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = ["B", "KB", "MB", "GB", "TB"];
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}