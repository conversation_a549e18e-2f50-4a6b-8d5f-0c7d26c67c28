﻿namespace Neuz.Core.SeedData;

[IgnoreUpdateSeed]
public class BdSysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu { Id = 1361000010000, Pid = 0, Title = "基础管理", Path = "/bd", Name = "", Component = "Layout", Icon = "ele-TakeawayBox", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 2100 },
            new SysMenu { Id = 1361000110000, Pid = 1361000010000, Title = "基础资料", Path = "/bd/bd", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1361000210000, Pid = 1361000010000, Title = "仓库信息", Path = "/bd/warehouse", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1361000310000, Pid = 1361000010000, Title = "物料信息", Path = "/bd/material", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Dir, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },

            new SysMenu { Id = 1361000010001, Pid = 1361000210000, Title = "库区", Path = "/bd/bdModel/BdWhAreaModel/库区", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 100 },
            new SysMenu { Id = 1361000010002, Pid = 1361000210000, Title = "库位", Path = "/bd/bdModel/BdWhLocModel/库位", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 110 },
            new SysMenu { Id = 1361000010003, Pid = 1361000310000, Title = "物料", Path = "/bd/bdMaterial", Name = "bdMaterial", Component = "/business/bd/bdMaterial/index.vue", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 120 },
            new SysMenu { Id = 1361000010004, Pid = 1361000310000, Title = "计量单位", Path = "/bd/bdModel/BdUnitModel/计量单位", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010005, Pid = 1361000110000, Title = "供应商", Path = "/bd/bdModel/BdSupplierModel/供应商", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010006, Pid = 1361000110000, Title = "客户", Path = "/bd/bdModel/BdCustomerModel/客户", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010007, Pid = 1361000110000, Title = "员工", Path = "/bd/bdModel/BdEmployeeModel/员工", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010008, Pid = 1361000110000, Title = "部门", Path = "/bd/bdModel/BdDepartmentModel/部门", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010009, Pid = 1361000210000, Title = "仓库", Path = "/bd/bdModel/BdWarehouseModel/仓库", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010010, Pid = 1361000110000, Title = "货主", Path = "/bd/bdModel/BdOwnerModel/货主", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010011, Pid = 1361000210000, Title = "仓库库区权限", Path = "/stk/stkRoleWhArea", Name = "stkRoleWhArea", Component = "/business/stk/stkRoleWhArea/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010012, Pid = 1361000110000, Title = "货主权限", Path = "/stk/stkRoleOwner", Name = "stkRoleOwner", Component = "/business/stk/stkRoleOwner/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },

            new SysMenu { Id = 1361000010013, Pid = 1361000310000, Title = "批号档案", Path = "/bd/bdBatchFile", Name = "bdBatchFile", Component = "/business/bd/bdBatchFile/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010014, Pid = 1361000310000, Title = "条码档案", Path = "/bd/bdBarcode", Name = "bdBarcode", Component = "/business/bd/bdBarcode/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010015, Pid = 1361000310000, Title = "容器", Path = "/bd/bdContainer", Name = "bdContainer", Component = "/business/bd/bdContainer/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010016, Pid = 1361000310000, Title = "条码档案日志", Path = "/bd/bdBarcodeLog", Name = "bdBarcodeLog", Component = "/business/bd/bdBarcodeLog/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010017, Pid = 1361000310000, Title = "容器日志", Path = "/bd/bdContainerLog", Name = "bdContainerLog", Component = "/business/bd/bdContainerLog/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 130 },
            new SysMenu { Id = 1361000010018, Pid = 1361000310000, Title = "辅助属性", Path = "/bd/bdModel/BdAuxPropModel/辅助属性", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 140 },
            new SysMenu { Id = 1361000010019, Pid = 1361000310000, Title = "辅助属性类型", Path = "/bd/bdModel/BdAuxPropTypeModel/辅助属性类型", Name = "", Component = "", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 150 },
            new SysMenu { Id = 1361000010020, Pid = 1361000310000, Title = "辅助属性值", Path = "/bd/bdAuxPropValue", Name = "bdAuxPropValue", Component = "/business/bd/bdAuxPropValue/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2022-02-10 00:00:00"), OrderNo = 160 },
        };
    }
}