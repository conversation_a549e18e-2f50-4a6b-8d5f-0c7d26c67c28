﻿namespace Neuz.Core.SeedData;

[IgnoreUpdateSeed]
public class NeuzSysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            // 系统设置
            new SysMenu{ Id=1340000010000, Pid=1310000000101, Title="编码规则", Path="/system/codeRule", Name="codeRule", Component="Layout",Icon="ele-Document", Type=MenuTypeEnum.Dir, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=2900 },
            new SysMenu{ Id=1340000010001, Pid=1340000010000, Title="系统编码规则", Path="/system/codeRule/sys", Name="sysCodeRule", Component="/business/sys/sysCodeRule/index",Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=160 },
            new SysMenu{ Id=1340000010002, Pid=1340000010000, Title="条码编码规则", Path="/system/codeRule/bar", Name="sysCodeRuleBarcode", Component="/business/sys/sysCodeRule/barcodeIndex",Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=160 },
            new SysMenu{ Id=1340000020000, Pid=1310000000101, Title="授权信息", Path="/system/license", Name="sysLicense", Component="/business/sys/sysLicense/index",Icon="ele-CollectionTag", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=3000 },
            
            // 平台管理
            // new SysMenu{ Id=1340010010000, Pid=1310000000301, Title="系统信息配置", Path="/platform/sysInfoSetting", Name="sysInfoSetting", Component="/business/sys/sysInfoSetting/index",Icon="ele-Postcard", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=210 },
            new SysMenu{ Id=1340010020000, Pid=1310000000301, Title="第三方登录身份", Path="/platform/sysThirdAccess", Name="sysThirdAccess", Component="/business/sys/sysThirdAccess/index",Icon="ele-Postcard", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=220 },
            
            // Cloud 接口调用日志
            new SysMenu{ Id=1340020010001, Pid=1310000000501, Title="Cloud 接口日志", Path="/system/log/erpCloudApiCallLog", Name="erpCloudApiCallLog", Component="/business/erp/erpCloudApiCallLog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=150 },
            new SysMenu{ Id=1340020010101, Pid=1340020010001, Title="查询", Permission="erpCloudApiCallLog/page", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1340020010201, Pid=1340020010001, Title="清空", Permission="erpCloudApiCallLog/clear", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },

            // 日志文件
            new SysMenu{ Id=1340030010001, Pid=1310000000501, Title="日志文件", Path="/system/log/sysFileLog", Name="sysFileLog", Component="/business/sys/sysFileLog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=140 },
            new SysMenu{ Id=1340030010101, Pid= 1340030010001, Title="查询", Permission="sysFileLog/page", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1340030010201, Pid=1340030010001, Title="删除", Permission="sysFileLog/delete", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrderNo=100 },

            // Pda保存单据日志
            new SysMenu{ Id=1340040010001, Pid=1310000000501, Title="Pda保存单据日志", Path="/system/log/pdaSaveBillParamLog", Name="pdaSaveBillParamLog", Component="/business/pdaSave/pdaSaveBillParamLog/index", Icon="ele-Document", Type=MenuTypeEnum.Menu, CreateTime=DateTime.Parse("2025-04-25 00:00:00"), OrderNo=150 },
            new SysMenu{ Id=1340040010101, Pid=1340040010001, Title="查询", Permission="pdaSaveBillParamLog/page", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2025-04-25 00:00:00"), OrderNo=100 },
            new SysMenu{ Id=1340040010201, Pid=1340040010001, Title="清空", Permission="pdaSaveBillParamLog/clear", Type=MenuTypeEnum.Btn, CreateTime=DateTime.Parse("2025-04-25 00:00:00"), OrderNo=100 },
        };
    }
}