﻿namespace Neuz.Application;

/// <summary>
/// 系统实体输出参数
/// </summary>
public class SysEntityOutput
{
    /// <summary>
    /// 实体名称
    /// </summary>
    public string EntityName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 系统实体属性输出参数
/// </summary>
public class SysEntityPropertyOutput
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}
