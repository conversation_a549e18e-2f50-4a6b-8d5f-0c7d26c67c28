﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储分配策略明细
/// </summary>
[SugarTable(null, "仓储分配策略明细")]
public class StkAllocatePolicyEntry : EntryEntityBase
{
    /// <summary>
    /// 分配规则Id
    /// </summary>
    [SugarColumn(ColumnDescription = "分配规则Id")]
    public long AllocateRuleId { get; set; }

    /// <summary>
    /// 分配规则
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AllocateRuleId))]
    public StkAllocateRule AllocateRule { get; set; }
}