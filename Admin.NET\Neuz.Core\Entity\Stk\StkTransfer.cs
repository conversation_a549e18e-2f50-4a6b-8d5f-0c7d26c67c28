﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存调拨单
/// </summary>
[SugarTable(null, "库存调拨单")]
public class StkTransfer : EsBillEntityBase
{
    /// <summary>
    /// 单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "单据日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "单据类型", Length = 80)]
    public string BillType { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [SugarColumn(ColumnDescription = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkTransferEntry.Id))]
    public List<StkTransferEntry> Entries { get; set; }
}