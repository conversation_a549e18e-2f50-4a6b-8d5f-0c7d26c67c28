﻿namespace Neuz.Core.Enum;

/// <summary>
/// 调拨通知单分配状态
/// </summary>
public enum StkTransferNoticeAllocateStatus
{
    /// <summary>
    /// 未分配
    /// </summary>
    [Description("未分配"), Theme("info")]
    UnAllocate = 0,

    /// <summary>
    /// 部分分配
    /// </summary>
    [Description("部分分配"), Theme("warning")]
    Allocating = 1,

    /// <summary>
    /// 分配失败
    /// </summary>
    [Description("分配失败"), Theme("danger")]
    AllocateFail = 2,

    /// <summary>
    /// 全部分配
    /// </summary>
    [Description("全部分配"), Theme("success")]
    FullAllocate = 9999,
}