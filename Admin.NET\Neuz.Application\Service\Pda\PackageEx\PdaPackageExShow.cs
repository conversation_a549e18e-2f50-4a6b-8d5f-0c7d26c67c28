﻿using Neuz.Application.Pda.Base;
using static Neuz.Application.Pda.Package.Data.PdaPackageData;

namespace Neuz.Application.Pda.PackageEx;

/// <summary>
/// 装箱前端显示(为旧版装箱扩展)
/// </summary>
public class PdaPackageExShow : PdaShow
{
    /// <summary>
    /// 装/拆箱
    /// </summary>
    public string Status { get; set; }


    /// <summary>
    /// 是否创建新箱
    /// </summary>
    public bool IsNew { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    public List<PdaPackageDataBarcodeInfo> Barcodes { get; set; } = new List<PdaPackageDataBarcodeInfo>();

    /// <summary>
    /// 箱号
    /// </summary>
    public string PackageNo { get; set; }

    // 源单数据
    public Dictionary<string, string> BillCells { get; } = new Dictionary<string, string>();
}