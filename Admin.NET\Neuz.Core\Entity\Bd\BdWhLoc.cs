﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库位
/// </summary>
[SugarTable(null, "库位")]
public class BdWhLoc : EsBdEntityBase
{
    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 上架顺序
    /// </summary>
    [SugarColumn(ColumnDescription = "上架顺序")]
    public int PutAwayOrder { get; set; }

    /// <summary>
    /// 拣货顺序
    /// </summary>
    [SugarColumn(ColumnDescription = "拣货顺序")]
    public int PickOrder { get; set; }

    /// <summary>
    /// 数量容量
    /// </summary>
    /// <remarks>
    /// 大于0 表示有限制，0 表示无限制
    /// </remarks>
    [SugarColumn(ColumnDescription = "数量容量")]
    public decimal QtyCapacity { get; set; }

    /// <summary>
    /// 重量容量
    /// </summary>
    [SugarColumn(ColumnDescription = "重量容量")]
    public decimal WeightCapacity { get; set; }

    /// <summary>
    /// 体积容量
    /// </summary>
    [SugarColumn(ColumnDescription = "体积容量")]
    public decimal VolumeCapacity { get; set; }

    /// <summary>
    /// 箱数容量
    /// </summary>
    [SugarColumn(ColumnDescription = "箱数容量")]
    public decimal PackageCapacity { get; set; }

    /// <summary>
    /// 最小库存
    /// </summary>
    [SugarColumn(ColumnDescription = "最小库存")]
    public decimal MinInvQty { get; set; }

    /// <summary>
    /// 最大库存
    /// </summary>
    [SugarColumn(ColumnDescription = "最大库存")]
    public decimal MaxInvQty { get; set; }
}