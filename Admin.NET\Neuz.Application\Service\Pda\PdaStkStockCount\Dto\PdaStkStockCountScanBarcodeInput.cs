﻿using Neuz.Application.Pda.Enum;

namespace Neuz.Application.Pda.PdaStkStockCount.Dto;

public class PdaStkStockCountScanBarcodeInput
{
    /// <summary>
    /// 扫描内容
    /// </summary>
    public string Barcode { get; set; }

    /// <summary>
    /// 仓库优先类型
    /// </summary>
    public FirstStockType FirstStockType { get; set; }

    /// <summary>
    /// 扫描条码策略
    /// </summary>
    public ScanBarcodeType ScanBarcodeType { get; set; }

    /// <summary>
    /// 是否确认扫描
    /// </summary>
    public bool IsRepeat { get; set; }

    /// <summary>
    /// 修改后数量(仅当IsRepeat为True时生效)
    /// </summary>
    public decimal? ModifyQty { get; set; }

    /// <summary>
    /// 是否移除待确认条码
    /// </summary>
    public bool RemoveFromWaitingList { get; set; }
}