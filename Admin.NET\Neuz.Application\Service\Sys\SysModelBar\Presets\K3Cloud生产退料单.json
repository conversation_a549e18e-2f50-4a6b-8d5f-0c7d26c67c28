{"$schema": "http://barModelSchema.json", "modelServiceName": "K3CloudBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "FBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FMaterialId.FNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FMaterialId.FName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FDate", "title": "单据日期", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "FStockOrgId.FNumber", "title": "收料组织编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockOrgId.FName", "title": "收料组织名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "SourceBillDate", "title": "日期", "sortable": true, "format": "Date"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "合格数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "_ExpUnit_", "title": "保质期单位"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "FStockOrgIdNumber", "title": "收料组织编码"}, {"fieldName": "FStockOrgIdName", "title": "收料组织名称"}, {"fieldName": "<PERSON><PERSON><PERSON>", "title": "已制作数量"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "SourceBillNo", "title": "单据编号", "sortable": true}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "CustomerNumber", "title": "客户编码"}, {"fieldName": "CustomerName", "title": "客户名称"}, {"fieldName": "SupplierNumber", "title": "供应商编码"}, {"fieldName": "SupplierName", "title": "供应商名称"}, {"fieldName": "WorkShopNumber", "title": "生产车间编码"}, {"fieldName": "WorkShopName", "title": "生产车间名称"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Cloud", "idFieldName": "FID", "entryIdFieldName": "FEntity_FEntryID", "content": "{\r\n    \"$schema\": \"http://k3CloudDataQuerySchema.json\",\r\n    \"formId\": \"PRD_ReturnMtrl\",\r\n    \"fieldKeys\": [\r\n        \"FID AS _id\",\r\n        \"FEntity_FEntryID AS _entryId\",\r\n        \"'PRD_ReturnMtrl' AS SourceBillKey\",\r\n        \"FID AS SourceBillId\",\r\n        \"FEntity_FEntryID AS SourceBillEntryId\",\r\n        \"FBillNo AS SourceBillNo\",\r\n        \"FEntity_FSeq AS SourceBillEntrySeq\",\r\n        \"FDate AS SourceBillDate\",\r\n        \"FMaterialId.FMasterId AS MaterialId\",\r\n        \"FMaterialId.FNumber AS MaterialNumber\",\r\n        \"FMaterialId.FName AS MaterialName\",\r\n        \"FMaterialId.FSpecification AS MaterialSpec\",\r\n        \"FQty AS Qty\",\r\n        \"FUnitID AS UnitId\",\r\n        \"FUnitID.FNumber AS UnitNumber\",\r\n        \"FUnitID.FName AS UnitName\",\r\n        \"FLot.FNumber AS BatchNo\",\r\n        \"FMaterialId.FIsBatchManage AS IsBatchManage\",\r\n        \"FMaterialId.FIsSNManage AS IsSnManage\",\r\n        \"FMaterialId.FIsKFPeriod AS IsKfPeriod\",\r\n        \"FMaterialId.FExpPeriod AS ExpPeriod\",\r\n        \"FMaterialId.FExpUnit AS _ExpUnit_\",\r\n        \"FProduceDate AS ProduceDate\",\r\n        \"FExpiryDate AS ExpiryDate\",\r\n        \"FAuxPropId AS AuxPropId\",\r\n        \"FAuxPropId.FNumber AS AuxPropNumber\",\r\n        \"FAuxPropId.FName AS AuxPropName\",\r\n        \"FStockOrgId\",\r\n        \"FStockOrgId.FNumber AS FStockOrgIdNumber\",\r\n        \"FStockOrgId.FName AS FStockOrgIdName\"\r\n    ],\r\n    \"filters\": [],\r\n    \"customFilter\": \"\"\r\n}"}}