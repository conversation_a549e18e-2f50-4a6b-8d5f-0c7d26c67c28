using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System.Security.Claims;
using Yitter.IdGenerator;

namespace Neuz.UnitTest
{
    /// <summary>
    /// 单元测试启动类
    /// </summary>
    public sealed class Startup : AppStartup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            // 注册远程服务
            services.AddHttpRemote();
            // 配置选项
            services.AddConfigurableOptions<DbConnectionOptions>();
            services.AddConfigurableOptions<SnowIdOptions>();
            services.AddConfigurableOptions<CacheOptions>();
            // 缓存注册
            services.AddCache();
            // SqlSugar
            services.AddSqlSugar();
            // 雪花Id
            YitIdHelper.SetIdGenerator(App.GetOptions<SnowIdOptions>());
            // 集成第三方多语言配置
            services.AddAppLocalization(settings => { services.AddJsonLocalization(options => options.ResourcesPath = settings.ResourcesPath); });

            // 事件总线
            services.AddEventBus(options =>
            {
                options.UseUtcTimestamp = false;
                // 不启用事件日志
                options.LogEnabled = false;
                // 事件执行器（失败重试）
                options.AddExecutor<RetryEventHandlerExecutor>();
                //// 替换事件源存储器
                //options.ReplaceStorer(serviceProvider =>
                //{
                //    var redisCache = serviceProvider.GetService<ICache>();
                //    // 创建默认内存通道事件源对象，可自定义队列路由key，比如这里是 eventbus
                //    return new RedisEventSourceStorer(redisCache, "eventbus", 3000);
                //});
            });

            // 模拟 Http 请求上下文 IHttpContextAccessor
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            var context = new DefaultHttpContext
            {
                // 超级管理员信息
                User = new ClaimsPrincipal(new ClaimsIdentity(new[]
                    {
                        new Claim(ClaimConst.UserId, "*************"),
                        new Claim(ClaimConst.Account, "superadmin"),
                        new Claim(ClaimConst.RealName, "超级管理员"),
                        new Claim(ClaimConst.AccountType, ((int)AccountTypeEnum.SuperAdmin).ToString()),
                        new Claim(ClaimConst.TenantId, SqlSugarConst.MainConfigId),
                    })
                )
            };

            mockHttpContextAccessor.Setup(o => o.HttpContext).Returns(context);
            services.AddSingleton(mockHttpContextAccessor.Object);
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
        }
    }
}