﻿namespace Neuz.Core.SeedData;

public class ProjMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            // WMS库存与K3库存比较报表
            new SysMenu { Id = 2370000710001, Pid = 1370000710000, Title = "WMS库存与K3库存比较", Path = "/bd/projInventoryComparisonReport", Name = "projInventoryComparisonReport", Component = "/business/proj/projInventoryComparisonReport/index", Icon = "ele-Document", Type = MenuTypeEnum.Menu, CreateTime = DateTime.Parse("2024-11-22 00:00:00"), OrderNo = 100 },
        };
    }
}