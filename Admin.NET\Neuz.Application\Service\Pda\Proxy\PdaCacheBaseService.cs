﻿using Furion.Localization;
using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Core.Entity.Pda.Erp;

namespace Neuz.Application.Pda.Proxy;

public class PdaCacheBaseService : ITransient
{
    protected IServiceProvider _serviceProvider;
    protected readonly Func<string, ITransient, object> _resolveNamed;

    public PdaCacheBaseService(IServiceProvider serviceProvider, Func<string, ITransient, object> resolveNamed)
    {
        _serviceProvider = serviceProvider;
        _resolveNamed = resolveNamed;
    }

    #region BasicModel

    /// <summary>
    /// 获取PDA BasicModel
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public IPdaLookupModel GetPdaBasicModel(string key)
    {
        if (!PdaProxy.BasicModels.ContainsKey(key)) throw Oops.Bah(L.Text["没有找到BasicModel的Key[{0}]", key]);
        var model = _resolveNamed(PdaProxy.BasicModels[key].GetType().Name, default) as IPdaLookupModel;
        return model;
    }

    /// <summary>
    /// 是否存在PdaBasicModel
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public bool IsExistPdaBasicModel(string key)
    {
        return PdaProxy.BasicModels.ContainsKey(key);
    }

    #endregion

    #region 删除暂存

    /// <summary>
    /// 删除暂存
    /// </summary>
    /// <param name="tranId"></param>
    public void DeleteTempStore(long tranId)
    {
        var rep = App.GetService<SqlSugarRepository<PdaTempStoreBill>>(_serviceProvider);
        var tempStores = rep.GetList(r => r.TranId.Equals(tranId));
        rep.Delete(tempStores);
    }

    #endregion
}