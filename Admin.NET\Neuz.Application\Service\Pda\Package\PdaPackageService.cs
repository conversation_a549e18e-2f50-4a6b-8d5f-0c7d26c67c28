﻿using Neuz.Application.Pda.Package.Data;
using Neuz.Application.Pda.Package.Dto;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Package;

/// <summary>
/// Pda装箱服务
/// </summary>
[ApiDescriptionSettings("PDA", Name = "PdaPackage", Order = 300)]
public class PdaPackageService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="serviceProvider"></param>
    public PdaPackageService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 扫描条码
    /// </summary>
    /// <param name="key"></param>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <returns></returns>
    [HttpGet("scanBarcode")]
    public async Task<dynamic> ScanBarcode([FromQuery] string key, [FromQuery] long tranId, [FromQuery] string barcode)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        if (string.IsNullOrEmpty(barcode)) throw Oops.Bah(PdaErrorCode.Pda1012);
        var billData = pdaCacheService.GetBillData(key, tranId);
        var billModel = (PdaPackageModel)pdaCacheService.GetPdaModel(key);
        await billModel.ScanBarcode(tranId, barcode);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 删除条码
    /// </summary>
    /// <returns></returns>
    [HttpPost("deleteBarcode")]
    public async Task<dynamic> DeleteBarcode([FromBody] PdaPackageServiceDeleteBarcodeInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        if (input.BarcodeId == 0) throw Oops.Bah(PdaErrorCode.Pda1026);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = (PdaPackageModel)pdaCacheService.GetPdaModel(input.Key);
        billModel.DeleteBarcode(input.TranId, input.BarcodeId);
        return await Task.FromResult(billData.DataShow);
    }

    /// <summary>
    /// 提交装箱
    /// </summary>
    /// <returns></returns>
    [HttpPost("submit")]
    public async Task<dynamic> Submit([FromBody] PdaPackageServiceSubmitInput input)
    {
        var pdaCacheService = App.GetService<PdaDataCacheService>(_serviceProvider);
        var billData = pdaCacheService.GetBillData(input.Key, input.TranId);
        var billModel = (PdaPackageModel)pdaCacheService.GetPdaModel(input.Key);
        await billModel.Submit(input.TranId);
        return await Task.FromResult(billData.DataShow);
    }
}