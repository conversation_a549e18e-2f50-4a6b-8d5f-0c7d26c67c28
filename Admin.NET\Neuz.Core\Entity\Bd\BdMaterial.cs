﻿namespace Neuz.Core.Entity;

/// <summary>
/// 物料
/// </summary>
[SugarTable(null, "物料")]
public class BdMaterial : EsBdEntityBase
{
    /// <summary>
    /// 规格型号
    /// </summary>
    [SugarColumn(ColumnDescription = "规格型号", Length = 200)]
    public string? Specification { get; set; }

    /// <summary>
    /// 物料分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料分组Id")]
    public long? MaterialGroupId { get; set; }

    /// <summary>
    /// 物料分组
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialGroupId))]
    [CustomSerializeFields]
    public BdDataGroup MaterialGroup { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 启用批号管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用批号管理")]
    public bool IsBatchManage { get; set; }

    /// <summary>
    /// 启用序列号管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用序列号管理")]
    public bool IsSnManage { get; set; }

    /// <summary>
    /// 启用保质期管理
    /// </summary>
    [SugarColumn(ColumnDescription = "启用保质期管理")]
    public bool IsKfPeriod { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    [SugarColumn(ColumnDescription = "保质期")]
    public int ExpPeriod { get; set; }

    /// <summary>
    /// 保质期单位
    /// </summary>
    [SugarColumn(ColumnDescription = "保质期单位")]
    public ExpUnit ExpUnit { get; set; }

    /// <summary>
    /// 含税单价
    /// </summary>
    [SugarColumn(ColumnDescription = "含税单价")]
    public decimal Price { get; set; }

    /// <summary>
    /// 长
    /// </summary>
    [SugarColumn(ColumnDescription = "长")]
    public decimal Length { get; set; }

    /// <summary>
    /// 宽
    /// </summary>
    [SugarColumn(ColumnDescription = "宽")]
    public decimal Width { get; set; }

    /// <summary>
    /// 高
    /// </summary>
    [SugarColumn(ColumnDescription = "高")]
    public decimal Height { get; set; }

    /// <summary>
    /// 净重
    /// </summary>
    [SugarColumn(ColumnDescription = "净重")]
    public decimal NetWeight { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    [SugarColumn(ColumnDescription = "毛重")]
    public decimal GrossWeight { get; set; }

    /// <summary>
    /// 物料体积
    /// </summary>
    [SugarColumn(ColumnDescription = "物料体积")]
    public decimal Volume { get; set; }

    /// <summary>
    /// 包装体积
    /// </summary>
    [SugarColumn(ColumnDescription = "包装体积")]
    public decimal PackingVolume { get; set; }

    /// <summary>
    /// 最小库存
    /// </summary>
    [SugarColumn(ColumnDescription = "最小库存")]
    public decimal MinInvQty { get; set; }

    /// <summary>
    /// 标准装箱量
    /// </summary>
    [SugarColumn(ColumnDescription = "标准装箱量")]
    public int StdPackingQty { get; set; }

    /// <summary>
    /// 启用收货检验
    /// </summary>
    [SugarColumn(ColumnDescription = "启用收货检验")]
    public bool IsCheckReceive { get; set; }

    /// <summary>
    /// 辅助属性类型编码
    /// </summary>
    /// <remarks>
    /// 支持多个，用逗号分隔
    /// </remarks>
    [SugarColumn(ColumnDescription = "辅助属性类型编码", Length = 1000)]
    public string? AuxPropTypeNumbers { get; set; }

    /// <summary>
    /// 辅助单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助单位Id")]
    public long? AuxUnitId { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxUnitId))]
    [CustomSerializeFields]
    public BdUnit AuxUnit { get; set; }
}