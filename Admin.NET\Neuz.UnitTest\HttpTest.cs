﻿using Furion;
using Furion.HttpRemote;
using Xunit;
using Xunit.Abstractions;

namespace Neuz.UnitTest
{
    public class HttpTest
    {
        private readonly ITestOutputHelper _output;

        public HttpTest(ITestOutputHelper tempOutput)
        {
            _output = tempOutput;
        }

        [Fact(DisplayName = "请求百度测试")]
        public async Task RequestBaiDu()
        {
            var httpRemoteService = App.GetService<IHttpRemoteService>();
            var rep = await httpRemoteService.GetAsync("https://www.baidu.com");
            Assert.True(rep.IsSuccessStatusCode);
        }

        [Theory(DisplayName = "带参数测试")]
        [InlineData(1, 2)]
        [InlineData(3, 4)]
        [InlineData(5, 7)]
        public void 带参数测试(int i, int j)
        {
            Assert.NotEqual(0, (i + j) % 2);
        }
    }
}