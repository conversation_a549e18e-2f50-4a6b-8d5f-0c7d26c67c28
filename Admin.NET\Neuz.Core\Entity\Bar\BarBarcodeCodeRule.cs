﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码档案编码规则绑定
/// </summary>
[SugarTable(null, "条码档案编码规则绑定")]
public class BarBarcodeCodeRule : EntityTenant
{
    /// <summary>
    /// 属性名称
    /// </summary>
    [SugarColumn(ColumnDescription = "属性名称", Length = 50)]
    public string PropertyName { get; set; }

    /// <summary>
    /// 功能点Key
    /// </summary>
    [SugarColumn(ColumnDescription = "功能点Key", Length = 100)]
    public string FuncKey { get; set; }

    /// <summary>
    /// 编码规则Id
    /// </summary>
    [SugarColumn(ColumnDescription = "编码规则Id")]
    public long CodeRuleId { get; set; }

    /// <summary>
    /// 编码规则
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(CodeRuleId))]
    public SysCodeRule CodeRule { get; set; }

    /// <summary>
    /// 是否默认规则
    /// </summary>
    /// <remarks>
    /// 同一属性名称中只有一个默认规则
    /// </remarks>
    [SugarColumn(ColumnDescription = "是否默认规则")]
    public bool IsDefault { get; set; }
}