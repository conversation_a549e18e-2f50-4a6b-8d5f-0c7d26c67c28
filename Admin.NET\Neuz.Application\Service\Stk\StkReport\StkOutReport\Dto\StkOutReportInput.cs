﻿using Magicodes.ExporterAndImporter.Core;
using static System.Int32;

namespace Neuz.Application;

/// <summary>
/// 出库报表输入参数
/// </summary>
public class StkOutReportInput : BasePageInput
{
    /// <summary>
    /// 单据编号
    /// </summary>
    public string BillNo { get; set; }

    /// <summary>
    /// 外部单号
    /// </summary>
    public string EsBillNo { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    public string BillType { get; set; }

    /// <summary>
    /// 单据日期开始
    /// </summary>
    public DateTime? BillDateBegin { get; set; }

    /// <summary>
    /// 单据日期结束
    /// </summary>
    public DateTime? BillDateEnd { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public string BatchNo { get; set; }

    /// <summary>
    /// 货主编码
    /// </summary>
    public string OwnerNumber { get; set; }

    /// <summary>
    /// 货主名称
    /// </summary>
    public string OwnerName { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    public string WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    public string WarehouseName { get; set; }

    /// <summary>
    /// 库区编码
    /// </summary>
    public string WhAreaNumber { get; set; }

    /// <summary>
    /// 库区名称
    /// </summary>
    public string WhAreaName { get; set; }

    /// <summary>
    /// 库位编码
    /// </summary>
    public string WhLocNumber { get; set; }

    /// <summary>
    /// 库位名称
    /// </summary>
    public string WhLocName { get; set; }

    /// <summary>
    /// 容器编码
    /// </summary>
    public string ContainerNumber { get; set; }

    /// <summary>
    /// 辅助属性编码
    /// </summary>
    public string AuxPropValueNumber { get; set; }

    /// <summary>
    /// 辅助属性名称
    /// </summary>
    public string AuxPropValueName { get; set; }

    /// <summary>
    /// 客户编码
    /// </summary>
    public string CustomerNumber { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string CustomerName { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 发运日期开始
    /// </summary>
    public DateTime? ShippingDateBegin { get; set; }

    /// <summary>
    /// 发运日期结束
    /// </summary>
    public DateTime? ShippingDateEnd { get; set; }

    /// <summary>
    /// 拣货日期开始
    /// </summary>
    public DateTime? PickingDateBegin { get; set; }

    /// <summary>
    /// 拣货日期结束
    /// </summary>
    public DateTime? PickingDateEnd { get; set; }
}