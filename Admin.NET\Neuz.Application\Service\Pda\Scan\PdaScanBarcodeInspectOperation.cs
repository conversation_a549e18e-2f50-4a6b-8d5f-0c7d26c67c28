﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan;

public class PdaScanBarcodeInspectOperation : PdaScanBarcodeOperationBase
{
    public PdaScanBarcodeInspectOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaScanBarcodeArgs args)
    {
        var barcodeService = App.GetService<BarBarcodeService>(_serviceProvider);
        var pdaCacheService = App.GetService<PdaCacheService>();
        var billData = pdaCacheService.GetBillData(args.TranId);
        var barcode = barcodeService.GetBarcodeAsync(args.BarcodeString).Result;
        if (barcode == null) return;
        var billModel = pdaCacheService.GetPdaBillModel(billData.BillModelKey);
        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        if (barcode.BarcodeType == BarcodeType.Deduct && barcode.Qty <= 0)
            throw Oops.Bah(PdaErrorCode.Pda1033, barcode.Barcode);
        if (barcode.BarcodeType == BarcodeType.Container && barcode.Qty <= 0)
        {
            //如果是出库/调拨类型,不允许扫描为0的容器条码
            if (billModel.Config.BillParams.OpType == BarOpType.OutStock || billModel.Config.BillParams.OpType == BarOpType.Transfer)
                throw Oops.Bah(PdaErrorCode.Pda1035, barcode.Barcode);
        }

        if (billModel.Config.BillParams.IsBarcodeOpTypeInOutLimit)
        {
            //如果是入库类型,需要不在库状态,如果是出库类型,必须在库
            if (billModel.Config.BillParams.OpType == BarOpType.InStock && barcode.IsInStock) throw Oops.Bah(L.Text["条码[{0}]已在库,不能入库", barcode.Barcode]);
            if (billModel.Config.BillParams.OpType == BarOpType.OutStock && !barcode.IsInStock) throw Oops.Bah(L.Text["条码[{0}]不在库,不能出库", barcode.Barcode]);
        }

        // 检验不需要判断什么类型条码,暂时当所有条码,都要弹窗
        PdaRestfulCode restfulCode = PdaRestfulCode.P104;
        //如果返回只有一个条码,并且为一物一码
        PdaExtrasRestfulResult<BarBarcode> result = new PdaExtrasRestfulResult<BarBarcode>
        {
            Code = (int)restfulCode,
            Message = null,
            Data = barcode,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        UnifyContext.Fill(result);

        args.Barcodes.Add(barcode);
        args.IsResult = true;
    }
}