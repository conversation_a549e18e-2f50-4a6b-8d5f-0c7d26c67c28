﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
		<NoWarn>1701;1702;8616;1591;8618;8619;8629;8602;8603;8604;8625;8765</NoWarn>
		<DocumentationFile>Admin.NET.Plugin.Core.xml</DocumentationFile>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Const\**" />
	  <Compile Remove="Extensions\**" />
	  <Compile Remove="Options\**" />
	  <EmbeddedResource Remove="Const\**" />
	  <EmbeddedResource Remove="Extensions\**" />
	  <EmbeddedResource Remove="Options\**" />
	  <None Remove="Const\**" />
	  <None Remove="Extensions\**" />
	  <None Remove="Options\**" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\PluginCore\PluginCore.AspNetCore\PluginCore.AspNetCore.csproj" />
	</ItemGroup> 

</Project>
