﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;

namespace Neuz.Application.Pda.LocalBill;

public interface IPdaLocalBillSchema : IPdaSchema
{
    /// <summary>
    /// 表头
    /// </summary>
    List<PdaLocalBillColumn> DestHead { get; set; }

    /// <summary>
    /// 条码 （匹配汇总行）
    /// </summary>
    List<PdaLocalBillColumn> Barcode { get; set; }

    /// <summary>
    /// 单据Link
    /// </summary>
    PdaBillLink BillLink { get; set; }

    /// <summary>
    /// 表体
    /// </summary>
    List<PdaLocalBillColumn> BillDetail { get; set; }
}

/// <summary>
/// 基础实现
/// </summary>
public class PdaLocalBillSchemaBase : IPdaLocalBillSchema
{
    public virtual List<PdaLocalBillColumn> DestHead { get; set; }
    public virtual List<PdaLocalBillColumn> Barcode { get; set; }
    public virtual PdaBillLink BillLink { get; set; }
    public virtual List<PdaLocalBillColumn> BillDetail { get; set; }
}