﻿namespace Neuz.Application.Pda.Proxy.Dto;

public class PdaApiDeleteDataInput
{
    /// <summary>
    /// Model的Key
    /// </summary>
    public string Key { get; set; }
    /// <summary>
    /// 事务Id
    /// </summary>
    public long TranId { get; set; }
    /// <summary>
    /// 需要删除的数据Key
    /// </summary>
    public string DataKey { get; set; }
    /// <summary>
    /// 需要删除的数据值 (通常是某行的主键Id)
    /// </summary>
    public string ValueKey { get; set; }
}