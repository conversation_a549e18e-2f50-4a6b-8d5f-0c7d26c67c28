﻿namespace Neuz.Application.Proj.ProjInventoryComparisonReport.Dto;

public class ProjInventoryComparisonOutput
{
    public long Id { get; set; }
    public string MaterialNumber { get; set; }
    public string MaterialName { get; set; }
    public long MaterialId { get; set; }
    public string BatchNo { get; set; }
    public DateTime? ProduceDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public string OwnerNumber { get; set; }
    public string OwnerName { get; set; }
    public long OwnerId { get; set; }
    public string WhLocEsId { get; set; }
    public string WarehouseNumber { get; set; }
    public long WarehouseId { get; set; }
    public string WarehouseName { get; set; }
    public string WhAreaNumber { get; set; }
    public string WhAreaName { get; set; }
    public long WhAreaId { get; set; }
    public string WhLocNumber { get; set; }
    public string WhLocName { get; set; }
    public string WhLocEsNumber { get; set; }
    public long WhLocId { get; set; }
    public long? AuxPropValueId { get; set; }
    public string AuxPropValueNumber { get; set; }
    public string AuxPropValueName { get; set; }
    public decimal? Qty { get; set; }
    public decimal? LockQty { get; set; }
    public decimal? AvailableQty { get; set; }
    public decimal? PreInQty { get; set; }
    public long UnitId { get; set; }
    public string UnitNumber { get; set; }
    public string UnitName { get; set; }
    public DateTime? CreateTime { get; set; }
    public DateTime? UpdateTime { get; set; }
    public string ContainerNumber { get; set; }
    public string Specification { get; set; }
    public decimal? K3InvQty { get; set; }
    public decimal? Difference { get; set; }
}