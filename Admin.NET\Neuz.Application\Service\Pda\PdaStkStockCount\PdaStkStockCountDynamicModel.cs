﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.PdaStkStockCount;
using Neuz.Application.Pda.PdaStkStockCount.Dto;

namespace Neuz.Application.Service.Pda.PdaStkStockCount;

public class PdaStkStockCountDynamicModel : PdaStkStockCountModel
{
    public PdaStkStockCountDynamicModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = "StkStockCountDynamic";

    public override async Task SelectLookupDataSourceInfo(long tranId, string valueKey)
    {
        var id = Convert.ToInt64(valueKey);
        var stockCount = await Rep.AsQueryable()
            .Where(r => r.StockCountStatus == StkStockCountStatus.GeneratedDetail && r.BillType == "DTPD")
            .Where(r => r.Id == id)
            .IncludeNavCol()
            .Take(Take).FirstAsync();
        var pdaData = GetPdaData(tranId);
        if (stockCount == null) return;

        pdaData.StkStockCount = stockCount;
        pdaData.StkStockCountEntries.Clear();

        stockCount.Entries.ForEach(r =>
        {
            pdaData.StkStockCountEntries.Add(new PdaStkStockCountEntry
            {
                DetailId = $"{YitIdHelper.NextId()}",
                IsNew = false,
                Entry = r,
                ScanQty = 0,
                IncludeBarcodes = new List<PdaLocalBillBarcode>()
            });
        });

        pdaData.StkStockCountStockEntries = stockCount.WhAreaEntries;
    }

    public override async Task<List<PdaLookupOutput>> LookupQuerySourceInfo(long tranId, string lookupValue)
    {
        var stockCounts = await Rep.AsQueryable()
            .Where(r => r.StockCountStatus == StkStockCountStatus.GeneratedDetail && r.BillType == "DTPD")
            .WhereIF(!string.IsNullOrEmpty(lookupValue), r => r.Name.Contains(lookupValue) || r.BillNo.Contains(lookupValue))
            .OrderByDescending(x => x.CreateTime)
            .Take(Take).ToListAsync();
        List<PdaLookupOutput> outputs = new List<PdaLookupOutput>();
        stockCounts.ForEach(r =>
        {
            outputs.Add(new PdaLookupOutput
            {
                Key = r.Id + "",
                Title = $"[{r.BillNo}]{r.Name}",
                SubTitle = r.ApproveTime == null ? "" : r.ApproveTime.Value.ToString("yyyy-MM-dd"),
            });
        });
        return outputs;
    }
}