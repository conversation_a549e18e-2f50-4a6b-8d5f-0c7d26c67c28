using Furion.Localization;
using Neuz.Application.Pda.InventoryQuery;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Bill.Model.Basic.Cloud;

public class PdaCloudInventoryQueryHelper
{
    public static string GetScanHeadOrgId(long tranId, string modelKey, IServiceProvider serviceProvider)
    {
        var dataCacheService = App.GetService<PdaDataCacheService>(serviceProvider);
        var pdaData = dataCacheService.GetBillData(modelKey, tranId);
        if (pdaData is not PdaInventoryQueryData data) throw Oops.Bah(L.Text["请先选择组织"]);

        var orgId = data.SourceCells["OrgId"] + "";
        if (!string.IsNullOrEmpty(orgId)) return orgId;

        throw Oops.Bah(L.Text["请先选择组织"]);
    }
}