﻿namespace Neuz.Application.Proj.ProjInventoryComparisonReport.Dto;

public class ProjInventoryComparisonTemp
{
    public string Id { get; set; }
    public string MaterialIdNumber { get; set; }
    public string LotText { get; set; }
    public string StockOrgIdNumber { get; set; }
    public string StockOrgIdName { get; set; }
    public decimal BaseQty { get; set; }
    public string StockId { get; set; }
    public string StockLocId { get; set; }
    public string StockLocIdNumber { get; set; }
    public string StockIdNumber { get; set; }
    public string StockIdName { get; set; }
    public string MaterialIdName { get; set; }
    public string BaseUnitIdNumber { get; set; }
    public string OwnerIdNumber { get; set; }
    public string AuxPropId { get; set; }
    public string AuxPropIdNumber { get; set; }
    public string AuxPropIdName { get; set; }
}