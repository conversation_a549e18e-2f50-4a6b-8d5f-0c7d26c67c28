﻿namespace Neuz.Core.Entity;

/// <summary>
/// 收货单
/// </summary>
[SugarTable(null, "收货单")]
public class StkReceive : EsBillEntityBase
{
    /// <summary>
    /// 单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "单据日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "单据类型", Length = 80)]
    public string BillType { get; set; }

    /// <summary>
    /// 业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "业务状态")]
    public StkReceiveStatus Status { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 部门Id
    /// </summary>
    [SugarColumn(ColumnDescription = "部门Id")]
    public long? DepartmentId { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DepartmentId))]
    [CustomSerializeFields]
    public BdDepartment Department { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    [SugarColumn(ColumnDescription = "供应商Id")]
    public long? SupplierId { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
    [CustomSerializeFields]
    public BdSupplier Supplier { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "客户Id")]
    public long? CustomerId { get; set; }

    /// <summary>
    /// 客户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(CustomerId))]
    [CustomSerializeFields]
    public BdCustomer Customer { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [SugarColumn(ColumnDescription = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkReceiveEntry.Id))]
    public List<StkReceiveEntry> Entries { get; set; }
}