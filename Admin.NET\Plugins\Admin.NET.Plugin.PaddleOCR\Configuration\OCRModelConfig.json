{"OCRModels": {"ch_PP-OCRv4": {"Name": "中英文模型V4 (轻量版)", "Description": "适合一般场景的轻量级模型", "ModelPath": "ch_PP-OCRv4", "Files": {"det_infer": {"Name": "ch_PP-OCRv4_det_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar", "Description": "文本检测模型"}, "rec_infer": {"Name": "ch_PP-OCRv4_rec_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar", "Description": "文本识别模型"}, "cls_infer": {"Name": "ch_ppocr_mobile_v2.0_cls_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar", "Description": "文本方向分类器"}, "keys": {"Name": "ppocr_keys.txt", "DownloadUrl": "https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.7/ppocr/utils/ppocr_keys_v1.txt", "Description": "中文字符集"}}}, "ch_ppocr_server_v2.0": {"Name": "服务器中英文模型v2", "Description": "高精度服务器版本，适合对准确率要求较高的场景", "ModelPath": "ch_ppocr_server_v2.0", "Files": {"det_infer": {"Name": "ch_ppocr_server_v2.0_det_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_server_v2.0_det_infer.tar", "Description": "文本检测模型"}, "rec_infer": {"Name": "ch_ppocr_server_v2.0_rec_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_server_v2.0_rec_infer.tar", "Description": "文本识别模型"}, "cls_infer": {"Name": "ch_ppocr_mobile_v2.0_cls_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar", "Description": "文本方向分类器"}, "keys": {"Name": "ppocr_keys.txt", "DownloadUrl": "https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.7/ppocr/utils/ppocr_keys_v1.txt", "Description": "中文字符集"}}}, "en_PP-OCRv3": {"Name": "英文和数字模型v3", "Description": "专门针对英文和数字优化的模型", "ModelPath": "en_PP-OCRv3", "Files": {"det_infer": {"Name": "en_PP-OCRv3_det_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv3/english/en_PP-OCRv3_det_infer.tar", "Description": "英文文本检测模型"}, "rec_infer": {"Name": "en_PP-OCRv3_rec_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv3/english/en_PP-OCRv3_rec_infer.tar", "Description": "英文文本识别模型"}, "cls_infer": {"Name": "ch_ppocr_mobile_v2.0_cls_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar", "Description": "文本方向分类器"}, "keys": {"Name": "en_dict.txt", "DownloadUrl": "https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.7/ppocr/utils/en_dict.txt", "Description": "英文字符集"}}}, "ch_PP-OCRv4_server": {"Name": "服务器中英文模型V4", "Description": "最新高精度服务器版本，性能和准确率最佳", "ModelPath": "ch_PP-OCRv4_server", "Files": {"det_infer": {"Name": "ch_PP-OCRv4_det_server_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_server_infer.tar", "Description": "服务器版文本检测模型"}, "rec_infer": {"Name": "ch_PP-OCRv4_rec_server_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_server_infer.tar", "Description": "服务器版文本识别模型"}, "cls_infer": {"Name": "ch_ppocr_mobile_v2.0_cls_infer", "DownloadUrl": "https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar", "Description": "文本方向分类器"}, "keys": {"Name": "ppocr_keys.txt", "DownloadUrl": "https://raw.githubusercontent.com/PaddlePaddle/PaddleOCR/release/2.7/ppocr/utils/ppocr_keys_v1.txt", "Description": "中文字符集"}}}}, "DownloadInstructions": {"Steps": ["1. 下载对应模型的tar文件", "2. 解压tar文件到对应的模型目录", "3. 确保目录结构正确：OcrModel/{ModelPath}/{FileName}/", "4. 重启应用程序以加载新模型"], "Notes": ["所有模型文件都需要放在 Plugins/Admin.NET.Plugin.PaddleOCR/OcrModel/ 目录下", "每个模型需要包含检测模型(det_infer)、识别模型(rec_infer)、分类器(cls_infer)和字符集文件(keys)", "如果某个模型目录不存在或不完整，该模型将不会被加载"]}}