﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Furion.InstantMessaging;
using Furion.Localization;
using Microsoft.AspNetCore.SignalR;

namespace Admin.NET.Core;

/// <summary>
/// 在线用户集线器
/// </summary>
[MapHub("/hubs/onlineUser")]
public class OnlineUserHub : Hub<IOnlineUserHub>
{
    private const string GROUP_ONLINE = "GROUP_ONLINE_"; // 租户分组前缀

    //private readonly SqlSugarRepository<SysOnlineUser> _sysOnlineUerRep;
    private readonly SysMessageService _sysMessageService;
    private readonly IHubContext<OnlineUserHub, IOnlineUserHub> _onlineUserHubContext;
    private readonly SysCacheService _sysCacheService;
    private readonly SysLicenseCheckService _sysLicenseCheckService;
    private readonly SysOnlineUserService _sysOnlineUserService;

    public OnlineUserHub( //SqlSugarRepository<SysOnlineUser> sysOnlineUerRep,
        SysMessageService sysMessageService,
        IHubContext<OnlineUserHub, IOnlineUserHub> onlineUserHubContext,
        SysCacheService sysCacheService,
        SysLicenseCheckService sysLicenseCheckService,
        SysOnlineUserService sysOnlineUserService,
        SqlSugarRepository<SysUser> sysUserRep)
    {
        //_sysOnlineUerRep = sysOnlineUerRep;
        _sysMessageService = sysMessageService;
        _onlineUserHubContext = onlineUserHubContext;
        _sysCacheService = sysCacheService;
        _sysLicenseCheckService = sysLicenseCheckService;
        _sysOnlineUserService = sysOnlineUserService;
    }

    /// <summary>
    /// 连接
    /// </summary>
    /// <returns></returns>
    public override async Task OnConnectedAsync()
    {
        var httpContext = Context.GetHttpContext();
        var token = httpContext.Request.Query["access_token"];
        var claims = JWTEncryption.ReadJwtToken(token)?.Claims;
        //如果web,pda端掉线重连,有可能token为空,导致claims为空
        if (claims == null) return;
        var client = Parser.GetDefault().Parse(httpContext.Request.Headers["User-Agent"]);

        var userId = claims?.FirstOrDefault(u => u.Type == ClaimConst.UserId)?.Value;
        var tenantId = claims?.FirstOrDefault(u => u.Type == ClaimConst.TenantId)?.Value;
        
        var clientType = claims?.FirstOrDefault(r => r.Type == ClaimConst.ClientType)?.Value;
        // 是否为 Pda 登录
        bool isPdaDevice = clientType != null && (clientType.ToLower() == LoginDeviceType.Pda.ToString().ToLower() || clientType == ((int)LoginDeviceType.Pda).ToString());

        // 如果是 Pda 登录
        if (isPdaDevice)
            await _sysOnlineUserService.PdaSingleLogin(long.Parse(userId), Context.ConnectionId);

        //授权校验
        var (isValid, errMsg) = _sysLicenseCheckService.OnlineUserCheck(Convert.ToInt64(tenantId), Convert.ToInt64(userId));
        if (!isValid)
        {
            Clients.Client(Context.ConnectionId).OverMaxConnect(errMsg).Wait();
            return;
        }

        var user = new SysOnlineUser
        {
            ConnectionId = Context.ConnectionId,
            UserId = string.IsNullOrWhiteSpace(userId) ? 0 : long.Parse(userId),
            UserName = claims?.FirstOrDefault(u => u.Type == ClaimConst.Account)?.Value,
            RealName = claims?.FirstOrDefault(u => u.Type == ClaimConst.RealName)?.Value,
            Time = DateTime.Now,
            Ip = httpContext.Connection.RemoteIpAddress.MapToIPv4().ToString(),
            Browser = client.UA.Family + client.UA.Major,
            Os = client.OS.Family + client.OS.Major,
            TenantId = string.IsNullOrWhiteSpace(tenantId) ? 0 : Convert.ToInt64(tenantId),

            Id = YitIdHelper.NextId(),
        };

        // 如果是 Pda 登录
        if (isPdaDevice)
            user.LoginDeviceType = LoginDeviceType.Pda;

        //await _sysOnlineUerRep.InsertAsync(user);
        _sysOnlineUserService.Add(user);

        _sysCacheService.Set(CacheConst.KeyUserOnline + user.UserId, user);

        // 以租户Id进行分组
        var groupName = $"{GROUP_ONLINE}{user.TenantId}";
        await _onlineUserHubContext.Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        //var userList = await _sysOnlineUerRep.AsQueryable().Filter("", true)
        //    .Where(u => u.TenantId == user.TenantId).Take(10).ToListAsync();

        var userList = (await _sysOnlineUserService.GetTenantOnlineUsers(user.TenantId.Value)).Take(10).ToList();

        await _onlineUserHubContext.Clients.Groups(groupName).OnlineUserList(new OnlineUserList
        {
            RealName = user.RealName,
            Online = true,
            UserList = userList
        });
    }

    /// <summary>
    /// 断开
    /// </summary>
    /// <param name="exception"></param>
    /// <returns></returns>
    public override async Task OnDisconnectedAsync(Exception exception)
    {
        if (string.IsNullOrEmpty(Context.ConnectionId)) return;

        //var user = await _sysOnlineUerRep.AsQueryable().Filter("", true).FirstAsync(u => u.ConnectionId == Context.ConnectionId);
        var user = _sysOnlineUserService.GetByConnectId(Context.ConnectionId);
        if (user == null) return;

        //await _sysOnlineUerRep.DeleteAsync(u => u.Id == user.Id);
        _sysOnlineUserService.Delete(user.Id);
        _sysCacheService.Remove(CacheConst.KeyUserOnline + user.UserId);

        // 通知当前组用户变动
        //var userList = await _sysOnlineUerRep.AsQueryable().Filter("", true)
        //    .Where(u => u.TenantId == user.TenantId).Take(10).ToListAsync();
        var userList = (await _sysOnlineUserService.GetTenantOnlineUsers(user.TenantId.Value)).Take(10).ToList();
        await _onlineUserHubContext.Clients.Groups($"{GROUP_ONLINE}{user.TenantId}").OnlineUserList(new OnlineUserList
        {
            RealName = user.RealName,
            Online = false,
            UserList = userList
        });
    }

    /// <summary>
    /// 强制下线
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task ForceOffline(OnlineUserHubInput input)
    {
        await _onlineUserHubContext.Clients.Client(input.ConnectionId).ForceOffline(L.Text["强制下线"]);
    }

    /// <summary>
    /// 发送信息给某个人
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public async Task ClientsSendMessage(MessageInput message)
    {
        await _sysMessageService.SendUser(message);
    }

    /// <summary>
    /// 发送信息给所有人
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public async Task ClientsSendMessagetoAll(MessageInput message)
    {
        await _sysMessageService.SendAllUser(message);
    }

    /// <summary>
    /// 发送消息给某些人（除了本人）
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public async Task ClientsSendMessagetoOther(MessageInput message)
    {
        await _sysMessageService.SendOtherUser(message);
    }

    /// <summary>
    /// 发送消息给某些人
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public async Task ClientsSendMessagetoUsers(MessageInput message)
    {
        await _sysMessageService.SendUsers(message);
    }
}