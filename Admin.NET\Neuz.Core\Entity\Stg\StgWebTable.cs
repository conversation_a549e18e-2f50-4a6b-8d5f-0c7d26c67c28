﻿namespace Neuz.Core.Entity;

/// <summary>
/// Web端表格配置
/// </summary>
[SugarTable(null, "Web端表格配置")]
public class StgWebTable : EntityTenant
{
    /// <summary>
    /// 表格Id
    /// </summary>
    [SugarColumn(ColumnDescription = "表格Id", Length = 200)]
    public string TableId { get; set; }

    /// <summary>
    /// 配置Json
    /// </summary>
    [SugarColumn(ColumnDescription = "配置Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Json { get; set; }

    /// <summary>
    /// 搜索配置Json
    /// </summary>
    [SugarColumn(ColumnDescription = "搜索配置Json", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? SearchJson { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long UserId { get; set; }
}