﻿using Furion.Localization;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.PdaStkStockCount.Dto;
using Neuz.Application.Pda.Proxy;
using Org.BouncyCastle.Crypto;

namespace Neuz.Application.Pda.PdaStkStockCount.Scan;

public class PdaStkStockCountPackageOperation : PdaLocalBillScanBarcodeOperationBase
{
    private SqlSugarRepository<BarBarcode> Rep => App.GetService<SqlSugarRepository<BarBarcode>>(_serviceProvider);
    /// <summary>
    /// 数据缓存服务
    /// </summary>
    protected PdaDataCacheService DataCacheService => App.GetService<PdaDataCacheService>(_serviceProvider);
    public PdaStkStockCountPackageOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        //查询装箱
        //1. 按条码号箱码
        var package = Rep.Change<BdContainer>().AsQueryable().First(r => r.Number == args.BarcodeString.Trim());
        //没有箱码,下一个扫描操作
        if (package == null) return;
        //查询是否包含条码
        var packageService = App.GetService<BarPackageService>(_serviceProvider);
        // TODO: 这里要通过箱查条码
        // var barcodes = packageService.QueryPackageBarcodeNotError(package.Number).GetAwaiter().GetResult();
        var barcodes = new List<BdBarcode>();
        if (barcodes.Count == 0) throw Oops.Bah(L.Text["[{0}]箱没有条码", package.Number]);

        var pdaData = (PdaStkStockCountData)DataCacheService.GetBillData(args.Key, args.TranId);
        var pdaModel = (PdaStkStockCountModel)DataCacheService.GetPdaModel(args.Key);
        var input = (PdaStkStockCountScanBarcodeInput)args.Properties["ScanBarcodeType"];

        //扫描箱是不允许改数量,所以忽略了扫描条码策略的弹窗修改
        //检证条码是否能扫描
        var verified = VerifyBarcode(pdaData, pdaModel, input, package, barcodes);
        if (!verified)
        {
            args.IsResult = true;
            return;
        }

        //添加到List列表
        List<PdaLocalBillBarcode> pdaBarcodes = new List<PdaLocalBillBarcode>();
        barcodes.ForEach(b =>
        {
            //1. 判断仓库优先
            PdaLocalBillBarcode pdaBarcode = new PdaLocalBillBarcode(b, b.Qty);
            // //TODO: 宏芯宇特殊处理
            // //这里要处理物料,可能不是基础资料的内码
            // var material = Rep.Change<BdMaterial>().GetFirst(r => r.Number == b.MaterialNumber);
            // pdaBarcode.CalcBarcode.MaterialId = material.Id + "";

            if (input.FirstStockType == FirstStockType.Barcode)
            {
                //条码优先
                //如果仓库为空,取选择的
                if (string.IsNullOrEmpty(b.WhAreaId + ""))
                {
                    if (pdaData.StockInfo == null || string.IsNullOrEmpty(pdaData.StockInfo.WhAreaId)) throw Oops.Bah(L.Text["条码没有仓库,请先选择仓库"]);
                    pdaBarcode.SetStockInfo(pdaData.StockInfo, false);
                }
                //如果条码上有仓库,直接取条码的,不用赋值仓库
            }
            else
            {
                //选择优先
                if (pdaData.StockInfo == null || string.IsNullOrEmpty(pdaData.StockInfo.WhAreaId)) throw Oops.Bah(L.Text["条码没有仓库,请先选择仓库"]);
                pdaBarcode.SetStockInfo(pdaData.StockInfo, false);
            }

            pdaBarcode.ContainerId = package.Id;
            pdaBarcodes.Add(pdaBarcode);
        });

        try
        {
            pdaBarcodes.ForEach(r =>
            {
                pdaModel.MatchingBarcode(args.TranId, r);
            });
        }
        catch (System.Exception)
        {
            //如果出错,回滚箱
            var removeBarcodes = pdaData.Barcodes.Where(r => r.ContainerId == package.Id).ToList();
            if (removeBarcodes.Count > 0) pdaModel.DeleteDataDeleteBarcode(args.TranId, removeBarcodes[0].DetailId);
            throw;
        }

        args.IsResult = true;
    }

    private bool VerifyBarcode(PdaStkStockCountData pdaData, PdaStkStockCountModel pdaModel, PdaStkStockCountScanBarcodeInput input, BdContainer package, List<BdBarcode> barcodes)
    {
        bool verified = true;
        barcodes.ForEach(barcode =>
        {
            if (pdaData.StkStockCount == null) throw Oops.Bah(L.Text["请先选择盘点单"]);
            if (pdaData.Barcodes.Any(r => r.Barcode.Id == barcode.Id)) throw Oops.Bah(L.Text["条码[{0}]已扫描", barcode.Barcode]);
            //找条码日志
            if (Rep.Change<StkStockCountBarcodeLog>().AsQueryable().Any(r => r.BarcodeId == barcode.Id && r.StockCountId == pdaData.StkStockCount.Id))
                throw Oops.Bah(L.Text["条码[{0}]已在其它盘点扫描", barcode.Barcode]);
        });

        var stockIds = pdaData.StkStockCountEntries.Select(f => f.Entry.WhAreaId + "").ToList().Distinct();
        
        if (!(stockIds.Contains(barcodes[0].WhAreaId + "")))
        {
            // 当前扫描的条码是否已扫描过待确认
            var IsWaitingConfirmBarcodesIncludeCurrentBarcode = pdaData.WaitingConfirmBarcodes.Any(a => a.TranId == pdaData.TranId && a.Barcode.Id == barcodes[0].Id);

            if (!IsWaitingConfirmBarcodesIncludeCurrentBarcode)
            {
                //throw Oops.Bah($"条码仓库与当前判断仓库不一致，是否确认扫描?");
                PdaRestfulCode restfulCode = PdaRestfulCode.P888;
                pdaData.WaitingConfirmBarcodes.Add(new WaitingConfirmBarcode
                {
                    TranId = pdaData.TranId,
                    Barcode = barcodes[0]
                });
                PdaExtrasRestfulResult<BarBarcode> result = new()
                {
                    Code = (int)restfulCode,
                    Message = L.Text["条码仓库与当前判断仓库不一致，是否确认扫描?"],
                    Data = null,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
                UnifyContext.Fill(result);
                verified = false;
            }
        }

        return verified;
    }
}