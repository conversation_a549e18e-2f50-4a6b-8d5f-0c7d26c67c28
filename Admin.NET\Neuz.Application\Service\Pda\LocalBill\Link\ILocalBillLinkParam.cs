﻿namespace Neuz.Application.Pda.LocalBill.Link;

/// <summary>
/// 单据转换规则参数
/// </summary>
public interface ILocalBillLinkParam
{
    /// <summary>
    /// 唯一标识
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 源单单据类型 (用作选择单过滤单据类型)
    /// </summary>
    public List<string> SourceBillTypes { get; set; }

    /// <summary>
    /// 转换规则映射
    /// </summary>
    public List<LocalBillLinkMapping> Mappings { get; set; }

    /// <summary>
    /// 计算规则映射
    /// </summary>
    public List<LocalBillQtyCalcMapping> CalcMappings { get; set; }

    /// <summary>
    /// 过滤条件
    /// </summary>
    public List<LocalBillFilter> Filters { get; set; }

    /// <summary>
    /// 明细显示内容
    /// </summary>
    public List<LocalBillContentInfo> DetailContents { get; set; }

    /// <summary>
    /// 条码规则映射
    /// </summary>
    public List<LocalBillLinkBarcodeMapping> BarcodeMappings { get; set; }

    /// <summary>
    /// 提交类型
    /// </summary>
    public Type SubmitBillType { get; set; }

    /// <summary>
    /// 提交规则映射
    /// </summary>
    public List<LocalBillLinkMapping> SubmitMappings { get; set; }

    /// <summary>
    /// 原单的明细字段名
    /// </summary>
    public string SourceEntryName { get; set; }

    /// <summary>
    /// 目标单明细字段名
    /// </summary>
    public string TargetEntryName { get; set; }

    /// <summary>
    /// 条码明细字段名
    /// </summary>
    public string BarcodeEntryName { get; set; }
}