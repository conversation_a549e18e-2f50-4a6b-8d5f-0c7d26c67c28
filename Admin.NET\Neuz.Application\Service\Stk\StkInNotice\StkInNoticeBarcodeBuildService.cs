﻿namespace Neuz.Application;

/// <summary>
/// WMS入库通知单条码档案构建服务
/// </summary>
// Injection 名称格式：BdBarcodeFuncKey:{FuncKey}，其中 FuncKey 为 前端指定的 modelKey（注意大小写）
[Injection(Named = "BdBarcodeFuncKey:stkInNotice")]
public class StkInNoticeBarcodeBuildService : BdBaseBarcodeBuildService, ITransient
{
    public StkInNoticeBarcodeBuildService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override List<BdBarcode> CreateNewBarcodes(string modelKey, List<ComboId> comboIds)
    {
        var list = new List<BdBarcode>();

        var entities = BarcodeCodeRuleRep.Context.Queryable<StkInNotice>()
            .Includes(u => u.Entries.Where(p => comboIds.Select(v => v.EntryId).Contains(p.EntryId)).ToList())
            .IncludeNavCol()
            .Where(u => comboIds.Select(v => v.Id).Contains(u.Id))
            .ToList();

        foreach (var entity in entities)
        {
            foreach (var entry in entity.Entries)
            {
                var barcode = new BdBarcode
                {
                    FuncKey = modelKey,
                    SrcBillKey = nameof(StkInNotice),
                    SrcBillNo = entity.BillNo,
                    SrcBillType = entity.BillType,
                    SrcBillEntrySeq = entry.Seq,
                    SrcBillId = entity.Id,
                    SrcBillEntryId = entry.EntryId,
                    Status = BdBarcodeStatus.Init,
                    MaterialId = entry.MaterialId,
                    Material = entry.Material,
                    BatchNo = entry.BatchNo,
                    BatchFile = null,
                    ProduceDate = entry.ProduceDate,
                    ExpiryDate = entry.ExpiryDate,
                    Qty = entry.Qty - entry.MadeQty,
                    UnitId = entry.UnitId,
                    Unit = entry.Unit,
                    ContainerId = null,
                    Container = null,
                    OwnerId = entry.OwnerId,
                    Owner = entry.Owner,
                    AuxPropValueId = entry.AuxPropValueId,
                    AuxPropValue = entry.AuxPropValue,
                    WarehouseId = entity.WarehouseId,
                    Warehouse = entity.Warehouse,
                    WhAreaId = null, // entry.WhAreaId,
                    WhArea = null, // entry.WhArea,
                    WhLocId = null,
                    WhLoc = null,
                    AuxQty = entry.AuxQty,
                    AuxUnitId = entry.AuxUnitId,
                    AuxUnit = entry.AuxUnit,
                };
                list.Add(barcode);
            }
        }

        return list;
    }

    public override void ReCalcMadeQty(string modelKey, List<ComboId> comboIds)
    {
        // 查询明细
        var entries = BarcodeCodeRuleRep.Context.Queryable<StkInNoticeEntry>()
            .Where(u => comboIds.Select(p => p.EntryId).Contains(u.EntryId))
            .ToList();

        // 获取单据条码已制作数量
        var barcodeMadeQtys = GetBarcodeMadeQty(modelKey, comboIds);

        // 处理已制作条码数量
        foreach (var entry in entries)
        {
            var barcodeQty = barcodeMadeQtys.FirstOrDefault(u => u.SrcBillId == entry.Id && u.SrcBillEntryId == entry.EntryId);
            entry.MadeQty = barcodeQty?.MadeQty ?? 0;
        }

        // 保存数据
        BarcodeCodeRuleRep.Context.Updateable(entries)
            .UpdateColumns(u => new { u.MadeQty }) // 只更新指定的列
            .ExecuteCommand();
    }

    public override List<(ComboId, decimal)> GetCanMakeQty(string modelKey, List<ComboId> comboIds)
    {
        // 查询明细
        var entries = BarcodeCodeRuleRep.Context.Queryable<StkInNoticeEntry>()
            .Where(u => comboIds.Select(p => p.EntryId).Contains(u.EntryId))
            .ToList();

        var returnList = new List<(ComboId, decimal)>();

        // 处理已制作条码数量
        foreach (var comboId in comboIds)
        {
            var entry = entries.FirstOrDefault(u => u.Id == comboId.Id && u.EntryId == comboId.EntryId);
            if (entry == null) continue;

            returnList.Add((comboId, entry.Qty - entry.MadeQty));
        }

        return returnList;
    }
}