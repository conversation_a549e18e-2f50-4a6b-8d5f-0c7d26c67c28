﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储分配规则
/// </summary>
[SugarTable(null, "仓储分配规则")]
public class StkAllocateRule : BdEntityBase
{
    /// <summary>
    /// 实体名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实体名称", Length = 50)]
    public string EntityName { get; set; }

    /// <summary>
    /// 是否允许更换库位
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许更换库位")]
    public bool IsAllowLocReplace { get; set; }

    /// <summary>
    /// 是否允许更换批号
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许更换批号")]
    public bool IsAllowBatchNoReplace { get; set; }

    /// <summary>
    /// 单据条件明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocateRuleBillFilterEntry.Id))]
    public List<StkAllocateRuleBillFilterEntry> BillFilterEntries { get; set; }

    /// <summary>
    /// 库存条件明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocateRuleInvFilterEntry.Id))]
    public List<StkAllocateRuleInvFilterEntry> InvFilterEntries { get; set; }

    /// <summary>
    /// 库存条件明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocateRuleInvOrderEntry.Id))]
    public List<StkAllocateRuleInvOrderEntry> InvOrderEntries { get; set; }

    /// <summary>
    /// 库位条件明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocateRuleWhLocFilterEntry.Id))]
    public List<StkAllocateRuleWhLocFilterEntry> WhLocFilterEntries { get; set; }

    /// <summary>
    /// 库位条件明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocateRuleWhLocOrderEntry.Id))]
    public List<StkAllocateRuleWhLocOrderEntry> WhLocOrderEntries { get; set; }
}