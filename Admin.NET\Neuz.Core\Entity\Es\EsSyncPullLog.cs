﻿namespace Neuz.Core.Entity;

/// <summary>
/// 外部系统同步拉取日志
/// </summary>
[SugarTable(null, "外部系统同步拉取日志")]
[LogTable]
[SugarIndex("index_{table}_N", nameof(Number), OrderByType.Asc)]
[SugarIndex("index_{table}_B", nameof(BillNo), OrderByType.Asc)]
public class EsSyncPullLog : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 255)]
    public string Number { get; set; }

    /// <summary>
    /// 外部系统类型
    /// </summary>
    [SugarColumn(ColumnDescription = "外部系统类型", Length = 255)]
    public string EsType { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "单据编号", Length = 255)]
    public string BillNo { get; set; }

    /// <summary>
    /// 提交参数
    /// </summary>
    [SugarColumn(ColumnDescription = "提交参数", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Param { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    [SugarColumn(ColumnDescription = "结果", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Result { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    [SugarColumn(ColumnDescription = "消息", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Message { get; set; }

    /// <summary>
    /// 是否保存成功
    /// </summary>
    [SugarColumn(ColumnDescription = "是否保存成功")]
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 保存单据内码
    /// </summary>
    [SugarColumn(ColumnDescription = "保存单据内码", Length = 500)]
    public string TargetBillId { get; set; }

    /// <summary>
    /// 保存单据编码
    /// </summary>
    [SugarColumn(ColumnDescription = "保存单据编码", Length = 500)]
    public string TargetBillNo { get; set; }
}