﻿using Furion.Localization;
using Magicodes.ExporterAndImporter.Excel;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 物料有效期报表服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkMaterialValidityReport", Order = 100)]
public class StkMaterialValidityReportService : IDynamicApiController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly SqlSugarRepository<StkInventory> _rep;

    /// <summary>
    /// 物料有效期报表服务
    /// </summary>
    public StkMaterialValidityReportService(IServiceProvider serviceProvider, SqlSugarRepository<StkInventory> rep)
    {
        _serviceProvider = serviceProvider;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("page")]
    public async Task<SqlSugarPagedList<StkMaterialValidityReportOutput>> PageAsync(StkMaterialValidityReportInput input)
    {
        var entities = await _rep.Context
            .AddWarehouseFilter<StkInventory>(_serviceProvider, u => u.WarehouseId) // 仓库权限
            .AddOwnerFilter<StkInventory>(_serviceProvider, u => u.OwnerId) // 货主权限
            .Queryable(
                _rep.AsQueryable()
                    .InnerJoin<BdMaterial>((t1, t2) => t1.MaterialId == t2.Id)
                    .LeftJoin<BdWarehouse>((t1, t2, t3) => t1.WarehouseId == t3.Id)
                    .LeftJoin<BdOwner>((t1, t2, t3, t4) => t1.OwnerId == t4.Id)
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber), (t1, t2, t3, t4) => t2.Number.Contains(input.MaterialNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.MaterialName), (t1, t2, t3, t4) => t2.Name.Contains(input.MaterialName))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber), (t1, t2, t3, t4) => t3.Number.Contains(input.WarehouseNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.WarehouseName), (t1, t2, t3, t4) => t3.Name.Contains(input.WarehouseName))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerNumber), (t1, t2, t3, t4) => t4.Number.Contains(input.OwnerNumber))
                    .WhereIF(!string.IsNullOrEmpty(input.OwnerName), (t1, t2, t3, t4) => t4.Name.Contains(input.OwnerName))
                    .WhereIF(!string.IsNullOrEmpty(input.BatchNo), (t1, t2, t3, t4) => t1.BatchNo.Contains(input.BatchNo))
                    .Where((t1, t2, t3, t4) => t2.IsKfPeriod == true)
                    .GroupBy((t1, t2, t3, t4) => new
                    {
                        Id = t1.MaterialId, MaterialId = t2.Id, MaterialNumber = t2.Number, MaterialName = t2.Name, WarehouseId = t3.Id, t1.ProduceDate, t1.ExpiryDate, t3.Number,
                        t3.Name, OwnerId = t4.Id, OwnerNumber = t4.Number, OwnerName = t4.Name,
                    })
                    .Select((t1, t2, t3, t4) => new StkMaterialValidityReportOutput
                    {
                        Id = t1.MaterialId,
                        MaterialId = t2.Id,
                        MaterialNumber = t2.Number,
                        MaterialName = t2.Name,
                        ProduceDate = t1.ProduceDate,
                        ExpiryDate = t1.ExpiryDate,
                        Qty = SqlFunc.AggregateSum(t1.Qty),
                        ExpirationDate = SqlFunc.DateDiff(DateType.Day, DateTime.Now, (DateTime)t1.ExpiryDate),
                        WarehouseId = t3.Id,
                        WarehouseNumber = t3.Number,
                        WarehouseName = t3.Name,
                        OwnerId = t4.Id,
                        OwnerNumber = t4.Number,
                        OwnerName = t4.Name,
                    })
            )
            .Distinct()
            .OrderBuilder(input)
            .ToPagedListAsync(input.Page, input.PageSize);
        return entities;
    }

    /// <summary>
    /// 按查询条件导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("exportByQuery")]
    public async Task<FileContentResult> ExportByQuery(StkMaterialValidityReportInput input)
    {
        input.Page = 1;
        input.PageSize = 50000;
        var output = await PageAsync(input);
        var exporter = new ExcelExporter();
        byte[] bytes = await exporter.ExportAsByteArray(output.Items.ToList());
        var fileName = L.Text["物料有效期报表_{0}.xlsx", DateTimeOffset.Now.ToString("yyyyMMddHHmmss")];
        return new FileContentResult(bytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") { FileDownloadName = fileName };
    }
}