﻿namespace Neuz.Application;

/// <summary>
/// Web端表格查询方案服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StgWebTableQueryPlan", Order = 100)]
public class StgWebTableQueryPlanService : IDynamicApiController
{
    private const string DefaultName = "默认方案";

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// 当前用户Id
    /// </summary>
    protected long CurUserId => long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");

    /// <summary>
    /// 仓储服务
    /// </summary>
    protected SqlSugarRepository<StgWebTableQueryPlan> Rep { get; }

    /// <summary>
    /// Web端表格查询方案服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    public StgWebTableQueryPlanService(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
        Rep = App.GetRequiredService<SqlSugarRepository<StgWebTableQueryPlan>>(serviceProvider);
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("add")]
    public async Task Add(StgWebTableQueryPlanAddInput input)
    {
        var queryPlan = await Rep.GetFirstAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Name == input.Name);
        if (queryPlan != null)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1000);

        await Rep.InsertAsync(new StgWebTableQueryPlan { TableId = input.TableId, UserId = CurUserId, Name = input.Name });
    }

    /// <summary>
    /// 保存
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("save")]
    public async Task Save(StgWebTableQueryPlanSaveInput input)
    {
        var queryPlan = await Rep.GetFirstAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Name == input.Name);
        if (queryPlan == null)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1001);

        queryPlan.Json = input.Json;
        await Rep.UpdateAsync(queryPlan);
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("delete")]
    public async Task Delete(StgWebTableQueryPlanDeleteInput input)
    {
        if (input.Name == DefaultName)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1002);

        var queryPlan = await Rep.GetFirstAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Name == input.Name);
        if (queryPlan == null)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1001);

        await Rep.DeleteAsync(queryPlan);
    }

    /// <summary>
    /// 获取
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("get")]
    public async Task<string> Get(StgWebTableQueryPlanGetInput input)
    {
        var queryPlan = await Rep.GetFirstAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Name == input.Name);
        if (queryPlan == null)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1001);

        return queryPlan.Json;
    }

    /// <summary>
    /// 获取列表
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("getList")]
    public async Task<List<string>> GetList(StringIdInput input)
    {
        var list = await Rep.GetListAsync(r => r.TableId == input.Id && r.UserId == CurUserId);
        var nameList = list.Select(u => u.Name).ToList();

        // 如果没有方案，创建一个默认方案
        if (nameList.Count == 0)
        {
            await Add(new StgWebTableQueryPlanAddInput { TableId = input.Id, Name = DefaultName });
            nameList.Add(DefaultName);
        }

        return nameList;
    }

    /// <summary>
    /// 重命名
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("rename")]
    public async Task Rename(StgWebTableQueryPlanRenameInput input)
    {
        if (input.OriginName == DefaultName)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1003);

        var queryPlan = await Rep.GetFirstAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Name == input.OriginName);
        if (queryPlan == null)
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1001);

        if (await Rep.IsAnyAsync(u => u.TableId == input.TableId && u.UserId == CurUserId && u.Id != queryPlan.Id && u.Name == input.Name))
            throw Oops.Bah(StgErrorCode.StgWebTableQueryPlan1000);

        input.Adapt(queryPlan);

        await Rep.UpdateAsync(queryPlan);
    }
}