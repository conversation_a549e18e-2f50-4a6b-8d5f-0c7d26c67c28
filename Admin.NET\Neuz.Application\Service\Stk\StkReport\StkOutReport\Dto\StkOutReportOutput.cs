﻿using Magicodes.ExporterAndImporter.Core;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 出库报表输出参数
/// </summary>
public class StkOutReportOutput
{
    /// <summary>
    /// 库存事务Id
    /// </summary>
    [ExporterHeader(DisplayName = "库存事务Id", IsIgnore = true)]
    public long Id { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    [ExporterHeader(DisplayName = "单据编号")]
    public string BillNo { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [ExporterHeader(DisplayName = "单据类型")]
    public string BillType { get; set; }

    /// <summary>
    /// 行号
    /// </summary>
    [ExporterHeader(DisplayName = "行号")]
    public int Seq { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [ExporterHeader(DisplayName = "物料Id", IsIgnore = true)]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    [ExporterHeader(DisplayName = "物料编码")]
    public string MaterialNumber { get; set; }

    /// <summary>
    /// 物料名称
    /// </summary>
    [ExporterHeader(DisplayName = "物料名称")]
    public string MaterialName { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [ExporterHeader(DisplayName = "批号", IsIgnore = true)]
    public string BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [ExporterHeader(DisplayName = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [ExporterHeader(DisplayName = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [ExporterHeader(DisplayName = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [ExporterHeader(DisplayName = "仓库Id", IsIgnore = true)]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    [ExporterHeader(DisplayName = "仓库编码")]
    public string WarehouseNumber { get; set; }

    /// <summary>
    /// 仓库名称
    /// </summary>
    [ExporterHeader(DisplayName = "仓库名称")]
    public string WarehouseName { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [ExporterHeader(DisplayName = "货主Id", IsIgnore = true)]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主编码
    /// </summary>
    [ExporterHeader(DisplayName = "货主编码")]
    public string OwnerNumber { get; set; }

    /// <summary>
    /// 货主名称
    /// </summary>
    [ExporterHeader(DisplayName = "货主名称")]
    public string OwnerName { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>

    [ExporterHeader(DisplayName = "库区Id", IsIgnore = true)]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区编码
    /// </summary>
    [ExporterHeader(DisplayName = "库区编码")]
    public string WhAreaNumber { get; set; }

    /// <summary>
    /// 库区名称
    /// </summary>
    [ExporterHeader(DisplayName = "库区名称")]
    public string WhAreaName { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [ExporterHeader(DisplayName = "库位Id", IsIgnore = true)]
    public long WhLocId { get; set; }

    /// <summary>
    /// 库位编码
    /// </summary>
    [ExporterHeader(DisplayName = "库位编码")]
    public string WhLocNumber { get; set; }

    /// <summary>
    /// 库位名称
    /// </summary>
    [ExporterHeader(DisplayName = "库位名称")]
    public string WhLocName { get; set; }

    /// <summary>
    /// 容器Id
    /// </summary>
    [ExporterHeader(DisplayName = "容器Id", IsIgnore = true)]
    public long ContainerId { get; set; }

    /// <summary>
    /// 容器编码
    /// </summary>
    [ExporterHeader(DisplayName = "容器编码")]
    public string ContainerNumber { get; set; }

    /// <summary>
    /// 辅助属性Id
    /// </summary>
    [ExporterHeader(DisplayName = "辅助属性Id", IsIgnore = true)]
    public long AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性编码
    /// </summary>
    [ExporterHeader(DisplayName = "辅助属性编码")]
    public string AuxPropValueNumber { get; set; }

    /// <summary>
    /// 辅助属性名称
    /// </summary>
    [ExporterHeader(DisplayName = "辅助属性名称")]
    public string AuxPropValueName { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    [ExporterHeader(DisplayName = "供应商Id", IsIgnore = true)]
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商编码
    /// </summary>
    [ExporterHeader(DisplayName = "供应商编码")]
    public string SupplierNumber { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    [ExporterHeader(DisplayName = "供应商名称")]
    public string SupplierName { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [ExporterHeader(DisplayName = "客户Id", IsIgnore = true)]
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户编码
    /// </summary>
    [ExporterHeader(DisplayName = "客户编码")]
    public string CustomerNumber { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    [ExporterHeader(DisplayName = "客户名称")]
    public string CustomerName { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [ExporterHeader(DisplayName = "单位Id", IsIgnore = true)]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位编码
    /// </summary>
    [ExporterHeader(DisplayName = "单位编码")]
    public string UnitNumber { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [ExporterHeader(DisplayName = "单位名称")]
    public string UnitName { get; set; }

    /// <summary>
    /// 外部单据编号
    /// </summary>
    [ExporterHeader(DisplayName = "外部单据编号")]
    public string EsBillNo { get; set; }

    /// <summary>
    /// 单据日期
    /// </summary>
    [ExporterHeader(DisplayName = "单据日期")]
    public DateTime? BillDate{ get; set; }

    /// <summary>
    /// 发运日期
    /// </summary>
    [ExporterHeader(DisplayName = "发运日期")]
    public DateTime? ShippingDate { get; set; }

    /// <summary>
    /// 拣货日期
    /// </summary>
    [ExporterHeader(DisplayName = "拣货日期")]
    public DateTime? PickingDate { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [ExporterHeader(DisplayName = "推送标记")]
    public PushFlag PushFlag { get; set; }



}