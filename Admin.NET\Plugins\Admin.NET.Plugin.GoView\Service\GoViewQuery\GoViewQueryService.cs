﻿using Admin.NET.Plugin.GoView.ErrorCodes;
using System.Text.RegularExpressions;
using Neuz.Application;
using Neuz.Application.ErrorCodes;

namespace Admin.NET.Plugin.GoView.Service;

/// <summary>
/// GoView 查询服务
/// </summary>q
[ApiDescriptionSettings(GoViewConst.GroupName, Module = "goview", Name = "query", Order = 100)]
public class GoViewQueryService : IDynamicApiController
{
    /// <summary>
    /// Sql 修改数据关键字正则表达式
    /// </summary>
    private readonly Regex _modifyDataKeywordRegex = new Regex(@"\bdelete\b|\binsert\b|\bupdate\b|\btruncate\b|\bdrop\b|\balter\b|\bexec\b|\bcreate\b", RegexOptions.IgnoreCase | RegexOptions.Singleline);

    /// <summary>
    /// 租户仓储
    /// </summary>
    protected SqlSugarRepository<SysTenant> TenantRep { get; }

    /// <summary>
    /// 上下文访问器
    /// </summary>
    private IHttpContextAccessor HttpContextAccessor { get; }

    public GoViewQueryService(SqlSugarRepository<SysTenant> tenantRep, IHttpContextAccessor httpContextAccessor)
    {
        TenantRep = tenantRep;
        HttpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// Sql 查询
    /// </summary>
    /// <param name="tenantName">租户名称，空为默认租户，当租户为库隔离时需要指定，否则为主库</param>
    /// <param name="sql">查询 Sql，当租户为 Id 隔离时需要自行处理数据隔离</param>
    /// <returns></returns>
    [HttpPost("sqlQuery")]
    [AllowAnonymous]
    public async Task<List<Dictionary<string, object>>> SqlQuery([FromQuery] string tenantName, [FromBody] SqlQueryInput sql)
    {
        // TODO: Sql 查询接口，身份验证实现待实现，现在不需要登录即可执行
        // 身份验证实现方式：传入 token，此 token 独立于正常的 jwt token 授权

        if (_modifyDataKeywordRegex.IsMatch(sql.Sql))
            throw Oops.Bah(GoViewErrorCode.G_0001);

        //查找租户
        var tenant = await TenantRep.AsQueryable()
            .LeftJoin<SysOrg>((u, v) => u.OrgId == v.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(tenantName), (u, v) => v.Name == tenantName)
            .OrderBy(u => u.Id)
            .FirstAsync();
        if (tenant == null)
            throw Oops.Bah(EsErrorCode.Es1002, tenantName);

        // 根据租户Id切换库连接
        var sqlSugarScope = App.GetRequiredService<SysTenantService>().GetTenantDbConnectionScope(tenant.Id);

        var table = await sqlSugarScope.Ado.GetDataTableAsync(sql.Sql);
        var list = table.ToDictionary();

        return list;
    }
}