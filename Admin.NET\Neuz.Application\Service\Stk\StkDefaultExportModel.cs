﻿using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 仓储默认导出模型
/// </summary>
public class StkDefaultExportModel : DefaultExportModel
{
    protected override List<ExportField> GetNavColExportFields(EntityMaintenance entityMaintenance, EntityInfo entityInfo, EntityColumnInfo navCol, string parentPropName,
        string parentPropDescription, ushort level)
    {
        if (navCol.UnderType == typeof(BdBarcode))
        {
            return new List<ExportField>
            {
                GenNavColExportField(entityMaintenance, entityInfo, navCol, nameof(BdBarcode.Barcode), parentPropName, parentPropDescription, true),
            };
        }

        return base.GetNavColExportFields(entityMaintenance, entityInfo, navCol, parentPropName, parentPropDescription, level);
    }
}