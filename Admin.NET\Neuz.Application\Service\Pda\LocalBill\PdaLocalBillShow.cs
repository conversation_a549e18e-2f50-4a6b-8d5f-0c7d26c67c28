﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Model.Bill;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.LocalBill;

public class PdaLocalBillShow : PdaShow
{
    /// <summary>
    /// 多源单的源单编码
    /// </summary>
    public List<string> SourceBill { get; set; } = new List<string>();
    /// <summary>
    /// 仓库
    /// </summary>
    public string Stock { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public PdaLocalBillDataDest DestData { get; set; } = new PdaLocalBillDataDest();

    /// <summary>
    /// 条码信息
    /// </summary>
    public List<PdaBarcodeShowInfo> Barcodes { get; set; } = new List<PdaBarcodeShowInfo>();

    /// <summary>
    /// PDA字段显示属性
    /// </summary>
    public List<PdaCellAttributes> CellAttributes = new List<PdaCellAttributes>();

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, object> ExtendArgs = new Dictionary<string, object>();

    /// <summary>
    /// 是否隐藏扫描完成的列
    /// </summary>
    public bool IsHideFinishScanQty { get; set; }
}