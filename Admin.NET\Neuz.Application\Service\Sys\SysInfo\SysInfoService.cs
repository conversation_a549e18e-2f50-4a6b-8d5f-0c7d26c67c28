﻿using Neuz.Application.Sys.SysInfo.Dto;

namespace Neuz.Application.Sys.SysInfo;

/// <summary>
/// 系统信息服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "SysInfo", Order = 100)]
public class SysInfoService : IDynamicApiController
{
    private readonly SysConfigService _configService;

    public SysInfoService(SysConfigService configService)
    {
        _configService = configService;
    }

    /// <summary>
    /// 获取 Pda 地址
    /// </summary>
    /// <returns></returns>
    [SuppressMonitor]
    [HttpGet("getPdaUrl")]
    [AllowAnonymous]
    public async Task<PdaUrlOutput> GetPdaUrl()
    {
        var output = new PdaUrlOutput
        {
            ApkUrl = await _configService.GetConfigValueByCode<string>("neuz_apk_url"),
            PdaServerUrl = await _configService.GetConfigValueByCode<string>("neuz_pda_server_url"),
        };

        return output;
    }
}