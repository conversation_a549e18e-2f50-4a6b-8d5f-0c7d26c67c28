﻿namespace Neuz.Application;

/// <summary>
/// 角色仓库库区权限服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkRoleWhArea", Order = 100)]
public class StkRoleWhAreaService : IDynamicApi<PERSON>ontroller, ITransient
{
    /// <summary>
    /// 角色仓库权限仓储服务
    /// </summary>
    protected SqlSugarRepository<StkRoleWarehouse> RoleWarehouseRep { get; }

    /// <summary>
    /// 角色仓库库区权限仓储服务
    /// </summary>
    protected SqlSugarRepository<StkRoleWhArea> RoleWhAreaRep { get; }

    /// <summary>
    /// 库区仓储服务
    /// </summary>
    protected SqlSugarRepository<BdWhArea> WhAreaRep { get; }

    /// <summary>
    /// 用户角色服务
    /// </summary>
    protected SysUserRoleService UserRoleService { get; }

    /// <summary>
    /// 缓存服务
    /// </summary>
    protected SysCacheService CacheService { get; }

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    public StkRoleWhAreaService(IServiceProvider serviceProvider)
    {
        RoleWarehouseRep = serviceProvider.GetService<SqlSugarRepository<StkRoleWarehouse>>();
        RoleWhAreaRep = serviceProvider.GetService<SqlSugarRepository<StkRoleWhArea>>();
        WhAreaRep = serviceProvider.GetService<SqlSugarRepository<BdWhArea>>();
        UserRoleService = serviceProvider.GetService<SysUserRoleService>();
        CacheService = serviceProvider.GetService<SysCacheService>();
        ServiceProvider = serviceProvider;
    }

    /// <summary>
    /// 获取角色仓库库区权限
    /// </summary>
    /// <returns></returns>
    [HttpGet("getRoleWhArea")]
    public async Task<StkRoleWhAreaOutput> GetRoleWhArea([FromQuery] long roleId)
    {
        var roleWarehouseIdList = (await RoleWarehouseRep.GetListAsync(u => u.RoleId == roleId)).Select(u => u.WarehouseId).ToList();
        var roleWhAreaIdList = (await RoleWhAreaRep.GetListAsync(u => u.RoleId == roleId)).Select(u => u.WhAreaId).ToList();

        return new StkRoleWhAreaOutput
        {
            WarehouseIds = roleWarehouseIdList,
            WhAreaIds = roleWhAreaIdList,
        };
    }

    /// <summary>
    /// 保存角色仓库库区权限
    /// </summary>
    /// <returns></returns>
    [HttpPost("saveRoleWhArea")]
    [UnitOfWork]
    public async Task SaveRoleWhArea(StkRoleWhAreaInput input)
    {
        var warehouseIds = input.WarehouseIds.Where(u => u > 0).ToList();

        // 删除角色仓库绑定
        await RoleWarehouseRep.DeleteAsync(u => u.RoleId == input.RoleId);
        //插入角色仓库绑定
        await RoleWarehouseRep.InsertRangeAsync(warehouseIds.Select(u => new StkRoleWarehouse
        {
            RoleId = input.RoleId,
            WarehouseId = u,
        }).ToList());

        // 删除角色仓库库区权限
        await RoleWhAreaRep.DeleteAsync(u => u.RoleId == input.RoleId);

        // 查询授权仓库下的库区信息
        var whAreaList = await WhAreaRep.GetListAsync(u => warehouseIds.Contains(u.WarehouseId));

        var stockIds = whAreaList.Select(u => u.Id).Intersect(input.WhAreaIds);

        // 插入角色仓库库区权限
        await RoleWhAreaRep.InsertRangeAsync(stockIds.Select(u => new StkRoleWhArea
        {
            RoleId = input.RoleId,
            WarehouseId = whAreaList.First(p => p.Id == u).WarehouseId,
            WhAreaId = u
        }).ToList());

        // 清除用户仓库/库区权限缓存
        var userIds = await UserRoleService.GetUserIdList(input.RoleId);
        foreach (var userId in userIds)
        {
            CacheService.Remove(CacheConst.KeyUserWarehouse + userId);
            CacheService.Remove(CacheConst.KeyUserWhArea + userId);
        }
    }

    /// <summary>
    /// 获取当前用户的仓库权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserWarehouseIds()
    {
        var userManager = App.GetService<UserManager>(ServiceProvider);
        return GetUserWarehouseIdsByUserId(userManager.UserId);
    }

    /// <summary>
    /// 获取用户的仓库权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserWarehouseIdsByUserId(long userId)
    {
        var warehouseIds = CacheService.Get<List<long>>(CacheConst.KeyUserWarehouse + userId);
        if (warehouseIds != null && warehouseIds.Count != 0)
            return warehouseIds;

        // 用户所属的角色Id集合
        var roleIds = UserRoleService.GetUserRoleIdList(userId).GetAwaiter().GetResult();

        warehouseIds = RoleWarehouseRep.AsQueryable()
            .Where(u => roleIds.Contains(u.RoleId))
            .Select(u => u.WarehouseId)
            .ToList();

        // 如果没有数据，返回一个不存在的假数据
        if (warehouseIds.Count == 0)
            warehouseIds.Add(-1);

        CacheService.Set(CacheConst.KeyUserWarehouse + userId, warehouseIds, TimeSpan.FromDays(1));

        return warehouseIds;
    }

    /// <summary>
    /// 获取当前用户的库区权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserWhAreaIds()
    {
        var userManager = App.GetService<UserManager>(ServiceProvider);
        return GetUserWhAreaIdsByUserId(userManager.UserId);
    }

    /// <summary>
    /// 获取用户的库区权限Id集合，如果没有数据，集合中返回一个 -1 值
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <returns></returns>
    [NonAction]
    public List<long> GetUserWhAreaIdsByUserId(long userId)
    {
        var whAreaIds = CacheService.Get<List<long>>(CacheConst.KeyUserWhArea + userId);
        if (whAreaIds != null && whAreaIds.Count != 0)
            return whAreaIds;

        // 用户所属的角色Id集合
        var roleIds = UserRoleService.GetUserRoleIdList(userId).GetAwaiter().GetResult();

        whAreaIds = RoleWhAreaRep.AsQueryable()
            .Where(u => roleIds.Contains(u.RoleId))
            .Select(u => u.WhAreaId)
            .Distinct()
            .ToList();

        // 如果没有数据，返回一个不存在的假数据
        if (whAreaIds.Count == 0)
            whAreaIds.Add(-1);

        CacheService.Set(CacheConst.KeyUserWhArea + userId, whAreaIds, TimeSpan.FromDays(1));

        return whAreaIds;
    }
}