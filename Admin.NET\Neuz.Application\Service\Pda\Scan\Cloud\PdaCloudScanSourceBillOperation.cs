﻿using Neuz.Application.Pda.Bill.Interface.Bill.Cloud;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Enum;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.Cloud;

public class PdaCloudScanSourceBillOperation : PdaScanBarcodeOperationBase
{
    public override void Operation(PdaScanBarcodeArgs args)
    {
        var pdaCacheService = App.GetService<PdaCacheService>(_serviceProvider);
        var billData = pdaCacheService.GetBillData(args.TranId);
        var billModel = (PdaCloudBillModelBase)pdaCacheService.GetPdaBillModel(billData.BillModelKey);
        var billSchema = billModel.Config.BillSchema;
        string lookupKey = $"{billSchema.BillLink.SourceKey}_{billSchema.BillLink.DestKey}({billModel.RuleId})";
        if (pdaCacheService.IsExistPdaBasicModel(lookupKey))
        {
            //如果barcode为空,尝试找源单
            var isSourceBarcode = billModel.SelectLookupData(args.TranId, lookupKey, args.BarcodeString, "SourceInfo", QueryType.Key);
            if (isSourceBarcode) args.IsResult = true;
        }
    }

    public PdaCloudScanSourceBillOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}