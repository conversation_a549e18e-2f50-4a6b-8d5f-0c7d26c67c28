﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill.StkInNotice_StkReceive;

/// <summary>
/// 收料通知单->收货单
/// </summary>
public class PdaLocalBillStkInNotice_StkReceiveModel : PdaLocalBillModel<StkInNotice, StkReceive, StkInNoticeEntry, StkReceiveEntry>
{
    public PdaLocalBillStkInNotice_StkReceiveModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkInNotice_StkReceive";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "supplierName",
                Caption = L.Text["供应商"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdSupplierModel",
                    LookupDataKey = "ScanHead.Supplier",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("SupplierId", "Id"),
                        new PdaLookupMapping("SupplierNumber", "Number"),
                        new PdaLookupMapping("SupplierName", "Name"),
                    }
                }
            }
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["仓位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkInNotice",
            SourceTitle = L.Text["收料通知单"],
            DestKey = "StkReceive",
            DestTitle = L.Text["收货单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceQty = true,
            IsMultiSource = false,
            SummaryOperationFields =
            [
                new PdaLocalBillSummaryOperationField { FieldName = "Material.Number", ScanFieldName = "MaterialNumber" },
                new PdaLocalBillSummaryOperationField { FieldName = "SrcBillId", ScanFieldName = "SrcBillId" },
                new PdaLocalBillSummaryOperationField { FieldName = "SrcBillEntryId", ScanFieldName = "SrcBillEntryId" },
            ]
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkInNotice>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkInNoticeEntry>(ServiceProvider, u => u.WhAreaId)
            .AddOwnerFilter<StkInNoticeEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .WhereIF(linkParam.SourceBillTypes.Count > 0, t1 => linkParam.SourceBillTypes.Contains(t1.BillType))
                .Where((t1) => t1.DocumentStatus == DocumentStatus.Approve && t1.Status != StkInNoticeStatus.Finish &&
                               (t1.BillNo.Contains(input.Keyword) || t1.EsBillNo.Contains(input.Keyword) || t1.Id == SqlFunc.Subqueryable<StkInNoticeEntry>()
                                   .LeftJoin<BdMaterial>((m1, m2) => m1.MaterialId == m2.Id)
                                   .Where((m1, m2) => m1.Id == t1.Id && m1.EntryStatus != StkInNoticeEntryStatus.Finish)
                                   .WhereIF(!string.IsNullOrEmpty(input.Keyword), (m1, m2) => m2.Number.Contains(input.Keyword))
                                   .GroupBy((m1, m2) => m1.Id)
                                   .Select((m1, m2) => m1.Id)))
                .Where(t1 => SqlFunc.Subqueryable<StkInNoticeEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(t1 => t1.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = $"{r.BillNo}[{r.EsBillNo}]", SubTitle = r.Date.ToString("yyyy-MM-dd") }).ToArray();
        return result;
    }
}