﻿namespace Neuz.Core.Entity;

/// <summary>
/// 仓储分配策略
/// </summary>
[SugarTable(null, "仓储分配策略")]
public class StkAllocatePolicy : BdEntityBase
{
    /// <summary>
    /// 实体名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实体名称", Length = 50)]
    public string EntityName { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkAllocatePolicyEntry.Id))]
    public List<StkAllocatePolicyEntry> Entries { get; set; }
}