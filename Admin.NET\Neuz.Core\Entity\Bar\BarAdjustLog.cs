﻿namespace Neuz.Core.Entity;

/// <summary>
/// 条码调整
/// </summary>
[SugarTable(null, "条码调整")]
[SugarIndex("index_{table}_B", nameof(Barcode), OrderByType.Asc)]
[SugarIndex("index_{table}_B2", nameof(BillNo), OrderByType.Asc)]
public class BarAdjustLog : EntityTenant
{
    /// <summary>
    /// 租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码日志Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码日志Id")]
    public long BarcodeLogId { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "单据编号", Length = 50)]
    public string? BillNo { get; set; }
}