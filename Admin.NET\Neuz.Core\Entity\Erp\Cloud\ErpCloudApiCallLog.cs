﻿namespace Neuz.Core.Entity.Cloud;

/// <summary>
/// Cloud 接口调用日志
/// </summary>
[SugarTable(null, "Cloud 接口调用日志")]
[SugarIndex("index_{table}_R", nameof(RelateTranId), OrderByType.Asc)]
[SugarIndex("index_{table}_B", nameof(BeginTime), OrderByType.Asc)]
[SugarIndex("index_{table}_E", nameof(EndTime), OrderByType.Asc)]
[LogTable]
public class ErpCloudApiCallLog : EntityBaseId
{
    /// <summary>
    /// 关联事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联事务Id")]
    public long? RelateTranId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "开始时间")]
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 耗时（毫秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "耗时（毫秒）")]
    public int UseTime { get; set; }

    /// <summary>
    /// 账套编码
    /// </summary>
    [SugarColumn(ColumnDescription = "账套编码")]
    public string AccountNumber { get; set; }

    /// <summary>
    /// K3 登录用户
    /// </summary>
    [SugarColumn(ColumnDescription = "K3 登录用户")]
    public string K3LoginUser { get; set; }

    /// <summary>
    /// 执行的动作名称
    /// </summary>
    [SugarColumn(ColumnDescription = "执行的动作名称")]
    public string ActionName { get; set; }

    /// <summary>
    /// 执行的动作内容
    /// </summary>
    [SugarColumn(ColumnDescription = "执行的动作内容", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Content { get; set; }

    /// <summary>
    /// 执行的动作结果
    /// </summary>
    [SugarColumn(ColumnDescription = "执行的动作结果", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Result { get; set; }
}