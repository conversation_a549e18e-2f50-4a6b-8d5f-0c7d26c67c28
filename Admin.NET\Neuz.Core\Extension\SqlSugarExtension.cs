﻿namespace Neuz.Core.Extension;

public static class SqlSugarExtension
{
    /// <summary>
    /// IncludesByNameString 最大参数数量
    /// </summary>
    private const int MaxParamCount = 5;

    /// <summary>
    /// 实体查询 Include 导航列
    /// </summary>
    /// <param name="query">SqlSugar 查询</param>
    /// <param name="onlyIncludeOneToMany">是否只 include 一对多</param>
    /// <typeparam name="TEntity"></typeparam>
    /// <remarks>
    /// Include 规则：<br/>
    /// 1. 当前实体的一对一导航列<br/>
    /// 2. 递归所有一对多的导航列（明细）和它的实体的一对一导航列<br/>
    /// 3. 一对一的导航列不会再 Include 它的实体里面的导航列<br/>
    /// 4. 最多支持5层深度的导航，即4层明细加上第4层明细的基础资料，或5层明细且第5层明细没有导航列<br/>
    /// </remarks>
    /// <returns></returns>
    public static ISugarQueryable<TEntity> IncludeNavCol<TEntity>(this ISugarQueryable<TEntity> query, bool onlyIncludeOneToMany = false) where TEntity : class
    {
        var entityInfo = query.Context.EntityMaintenance.GetEntityInfo(typeof(TEntity));

        InnerIncludeNavCol(query, entityInfo, new List<string>(), onlyIncludeOneToMany);

        return query;
    }

    /// <summary>
    /// 递归 Include 实体查询的导航列
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <param name="query">SqlSugar 查询</param>
    /// <param name="entityInfo">当前实体信息</param>
    /// <param name="parentColNames">父列名称集合</param>
    /// <param name="onlyIncludeOneToMany">是否只 include 一对多</param>
    /// <returns></returns>
    private static ISugarQueryable<TEntity> InnerIncludeNavCol<TEntity>(ISugarQueryable<TEntity> query, EntityInfo entityInfo, List<string> parentColNames, bool onlyIncludeOneToMany)
    {
        //IncludesByNameString 的参数数量
        var paramCount = parentColNames.Count + 1;
        if (paramCount > MaxParamCount) return query;

        var method = query.GetType().GetMethods().First(u => u.Name == nameof(query.IncludesByNameString) && u.GetParameters().Length == paramCount);
        var navCols = entityInfo.Columns.Where(u => u.Navigat != null).ToList();
        var oneToOneNavCols = navCols.Where(u => u.Navigat.GetNavigateType() == NavigateType.OneToOne).ToList();
        var oneToManyNavCols = navCols.Where(u => u.Navigat.GetNavigateType() == NavigateType.OneToMany).ToList();

        if (!onlyIncludeOneToMany)
        {
            //Include 一对一的导航列
            foreach (var navCol in oneToOneNavCols)
            {
                var arr = new object[paramCount];
                parentColNames.ToArray().CopyTo(arr, 0);
                arr[paramCount - 1] = navCol.PropertyName;
                method.Invoke(query, arr);
            }
        }

        //Include 一对多的导航列
        foreach (var navCol in oneToManyNavCols)
        {
            var arr = new object[paramCount];
            parentColNames.ToArray().CopyTo(arr, 0);
            arr[paramCount - 1] = navCol.PropertyName;
            method.Invoke(query, arr);

            var childEntityInfo = query.Context.EntityMaintenance.GetEntityInfo(navCol.UnderType.GenericTypeArguments[0]);
            parentColNames.Add(navCol.PropertyName);
            InnerIncludeNavCol(query, childEntityInfo, parentColNames, onlyIncludeOneToMany);
            parentColNames.Remove(navCol.PropertyName);
        }

        return query;
    }
}