﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Interface.Bill;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Helper;
using Neuz.Application.Pda.PdaStkStockCount.Dto;
using Neuz.Application.Pda.PdaStkStockCount.Scan;
using Neuz.Application.Pda.Proxy.Dto;
using Neuz.Application.Service.Pda.Bill.Interface.Bill;
using Neuz.Application.Service.Pda.SplitBarcode.Dto;
using Neuz.Core.Entity.Pda.Erp;
using SqlSugar;
using Stimulsoft.Report;
using Stimulsoft.Report.Export;

namespace Neuz.Application.Pda.PdaStkStockCount;

public class PdaStkStockCountModel : PdaModelBillBase<PdaStkStockCountShow, PdaStkStockCountData>
{
    /// <summary>
    /// lookup显示的行数
    /// </summary>
    protected int Take { get; } = 50;

    /// <summary>
    /// 是否源单外物料
    /// </summary>
    public virtual bool IsOverSourceItem { get; set; } = true;

    /// <summary>
    /// 是否能超源单数量
    /// </summary>
    public virtual bool IsOverSourceQty { get; set; } = true;

    protected SqlSugarRepository<StkStockCount> Rep => App.GetService<SqlSugarRepository<StkStockCount>>(ServiceProvider);

    public PdaStkStockCountModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public override string Key { get; } = "StkStockCount";
    public override IPdaSchema BillSchema { get; } = new PdaSchema();

    public override void Initialization()
    {
        ScanBarcodeOperations.Add(App.GetService<PdaStkStockCountPackageOperation>(ServiceProvider));
        ScanBarcodeOperations.Add(App.GetService<PdaStkStockCountBarcodeOperation>(ServiceProvider));
        ScanBarcodeOperations.Add(App.GetService<PdaStkStockCountSourceOperation>(ServiceProvider));
        ScanBarcodeOperations.Add(App.GetService<PdaStkStockCountStockOperation>(ServiceProvider));
    }

    public override void RefreshShow(long tranId)
    {
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.StkStockCount?.BillNo;
        pdaShow.StockCaption = pdaData.StockInfo == null ? "" : $"[{pdaData.StockInfo?.WhAreaName}]{pdaData.StockInfo?.WhLocName}";

        //处理Detail的显示
        pdaShow.Details.Clear();
        var showEntriesQueryable = pdaData.StkStockCountEntries.AsQueryable();
        if (!string.IsNullOrEmpty(pdaData.DetailPagination.SearchValue))
            showEntriesQueryable = showEntriesQueryable.Where(r =>
                (r.Entry.Material == null ? "" : r.Entry.Material.Number + "").Contains(pdaData.DetailPagination.SearchValue, StringComparison.OrdinalIgnoreCase)
                || (r.Entry.Material == null ? "" : r.Entry.Material.Name).Contains(pdaData.DetailPagination.SearchValue, StringComparison.OrdinalIgnoreCase)
                || (r.Entry.BatchNo + "").Contains(pdaData.DetailPagination.SearchValue, StringComparison.OrdinalIgnoreCase));

        // 如果是盲盘，只显示有条码的明细
        //if (pdaShow.IsBlindCount)
        showEntriesQueryable = showEntriesQueryable.Where(r => r.IncludeBarcodes.Count > 0);

        var showEntriesCount = showEntriesQueryable.Count();
        var showEntries = showEntriesQueryable
            .Skip((pdaData.DetailPagination.Page - 1) * pdaData.DetailPagination.PageSize)
            .Take(pdaData.DetailPagination.PageSize)
            .ToList();
        pdaData.DetailPagination.Total = showEntriesCount;
        pdaShow.DetailPagination = pdaData.DetailPagination;
        showEntries.ForEach(r =>
        {
            decimal diffQty = 0;
            // 计算差异数
            diffQty = (r.ScanQty - (r.Entry.AcctQty - r.Entry.CountQty));
            pdaShow.Details.Add(new PdaStkStockCountDetailShowInfo
            {
                Properties = null,
                DetailId = r.DetailId,
                Title = $"[{r.Entry.Material?.Number}]{r.Entry.Material?.Name}",
                SubTitle = L.Text["批号: {0}\r\n库区库位: {1}[{2}]", r.Entry.BatchNo, r.Entry.WhArea?.Name, r.Entry.WhLoc?.Name],
                ScanQty = r.ScanQty,
                Qty = r.Entry.AcctQty, // 盲盘不显示分录总数量
                IsNew = false,
                IncludeBarcodes = null,
                CheckQty = r.Entry.CountQty,
                DiffQty = diffQty,
                BarcodeCount = r.IncludeBarcodes.Count,
            });
        });

        //统计信息
        pdaShow.Summary.Qty = pdaData.StkStockCountEntries.Sum(r => r.Entry.AcctQty);
        pdaShow.Summary.CheckQty = pdaData.StkStockCountEntries.Sum(r => r.Entry.CountQty);
        pdaShow.Summary.ScanQty = pdaData.StkStockCountEntries.Sum(r => r.ScanQty);
        pdaShow.Summary.ProfitQty = pdaData.StkStockCountEntries.Sum(r => r.Entry.CountQty + r.ScanQty - r.Entry.AcctQty > 0 ? r.Entry.CountQty + r.ScanQty - r.Entry.AcctQty : 0);
        pdaShow.Summary.LoseQty = pdaData.StkStockCountEntries.Sum(r => r.Entry.AcctQty - r.Entry.CountQty - r.ScanQty > 0 ? r.Entry.AcctQty - r.Entry.CountQty - r.ScanQty : 0);

        //扫描条码数
        pdaShow.ScanBarcodeCount = pdaData.Barcodes.Count;
        //处理条码显示
        pdaShow.Barcodes = new List<PdaBarcodeShowInfo>();
        if (!string.IsNullOrEmpty(pdaData.BarcodePagination.SearchValue))
        {
            //如果有查询条件
            var barcodeInput = JsonConvert.DeserializeObject<PdaStkStockCountShowBarcodeInput>(pdaData.BarcodePagination.SearchValue);
            var barcodeQueryable = pdaData.Barcodes.AsQueryable();
            //是否从明细点
            if (!string.IsNullOrEmpty(barcodeInput.DetailId))
            {
                //如果是从明细点
                var includes = pdaData.StkStockCountEntries.First(r => r.DetailId == barcodeInput.DetailId).IncludeBarcodes.Select(r => r.DetailId).ToList();
                barcodeQueryable = barcodeQueryable.Where(r => includes.Contains(r.DetailId));
            }

            if (!string.IsNullOrEmpty(barcodeInput.SearchValue))
            {
                barcodeQueryable = barcodeQueryable.Where(r => r.Barcode.Barcode.Contains(barcodeInput.SearchValue));
            }

            var barcodeTotalCount = barcodeQueryable.Count();
            var showBarcodes = barcodeQueryable
                .Skip((pdaData.BarcodePagination.Page - 1) * pdaData.BarcodePagination.PageSize)
                .Take(pdaData.BarcodePagination.PageSize)
                .ToList();
            showBarcodes.ForEach(r =>
            {
                pdaShow.Barcodes.Add(new PdaBarcodeShowInfo
                {
                    Properties = null,
                    DetailId = r.DetailId,
                    BarcodeId = r.Barcode.Id + "",
                    Title = $"{r.Barcode.Barcode}",
                    SubTitle = r.Barcode.Material.Name,
                    Value = $"数量:{r.CalcBarcode.ScanQty:0.######}/{r.Barcode.Qty.ToString("0.######")}",
                    StockKey = $"{r.CalcBarcode.WhAreaId}_{r.CalcBarcode.WhLocId}",
                    StockValue = $"{r.CalcBarcode.WhArea?.Name}[{r.CalcBarcode.WhLoc?.Name}]",
                    ScanQty = $"{r.CalcBarcode.ScanQty:0.######}",
                    Qty = $"{r.Barcode.Qty:0.######}"
                });
            });
            //清空搜索结果
            //pdaData.BarcodePagination.SearchValue = "";
            pdaData.BarcodePagination.Total = barcodeTotalCount;
            pdaShow.BarcodePagination = pdaData.BarcodePagination;
        }

        DataCacheService.SaveRedis(Key, tranId);
    }

    public override void BillDataInitialization(IPdaData pdaData)
    {
        var data = (PdaStkStockCountData)pdaData;
        data.DetailPagination = new PdaApiPagination
        {
            Page = 1,
            Total = 0,
            PageSize = Take,
            SearchValue = ""
        };
        data.BarcodePagination = new PdaApiPagination
        {
            Page = 1,
            Total = 0,
            PageSize = Take,
            SearchValue = ""
        };
    }

    /// <summary>
    /// 需要匹配的列
    /// </summary>
    public virtual List<string> SummaryBillFields { get; } = new List<string>()
    {
        "MaterialId",
        "WhAreaId",
        "WhLocId",
        "BatchNo",
        "ProduceDate",
        "ExpiryDate",
        "AuxPropValueId",
        "UnitId",
    };


    /// <summary>
    /// 扫描操作
    /// </summary>
    protected virtual List<IPdaLocalBillScanBarcodeOperation> ScanBarcodeOperations { get; set; } = new();

    /// <summary>
    /// 盘点方案Lookup
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="lookupValue"></param>
    /// <returns></returns>
    public virtual async Task<List<PdaLookupOutput>> LookupQuerySourceInfo(long tranId, string lookupValue)
    {
        var stockCounts = await Rep.AsQueryable()
            .Where(r => r.StockCountStatus == StkStockCountStatus.GeneratedDetail && r.BillType == "JTPD")
            .WhereIF(!string.IsNullOrEmpty(lookupValue), r => r.Name.Contains(lookupValue) || r.BillNo.Contains(lookupValue))
            .OrderByDescending(x => x.CreateTime)
            .Take(Take).ToListAsync();
        List<PdaLookupOutput> outputs = new List<PdaLookupOutput>();
        stockCounts.ForEach(r =>
        {
            outputs.Add(new PdaLookupOutput
            {
                Key = r.Id + "",
                Title = $"[{r.BillNo}]{r.Name}",
                SubTitle = r.ApproveTime == null ? "" : r.ApproveTime.Value.ToString("yyyy-MM-dd"),
            });
        });
        return outputs;
    }

    /// <summary>
    /// 仓库仓位Lookup
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="lookupValue"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private async Task<List<PdaLookupOutput>> LookupQueryStockInfo(long tranId, string lookupValue)
    {
        var pdaData = GetPdaData(tranId);
        if (pdaData.StkStockCountStockEntries.Count == 0 || pdaData.StkStockCount == null) throw Oops.Bah(L.Text["请先选择盘点单"]);

        var ids = pdaData.StkStockCountStockEntries.Select(r => r.WhAreaId).ToList();

        var stocks = await Rep.Change<BdWhArea>().AsQueryable()
            .LeftJoin<BdWhLoc>((t1, t2) => t1.Id == t2.WhAreaId)
            .Where((t1, t2) => ids.Contains(t1.Id))
            .WhereIF(!string.IsNullOrEmpty(lookupValue),
                (t1, t2) => t1.Number.Contains(lookupValue) || t1.Name.Contains(lookupValue) || t2.Number.Contains(lookupValue) || t2.Name.Contains(lookupValue))
            .Select((t1, t2) => new { t1, t2 })
            .Take(Take)
            .ToListAsync();
        List<PdaLookupOutput> outputs = new List<PdaLookupOutput>();
        stocks.ForEach(r =>
        {
            outputs.Add(new PdaLookupOutput
            {
                Key = $"{r.t1.Id}__{r.t2.Id}",
                Title = $"[{r.t1.Number}]{r.t1.Name}",
                SubTitle = $"[{r.t2.Number}]{r.t2.Name}",
            });
        });
        return outputs;
    }

    /// <summary>
    /// 选择仓库
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="valueKey"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task SelectLookupDataStockInfo(long tranId, string valueKey)
    {
        var values = valueKey.Split("__");
        if (values.Length < 2) throw Oops.Bah(L.Text["格式不正确"]);
        var stockId = Convert.ToInt64(values[0]);
        var stockLocId = Convert.ToInt64(values[1]);

        var stock = await Rep.Change<BdWhArea>().AsQueryable()
            .LeftJoin<BdWhLoc>((t1, t2) => t1.Id == t2.WhAreaId)
            .Where((t1, t2) => t1.Id == stockId && t2.Id == stockLocId)
            .Select((t1, t2) => new { t1, t2 })
            .Take(Take)
            .FirstAsync();
        if (stock == null) throw Oops.Bah(L.Text["找不到[{0}]仓库仓位", valueKey]);
        var pdaData = GetPdaData(tranId);
        pdaData.StockInfo = new PdaLocalBillStockInfo
        {
            Properties = null,
            WhAreaId = stock.t1.Id + "",
            WhAreaNumber = stock.t1.Number,
            WhAreaName = stock.t1.Name,
            WhLocId = stock.t2.Id + "",
            WhLocNumber = stock.t2.Number,
            WhLocName = stock.t2.Name,
        };
    }

    /// <summary>
    /// 选择盘点单
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="valueKey"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async virtual Task SelectLookupDataSourceInfo(long tranId, string valueKey)
    {
        var id = Convert.ToInt64(valueKey);
        var stockCount = await Rep.AsQueryable()
            .Where(r => r.StockCountStatus == StkStockCountStatus.GeneratedDetail && r.BillType == "JTPD")
            .Where(r => r.Id == id)
            .IncludeNavCol()
            .Take(Take).FirstAsync();
        var pdaData = GetPdaData(tranId);
        pdaData.StkStockCount = stockCount;
        pdaData.StkStockCountEntries.Clear();

        stockCount.Entries.ForEach(r =>
        {
            pdaData.StkStockCountEntries.Add(new PdaStkStockCountEntry
            {
                DetailId = $"{YitIdHelper.NextId()}",
                IsNew = false,
                Entry = r,
                ScanQty = 0,
                IncludeBarcodes = new List<PdaLocalBillBarcode>()
            });
        });

        pdaData.StkStockCountStockEntries = stockCount.WhAreaEntries;
    }

    /// <summary>
    /// 更新分页
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldValue"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void SelectFieldDataSearchDetail(long tranId, string fieldValue)
    {
        var input = JsonConvert.DeserializeObject<PdaStkStockCountPaginationInput>(fieldValue);
        var pdaData = GetPdaData(tranId);
        pdaData.DetailPagination.Page = input.PageIndex;
        pdaData.DetailPagination.SearchValue = input.SearchValue;
    }

    /// <summary>
    /// 更新分页
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="fieldValue"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void SelectFieldDataSearchBarcode(long tranId, string fieldValue)
    {
        var input = JsonConvert.DeserializeObject<PdaStkStockCountPaginationInput>(fieldValue);
        var pdaData = GetPdaData(tranId);
        pdaData.BarcodePagination.Page = input.PageIndex;
        pdaData.BarcodePagination.SearchValue = input.SearchValue;
    }

    /// <summary>
    /// 删除条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="detailId"></param>
    /// <exception cref="NotImplementedException"></exception>
    public void DeleteDataDeleteBarcode(long tranId, string detailId)
    {
        var pdaData = GetPdaData(tranId);
        var removeBarcode = pdaData.Barcodes.FirstOrDefault(r => r.DetailId == detailId);
        if (removeBarcode == null) throw Oops.Bah(PdaErrorCode.Pda1029, detailId);
        List<PdaLocalBillBarcode> tmpPdaBarcodes = new List<PdaLocalBillBarcode>();
        pdaData.Barcodes.ForEach(b =>
        {
            if (removeBarcode.ContainerId is 0)
            {
                //如果无箱
                if (b.DetailId != detailId)
                    tmpPdaBarcodes.Add(b);
            }
            else
            {
                //如果有箱
                if (b.ContainerId != removeBarcode.ContainerId) tmpPdaBarcodes.Add(b);
            }
        });
        pdaData.Barcodes.Clear();
        //把扫描数量设置为0
        pdaData.StkStockCountEntries.ForEach(r =>
        {
            r.ScanQty = 0;
            r.IncludeBarcodes.Clear();
        });
        foreach (var barcode in tmpPdaBarcodes)
        {
            MatchingBarcode(tranId, barcode);
        }
    }

    /// <summary>
    /// 匹配条码
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="pdaBarcode"></param>
    public void MatchingBarcode(long tranId, PdaLocalBillBarcode pdaBarcode)
    {
        var pdaData = GetPdaData(tranId);

        //2. 找到需要匹配的列,组成key
        //查找匹配的行
        List<string> values = new List<string>();
        foreach (var field in SummaryBillFields)
        {
            if (field == "BarcodeId")
            {
                var value = pdaBarcode.Barcode.Id;
                values.Add($"{value}");
            }
            else
            {
                var value = PdaHelper.GetValue(pdaBarcode.CalcBarcode, field);
                values.Add($"{value}");
            }
        }

        var keyValue = string.Join("|", values);


        var matchingData = pdaData.StkStockCountEntries.Where(r =>
        {
            List<string> rKeyValues = new List<string>();
            foreach (string field in SummaryBillFields)
            {
                var value = PdaHelper.GetValue(r.Entry, field);
                rKeyValues.Add($"{value}");
            }

            return string.Join("|", rKeyValues.ToArray()) == keyValue;
        }).ToList();
        //如果没有找到匹配的行
        if (matchingData.Count <= 0)
        {
            //不支持源单外物料 报错
            if (!IsOverSourceItem)
            {
                throw Oops.Bah(PdaErrorCode.Pda1016);
            }

            //如果支持源单外物料 (无源单)
            //把条码的值，写到Detail
            //填充明细
            //为了效率,实体类只填充Number和Name
            var detail = new StkStockCountEntry
            {
                EntryId = 0,
                Id = pdaData.StkStockCount.Id,
                Seq = 0,
                EsEntryId = null,
                MaterialId = Convert.ToInt64(pdaBarcode.CalcBarcode.MaterialId),
                //Material = Rep.Change<BdMaterial>().GetFirst(r => r.Id == Convert.ToInt64(pdaBarcode.CalcBarcode.MaterialId)),
                Material = new BdMaterial()
                    { Id = Convert.ToInt64(pdaBarcode.CalcBarcode.MaterialId), Number = pdaBarcode.CalcBarcode.Material.Number, Name = pdaBarcode.CalcBarcode.Material.Name },
                WhAreaId = Convert.ToInt64(pdaBarcode.CalcBarcode.WhAreaId),
                //Stock = Rep.Change<BdStock>().GetFirst(r => r.Id == Convert.ToInt64(pdaBarcode.CalcBarcode.StockId)),
                WhArea = new BdWhArea()
                    { Id = Convert.ToInt64(pdaBarcode.CalcBarcode.WhAreaId), Number = pdaBarcode.CalcBarcode.WhArea.Number, Name = pdaBarcode.CalcBarcode.WhArea.Name },
                WhLocId = Convert.ToInt64(pdaBarcode.CalcBarcode.WhLocId),
                //StockLoc = Rep.Change<BdStockLoc>().GetFirst(r => r.Id == Convert.ToInt64(pdaBarcode.CalcBarcode.StockLocId)),
                WhLoc = new BdWhLoc()
                    { Id = Convert.ToInt64(pdaBarcode.CalcBarcode.WhLocId), Number = pdaBarcode.CalcBarcode.WhLoc.Number, Name = pdaBarcode.CalcBarcode.WhLoc.Name },
                UnitId = Convert.ToInt64(pdaBarcode.CalcBarcode.UnitId),
                //Unit = Rep.Change<BdUnit>().GetFirst(r => r.Id == Convert.ToInt64(pdaBarcode.CalcBarcode.UnitId)),
                Unit = new BdUnit() { Id = Convert.ToInt64(pdaBarcode.CalcBarcode.UnitId), Number = pdaBarcode.CalcBarcode.Unit.Number, Name = pdaBarcode.CalcBarcode.Unit.Name },
                BatchNo = pdaBarcode.CalcBarcode.BatchNo,
                ProduceDate = pdaBarcode.CalcBarcode.ProduceDate,
                ExpiryDate = pdaBarcode.CalcBarcode.ExpiryDate,
                AcctQty = 0,
                CountQty = 0,
                GainQty = 0,
                LossQty = 0,
                IsSystem = false,
                // BarcodeId = pdaBarcode.Barcode.Id,
                // Barcode = pdaBarcode.Barcode
            };
            var entry = new PdaStkStockCountEntry
            {
                DetailId = $"{YitIdHelper.NextId()}",
                IsNew = true,
                Entry = detail,
                ScanQty = 0,
                IncludeBarcodes = new List<PdaLocalBillBarcode>()
            };

            pdaData.StkStockCountEntries.Add(entry);
            matchingData.Add(entry);
        }

        //校验是否超源单数量
        var diffQty = pdaBarcode.CalcBarcode.ScanQty;
        if (!IsOverSourceItem && !IsOverSourceQty)
        {
            //查找到匹配的明细，再用QTY判断是否超
            foreach (var data in matchingData)
            {
                var qty = data.Entry.AcctQty - data.Entry.CountQty - data.ScanQty;
                diffQty -= qty;
            }

            if (diffQty > 0) throw Oops.Bah(PdaErrorCode.Pda1014);
        }

        //校验通过,写数据
        //这里是扫描，默认带条码数量
        diffQty = pdaBarcode.CalcBarcode.ScanQty;

        foreach (PdaStkStockCountEntry detail in matchingData)
        {
            if (diffQty <= 0) break;
            decimal includeQty;
            var qty = detail.Entry.AcctQty - detail.Entry.CountQty - detail.ScanQty;
            if (qty <= 0) continue;
            if (diffQty > qty)
            {
                //如果可扣减数量大于源单数量,includeQty为源单数量
                includeQty = qty;
                detail.ScanQty = detail.Entry.AcctQty - detail.Entry.CountQty;
                diffQty -= qty;
            }
            else
            {
                includeQty = diffQty;
                detail.ScanQty += diffQty;
                diffQty = 0;
            }

            pdaBarcode.CalcBarcode.ScanQty = includeQty;
            detail.IncludeBarcodes.Add(pdaBarcode);
        }

        //如果剩余数量大于0
        if (diffQty > 0)
        {
            //如果超源单数量，直接把剩余数量填到匹配的最后一行记录上
            if (IsOverSourceQty)
            {
                var lastMatching = matchingData[^1];
                lastMatching.ScanQty += diffQty;

                var exPdaBarcode = lastMatching.IncludeBarcodes.FirstOrDefault(r => r.DetailId == pdaBarcode.DetailId);
                if (exPdaBarcode == null)
                {
                    // 之前没有匹配到条码，新增
                    pdaBarcode.CalcBarcode.ScanQty = diffQty;
                    lastMatching.IncludeBarcodes.Add(pdaBarcode);
                }
                else
                {
                    // 更新条码扫描数量
                    pdaBarcode.CalcBarcode.ScanQty += diffQty;
                }
            }
            else
            {
                throw Oops.Bah(PdaErrorCode.Pda1016);
            }
        }

        pdaData.Barcodes.Add(pdaBarcode);
    }

    /// <summary>
    /// 提交单据
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="submitData"></param>
    /// <param name="isRepeat"></param>
    /// <exception cref="NotImplementedException"></exception>
    private async Task SubmitBill(long tranId, object submitData, bool isRepeat)
    {
        var pdaData = GetPdaData(tranId);
        if (pdaData.Barcodes.Count == 0) throw Oops.Bah(L.Text["没有提交数据"]);

        var stkStockCountService = App.GetService<StkStockCountService>(ServiceProvider);

        //组装提交信息
        var details = new List<StkStockCountSubmitDetail>();
        pdaData.StkStockCountEntries.ForEach(r =>
        {
            if (r.IncludeBarcodes.Count == 0) return;
            var detail = new StkStockCountSubmitDetail
            {
                StockCountEntryId = r.Entry.EntryId,
                WhAreaId = r.Entry.WhAreaId,
                WhLocId = r.Entry.WhLocId,
                DetailInfos = new List<StkStockCountSubmitDetailInfo>()
            };

            r.IncludeBarcodes.ForEach(b =>
            {
                detail.DetailInfos.Add(new StkStockCountSubmitDetailInfo
                {
                    BarcodeId = b.Barcode.Id,
                    Qty = b.CalcBarcode.ScanQty
                });
            });

            details.Add(detail);
        });

        var submitStockCountData = new StkStockCountSubmitInput
        {
            StockCountId = pdaData.StkStockCount.Id,
            TranId = tranId,
            Details = details
        };

        PdaSaveBillLog log = new PdaSaveBillLog
        {
            TranId = tranId,
            SourceKey = "StkStockCount",
            TargetKey = "StkStockCount",
            TargetBillId = pdaData.StkStockCount.Id + "",
            TargetBillNo = pdaData.StkStockCount.BillNo,
            Result = JsonConvert.SerializeObject(submitStockCountData),
            Extra1 = null,
            Extra2 = null,
            Extra3 = null
        };
        await Rep.Change<PdaSaveBillLog>().InsertAsync(log);

        await stkStockCountService.Submit(submitStockCountData);

        pdaData.WaitingConfirmBarcodes.Clear();
    }

    #region PDA操作方法

    public override Task ScanBarcode(long tranId, string barcode, bool isRepeat, ExtensionObject ext)
    {
        var input = JsonConvert.DeserializeObject<PdaStkStockCountScanBarcodeInput>(barcode);
        if (input == null) throw Oops.Bah(L.Text["扫描的内容有误[{0}]", barcode]);
        var pdaData = GetPdaData(tranId);
        if (pdaData.StkStockCount != null)
            if (!string.IsNullOrWhiteSpace(pdaData.StkStockCount.BillNo) && pdaData.StkStockCount.BillNo == input.Barcode)
                throw Oops.Bah(L.Text["请勿重复扫描单号[{0}]", input.Barcode]);
        if (input.RemoveFromWaitingList)
        {
            pdaData.WaitingConfirmBarcodes.Clear();
            return Task.CompletedTask;
        }

        //分几种情况
        //1. 扫描单号  2. 扫描箱码   3. 扫描条码    4. 扫描仓库仓位
        PdaLocalBillScanBarcodeArgs args = new PdaLocalBillScanBarcodeArgs
        {
            TranId = tranId,
            Key = Key,
            BarcodeString = input.Barcode,
            Barcodes = new List<BdBarcode>(),
            IsResult = false,
            IsRepeat = input.IsRepeat,
            Properties = new Dictionary<string, object>()
            {
                { "ScanBarcodeType", input }
            }
        };


        foreach (var scanBarcodeOperation in ScanBarcodeOperations)
        {
            if (args.IsResult) break;
            scanBarcodeOperation.Operation(args);
        }

        if (!args.IsResult) throw Oops.Bah(PdaErrorCode.Pda1013, input.Barcode);
        RefreshShow(tranId);
        return Task.CompletedTask;
    }

    public override async Task<List<PdaLookupOutput>> LookupQuery(long tranId, string lookupKey, string lookupValue)
    {
        switch (lookupKey)
        {
            case "SourceInfo":
                return await LookupQuerySourceInfo(tranId, lookupValue);
            case "StockInfo":
                return await LookupQueryStockInfo(tranId, lookupValue);
            default:
                throw new NotImplementedException();
        }
    }

    public override async Task SelectLookupData(long tranId, string lookupKey, string valueKey)
    {
        switch (lookupKey)
        {
            case "SourceInfo":
                await SelectLookupDataSourceInfo(tranId, valueKey);
                break;
            case "StockInfo":
                await SelectLookupDataStockInfo(tranId, valueKey);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task SelectFieldData(long tranId, string fieldKey, string fieldValue)
    {
        switch (fieldKey)
        {
            case "SearchDetail":
                SelectFieldDataSearchDetail(tranId, fieldValue);
                break;
            case "SearchBarcode":
                SelectFieldDataSearchBarcode(tranId, fieldValue);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task DeleteData(long tranId, string dataKey, string valueKey)
    {
        switch (dataKey)
        {
            case "DeleteBarcode":
                DeleteDataDeleteBarcode(tranId, valueKey);
                break;
            default:
                throw new NotImplementedException();
        }

        RefreshShow(tranId);
    }

    public override async Task Submit(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        switch (submitKey)
        {
            case "SubmitBill":
                await SubmitBill(tranId, submitData, isRepeat);
                break;
            default:
                throw new NotImplementedException();
        }
    }

    public override async Task<object> SubmitReturnData(long tranId, string submitKey, object submitData, bool isRepeat)
    {
        var service = App.GetService<SysReportTemplateService>(ServiceProvider);
        var printInfo = JsonConvert.DeserializeObject<PdaStkStockCountPrintInfo>(submitData + "");
        var bdBar = await Rep.Change<BdBarcode>().AsQueryable().IncludeNavCol().FirstAsync(x => x.Id == SqlFunc.ToInt64(printInfo.BarcodeId));
        var BarBarcode = await Rep.Change<BarBarcode>().AsQueryable().IncludeNavCol().FirstAsync(x => x.Barcode == bdBar.Barcode);
        var pdaData = GetPdaData(tranId);
        bdBar.Qty = printInfo.Fqty;
        BarBarcode.Qty = printInfo.Fqty;
        // 保存条码档案日志
        BdBarcodeLog log = new BdBarcodeLog
        {
            OpTranId = 0,
            BarcodeId = bdBar.Id,
            Barcode = bdBar.Barcode,
            OriginQty = bdBar.Qty,
            OpQty = printInfo.Fqty,
            UnitId = bdBar.UnitId,
            OriginStatus = bdBar.Status,
            CurStatus = bdBar.Status,
            MaterialId = bdBar.MaterialId,
            BatchNo = bdBar.BatchNo,
            ProduceDate = bdBar.ProduceDate,
            ExpiryDate = bdBar.ExpiryDate,
            RelBillKey = bdBar.SrcBillKey,
            RelBillId = bdBar.SrcBillId,
            RelBillEntryId = bdBar.SrcBillEntryId,
            RelBillNo = bdBar.SrcBillNo,
            RelBillType = "ModifyBarcode",
            RelBillEntrySeq = bdBar.SrcBillEntrySeq,
            SrcWhAreaId = bdBar.WhAreaId,
            SrcWhLocId = bdBar.WhLocId,
            DestWhAreaId = bdBar.WhAreaId,
            DestWhLocId = bdBar.WhLocId,
            OriginBarcodeJson = bdBar.ToJson(),
        };
        await Rep.Change<BdBarcodeLog>().InsertAsync(log);
        await Rep.Change<BarBarcode>().UpdateAsync(BarBarcode);
        await Rep.Change<BdBarcode>().UpdateAsync(bdBar);
        var report = service.GetReport(Convert.ToInt64(printInfo.TemplateId), typeof(BarBarcodeService).AssemblyQualifiedName, new List<string>() { printInfo.BarcodeId }).Result;
        await report.RenderAsync();
        List<string> base64Strings = new();
        StiPngExportService stiPngExportService = new();
        for (int i = 0; i < report.RenderedPages.Count; i++)
        {
            var ms = new MemoryStream();
            //report.ExportDocument(StiExportFormat.ImagePng, ms, new StiEmfExportSettings() { PageRange = new StiPagesRange(i + 1) });
            StiImageExportSettings settings = new StiPngExportSettings
            {
                //加了下面这个就会报错,应该是用了默认格式保存
                //settings.ImageType = StiImageType.Png;
                PageRange = new StiPagesRange(i + 1),
                ImageResolution = printInfo.Dpi ?? 200,
            };
            stiPngExportService.ExportImage(report, ms, settings);
            ms.Seek(0, SeekOrigin.Begin);

            //服务器端处理返回的图像指令
            var resultBytes = PdaHelper.GetBitmapData(ms.ToArray());
            var base64String = Convert.ToBase64String(resultBytes);
            base64Strings.Add(base64String);
        }


        return base64Strings;
    }

    #endregion

    #region PdaSchema

    public class PdaSchema : IPdaSchema
    {
    }

    #endregion
}

public class PdaStkStockCountPrintInfo : PdaPrintInfo
{
    public string BarcodeId { get; set; }

    public string TemplateId { get; set; }

    public decimal Fqty { get; set; }
}