﻿namespace Neuz.Application.Pda.LocalBill.Link;

/// <summary>
/// 转换规则映射 (表体字段不支持写到表头,因为表体有多行)
/// </summary>
public class LocalBillLinkMapping
{
    /// <summary>
    /// 本地单据字段名
    /// </summary>
    public string LocalBillFiledName { get; set; }

    /// <summary>
    /// PDA扫描对应字段名
    /// </summary>
    public string ScanFiledName { get; set; }

    /// <summary>
    /// 映射PDA扫描对应表头还是表体
    /// </summary>
    public LocalBillLinkMappingScanType ScanType { get; set; }

    /// <summary>
    /// 默认值 (要写的字段需要为空)
    /// </summary>
    public object DefaultValue { get; set; }
}

/// <summary>
/// 条码转换规则映射
/// </summary>
public class LocalBillLinkBarcodeMapping
{
    /// <summary>
    /// PDA扫描条码字段名
    /// </summary>
    public string ScanBarcodeName { get; set; }

    /// <summary>
    /// PDA保存条码字段名
    /// </summary>
    public string SaveBarcodeName { get; set; }

    /// <summary>
    /// 默认值 (要写的字段需要为空)
    /// </summary>
    public object DefaultValue { get; set; }
}

/// <summary>
/// 映射PDA扫描对应表头还是表体
/// </summary>
public enum LocalBillLinkMappingScanType
{
    /// <summary>
    /// 表头
    /// </summary>
    Head,

    /// <summary>
    /// 表体
    /// </summary>
    Detail
}

/// <summary>
/// 数量计算映射
/// </summary>
public class LocalBillQtyCalcMapping
{
    /// <summary>
    /// 本地单据需要计算的字段名
    /// </summary>
    public List<LocalBillCalcFieldMapping> CalcFiledNames { get; set; }

    /// <summary>
    /// PDA扫描对应字段名 (必须为数字类型字段)
    /// </summary>
    public string ScanFiledName { get; set; }

    /// <summary>
    /// 映射PDA扫描对应表头还是表体
    /// </summary>
    public LocalBillLinkMappingScanType ScanType { get; set; }
}

/// <summary>
/// 本地单据字段计算映射
/// </summary>
public class LocalBillCalcFieldMapping
{
    /// <summary>
    /// 本地单据字段名
    /// </summary>
    public string LocalBillFiledName { get; set; }

    /// <summary>
    /// 需要计算类型
    /// </summary>
    public LocalBillCalcType CalcType { get; set; }

    /// <summary>
    /// 分组标识 (时间关系,暂时未实现)
    /// </summary>
    public string GroupKey { get; set; }
}

/// <summary>
/// 需要计算类型
/// </summary>
public enum LocalBillCalcType
{
    /// <summary>
    /// 相加
    /// </summary>
    Add,

    /// <summary>
    /// 相减
    /// </summary>
    Sub,

    /// <summary>
    /// 相乘
    /// </summary>
    Mul,

    /// <summary>
    /// 相除
    /// </summary>
    Div
}

/// <summary>
/// 
/// </summary>
public class LocalBillContentInfo
{
    /// <summary>
    /// 扫描明细字段名
    /// </summary>
    public string ScanFiledName { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 显示多少列 (默认1列) (由于时间关系,未实现)
    /// </summary>
    public int Col { get; set; } = 1;
}

/// <summary>
/// 源单过滤条件
/// </summary>
public class LocalBillFilter
{
    /// <summary>
    /// 映射PDA扫描对应表头还是表体
    /// </summary>
    public LocalBillLinkMappingScanType ScanType { get; set; }

    /// <summary>
    /// 本地单据字段名
    /// </summary>
    public string ScanFiledName { get; set; }

    /// <summary>
    /// 源单过滤条件类型
    /// </summary>
    public LocalBillFilterCondition Condition { get; set; }

    /// <summary>
    /// 对比的值
    /// </summary>
    public object Value { get; set; }
}

/// <summary>
/// 源单过滤条件类型
/// </summary>
public enum LocalBillFilterCondition
{
    /// <summary>
    /// 相同
    /// </summary>
    Equals,

    /// <summary>
    /// 不相同
    /// </summary>
    NotEquals
}