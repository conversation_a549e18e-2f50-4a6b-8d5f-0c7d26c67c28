﻿using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库存锁定事务服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkInventoryPreInLog", Order = 100)]
public class StkInventoryPreInLogService : NeuzBaseService<StkInventoryPreInLog>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 库存锁定事务服务构造函数
    /// </summary>
    public StkInventoryPreInLogService(IServiceProvider serviceProvider, SqlSugarRepository<StkInventoryPreInLog> rep) : base(serviceProvider, rep)
    {
    }

    [NonAction]
    public override Task<long> AddAsync(StkInventoryPreInLog input)
    {
        return base.AddAsync(input);
    }

    [NonAction]
    public override Task UpdateAsync(StkInventoryPreInLog input)
    {
        return base.UpdateAsync(input);
    }

    [NonAction]
    public override Task<List<ExecResult>> DeleteAsync(IdsInput input)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task Import(IFormFile file)
    {
        throw new NotSupportedException();
    }

    [NonAction]
    public override Task<FileContentResult> ImportTemplate()
    {
        throw new NotSupportedException();
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "InvPreInLogType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "RelBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "RelBillType", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "RelSourceBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "InvPreInLogType",
            "RelBillNo",
            "RelSeq",
            "MaterialNumber",
            "MaterialName",
            "BatchNo",
            "ProduceDate",
            "ExpiryDate",
            "Qty",
            "UnitNumber",
            "UnitName",
            "OwnerNumber",
            "OwnerName",
            "AuxPropValueNumber",
            "AuxPropValueName",
            "WarehouseNumber",
            "WarehouseName",
            "WhAreaNumber",
            "WhAreaName",
            "WhLocNumber",
            "WhLocName",
            "ContainerNumber",
            "RelSourceBillNo",
            "CreateTime",
            "CreateUserName",
            "GroupName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }
}