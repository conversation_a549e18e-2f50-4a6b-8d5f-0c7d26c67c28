﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using PluginCore;
using PluginCore.AspNetCore.Extensions;
using PluginCore.Interfaces;
using PluginCore.Models;

namespace Admin.NET.Plugin.PluginCore;

// 模拟 ConfigureService
public sealed class StartupServiceComponent : IServiceComponent
{
    public void Load(IServiceCollection services, ComponentContext componentContext)
    {
        // 1. 添加 PluginCore
        services.AddPluginCore();
        IPluginManager pluginManager = App.GetService<IPluginManager>();
        IDynamicApiRuntimeChangeProvider provider = App.GetService<IDynamicApiRuntimeChangeProvider>();

        #region 获取 PluginConfigModel

        PluginConfigModel pluginConfigModel = PluginConfigModelFactory.Create();

        #endregion 获取 PluginConfigModel

        // 已启用的插件

        #region 加载 已启用插件的Assemblies

        IList<string> enabledPluginIds = pluginConfigModel.EnabledPlugins;
        foreach (var pluginId in enabledPluginIds)
        {
            //9.载入Furion动态插件
            var pluginMainAssembly = pluginManager.GetPluginAssembly(pluginId);
            // 将程序集添加进动态 WebAPI 应用部件
            provider.AddAssembliesWithNotifyChanges(pluginMainAssembly);
        }

        #endregion 加载 已启用插件的Assemblies
    }
}

// 模拟 Configure
public sealed class StartupApplicationComponent : IApplicationComponent
{
    public void Load(IApplicationBuilder app, IWebHostEnvironment env, ComponentContext componentContext)
    {
        // 2. 使用 PluginCore
        app.UsePluginCore();
    }
}