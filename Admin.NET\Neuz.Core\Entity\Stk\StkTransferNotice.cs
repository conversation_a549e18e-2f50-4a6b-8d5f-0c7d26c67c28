﻿namespace Neuz.Core.Entity;

/// <summary>
/// 调拨通知单
/// </summary>
[SugarTable(null, "调拨通知单")]
public class StkTransferNotice : EsBillEntityBase
{
    /// <summary>
    /// 单据日期
    /// </summary>
    [SugarColumn(ColumnDescription = "单据日期")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 单据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "单据类型", Length = 80)]
    public string BillType { get; set; }

    /// <summary>
    /// 业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "业务状态")]
    public StkTransferNoticeStatus Status { get; set; }

    /// <summary>
    /// 分配状态
    /// </summary>
    [SugarColumn(ColumnDescription = "分配状态")]
    public StkTransferNoticeAllocateStatus AllocateStatus { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库Id")]
    public long WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
    [CustomSerializeFields]
    public BdWarehouse Warehouse { get; set; }

    /// <summary>
    /// 推送标记
    /// </summary>
    [SugarColumn(ColumnDescription = "推送标记")]
    public PushFlag PushFlag { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(StkTransferNoticeEntry.Id))]
    public List<StkTransferNoticeEntry> Entries { get; set; }
}