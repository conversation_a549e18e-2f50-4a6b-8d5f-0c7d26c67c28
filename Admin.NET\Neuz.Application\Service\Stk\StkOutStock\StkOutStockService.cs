﻿using Furion.Localization;
using Neuz.Application.ExternalSystem;
using Neuz.Application.ExternalSystem.Dto;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 出库单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkOutStock", Order = 100)]
public class StkOutStockService : StkBaseBillService<StkOutStock>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkOutStock);

    /// <summary>
    /// 出库单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkOutStockService(IServiceProvider serviceProvider, SqlSugarRepository<StkOutStock> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Status", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "Status",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "SupplierNumber",
            "SupplierName",
            "CustomerNumber",
            "CustomerName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_SrcWhAreaNumber",
            "Entries_SrcWhAreaName",
            "Entries_SrcWhLocNumber",
            "Entries_SrcWhLocName",
            "Entries_DestWhAreaNumber",
            "Entries_DestWhAreaName",
            "Entries_DestWhLocNumber",
            "Entries_DestWhLocName",
            "Entries_Qty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_ReturnQty",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_Price",
            "Entries_TotalPrice",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_SrcWhAreaId"); // 库区权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_DestWhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkOutStock entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkOutStock entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkOutStock entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);
            // 判断发运库区是否已填
            if (entry.DestWhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1017);
            // 判断发运库位是否已填
            if (entry.DestWhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1018);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (entry.Qty != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkOutStock entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkOutStock entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkOutStock entity)
    {
        return entity.Entries.Where(u => u.SrcTaskId != null).Select(u => u.SrcTaskId.Value).Distinct().ToList();
    }

    protected override void OnAfterAudit(StkOutStock entity)
    {
        base.OnAfterAudit(entity);

        // 任务库存数量处理
        TaskInvQtyHandle(entity);

        // TODO: 出库单审核，是否需要更新条码状态，如果需要，需要在条码状态中增加一个“待出库/待发运”之类的状态？

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);

        var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
        var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkOutStock>(EntityName).GetAwaiter().GetResult()
            .FirstOrDefault(u => u.Item1.Number == entity.BillType);

        // 是否自动发运
        if (billTypeExt.Item2.AutoShip)
        {
            // 发运
            var shipResult = Ship(new IdsInput { Ids = new List<long> { entity.Id } }).GetAwaiter().GetResult();
            if (!shipResult.First().IsSuccess)
                throw Oops.Bah(shipResult.First().Message);
        }
    }

    protected override void OnBeforeUnAudit(StkOutStock entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.Status != StkOutStockStatus.UnShip)
            throw Oops.Bah(StkErrorCode.StkOutStock1002, entity.BillNo);

        if (entity.Entries.Any(u => u.ReturnQty > 0))
            throw Oops.Bah(StkErrorCode.StkOutStock1008, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkOutStock entity)
    {
        base.OnAfterUnAudit(entity);

        // TODO: 出库单反审核，是否需要更新条码状态，如果需要，需要回滚条码状态？

        // TODO：批号档案处理

        // 回滚库存更新（其中包含了任务库存数量处理的回滚）
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 任务库存数量处理
    /// </summary>
    /// <param name="entity"></param>
    private void TaskInvQtyHandle(StkOutStock entity)
    {
        var taskEntryList = Rep.Context.Queryable<StkTaskEntry>()
            .Where(u => entity.Entries.Where(p => p.SrcTaskEntryId != null).Select(p => p.SrcTaskEntryId).Contains(u.EntryId)).ToList();

        var invChangeList = new List<StkInvChange>();
        foreach (var entry in entity.Entries.Where(u => u.SrcTaskEntryId != null))
        {
            var taskEntry = taskEntryList.First(u => u.EntryId == entry.SrcTaskEntryId);

            // 变更数量，取“单据明细的数量”和“任务剩余可执行数量”的较小值
            var changeQty = Math.Min(entry.Qty, taskEntry.RemainExecQty);

            // 超数量出库的明细行，关联的任务已经处理完剩余可执行数量，就会存在变更数量为0的情况
            if (changeQty == 0) continue;

            // 扣减剩余可执行数量，用于辅助计算剩余可以变更的数量，不提交到数据库
            taskEntry.RemainExecQty -= changeQty;

            // 拣货减锁定
            if (taskEntry.RelLockInvId != null)
                invChangeList.Add(new StkInvChange
                {
                    InvLockLogType = StkInvLockLogType.PickMinus,
                    MaterialId = taskEntry.MaterialId,
                    BatchNo = taskEntry.BatchNo,
                    ProduceDate = taskEntry.ProduceDate,
                    ExpiryDate = taskEntry.ExpiryDate,
                    LockQty = -changeQty,
                    OwnerId = taskEntry.OwnerId,
                    AuxPropValueId = taskEntry.AuxPropValueId,
                    WhLocId = taskEntry.SrcWhLocId!.Value,
                    ContainerId = taskEntry.SrcContainerId,
                    UnitId = taskEntry.UnitId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = entry.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = entry.EntryId,
                    RelSourceBillNo = null,
                    // 不传入库存Id，因为反审核后再重新审核，虽然数量回来了，但是会变成一行新的库存数据行
                    // InventoryId = taskEntry.RelLockInvId,
                });

            // 拣货减预入库
            if (taskEntry.RelPreInInvId != null)
                invChangeList.Add(new StkInvChange
                {
                    InvPreInLogType = StkInvPreInLogType.PickMinus,
                    MaterialId = taskEntry.MaterialId,
                    BatchNo = taskEntry.BatchNo,
                    ProduceDate = taskEntry.ProduceDate,
                    ExpiryDate = taskEntry.ExpiryDate,
                    PreInQty = -changeQty,
                    OwnerId = taskEntry.OwnerId,
                    AuxPropValueId = taskEntry.AuxPropValueId,
                    WhLocId = taskEntry.DestWhLocId!.Value,
                    ContainerId = taskEntry.DestContainerId,
                    UnitId = taskEntry.UnitId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = entry.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = entry.EntryId,
                    RelSourceBillNo = null,
                    // 不传入库存Id，因为反审核后再重新审核，虽然数量回来了，但是会变成一行新的库存数据行
                    // InventoryId = taskEntry.RelPreInInvId,
                });
        }

        InventoryService.UpdateInventory(invChangeList, "审核");
    }

    /// <summary>
    /// 发运
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("ship")]
    public Task<List<ExecResult>> Ship(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = InnerGetAsync(u => u.Id == id).GetAwaiter().GetResult();
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);
            if (entity.IsCancel)
                throw Oops.Bah(BaseErrorCode.BaseBill1006, entity.BillNo);

            if (entity.DocumentStatus != DocumentStatus.Approve || entity.Status != StkOutStockStatus.UnShip)
                throw Oops.Bah(StkErrorCode.StkOutStock1001, entity.BillNo);

            //开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 更新为已发运
            entity.Status = StkOutStockStatus.Finish;
            Rep.Update(entity);

            // 更新条码档案
            UpdateBarcodeInfo(entity);

            // 发运更新库存
            ShipUpdateInventory(entity);

            // 上游单据刷新状态
            SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);

            //提交事务
            uow.Commit();

            return L.Text["单据: {0} 发运成功", entity.BillNo];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 取消发运
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("cancelShip")]
    public Task<List<ExecResult>> CancelShip(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = InnerGetAsync(u => u.Id == id).GetAwaiter().GetResult();
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);
            if (entity.IsCancel)
                throw Oops.Bah(BaseErrorCode.BaseBill1006, entity.BillNo);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.StkOutStock1004, entity.BillNo);
            if (entity.DocumentStatus != DocumentStatus.Approve && entity.Status != StkOutStockStatus.Finish)
                throw Oops.Bah(StkErrorCode.StkOutStock1003, entity.BillNo);

            //开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            // 更新为未发运
            entity.Status = StkOutStockStatus.UnShip;
            Rep.Update(entity);

            // TODO：出库单，如果审核时也更新条码档案状态，那么按单的条码档案日志回滚，会回滚多了，需要区分开
            // 回滚条码档案
            if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
                BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

            // 回滚库存更新（其中包含了任务库存数量处理的回滚）
            InventoryService.RollbackInventory(EntityName, entity.Id, "发运");

            // 上游单据刷新状态
            SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);

            //提交事务
            uow.Commit();

            return L.Text["单据: {0} 取消发运成功", entity.BillNo];
        });

        return Task.FromResult(execResults);
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkOutStock entity)
    {
        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = OpQtyType.Decrease,
                CurStatus = BdBarcodeStatus.Out,
                BatchNo = barcodeEntry.BatchNo,
                ProduceDate = barcodeEntry.Barcode.ProduceDate,
                ExpiryDate = barcodeEntry.Barcode.ExpiryDate,
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = entry.SrcWhAreaId,
                SrcWhLocId = entry.SrcWhLocId,
                DestWhAreaId = entry.DestWhAreaId,
                DestWhLocId = entry.DestWhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkOutStock entity)
    {
        var taskEntryList = Rep.Context.Queryable<StkTaskEntry>()
            .Where(u => entity.Entries.Where(p => p.SrcTaskEntryId != null).Select(p => p.SrcTaskEntryId).Contains(u.EntryId)).ToList();

        var changes = entity.Entries.SelectMany(u =>
        {
            var taskEntry = taskEntryList.FirstOrDefault(p => p.EntryId == u.SrcTaskEntryId);

            // 变更数量，取“单据明细的数量”和“任务剩余可执行数量”的较小值
            var taskHandleQty = Math.Min(u.Qty, taskEntry?.RemainExecQty ?? 0);
            if (taskEntry != null)
                taskEntry.RemainExecQty -= taskHandleQty; // 扣减剩余可执行数量，用于辅助计算剩余可以变更的数量，不提交到数据库

            var innerChanges = new List<StkInvChange>();
            // 如果有拣货库位，扣减拣货库位
            if (u.SrcWhLocId != null)
            {
                innerChanges.Add(new StkInvChange
                {
                    InvLogType = StkInvLogType.PickMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = -u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.SrcWhLocId.Value,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                });
            }

            // 处理发运库位
            innerChanges.Add(new StkInvChange
            {
                InvLogType = StkInvLogType.PickPlus,
                InvLockLogType = StkInvLockLogType.PickPlus,
                MaterialId = u.MaterialId,
                BatchNo = u.BatchNo,
                ProduceDate = u.ProduceDate,
                ExpiryDate = u.ExpiryDate,
                Qty = u.SrcWhLocId != null ? u.Qty : 0, // 没有拣货库位，物料不用从A转移到B，直接从B出，所以数量为0
                UnitId = u.UnitId,
                WhLocId = u.DestWhLocId,
                SourceWhLocId = u.SrcWhLocId,
                ContainerId = u.ContainerId,
                OwnerId = u.OwnerId,
                AuxPropValueId = u.AuxPropValueId,
                RelBillKey = EntityName,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelSeq = u.Seq,
                RelBillId = entity.Id,
                RelBillEntryId = u.EntryId,
                RelSourceBillNo = null,
                LockQty = u.Qty, // 同时增加锁定数量
            });

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }

    /// <summary>
    /// 发运更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void ShipUpdateInventory(StkOutStock entity)
    {
        var changes = entity.Entries.SelectMany(u =>
        {
            var innerChanges = new List<StkInvChange>
            {
                new StkInvChange
                {
                    InvLogType = StkInvLogType.ShipMinus,
                    InvLockLogType = StkInvLockLogType.ShipMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = -u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.DestWhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                    LockQty = -u.Qty, // 同时减锁定数量
                },
            };

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "发运");
    }

    /// <summary>
    /// 下推入库通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("pushInNotice")]
    public StkInNotice PushInNotice(IdInput input)
    {
        var entity = Rep.AsQueryable().IncludeNavCol().First(u => u.Id == input.Id);
        if (entity == null) throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
        var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkOutStock>(EntityName).GetAwaiter().GetResult()
            .FirstOrDefault(u => u.Item1.Number == entity.BillType);

        if (!billTypeExt.Item2.AllowReturn) throw Oops.Bah(StkErrorCode.StkOutStock1006, entity.BillType);
        if (entity.Status != StkOutStockStatus.Finish) throw Oops.Bah(StkErrorCode.StkOutStock1007, entity.BillNo);

        // 下级单据类型
        var nextBillType = billTypeExt.Item1.NextEntries?.FirstOrDefault(u => u.NextEntityName == nameof(StkInNotice))?.NextBillTypeNumber;

        var inNoticeEntity = new StkInNotice
        {
            DocumentStatus = DocumentStatus.Create,
            Memo = null,
            EsBillNo = entity.EsBillNo,
            EsId = entity.EsId,
            Date = DateTime.Now.Date,
            BillType = nextBillType,
            Status = StkInNoticeStatus.UnHandle,
            WarehouseId = entity.WarehouseId,
            Warehouse = entity.Warehouse,
            DepartmentId = entity.DepartmentId,
            Department = entity.Department,
            SupplierId = entity.SupplierId,
            Supplier = entity.Supplier,
            CustomerId = entity.CustomerId,
            Customer = entity.Customer,
            PushFlag = PushFlag.None,
            Entries = new List<StkInNoticeEntry>(),
        };

        foreach (var u in entity.Entries.Where(u => u.Qty - u.ReturnQty > 0))
        {
            var qty = u.Qty - u.ReturnQty;
            inNoticeEntity.Entries.Add(new StkInNoticeEntry
            {
                Seq = inNoticeEntity.Entries.Count + 1,
                EsEntryId = u.EsEntryId,
                EntryStatus = StkInNoticeEntryStatus.UnHandle,
                MaterialId = u.MaterialId,
                Material = u.Material,
                BatchNo = u.BatchNo,
                BatchFile = u.BatchFile,
                ProduceDate = u.ProduceDate,
                ExpiryDate = u.ExpiryDate,
                WhAreaId = u.DestWhAreaId,
                WhArea = u.DestWhArea,
                Qty = qty,
                ReceiveQty = 0,
                RemainReceiveQty = qty,
                InQty = 0,
                RemainInQty = qty,
                ReturnQty = 0,
                UnitId = u.UnitId,
                Unit = u.Unit,
                OwnerId = u.OwnerId,
                Owner = u.Owner,
                AuxPropValueId = u.AuxPropValueId,
                AuxPropValue = u.AuxPropValue,
                GrossWeight = u.GrossWeight,
                PackingVolume = u.PackingVolume,
                PackingQty = u.PackingQty,
                Price = u.Price,
                TotalPrice = u.TotalPrice,
                SrcBillKey = EntityName,
                SrcBillNo = entity.BillNo,
                SrcBillEntrySeq = u.Seq,
                SrcBillId = entity.Id,
                SrcBillEntryId = u.EntryId,
                EntryMemo = null,
                MadeQty = 0
            });
        }

        if (inNoticeEntity.Entries.Count == 0)
            throw Oops.Bah(StkErrorCode.StkOutStock1005, entity.BillNo);

        return inNoticeEntity;
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkOutStock>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        var srcBillKey = EntityName;

        // 查询
        var entities = Rep.Context.Queryable<StkOutStock>().Includes(u => u.Entries).Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            // 入库通知单明细集合
            var stkInNoticeEntryList = Rep.Context.Queryable<StkInNoticeEntry>()
                .LeftJoin<StkInNotice>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .ToList();

            foreach (var entry in entity.Entries)
            {
                // 退货数量
                var returnQty = stkInNoticeEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);

                entry.ReturnQty = returnQty;
            }

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }

        // 刷新出库通知单状态
        var outNoticeIds = entities
            .SelectMany(u => u.Entries
                .Where(p => p.SrcBillKey == nameof(StkOutNotice) && p.SrcBillId != null)
                .Select(p => p.SrcBillId.Value)
            )
            .Distinct()
            .ToList();
        if (outNoticeIds.Count > 0)
        {
            var stkOutNoticeService = App.GetService<StkOutNoticeService>(ServiceProvider);
            stkOutNoticeService.RefreshStatus(outNoticeIds);
        }
    }

    /// <summary>
    /// 推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("push")]
    public Task<List<ExecResult>> Push(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = Rep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.Stk1029, entity.BillNo);

            var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
            var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkOutStock>(EntityName).GetAwaiter().GetResult()
                .FirstOrDefault(u => u.Item1.Number == entity.BillType);

            if (string.IsNullOrEmpty(billTypeExt.Item2.PushSettingNumberToUse))
                throw Oops.Bah(StkErrorCode.Stk1030, entity.BillType);

            // 查询推送设置
            var pushSettingRep = ServiceProvider.GetService<SqlSugarRepository<EsSyncPushSetting>>();
            var pushSetting = pushSettingRep.GetFirst(u => u.Number == billTypeExt.Item2.PushSettingNumberToUse);

            var esType = pushSetting?.EsType;
            var settingNumber = pushSetting?.Number;

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var externalSyncService = ServiceProvider.GetService<ExternalSyncService>();
            var pushResults = externalSyncService.Push(new EsSyncPushInput2 { BillNo = entity.BillNo, SettingNumber = settingNumber, EsType = esType }).GetAwaiter().GetResult();
            var pushResult = pushResults.First();
            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            // 提交事务
            uow.Commit();

            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            return pushResult.Message;
        });

        return Task.FromResult(execResults);
    }
}