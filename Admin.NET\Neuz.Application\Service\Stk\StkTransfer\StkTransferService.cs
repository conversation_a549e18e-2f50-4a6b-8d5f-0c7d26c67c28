using Neuz.Application.ExternalSystem;
using Neuz.Application.ExternalSystem.Dto;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 库存调拨单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkTransfer", Order = 100)]
public class StkTransferService : StkBaseBillService<StkTransfer>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkTransfer);

    /// <summary>
    /// 库存调拨单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkTransferService(IServiceProvider serviceProvider, SqlSugarRepository<StkTransfer> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "WarehouseNumber",
            "WarehouseName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_SrcWhAreaNumber",
            "Entries_SrcWhAreaName",
            "Entries_SrcWhLocNumber",
            "Entries_SrcWhLocName",
            "Entries_SrcContainerNumber",
            "Entries_DestWhAreaNumber",
            "Entries_DestWhAreaName",
            "Entries_DestWhLocNumber",
            "Entries_DestWhLocName",
            "Entries_DestContainerNumber",
            "Entries_Qty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_SrcWhAreaId"); // 库区权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_DestWhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkTransfer entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkTransfer entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkTransfer entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);
            // 判断调出库区是否已填
            if (entry.SrcWhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1008);
            // 判断调出库位是否已填
            if (entry.SrcWhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1009);
            // 判断调入库区是否已填
            if (entry.DestWhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1010);
            // 判断调入库位是否已填
            if (entry.DestWhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1011);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (entry.Qty != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkTransfer entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkTransfer entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkTransfer entity)
    {
        return entity.Entries.Where(u => u.SrcTaskId != null).Select(u => u.SrcTaskId.Value).Distinct().ToList();
    }

    protected override void OnAfterAudit(StkTransfer entity)
    {
        base.OnAfterAudit(entity);

        // 任务库存数量处理
        TaskInvQtyHandle(entity);

        // 更新条码档案
        UpdateBarcodeInfo(entity);

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkTransfer entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkTransfer entity)
    {
        base.OnAfterUnAudit(entity);

        // 回滚条码档案
        if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
            BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

        // TODO：批号档案处理

        // 回滚库存更新（其中包含了任务库存数量处理的回滚）
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 任务库存数量处理
    /// </summary>
    /// <param name="entity"></param>
    private void TaskInvQtyHandle(StkTransfer entity)
    {
        var taskEntryList = Rep.Context.Queryable<StkTaskEntry>()
            .Where(u => entity.Entries.Where(p => p.SrcTaskEntryId != null).Select(p => p.SrcTaskEntryId).Contains(u.EntryId)).ToList();

        var invChangeList = new List<StkInvChange>();
        foreach (var entry in entity.Entries.Where(u => u.SrcTaskEntryId != null))
        {
            var taskEntry = taskEntryList.First(u => u.EntryId == entry.SrcTaskEntryId);

            // 变更数量，取“单据明细的数量”和“任务剩余可执行数量”的较小值
            var changeQty = Math.Min(entry.Qty, taskEntry.RemainExecQty);

            // 超数量调拨的明细行，关联的任务已经处理完剩余可执行数量，就会存在变更数量为0的情况
            if (changeQty == 0) continue;

            // 扣减剩余可执行数量，用于辅助计算剩余可以变更的数量，不提交到数据库
            taskEntry.RemainExecQty -= changeQty;

            // 调拨出减锁定
            if (taskEntry.RelLockInvId != null)
                invChangeList.Add(new StkInvChange
                {
                    InvLockLogType = StkInvLockLogType.TransferOutMinus,
                    MaterialId = taskEntry.MaterialId,
                    BatchNo = taskEntry.BatchNo,
                    ProduceDate = taskEntry.ProduceDate,
                    ExpiryDate = taskEntry.ExpiryDate,
                    LockQty = -changeQty,
                    OwnerId = taskEntry.OwnerId,
                    AuxPropValueId = taskEntry.AuxPropValueId,
                    WhLocId = taskEntry.SrcWhLocId!.Value,
                    ContainerId = taskEntry.SrcContainerId,
                    UnitId = taskEntry.UnitId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = entry.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = entry.EntryId,
                    RelSourceBillNo = null,
                    // 不传入库存Id，因为反审核后再重新审核，虽然数量回来了，但是会变成一行新的库存数据行
                    // InventoryId = taskEntry.RelLockInvId,
                });

            // 调拨入减预入库
            if (taskEntry.RelPreInInvId != null)
                invChangeList.Add(new StkInvChange
                {
                    InvPreInLogType = StkInvPreInLogType.TransferInMinus,
                    MaterialId = taskEntry.MaterialId,
                    BatchNo = taskEntry.BatchNo,
                    ProduceDate = taskEntry.ProduceDate,
                    ExpiryDate = taskEntry.ExpiryDate,
                    PreInQty = -changeQty,
                    OwnerId = taskEntry.OwnerId,
                    AuxPropValueId = taskEntry.AuxPropValueId,
                    WhLocId = taskEntry.DestWhLocId!.Value,
                    ContainerId = taskEntry.DestContainerId,
                    UnitId = taskEntry.UnitId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = entry.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = entry.EntryId,
                    RelSourceBillNo = null,
                    // 不传入库存Id，因为反审核后再重新审核，虽然数量回来了，但是会变成一行新的库存数据行
                    // InventoryId = taskEntry.RelPreInInvId,
                });
        }

        InventoryService.UpdateInventory(invChangeList, "审核");
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkTransfer entity)
    {
        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            // 操作数量类型 = 数量相等 ? 替换 : 减少
            var opQtyType = barcodeEntry.Barcode.Qty == barcodeEntry.Qty ? OpQtyType.Replace : OpQtyType.Decrease;
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = opQtyType,
                CurStatus = barcodeEntry.Barcode.Status,
                BatchNo = barcodeEntry.BatchNo,
                ProduceDate = barcodeEntry.Barcode.ProduceDate,
                ExpiryDate = barcodeEntry.Barcode.ExpiryDate,
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = entry.SrcWhAreaId,
                SrcWhLocId = entry.SrcWhLocId,
                DestWhAreaId = entry.DestWhAreaId,
                DestWhLocId = entry.DestWhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkTransfer entity)
    {
        var taskEntryList = Rep.Context.Queryable<StkTaskEntry>()
            .Where(u => entity.Entries.Where(p => p.SrcTaskEntryId != null).Select(p => p.SrcTaskEntryId).Contains(u.EntryId)).ToList();

        var changes = entity.Entries.SelectMany(u =>
        {
            var taskEntry = taskEntryList.FirstOrDefault(p => p.EntryId == u.SrcTaskEntryId);

            // 变更数量，取“单据明细的数量”和“任务剩余可执行数量”的较小值
            var taskHandleQty = Math.Min(u.Qty, taskEntry?.RemainExecQty ?? 0);
            if (taskEntry != null)
                taskEntry.RemainExecQty -= taskHandleQty; // 扣减剩余可执行数量，用于辅助计算剩余可以变更的数量，不提交到数据库

            var innerChanges = new List<StkInvChange>
            {
                new StkInvChange
                {
                    InvLogType = StkInvLogType.TransferOutMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = -u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.SrcWhLocId,
                    SourceWhLocId = null,
                    ContainerId = u.SrcContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                },
                new StkInvChange
                {
                    InvLogType = StkInvLogType.TransferInPlus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.DestWhLocId,
                    SourceWhLocId = u.SrcWhLocId,
                    ContainerId = u.DestContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = null,
                }
            };

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }

    /// <summary>
    /// 推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("push")]
    public Task<List<ExecResult>> Push(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = Rep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.Stk1029, entity.BillNo);

            var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
            var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkTransfer>(EntityName).GetAwaiter().GetResult()
                .FirstOrDefault(u => u.Item1.Number == entity.BillType);

            if (string.IsNullOrEmpty(billTypeExt.Item2.PushSettingNumberToUse))
                throw Oops.Bah(StkErrorCode.Stk1030, entity.BillType);

            // 查询推送设置
            var pushSettingRep = ServiceProvider.GetService<SqlSugarRepository<EsSyncPushSetting>>();
            var pushSetting = pushSettingRep.GetFirst(u => u.Number == billTypeExt.Item2.PushSettingNumberToUse);

            var esType = pushSetting?.EsType;
            var settingNumber = pushSetting?.Number;

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var externalSyncService = ServiceProvider.GetService<ExternalSyncService>();
            var pushResults = externalSyncService.Push(new EsSyncPushInput2 { BillNo = entity.BillNo, SettingNumber = settingNumber, EsType = esType }).GetAwaiter().GetResult();
            var pushResult = pushResults.First();
            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            // 提交事务
            uow.Commit();

            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            return pushResult.Message;
        });

        return Task.FromResult(execResults);
    }
}