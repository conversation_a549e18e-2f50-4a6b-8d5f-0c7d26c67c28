﻿using Admin.NET.Core;
using Furion;
using Neuz.Application;
using Xunit.Abstractions;
using Xunit;
using Neuz.Core.Entity;
using SqlSugar;
using Neuz.Core.Enum;

namespace Neuz.UnitTest;

public class QueryDefineTest
{
    private readonly ITestOutputHelper _output;

    public QueryDefineTest(ITestOutputHelper tempOutput)
    {
        _output = tempOutput;
    }

    [Fact(DisplayName = "动态查询测试")]
    public void RuleInsertTest()
    {
        var rep = App.GetService<SqlSugarRepository<TestBdWhArea>>();

        IQueryDefine queryDefine = new DefaultQueryDefine();
        var a = queryDefine.GetEntityTypeTableInfos(rep.Context, typeof(TestBdWhArea));
        var b = queryDefine.GetEntityTypeColumnInfos(rep.Context, typeof(TestBdWhArea));
        var c = rep.Context.Queryable<object>();
        var d = c.ToSqlString();

        var e = rep.Context.Queryable<object>().AS("bar_barcode", "b");
        var f = e.ToPageList(1, 100);
    }
}

/// <summary>
/// 测试库位
/// </summary>
[SugarTable(null, "测试库位")]
public class TestBdWhArea : EsBdEntityBase
{
    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 测试库位明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TestBdWhLocEntry.Id))]
    public List<TestBdWhLocEntry> TestBdWhLocEntries { get; set; }
}


/// <summary>
/// 测试库位明细
/// </summary>
[SugarTable(null, "测试库位明细")]
public class TestBdWhLocEntry : EntryEntityBase
{
    /// <summary>
    /// 类型
    /// </summary>
    [SugarColumn(ColumnDescription = "类型")]
    public ElementType ElementType { get; set; }

    /// <summary>
    /// 子库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "子库区Id")]
    public long ChildWhAreaId { get; set; }

    /// <summary>
    /// 子库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ChildWhAreaId))]
    public BdWhArea ChildWhArea { get; set; }
}