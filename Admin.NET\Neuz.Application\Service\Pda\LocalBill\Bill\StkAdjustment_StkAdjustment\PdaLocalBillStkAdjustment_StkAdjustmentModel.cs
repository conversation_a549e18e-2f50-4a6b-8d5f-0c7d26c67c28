﻿using Furion.Localization;
using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Dto;
using Neuz.Application.Pda.LocalBill.Link;
using Neuz.Application.Pda.LocalBill.LocalBillDto;
using SqlSugar;

namespace Neuz.Application.Pda.LocalBill.Bill._StkAdjustment;

/// <summary>
/// 无源调整单
/// </summary>
public class PdaLocalBillStkAdjustment_StkAdjustmentModel : PdaLocalBillModel<StkAdjustment, StkAdjustment, StkAdjustmentEntry, StkAdjustmentEntry>
{
    public PdaLocalBillStkAdjustment_StkAdjustmentModel(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override string Key { get; } = "StkAdjustment_StkAdjustment";

    /// <inheritdoc/>
    public override void BillDataInitialization(IPdaData pdaData)
    {
        base.BillDataInitialization(pdaData);
        var billData = GetPdaData(pdaData.TranId);
        SetScanHeadValue(billData.TranId, "date", DateTime.Now.Date);
        RefreshShow(pdaData.TranId);
    }

    /// <inheritdoc/>
    public class PdaSchema : IPdaLocalBillSchema
    {
        public List<PdaLocalBillColumn> DestHead { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn()
            {
                Fieldname = "date",
                Caption = L.Text["日期"],
                Type = "date",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.Date"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "destBillTypeName",
                Caption = L.Text["单据类型"],
                Type = "lookup",
                Readonly = true,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdAdjustmentBillTypeModel",
                    LookupDataKey = "ScanHead.DestBillType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DestBillType", "Number"),
                        new PdaLookupMapping("DestBillTypeName", "Name"),
                    },
                    Properties = { ["EntityName"] = "StkAdjustment", ["Direction"] = "Decrease" }
                },
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "departmentName",
                Caption = L.Text["部门"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdDepartmentModel",
                    LookupDataKey = "ScanHead.Department",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("DepartmentId", "Id"),
                        new PdaLookupMapping("DepartmentNumber", "Number"),
                        new PdaLookupMapping("DepartmentName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "outTypeName",
                Caption = L.Text["出库类型"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictOutTypeModel",
                    LookupDataKey = "ScanHead.OutType",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("OutTypeId", "Id"),
                        new PdaLookupMapping("OutTypeNumber", "Number"),
                        new PdaLookupMapping("OutTypeName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "expenseName",
                Caption = L.Text["费用项目"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictExpenseModel",
                    LookupDataKey = "ScanHead.Expense",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("ExpenseId", "Id"),
                        new PdaLookupMapping("ExpenseNumber", "Number"),
                        new PdaLookupMapping("ExpenseName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "contractProjectName",
                Caption = L.Text["合同项目名称"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictContractProjectModel",
                    LookupDataKey = "ScanHead.ContractProject",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("ContractProjectId", "Id"),
                        new PdaLookupMapping("ContractProjectNumber", "Number"),
                        new PdaLookupMapping("ContractProjectName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "projectName",
                Caption = L.Text["项目名称"],
                Type = "string",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanHead.ProjectName"
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "customerName",
                Caption = L.Text["客户"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdCustomerModel",
                    LookupDataKey = "ScanHead.Customer",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("CustomerId", "Id"),
                        new PdaLookupMapping("CustomerNumber", "Number"),
                        new PdaLookupMapping("CustomerName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "employeeName",
                Caption = L.Text["领料人"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "BdEmployeeModel",
                    LookupDataKey = "ScanHead.Employee",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("EmployeeId", "Id"),
                        new PdaLookupMapping("EmployeeNumber", "Number"),
                        new PdaLookupMapping("EmployeeName", "Name"),
                    }
                }
            },
            new PdaLocalBillColumn()
            {
                Fieldname = "assetCategoryName",
                Caption = L.Text["资产出库类别"],
                Type = "lookup",
                Readonly = false,
                IsShow = true,
                Lookup = new PdaLocalBillLookup
                {
                    LookupKey = "DictAssetCategoryModel",
                    LookupDataKey = "ScanHead.AssetCategory",
                    LookupMappings = new List<PdaLookupMapping>()
                    {
                        new PdaLookupMapping("AssetCategoryId", "Id"),
                        new PdaLookupMapping("AssetCategoryNumber", "Number"),
                        new PdaLookupMapping("AssetCategoryName", "Name"),
                    }
                }
            },
        };

        /// <inheritdoc/>
        public List<PdaLocalBillColumn> Barcode { get; set; } = new List<PdaLocalBillColumn>()
        {
            new PdaLocalBillColumn
            {
                Fieldname = "id",
                Caption = L.Text["条形码内码"],
                Type = "string",
                Readonly = true,
                IsShow = false,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "barcode",
                Caption = L.Text["条形码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Number",
                Caption = L.Text["物料代码"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "material.Name",
                Caption = L.Text["物料名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "batchNo",
                Caption = L.Text["批号"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "produceDate",
                Caption = L.Text["生产日期"],
                Type = "date",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whAreaName",
                Caption = L.Text["库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "whLocName",
                Caption = L.Text["库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhAreaName",
                Caption = L.Text["调入库区名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "destWhLocName",
                Caption = L.Text["调入库位名称"],
                Type = "string",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
            new PdaLocalBillColumn
            {
                Fieldname = "scanQty",
                Caption = L.Text["数量"],
                Type = "number",
                Readonly = false,
                IsShow = true,
                Lookup = null,
                FieldDataKey = "ScanQty"
            },
            new PdaLocalBillColumn
            {
                Fieldname = "qty",
                Caption = L.Text["条码数量"],
                Type = "number",
                Readonly = true,
                IsShow = true,
                Lookup = null
            },
        };

        public PdaBillLink BillLink { get; set; } = new PdaBillLink
        {
            SourceKey = "StkAdjustment",
            SourceTitle = "库存调整单",
            DestKey = "StkAdjustment",
            DestTitle = L.Text["库存调整单"],
            Rob = 1
        };

        public List<PdaLocalBillColumn> BillDetail { get; set; }
    }

    /// <inheritdoc/>
    public override PdaLocalBillModelConfig Config { get; set; } = new PdaLocalBillModelConfig()
    {
        BillSchema = new PdaSchema(),
        BillParams = new PdaLocalBillModelParams()
        {
            IsOverSourceItem = true,
            IsOverSourceQty = true
        }
    };

    /// <inheritdoc/>
    public override async Task<SqlSugarPagedList<PdaLookupOutput>> QueryBillList(PdaLocalBillPageInput input)
    {
        var linkParam = GetLinkParam(input.TranId);
        var bills = await SourceRep.Context
            .AddWarehouseFilter<StkAdjustment>(ServiceProvider, u => u.WarehouseId)
            .AddWhAreaFilter<StkAdjustmentEntry>(ServiceProvider, u => u.WhAreaId)
            .AddOwnerFilter<StkAdjustmentEntry>(ServiceProvider, u => u.OwnerId)
            .Queryable(SourceRep.AsQueryable()
                .WhereIF(linkParam.SourceBillTypes.Count > 0, t1 => linkParam.SourceBillTypes.Contains(t1.BillType))
                .Where(t1 => t1.BillNo.Contains(input.Keyword) && t1.DocumentStatus == DocumentStatus.Approve && t1.Id == SqlFunc.Subqueryable<StkAdjustmentEntry>()
                    .Where(t2 => t2.Id == t1.Id)
                    .GroupBy(t2 => t2.Id)
                    .Select(t2 => t2.Id))
                // 这里是为了数据隔离权限
                .Where(t1 => SqlFunc.Subqueryable<StkAdjustmentEntry>().EnableTableFilter()
                    .Where(e => e.Id == t1.Id)
                    .Any())
            )
            .OrderBy(t1 => t1.CreateTime, OrderByType.Desc)
            .ToPagedListAsync(input.Page, input.PageSize);
        var result = bills.Adapt<SqlSugarPagedList<PdaLookupOutput>>();
        result.Items = bills.Items.Select(r => new PdaLookupOutput() { Key = r.Id + "", Title = $"{r.BillNo}[{r.EsBillNo}]", SubTitle = r.Date.ToString("yyyy-MM-dd") }).ToArray();
        return result;
    }

    /// <summary>
    /// 自定义的字段, 需要处理带到源单下
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="hands"></param>
    /// <param name="sourceDetails"></param>
    /// <param name="barcode"></param>
    public override void AfterSetSourceInfo(long tranId, List<PdaLocalBillSourceHead> hands, List<PdaLocalBillSourceDetail> sourceDetails, BdBarcode barcode)
    {
        base.AfterSetSourceInfo(tranId, hands, sourceDetails, barcode);
        if (hands is not { Count: > 0 }) return;
        var hand = hands[0];
        // DictOutType
        GetIdNumberName(tranId, "DictOutType", hand["OutTypeNumber"] + "", "OutType");

        // DictExpense
        GetIdNumberName(tranId, "DictExpense", hand["ExpenseNumber"] + "", "Expense");

        // DictContractProject
        GetIdNumberName(tranId, "DictContractProject", hand["ContractProjectNumber"] + "", "ContractProject");

        // 单据类型
        var billTypeNumber = GetBillType(tranId).Result;
        var billType = Rep.Change<StkBillType>().GetFirst(r => r.Number == billTypeNumber);
        SetScanHeadValue(tranId, "DestBillType", billType.Number);
        SetScanHeadValue(tranId, "DestBillTypeName", billType.Name);
    }

    private void GetIdNumberName(long tranId, string code, string value, string setFieldname)
    {
        if (string.IsNullOrEmpty(value)) return;
        var outDic = Rep.Change<SysDictType>().AsQueryable().LeftJoin<SysDictDataTenant>((t1, t2) => t1.Id == t2.DictTypeId)
            .Where((t1, t2) => t1.Code == code && t1.IsDelete == false && t2.Value == value)
            .Select((t1, t2) => new { t2.Id, t2.Code, t2.Value })
            .First();
        if (outDic == null) return;
        var idNumberName = new IdNumberName
        {
            Id = outDic.Id,
            Number = outDic.Code,
            Name = outDic.Value
        };
        var pdaData = GetPdaData(tranId);
        pdaData.ScanHead[setFieldname + "Id"] = idNumberName.Id;
        pdaData.ScanHead[setFieldname + "Number"] = idNumberName.Number;
        pdaData.ScanHead[setFieldname + "Name"] = idNumberName.Name;
    }

    public override void RefreshShow(long tranId)
    {
        base.RefreshShow(tranId);
        var pdaData = GetPdaData(tranId);
        var pdaShow = GetPdaShow(tranId);
        pdaShow.SourceBill = pdaData.SourceHeads.Select(r => $"{r.SrcBillNo} - {r["EsSrcBillNo"]}").ToList();
    }

    private class IdNumberName
    {
        public long Id { get; set; }
        public string Number { get; set; }
        public string Name { get; set; }
    }

    protected override StkAdjustment SetSubmitObj(long tranId, ILocalBillLinkParam linkParam, Dictionary<string, PdaLocalBillSummaryInfo> summaryDetails)
    {
        var submitObj = base.SetSubmitObj(tranId, linkParam, summaryDetails);

        var pdaData = GetPdaData(tranId);

        // 如果是QTCKD07_SYS类型, 需要校验
        if (pdaData.SourceHeads.Count > 0 && pdaData.SourceHeads[0]["destbilltype"] + "" == "QTCKD07_SYS")
        {
            if (string.IsNullOrEmpty(pdaData.ScanHead["assetCategoryName"] + "")) throw Oops.Bah("请选择资产出库类别");
        }

        return submitObj;
    }
}