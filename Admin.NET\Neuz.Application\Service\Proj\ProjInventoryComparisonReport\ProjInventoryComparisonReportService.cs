﻿using Furion.Localization;
using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Proj.ProjInventoryComparisonReport.Dto;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SqlSugar;

namespace Neuz.Application.Proj.ProjInventoryComparisonReport;

/// <summary>
/// 库存比较报表服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "ProjInventoryComparisonReport", Order = 100)]
public class ProjInventoryComparisonReportService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 当前用户Id
    /// </summary>
    protected long CurUserId => long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");

    /// <summary>
    /// 仓储服务
    /// </summary>
    public SqlSugarRepository<StkInventory> Rep { get; }

    /// <summary>
    /// 服务提供对象
    /// </summary>
    protected IServiceProvider ServiceProvider { get; }

    private readonly ISqlSugarClient _db;

    /// <summary>
    /// K3Cloud 接口
    /// </summary>
    protected K3CloudInterface K3CloudInterface { get; }

    /// <summary>
    /// 库存调整单服务
    /// </summary>
    protected StkAdjustmentService StkAdjustmentService { get; set; }

    /// <summary>
    /// 字典服务
    /// </summary>
    private readonly SqlSugarRepository<SysDictType> _sysDictReq;

    /// <summary>
    /// 用户仓储
    /// </summary>
    protected SqlSugarRepository<SysUser> UserRep { get; }

    /// <summary>
    /// 库位仓储
    /// </summary>
    protected SqlSugarRepository<BdWhLoc> bdWhLocRep { get; }

    /// <summary>
    /// 租户仓储
    /// </summary>
    protected SqlSugarRepository<SysTenant> TenantRep { get; }

    /// <summary>
    /// 上下文访问器
    /// </summary>
    protected IHttpContextAccessor HttpContextAccessor { get; }
    
    /// <summary>
    /// 内存缓存服务
    /// </summary>
    protected IMemoryCache MemoryCache { get; set; }

    public ProjInventoryComparisonReportService(ISqlSugarClient db, IServiceProvider serviceProvider,
        SqlSugarRepository<StkInventory> rep, IHttpContextAccessor httpContextAccessor)
    {
        Rep = rep;
        ServiceProvider = serviceProvider;
        _db = db;
        K3CloudInterface = serviceProvider.GetService<K3CloudInterface>();
        StkAdjustmentService = serviceProvider.GetService<StkAdjustmentService>();
        _sysDictReq = serviceProvider.GetService<SqlSugarRepository<SysDictType>>();
        bdWhLocRep = serviceProvider.GetService<SqlSugarRepository<BdWhLoc>>();
        UserRep = serviceProvider.GetService<SqlSugarRepository<SysUser>>();
        TenantRep = serviceProvider.GetService<SqlSugarRepository<SysTenant>>();
        HttpContextAccessor = httpContextAccessor;
        MemoryCache = serviceProvider.GetService<IMemoryCache>();
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input">输入参数</param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<ProjInventoryComparisonOutput>> PageAsync(
        ProjInventoryComparisonInput input)
    {
        // 组织列表，只显示列表有得组织，当列表为空时，默认显示所有组织
        List<string> orgList = new List<string>();

        var k3List = new List<QueryFilter>();
        k3List.Add(new("FBaseQty", QueryType.GreaterThan, 0));
        if (!string.IsNullOrEmpty(input.MaterialNumber))
        {
            k3List.Add(new("FMaterialId.FNumber", QueryType.Contains, input.MaterialNumber));
        }

        if (!string.IsNullOrEmpty(input.MaterialName))
        {
            k3List.Add(new("FMaterialId.FName", QueryType.Contains, input.MaterialName));
        }

        if (!string.IsNullOrEmpty(input.WarehouseNumber))
        {
            k3List.Add(new("FStockOrgId.FNumber", QueryType.Contains, input.WarehouseNumber));
        }

        if (!string.IsNullOrEmpty(input.WarehouseName))
        {
            k3List.Add(new("FStockOrgId.FName", QueryType.Contains, input.WarehouseName));
        }

        if (!string.IsNullOrEmpty(input.WhAreaNumber))
        {
            k3List.Add(new("FStockId.FNumber", QueryType.Contains, input.WhAreaNumber));
        }

        if (!string.IsNullOrEmpty(input.WhAreaName))
        {
            k3List.Add(new("FStockId.FName", QueryType.Contains, input.WhAreaName));
        }

        var inventoryOrganization = await _sysDictReq.Context
            .Queryable(
                _sysDictReq.AsQueryable()
                    .LeftJoin<SysDictData>((t, d) => t.Id == d.DictTypeId)
                    .Where((t, d) => t.Code == "InventoryOrganization")
                    .Where((t, d) => d.Status == StatusEnum.Enable)
                    .Where((t, d) => t.Status == StatusEnum.Enable)
                    .Select((t, d) => d)
            ).ToListAsync();

        if (inventoryOrganization != null)
        {
            foreach (var item in inventoryOrganization)
            {
                orgList.Add(item.Code);
            }

            if (orgList.Count > 0)
            {
                k3List.Add(new("FStockOrgId.FNumber", QueryType.StdIn, orgList /*组织列表*/));
            }
        }

        var tableName = "Temp" + CurUserId;

        if (input.Page == 1)
        {
            await GetK3Inventory(k3List, tableName);
        }

        if (input.AccordingTo == "2")
        {
            // 以金蝶库存为主
            // 金蝶库存
            var query = await _db.Queryable<ProjInventoryComparisonTemp>().AS(tableName, "a")
                .LeftJoin<BdMaterial>((i, m) => i.MaterialIdNumber == m.EsNumber)
                .LeftJoin<BdWarehouse>((i, m, w) => i.StockOrgIdNumber == w.EsNumber)
                .LeftJoin<BdWhArea>((i, m, w, a) => i.StockId == a.EsId)
                .LeftJoin<BdWhLoc>((i, m, w, a, l) => (i.StockLocId == "0" ? $"{i.StockIdNumber}-{i.StockOrgIdNumber}" : i.StockLocIdNumber) == l.Number && l.WhAreaId == a.Id)
                .LeftJoin<BdOwner>((i, m, w, a, l, o) => i.OwnerIdNumber == o.EsNumber)
                .LeftJoin<BdUnit>((i, m, w, a, l, o, u) => i.BaseUnitIdNumber == u.EsNumber)
                .LeftJoin<BdAuxPropValue>((i, m, w, a, l, o, u, v) => (i.AuxPropIdNumber ?? "") == (v.Number ?? ""))
                .LeftJoin<StkInventory>((i, m, w, a, l, o, u, v, t) => m.Id == t.MaterialId && SqlFunc.IsNull(i.LotText, "") == SqlFunc.IsNull(t.BatchNo, "") &&
                                                                       w.Id == t.WarehouseId && a.Id == t.WhAreaId &&
                                                                       l.Id == t.WhLocId && SqlFunc.IsNull(v.Id, 0) == SqlFunc.IsNull(t.AuxPropValueId, 0))
                .Where((i, m, w, a, l, o, u, v, t) => SqlFunc.IsNull(t.Qty, 0) - i.BaseQty != 0)
                .Select((i, m, w, a, l, o, u, v, t) => new ProjInventoryComparisonOutput
                {
                    Id = t.Id,
                    MaterialNumber = m.Number,
                    MaterialName = m.Name,
                    MaterialId = m.Id,
                    BatchNo = i.LotText,
                    ProduceDate = t.ProduceDate,
                    ExpiryDate = t.ExpiryDate,
                    OwnerNumber = o.Number,
                    OwnerName = o.Name,
                    OwnerId = o.Id,
                    WarehouseNumber = w.Number,
                    WarehouseId = w.Id,
                    WarehouseName = w.Name,
                    WhAreaNumber = a.Number,
                    WhAreaName = a.Name,
                    WhAreaId = a.Id,
                    WhLocNumber = l.Number,
                    WhLocName = l.Name,
                    WhLocEsNumber = l.EsNumber,
                    WhLocId = l.Id,
                    AuxPropValueId = v.Id,
                    AuxPropValueNumber = v.Number,
                    AuxPropValueName = v.Name,
                    Qty = Convert.ToDecimal(Convert.ToString(t.Qty) ?? "0"),
                    LockQty = t.LockQty,
                    AvailableQty = t.AvailableQty,
                    PreInQty = t.PreInQty,
                    UnitId = u.Id,
                    UnitNumber = u.Number,
                    UnitName = u.Name,
                    CreateTime = t.CreateTime,
                    UpdateTime = t.UpdateTime,
                    ContainerNumber = "",
                    Specification = m.Specification,
                    K3InvQty = Convert.ToDecimal(i.BaseQty),
                    Difference = Convert.ToDecimal(Convert.ToString(t.Qty) ?? "0") - Convert.ToDecimal(i.BaseQty) //差异数
                }).ToPagedListAsync(input.Page, input.PageSize);

            return query;
        }
        else
        {
            // 以wms库存为主的写法
            // 金蝶库存
            var tableNameType = _db.Queryable<ProjInventoryComparisonTemp>().AS(tableName, "a")
                .GroupBy(i => new
                {
                    i.MaterialIdNumber,
                    i.LotText,
                    i.StockOrgIdNumber,
                    i.StockOrgIdName,
                    i.StockId,
                    i.StockLocId,
                    i.StockLocIdNumber,
                    i.StockIdNumber,
                    i.StockIdName,
                    i.MaterialIdName,
                    i.AuxPropIdNumber
                })
                .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber),
                    i => i.MaterialIdNumber.Contains(input.MaterialNumber))
                .WhereIF(!string.IsNullOrEmpty(input.MaterialName),
                    i => i.MaterialIdName.Contains(input.MaterialName))
                .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber),
                    i => i.StockOrgIdNumber.Contains(input.WarehouseNumber))
                .WhereIF(!string.IsNullOrEmpty(input.WarehouseName),
                    i => i.StockOrgIdName.Contains(input.WarehouseName))
                .WhereIF(!string.IsNullOrEmpty(input.WhAreaNumber),
                    i => i.StockIdNumber.Contains(input.WhAreaNumber))
                .WhereIF(!string.IsNullOrEmpty(input.WhAreaName),
                    i => i.StockIdName.Contains(input.WhAreaName))
                .Select(
                    "a.MaterialIdNumber,a.LotText,a.StockOrgIdNumber,a.StockOrgIdName,a.StockId,a.StockLocId,a.StockLocIdNumber,a.StockIdNumber,a.StockIdName,a.MaterialIdName,a.AuxPropIdNumber,sum(a.BaseQty) as BaseQty");
            // WMS即时库存
            var query = await Rep.Context
                .AddWarehouseFilter<StkInventory>(ServiceProvider, u => u.WarehouseId) // 仓库权限
                .AddWhAreaFilter<StkInventory>(ServiceProvider, u => u.WhAreaId) // 库区权限
                .AddOwnerFilter<StkInventory>(ServiceProvider, u => u.OwnerId) // 货主权限
                .Queryable(
                    Rep.AsQueryable()
                        .LeftJoin<BdMaterial>((i, m) => i.MaterialId == m.Id)
                        .LeftJoin<BdWarehouse>((i, m, w) => i.WarehouseId == w.Id)
                        .LeftJoin<BdWhArea>((i, m, w, a) => i.WhAreaId == a.Id)
                        .LeftJoin<BdWhLoc>((i, m, w, a, l) => i.WhLocId == l.Id)
                        .LeftJoin<BdOwner>((i, m, w, a, l, o) => i.OwnerId == o.Id)
                        .LeftJoin<BdUnit>((i, m, w, a, l, o, u) => i.UnitId == u.Id)
                        .LeftJoin<BdContainer>((i, m, w, a, l, o, u, c) => i.ContainerId == c.Id)
                        .LeftJoin<BdAuxPropValue>((i, m, w, a, l, o, u, c, v) => i.AuxPropValueId == v.Id)
                        .LeftJoin(tableNameType,
                            (i, m, w, a, l, o, u, c, v, t) => m.EsNumber == t.MaterialIdNumber && i.BatchNo == t.LotText &&
                                                              w.EsNumber == t.StockOrgIdNumber && a.EsId == t.StockId &&
                                                              (a.Number == l.Number ? "*" : l.Number) == t.StockLocIdNumber &&
                                                              (v.Number ?? "") == (t.AuxPropIdNumber ?? ""))
                        // l.EsId == (t.StockLocId == null ? "0" : t.StockLocId+""))
                        .WhereIF(!string.IsNullOrEmpty(input.MaterialNumber),
                            (i, m, w, a, l, o, u, c, v, t) => m.EsNumber.Contains(input.MaterialNumber))
                        .WhereIF(!string.IsNullOrEmpty(input.MaterialName),
                            (i, m, w, a, l, o, u, c, v, t) => m.Name.Contains(input.MaterialName))
                        .WhereIF(!string.IsNullOrEmpty(input.WarehouseNumber),
                            (i, m, w, a, l, o, u, c, v, t) => w.Number.Contains(input.WarehouseNumber))
                        .WhereIF(!string.IsNullOrEmpty(input.WarehouseName),
                            (i, m, w, a, l, o, u, c, v, t) => w.Name.Contains(input.WarehouseName))
                        .WhereIF(!string.IsNullOrEmpty(input.WhAreaNumber),
                            (i, m, w, a, l, o, u, c, v, t) => a.Number.Contains(input.WhAreaNumber))
                        .WhereIF(!string.IsNullOrEmpty(input.WhAreaName),
                            (i, m, w, a, l, o, u, c, v, t) => a.Name.Contains(input.WhAreaName))
                        // .WhereIF(orgList.Count > 0, (i, m, w, a, l, o, u, c, t) => orgList.Contains(w.Number))
                        // .Where((i, m, w, a, l, o, u, c, t) => i.Qty > 0)
                        .Where((i, m, w, a, l, o, u, c, v, t) =>
                            Convert.ToDecimal(i.Qty) - SqlFunc.IsNull(t.BaseQty, 0) != 0)
                        // .Where((i, m, w, a, l, o, u, c) => a.Name=="1D1")
                        .Select((i, m, w, a, l, o, u, c, v, t) => new ProjInventoryComparisonOutput
                        {
                            Id = i.Id,
                            MaterialNumber = m.Number,
                            MaterialName = m.Name,
                            MaterialId = m.Id,
                            BatchNo = i.BatchNo,
                            ProduceDate = i.ProduceDate,
                            ExpiryDate = i.ExpiryDate,
                            OwnerNumber = o.Number,
                            OwnerName = o.Name,
                            OwnerId = o.Id,
                            WarehouseNumber = w.Number,
                            WarehouseId = w.Id,
                            WarehouseName = w.Name,
                            WhAreaNumber = a.Number,
                            WhAreaName = a.Name,
                            WhAreaId = a.Id,
                            WhLocNumber = l.Number,
                            WhLocName = l.Name,
                            WhLocEsNumber = l.EsNumber,
                            WhLocId = l.Id,
                            AuxPropValueId = v.Id,
                            AuxPropValueNumber = v.Number,
                            AuxPropValueName = v.Name,
                            Qty = Convert.ToDecimal(i.Qty),
                            LockQty = i.LockQty,
                            AvailableQty = i.AvailableQty,
                            PreInQty = i.PreInQty,
                            UnitId = u.Id,
                            UnitNumber = u.Number,
                            UnitName = u.Name,
                            CreateTime = i.CreateTime,
                            UpdateTime = i.UpdateTime,
                            ContainerNumber = c.Number,
                            Specification = m.Specification,
                            K3InvQty = Convert.ToDecimal(Convert.ToString(t.BaseQty) ?? "0"),
                            Difference = Convert.ToDecimal(i.Qty) - Convert.ToDecimal(Convert.ToString(t.BaseQty) ?? "0") //差异数
                        })
                )
                .ToPagedListAsync(input.Page, input.PageSize);
            return query;
        }
    }

    /// <summary>
    /// 创建库存盘点单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("createStkAdjustment")]
    public async Task<string> CreateStkAdjustment(CreateStkAdjustmentInput input)
    {
        //根据 StoreID分组
        var warehouseList = input.List.GroupBy(x => x.WarehouseId)
            .Select(group => new
            {
                WarehouseId = group.Key,
                List = group.ToList()
            }).ToList();


        foreach (var warehouse in warehouseList)
        {
            var stkAdjustmentListR = input.List.FindAll(u => u.WarehouseId == warehouse.WarehouseId && u.Qty < 0); //入库
            var stkAdjustmentListC = input.List.FindAll(u => u.WarehouseId == warehouse.WarehouseId && u.Qty > 0); //出库

            if (stkAdjustmentListR.Count > 0)
            {
                var stkAdjustmentList = new List<StkAdjustmentEntry>();
                foreach (var item in stkAdjustmentListR)
                {
                    if (item.WhLocId == 0 && item.WhLocNo != "")
                    {
                        // 查询库位编码
                        var whLoc = await _db.Queryable<BdWhLoc>().FirstAsync(u => u.Number == item.WhLocNo);
                        if (whLoc != null)
                        {
                            item.WhLocId = whLoc.Id;
                        }
                        else
                        {
                            throw new Exception(L.Text["库位编码{0}不存在", item.WhLocNo]);
                        }
                    }

                    var stkAdjustmentEntry = new StkAdjustmentEntry()
                    {
                        MaterialId = item.MaterialId,
                        BatchNo = item.BatchNo,
                        WhAreaId = item.WhAreaId,
                        Qty = -item.Qty,
                        UnitId = item.UnitId,
                        OwnerId = item.OwnerId,
                        WhLocId = item.WhLocId,
                        AuxPropValueId = item.AuxPropValueId,
                    };
                    stkAdjustmentList.Add(stkAdjustmentEntry);
                }

                var localObject = new StkAdjustment
                {
                    BillType = "KCTZRK",
                    WarehouseId = stkAdjustmentListR[0].WarehouseId,
                    Entries = stkAdjustmentList,
                    Date = DateTime.Now.Date
                };

                await StkAdjustmentService.AddAsync(localObject);

                var result =
                    await StkAdjustmentService.AuditAsync(new IdsInput { Ids = new List<long> { localObject.Id } });
                if (!result.First().IsSuccess)
                    throw Oops.Bah(result.First().Message);
            }

            if (stkAdjustmentListC.Count > 0)
            {
                // 生成盘亏单
                var stkAdjustmentList = new List<StkAdjustmentEntry>();
                foreach (var item in stkAdjustmentListC)
                {
                    // 查询库存
                    // 库存库存不足扣则说明是锁定库存，跳过处理
                    var inv = await Rep.Context.Queryable<StkInventory>()
                        .Where(u => u.MaterialId == item.MaterialId && u.BatchNo == item.BatchNo &&
                                    u.WhAreaId == item.WhAreaId && u.WhLocId == item.WhLocId &&
                                    u.WarehouseId == item.WarehouseId && u.OwnerId == item.OwnerId && u.AuxPropValueId == item.AuxPropValueId)
                        .FirstAsync();

                    if (inv.AvailableQty < item.Qty)
                        continue;

                    if (item.WhLocId == 0 && item.WhLocNo != "")
                    {
                        // 查询库位编码
                        var whLoc = await _db.Queryable<BdWhLoc>().FirstAsync(u => u.Number == item.WhLocNo);
                        if (whLoc != null)
                        {
                            item.WhLocId = whLoc.Id;
                        }
                        else
                        {
                            throw new Exception(L.Text[$"库位编码{item.WhLocNo}不存在"]);
                        }
                    }

                    var stkAdjustmentEntry = new StkAdjustmentEntry()
                    {
                        MaterialId = item.MaterialId,
                        BatchNo = item.BatchNo,
                        WhAreaId = item.WhAreaId,
                        Qty = -item.Qty,
                        UnitId = item.UnitId,
                        OwnerId = item.OwnerId,
                        WhLocId = item.WhLocId,
                        AuxPropValueId = item.AuxPropValueId,
                    };
                    stkAdjustmentList.Add(stkAdjustmentEntry);
                }

                if (stkAdjustmentList.Count > 0)
                {
                    var localObject = new StkAdjustment
                    {
                        BillType = "KCTZCK",
                        WarehouseId = stkAdjustmentListC[0].WarehouseId,
                        Entries = stkAdjustmentList,
                        Date = DateTime.Now.Date
                    };

                    await StkAdjustmentService.AddAsync(localObject);

                    var result =
                        await StkAdjustmentService.AuditAsync(new IdsInput { Ids = new List<long> { localObject.Id } });
                    if (!result.First().IsSuccess)
                        throw Oops.Bah(result.First().Message);
                }
            }
        }

        return L.Text["生成成功"];
    }

    [NonAction]
    public async Task<int> GetK3Inventory(List<QueryFilter> inputList, string tableName)
    {
        var list = _db.DbMaintenance.GetTableInfoList();
        // 存在就删除
        var tableNameInfo = list.FindAll(u => u.Name == tableName);
        try
        {
            _db.DbMaintenance.DropTable(tableName);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }

        var type = _db.DynamicBuilder().CreateClass(tableName, new SugarTable(), typeof(ProjInventoryComparisonTemp))
            .CreateProperty("Id", typeof(string))
            .CreateProperty("MaterialIdNumber", typeof(string))
            .CreateProperty("LotText", typeof(string))
            .CreateProperty("StockOrgIdNumber", typeof(string))
            .CreateProperty("StockOrgIdName", typeof(string))
            .CreateProperty("BaseQty", typeof(decimal))
            .CreateProperty("StockId", typeof(string))
            .CreateProperty("StockLocId", typeof(string))
            .CreateProperty("StockLocIdNumber", typeof(string))
            .CreateProperty("StockIdNumber", typeof(string))
            .CreateProperty("MaterialIdName", typeof(string))
            .CreateProperty("StockIdName", typeof(string))
            .CreateProperty("BaseUnitIdNumber", typeof(string))
            .CreateProperty("OwnerIdNumber", typeof(string))
            .CreateProperty("AuxPropId", typeof(string))
            .CreateProperty("AuxPropIdNumber", typeof(string))
            .CreateProperty("AuxPropIdName", typeof(string))
            .BuilderType();
        _db.CodeFirst.InitTables(type); //创建表


        // 查询K3即时库存
        var client = K3CloudInterface.GetK3CloudClient();
        var stkInventoryList = await client.QueryBillData(new QueryBillParam
        {
            FormId = "STK_Inventory",
            FieldKeys = new List<string>
            {
                "FID",
                "FMaterialId.FNumber AS FMaterialIdNumber",
                "FMaterialId.FName AS FMaterialIdName",
                "FLot.FName AS FLOT_TEXT",
                "FStockOrgId.FNumber AS FStockOrgIdNumber",
                "FStockOrgId.FName AS FStockOrgIdName",
                "FBaseQty",
                "FStockId",
                "FStockLocId",
                "'' AS FStockLocIdNUMBER",
                "FStockId.FNumber AS FStockIdNumber",
                "FStockId.FName AS FStockIdName",
                "FBaseUnitId.FNumber AS FBaseUnitIdNumber",
                "FOwnerId.FNumber AS FOwnerIdNumber",
                "FAuxPropId",
                "FAuxPropId.FNumber AS FAuxPropIdNumber",
                "FAuxPropId.FName AS FAuxPropIdName",
            },
            Filters = inputList
        });

        List<ProjInventoryComparisonTemp> projInventoryComparisonTemps = new List<ProjInventoryComparisonTemp>();

        foreach (var item in stkInventoryList)
        {
            var stockLocInfo = client.GetStockLocInfo(Convert.ToInt64(item["FStockLocId"]));

            item["FStockLocIdNUMBER"] = stockLocInfo.Number;


            ProjInventoryComparisonTemp projInventoryComparisonTemp = new ProjInventoryComparisonTemp();
            projInventoryComparisonTemp.Id = item["FID"] + "";
            projInventoryComparisonTemp.MaterialIdNumber = item["FMaterialIdNumber"] + "";
            projInventoryComparisonTemp.LotText = item["FLOT_TEXT"] + "";
            projInventoryComparisonTemp.StockOrgIdNumber = item["FStockOrgIdNumber"] + "";
            projInventoryComparisonTemp.StockOrgIdName = item["FStockOrgIdName"] + "";
            Decimal fBaseQty = 0.0M;
            string fBaseQtyStr = item["FBaseQty"] + "";
            if (fBaseQtyStr!.Contains("E") || fBaseQtyStr.Contains("e"))
            {
                fBaseQty = Decimal.Parse(fBaseQtyStr, System.Globalization.NumberStyles.Float);
            }
            else
            {
                fBaseQty = Convert.ToDecimal(item["FBaseQty"] + "");
            }

            projInventoryComparisonTemp.BaseQty = fBaseQty;
            projInventoryComparisonTemp.StockId = item["FStockId"] + "";
            projInventoryComparisonTemp.StockLocId = item["FStockLocId"] + "";
            projInventoryComparisonTemp.StockLocIdNumber = stockLocInfo.Number;
            projInventoryComparisonTemp.StockIdNumber = item["FStockIdNumber"] + "";
            projInventoryComparisonTemp.StockIdName = item["FStockIdName"] + "";
            projInventoryComparisonTemp.MaterialIdName = item["FMaterialIdName"] + "";
            projInventoryComparisonTemp.BaseUnitIdNumber = item["FBaseUnitIdNumber"] + "";
            projInventoryComparisonTemp.OwnerIdNumber = item["FOwnerIdNumber"] + "";
            projInventoryComparisonTemp.AuxPropId = item["FAuxPropId"] + "";
            projInventoryComparisonTemp.AuxPropIdNumber = item["FAuxPropIdNumber"] + "";
            projInventoryComparisonTemp.AuxPropIdName = item["FAuxPropIdName"] + "";
            projInventoryComparisonTemps.Add(projInventoryComparisonTemp);
        }

        // 创建不存在的辅助属性
        var materialAuxPropIds = stkInventoryList.Where(u => u["FAuxPropId"] != null && Convert.ToInt64(u["FAuxPropId"]) > 0)
            .Select(u => new { AuxPropId = Convert.ToInt64(u["FAuxPropId"]), MaterialId = Convert.ToInt64(u["FMaterialId"]) })
            .ToList();
        
        foreach (var item in materialAuxPropIds)
        {
            // 获取或创建辅助属性值
            GetOrCreateAuxPropValue(item.AuxPropId, item.MaterialId);
        }

        return await _db.Fastest<ProjInventoryComparisonTemp>().AS(tableName)
            .BulkCopyAsync(projInventoryComparisonTemps);
    }

    /// <summary>
    /// 使用租户下的管理员帐号设置用户上下文，如果 <paramref name="tenantName"/> 为空，则使用第一个租户
    /// </summary>
    /// <param name="tenantName"></param>
    [NonAction]
    protected void SetContext(string tenantName = "")
    {
        //查找租户
        var tenant = TenantRep.AsQueryable()
            .LeftJoin<SysOrg>((u, v) => u.OrgId == v.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(tenantName), (u, v) => v.Name == tenantName)
            .OrderBy(u => u.Id)
            .First();
        if (tenant == null)
            throw Oops.Bah(EsErrorCode.Es1002, tenantName);

        //查找租户的管理员
        var user = UserRep.AsQueryable().Filter(null, true).First(o => o.AccountType == AccountTypeEnum.SysAdmin && o.TenantId == tenant.Id);
        //设置当前的 Http 上下文
        HttpContextAccessor.HttpContext = user.ToHttpContext();

        // 240813 重新设置数据库Aop, 防止租户信息丢失
        if (tenant.Id + "" == SqlSugarConst.MainConfigId)
        {
            // 必须是主租户才需要重新设置, 否则会报错ConfigId was not found 
            var iTenant = UserRep.AsTenant();
            var sqlSugarScopeProvider = iTenant.GetConnectionScope(tenant.Id + "");
            SqlSugarSetup.SetDbAop(sqlSugarScopeProvider, true);
        }
    }

    /// <summary>
    /// 获取或创建辅助属性值
    /// </summary>
    /// <param name="value">金蝶辅助属性Id</param>
    /// <param name="materialId">本地物料Id</param>
    /// <returns></returns>
    private BdAuxPropValue GetOrCreateAuxPropValue(object value, long materialId)
    {
        if (value == null || Convert.ToInt64(value) == 0) return null;
        var cacheKey = $"ProjInventoryComparisonReportService_{nameof(GetOrCreateAuxPropValue)}_{materialId}_{value}";
        return MemoryCache.GetOrCreate(cacheKey, cacheEntry =>
        {
            cacheEntry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);

            var storageValueList = new List<BdAuxPropValueService.KeyValue>();
            var client = K3CloudInterface.GetK3CloudClient();
            // 金蝶辅助的辅助属性
            var cloudAuxPropInfo = client.GetAuxPropInfo(Convert.ToInt32(value));
            for (int i = 0; i < cloudAuxPropInfo.FlexNumbers.Count; i++)
            {
                var flexKey = cloudAuxPropInfo.FlexKeys[i];
                var id = flexKey[2..]; // FF100001，去掉前面的 FF
                var flexNumber = cloudAuxPropInfo.FlexNumbers[i];
                var flexValueType = cloudAuxPropInfo.FlexValueTypes[i];
                if (flexValueType == K3CloudClient.AuxPropFlexInfo.FlexValueType.手工输入)
                {
                    var localAuxPropType = Rep.Context.Queryable<BdAuxPropType>().First(at => at.EsId == id);
                    if (localAuxPropType == null)
                        throw Oops.Bah(L.Text["Cloud 辅助属性类型Id: {0} 不存在", id]);

                    storageValueList.Add(new BdAuxPropValueService.KeyValue(localAuxPropType.Number, flexNumber));
                }
                else
                {
                    // 查辅助属性类型和辅助属性
                    var localAuxPropInfo = Rep.Context.Queryable<BdAuxProp>()
                        .LeftJoin<BdAuxPropType>((a, at) => a.AuxPropTypeId == at.Id)
                        .Where((a, at) => at.EsId == id && a.EsNumber == flexNumber)
                        .Select((a, at) => new { AuxProp = a, AuxPropTypeNumber = at.Number })
                        .First();
                    if (localAuxPropInfo == null)
                        throw Oops.Bah(L.Text["Cloud 辅助属性类型Id: {0} 辅助属性: {1} 不存在", id, flexNumber]);

                    storageValueList.Add(new BdAuxPropValueService.KeyValue(localAuxPropInfo.AuxPropTypeNumber, localAuxPropInfo.AuxProp));
                }
            }

            var service = App.GetService<BdAuxPropValueService>(ServiceProvider);
            // 保存辅助属性值并返回
            return service.SaveValue(materialId, new BdAuxPropValue { StorageValue = JSON.Serialize(storageValueList) });
        });
    }
}