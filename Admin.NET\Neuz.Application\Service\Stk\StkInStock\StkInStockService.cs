using Neuz.Application.ExternalSystem;
using Neuz.Application.ExternalSystem.Dto;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 入库单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkInStock", Order = 100)]
public class StkInStockService : StkBaseBillService<StkInStock>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkInStock);

    /// <summary>
    /// 入库单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkInStockService(IServiceProvider serviceProvider, SqlSugarRepository<StkInStock> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_DestWhLocName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_ContainerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_NoticeBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_NoticeEsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "SupplierNumber",
            "SupplierName",
            "CustomerNumber",
            "CustomerName",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_SrcWhAreaNumber",
            "Entries_SrcWhAreaName",
            "Entries_SrcWhLocNumber",
            "Entries_SrcWhLocName",
            "Entries_DestWhAreaNumber",
            "Entries_DestWhAreaName",
            "Entries_DestWhLocNumber",
            "Entries_DestWhLocName",
            "Entries_Qty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_ContainerNumber",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_ReturnQty",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_Price",
            "Entries_TotalPrice",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_SrcWhAreaId"); // 库区权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_DestWhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkInStock entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkInStock entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkInStock entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", true);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);
            // 判断上架库区是否已填
            if (entry.DestWhAreaId == 0) throw Oops.Bah(StkErrorCode.Stk1015);
            // 判断上架库位是否已填
            if (entry.DestWhLocId == 0) throw Oops.Bah(StkErrorCode.Stk1016);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (materialInfo.IsBatchManage && string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1001, materialInfo.Number);
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (materialInfo.IsKfPeriod && entry.ProduceDate == null) throw Oops.Bah(StkErrorCode.Stk1003, materialInfo.Number);
            if (materialInfo.IsKfPeriod && entry.ExpiryDate == null) throw Oops.Bah(StkErrorCode.Stk1004, materialInfo.Number);

            // 提前创建分录Id
            if (entry.EntryId == 0)
                entry.EntryId = YitIdHelper.NextId();

            if (entry.BarcodeEntries is { Count: > 0 })
            {
                // 判断条码是否重复
                var duplicateBarcode = entry.BarcodeEntries.GroupBy(u => u.BarcodeId).Where(u => u.Count() > 1).Select(u => u.Key).ToList();
                if (duplicateBarcode.Count > 0) throw Oops.Bah(StkErrorCode.Stk1007, string.Join(", ", duplicateBarcode));

                // 当前明细行条码汇总数量
                var barcodeQty = entry.BarcodeEntries.Sum(u => u.Qty);
                // 校验条码数量是否与明细数量一致
                if (entry.Qty != barcodeQty) throw Oops.Bah(StkErrorCode.Stk1013, entry.Seq);

                // 填充关联信息
                foreach (var barcodeEntry in entry.BarcodeEntries)
                {
                    barcodeEntry.RelEntryId = entry.EntryId;
                    barcodeEntry.RelEntrySeq = entry.Seq;
                }
            }
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkInStock entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkInStock entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkInStock entity)
    {
        return entity.Entries.Where(u => u.SrcTaskId != null).Select(u => u.SrcTaskId.Value).Distinct().ToList();
    }

    protected override void OnAfterAudit(StkInStock entity)
    {
        base.OnAfterAudit(entity);

        // 任务库存数量处理（如果有则在此处调用）

        // 更新条码档案
        UpdateBarcodeInfo(entity);

        // TODO：批号档案处理

        // 更新库存
        UpdateInventory(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkInStock entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);

        if (entity.Entries.Any(u => u.ReturnQty > 0))
            throw Oops.Bah(StkErrorCode.StkInStock1004, entity.BillNo);
    }

    protected override void OnAfterUnAudit(StkInStock entity)
    {
        base.OnAfterUnAudit(entity);

        // 回滚条码档案
        if (entity.Entries.Any(u => u.BarcodeEntries is { Count: > 0 }))
            BarcodeService.RollbackBarcodeInfo(EntityName, entity.Id);

        // TODO：批号档案处理

        // 回滚库存更新
        InventoryService.RollbackInventory(EntityName, entity.Id, "审核");

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 更新条码档案
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateBarcodeInfo(StkInStock entity)
    {
        var changes = entity.Entries.SelectMany(entry => entry.BarcodeEntries.Select(barcodeEntry =>
        {
            return new BdBarcodeChange
            {
                OpTranId = null,
                BarcodeId = barcodeEntry.BarcodeId,
                OpQty = barcodeEntry.Qty,
                OpAuxQty = barcodeEntry.AuxQty,
                OpQtyType = OpQtyType.Replace,
                CurStatus = BdBarcodeStatus.In,
                BatchNo = barcodeEntry.BatchNo,
                ProduceDate = barcodeEntry.Barcode.ProduceDate,
                ExpiryDate = barcodeEntry.Barcode.ExpiryDate,
                RelBillKey = EntityName,
                RelBillId = entity.Id,
                RelBillEntryId = entry.EntryId,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelBillEntrySeq = entry.Seq,
                SrcWhAreaId = entry.SrcWhAreaId,
                SrcWhLocId = entry.SrcWhLocId,
                DestWhAreaId = entry.DestWhAreaId,
                DestWhLocId = entry.DestWhLocId,
            };
        })).ToList();

        BarcodeService.UpdateBarcodeInfo(changes);
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="entity">包含导航列的实体</param>
    private void UpdateInventory(StkInStock entity)
    {
        var changes = entity.Entries.SelectMany(u =>
        {
            var innerChanges = new List<StkInvChange>();

            // 如果有收货库位，扣减收货库位
            if (u.SrcWhLocId != null)
            {
                innerChanges.Add(new StkInvChange
                {
                    InvLogType = StkInvLogType.PutAwayMinus,
                    MaterialId = u.MaterialId,
                    BatchNo = u.BatchNo,
                    ProduceDate = u.ProduceDate,
                    ExpiryDate = u.ExpiryDate,
                    Qty = -u.Qty,
                    UnitId = u.UnitId,
                    WhLocId = u.SrcWhLocId.Value,
                    SourceWhLocId = null,
                    ContainerId = u.ContainerId,
                    OwnerId = u.OwnerId,
                    AuxPropValueId = u.AuxPropValueId,
                    RelBillKey = EntityName,
                    RelBillNo = entity.BillNo,
                    RelBillType = entity.BillType,
                    RelSeq = u.Seq,
                    RelBillId = entity.Id,
                    RelBillEntryId = u.EntryId,
                    RelSourceBillNo = u.SrcBillNo,
                });
            }

            // 增加上架库位
            innerChanges.Add(new StkInvChange
            {
                InvLogType = StkInvLogType.PutAwayPlus,
                MaterialId = u.MaterialId,
                BatchNo = u.BatchNo,
                ProduceDate = u.ProduceDate,
                ExpiryDate = u.ExpiryDate,
                Qty = u.Qty,
                UnitId = u.UnitId,
                WhLocId = u.DestWhLocId,
                SourceWhLocId = u.SrcWhLocId,
                ContainerId = u.ContainerId,
                OwnerId = u.OwnerId,
                AuxPropValueId = u.AuxPropValueId,
                RelBillKey = EntityName,
                RelBillNo = entity.BillNo,
                RelBillType = entity.BillType,
                RelSeq = u.Seq,
                RelBillId = entity.Id,
                RelBillEntryId = u.EntryId,
                RelSourceBillNo = u.SrcBillNo,
            });

            return innerChanges;
        }).ToList();

        InventoryService.UpdateInventory(changes, "审核");
    }

    /// <summary>
    /// 下推出库通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("pushOutNotice")]
    public StkOutNotice PushOutNotice(IdInput input)
    {
        var entity = Rep.AsQueryable().IncludeNavCol().First(u => u.Id == input.Id);
        if (entity == null) throw Oops.Bah(BaseErrorCode.Base1000, input.Id);

        var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
        var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkInStock>(EntityName).GetAwaiter().GetResult()
            .FirstOrDefault(u => u.Item1.Number == entity.BillType);

        if (!billTypeExt.Item2.AllowReturn) throw Oops.Bah(StkErrorCode.StkInStock1002, entity.BillType);
        if (entity.DocumentStatus != DocumentStatus.Approve) throw Oops.Bah(StkErrorCode.StkInStock1003, entity.BillNo);

        // 下级单据类型
        var nextBillType = billTypeExt.Item1.NextEntries?.FirstOrDefault(u => u.NextEntityName == nameof(StkOutNotice))?.NextBillTypeNumber;

        var outNoticeEntity = new StkOutNotice
        {
            DocumentStatus = DocumentStatus.Create,
            Memo = null,
            EsBillNo = entity.EsBillNo,
            EsId = entity.EsId,
            Date = DateTime.Now.Date,
            BillType = nextBillType,
            Status = StkOutNoticeStatus.UnHandle,
            WarehouseId = entity.WarehouseId,
            Warehouse = entity.Warehouse,
            DepartmentId = entity.DepartmentId,
            Department = entity.Department,
            SupplierId = entity.SupplierId,
            Supplier = entity.Supplier,
            CustomerId = entity.CustomerId,
            Customer = entity.Customer,
            PushFlag = PushFlag.None,
            Entries = new List<StkOutNoticeEntry>(),
        };

        foreach (var u in entity.Entries.Where(u => u.Qty - u.ReturnQty > 0))
        {
            var qty = u.Qty - u.ReturnQty;
            outNoticeEntity.Entries.Add(new StkOutNoticeEntry
            {
                Seq = outNoticeEntity.Entries.Count + 1,
                EsEntryId = u.EsEntryId,
                EntryStatus = StkOutNoticeEntryStatus.UnHandle,
                MaterialId = u.MaterialId,
                Material = u.Material,
                BatchNo = u.BatchNo,
                BatchFile = u.BatchFile,
                ProduceDate = u.ProduceDate,
                ExpiryDate = u.ExpiryDate,
                WhAreaId = u.DestWhAreaId,
                WhArea = u.DestWhArea,
                WhLocId = u.DestWhLocId,
                WhLoc = u.DestWhLoc,
                Qty = qty,
                OutQty = 0,
                AllocateQty = 0,
                RemainOutQty = qty,
                UnitId = u.UnitId,
                Unit = u.Unit,
                OwnerId = u.OwnerId,
                Owner = u.Owner,
                AuxPropValueId = u.AuxPropValueId,
                AuxPropValue = u.AuxPropValue,
                GrossWeight = u.GrossWeight,
                PackingVolume = u.PackingVolume,
                PackingQty = u.PackingQty,
                Price = u.Price,
                TotalPrice = u.TotalPrice,
                SrcBillKey = EntityName,
                SrcBillNo = entity.BillNo,
                SrcBillEntrySeq = u.Seq,
                SrcBillId = entity.Id,
                SrcBillEntryId = u.EntryId,
                EntryMemo = null,
                MadeQty = 0,
            });
        }

        if (outNoticeEntity.Entries.Count == 0)
            throw Oops.Bah(StkErrorCode.StkInStock1001, entity.BillNo);

        return outNoticeEntity;
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkInStock>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        var srcBillKey = EntityName;

        // 查询
        var entities = Rep.Context.Queryable<StkInStock>().Includes(u => u.Entries).Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            // 出库通知单明细集合
            var stkOutNoticeEntryList = Rep.Context.Queryable<StkOutNoticeEntry>()
                .LeftJoin<StkOutNotice>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .ToList();

            foreach (var entry in entity.Entries)
            {
                // 退货数量
                var returnQty = stkOutNoticeEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty);

                entry.ReturnQty = returnQty;
            }

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }

        // 刷新收货单状态
        var receiveIds = entities
            .SelectMany(u => u.Entries
                .Where(p => p.SrcBillKey == nameof(StkReceive) && p.SrcBillId != null)
                .Select(p => p.SrcBillId.Value)
            )
            .Distinct()
            .ToList();
        if (receiveIds.Count > 0)
        {
            var stkReceiveService = App.GetService<StkReceiveService>(ServiceProvider);
            stkReceiveService.RefreshStatus(receiveIds);
        }
    }

    /// <summary>
    /// 推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("push")]
    public Task<List<ExecResult>> Push(IdsInput input)
    {
        var execResults = ExecActions(input.Ids, id =>
        {
            var entity = Rep.GetFirst(u => u.Id == id);
            if (entity == null)
                throw Oops.Bah(BaseErrorCode.Base1000, id);

            if (entity.PushFlag == PushFlag.Success)
                throw Oops.Bah(StkErrorCode.Stk1029, entity.BillNo);

            var billTypeExtService = ServiceProvider.GetService<StkBillTypeExtService>();
            var billTypeExt = billTypeExtService.GetExtList<StkBillTypeExtStkInStock>(EntityName).GetAwaiter().GetResult()
                .FirstOrDefault(u => u.Item1.Number == entity.BillType);

            if (string.IsNullOrEmpty(billTypeExt.Item2.PushSettingNumberToUse))
                throw Oops.Bah(StkErrorCode.Stk1030, entity.BillType);

            // 查询推送设置
            var pushSettingRep = ServiceProvider.GetService<SqlSugarRepository<EsSyncPushSetting>>();
            var pushSetting = pushSettingRep.GetFirst(u => u.Number == billTypeExt.Item2.PushSettingNumberToUse);

            var esType = pushSetting?.EsType;
            var settingNumber = pushSetting?.Number;

            // 开启事务，如有外部事务，内部事务用外部事务
            using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

            var externalSyncService = ServiceProvider.GetService<ExternalSyncService>();
            var pushResults = externalSyncService.Push(new EsSyncPushInput2 { BillNo = entity.BillNo, SettingNumber = settingNumber, EsType = esType }).GetAwaiter().GetResult();
            var pushResult = pushResults.First();
            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            // 提交事务
            uow.Commit();

            if (pushResult.Status != EsSyncHandleStatus.Success)
                throw Oops.Bah(pushResult.Message);

            return pushResult.Message;
        });

        return Task.FromResult(execResults);
    }
}