﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存盘点条码日志
/// </summary>
[SugarTable(null, "库存盘点条码日志")]
public class StkStockCountBarcodeLog : EntityTenant
{
    /// <summary>
    /// 事务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "事务Id")]
    public long TranId { get; set; }

    /// <summary>
    /// 盘点Id
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点Id")]
    public long StockCountId { get; set; }

    /// <summary>
    /// 盘点明细Id
    /// </summary>
    [SugarColumn(ColumnDescription = "盘点明细Id")]
    public long StockCountEntryId { get; set; }

    /// <summary>
    /// 条码Id
    /// </summary>
    [SugarColumn(ColumnDescription = "条码Id")]
    public long BarcodeId { get; set; }

    /// <summary>
    /// 条码
    /// </summary>
    [SugarColumn(ColumnDescription = "条码", Length = 200)]
    public string Barcode { get; set; }

    /// <summary>
    /// 库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库区Id")]
    public long WhAreaId { get; set; }

    /// <summary>
    /// 库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhAreaId))]
    [CustomSerializeFields]
    public BdWhArea WhArea { get; set; }

    /// <summary>
    /// 库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "库位Id")]
    public long WhLocId { get; set; }

    /// <summary>
    /// 库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(WhLocId))]
    [CustomSerializeFields]
    public BdWhLoc WhLoc { get; set; }

    /// <summary>
    /// 条码盘点数量
    /// </summary>
    [SugarColumn(ColumnDescription = "条码盘点数量")]
    public decimal Qty { get; set; }
}