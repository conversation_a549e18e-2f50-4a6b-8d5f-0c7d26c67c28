﻿using Neuz.Application.Pda.Bill.Interface.Basic;
using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.Wise;

public class PdaWiseScanStockOperation : PdaScanBarcodeOperationBase
{
    public override void Operation(PdaScanBarcodeArgs args)
    {
        string regexStr = "CK(?<CK>.+)\\|CW(?<CW>.+)";
        Regex regex = new Regex(regexStr, RegexOptions.IgnoreCase);
        var match = regex.Match(args.BarcodeString);
        var pdaCacheService = App.GetService<PdaCacheService>(_serviceProvider);
        //仓库仓位条码
        if (!match.Success) return;
        {
            var billData = pdaCacheService.GetBillData(args.TranId);
            var billModel = pdaCacheService.GetPdaBillModel(billData.BillModelKey);
            string ck = match.Groups["CK"].Value;
            string cw = match.Groups["CW"].Value;
            var lookupModel = pdaCacheService.GetPdaBasicModel("t_stock");
            var stockModel = lookupModel as IPdaBasicLookupModel;
            var table = stockModel.QueryCustomData(args.TranId, $"{ck}_{cw}");
            if (table == null || table.Rows.Count <= 0) throw Oops.Bah(PdaErrorCode.Pda1025, ck, cw);
            var row = table.Rows[0];
            billData.StockInfo = row.ToDictionary().Adapt<PdaBillStockInfo>();
            billModel.RefreshShow(args.TranId);
            args.IsResult = true;
        }
    }

    public PdaWiseScanStockOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}