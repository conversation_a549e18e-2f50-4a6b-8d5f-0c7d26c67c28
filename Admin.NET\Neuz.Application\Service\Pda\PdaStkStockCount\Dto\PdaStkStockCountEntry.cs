﻿using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.PdaStkStockCount.Dto;

public class PdaStkStockCountEntry
{
    /// <summary>
    /// 明细Id
    /// </summary>
    public string DetailId { get; set; }
    /// <summary>
    /// 是否新记录
    /// </summary>
    public bool IsNew { get; set; }
    /// <summary>
    /// 明细
    /// </summary>
    public StkStockCountEntry Entry { get; set; }
    /// <summary>
    /// 扫描数量
    /// </summary>
    public decimal ScanQty { get; set; }
    /// <summary>
    /// 明细包含条码
    /// </summary>
    public List<PdaLocalBillBarcode> IncludeBarcodes { get; set; }
}