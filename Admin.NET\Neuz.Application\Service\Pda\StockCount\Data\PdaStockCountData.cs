﻿using Neuz.Application.Pda.Base;
using Neuz.Application.Pda.Bill.Model.Bill;
using Neuz.Application.Pda.Contract;

namespace Neuz.Application.Pda.StockCount.Data;

public class PdaStockCountData : IPdaData
{
    public string ModelKey { get; set; }
    public long TranId { get; set; }
    public long UserId { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CreateDateTime { get; }

    /// <summary>
    /// 仓库信息
    /// </summary>
    public PdaBillStockInfo StockInfo { get; set; } = new PdaBillStockInfo();

    /// <summary>
    /// 源单头
    /// </summary>
    public List<PdaStockCountSourceHead> SourceHeads { get; } = new List<PdaStockCountSourceHead>();

    /// <summary>
    /// 源单明细
    /// </summary>
    public List<PdaStockCountSourceDetail> SourceDetails { get; } = new List<PdaStockCountSourceDetail>();

    /// <summary>
    /// 扫描表头(目标单表头)
    /// </summary>
    public PdaStockCountScanHead ScanHead { get; set; } = new PdaStockCountScanHead();

    /// <summary>
    /// 扫描表体(目标单表体)
    /// </summary>
    public List<PdaStockCountScanDetail> ScanDetails { get; } = new List<PdaStockCountScanDetail>();

    public List<PdaBarcode> BarcodeList { get; } = new List<PdaBarcode>();

    public Dictionary<string, List<PdaIncludeBarcode>> DetailIncludeBarcodes { get; } = new Dictionary<string, List<PdaIncludeBarcode>>();

    public PdaShow DataShow { get; set; } = new PdaStockCountDataShow();

    public bool IsEmptyData()
    {
        return SourceDetails.Count <= 0;
    }
}

public class PdaStockCountDataShow : PdaShow
{
    /// <summary>
    /// 多源单的源单编码
    /// </summary>
    public List<string> SourceBill { get; set; } = new List<string>();
    /// <summary>
    /// 仓库
    /// </summary>
    public string Stock { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public PdaBillDataDest DestData { get; set; } = new PdaBillDataDest();

    /// <summary>
    /// 条码信息
    /// </summary>
    public List<PdaBarcodeShowInfo> Barcodes { get; set; } = new List<PdaBarcodeShowInfo>();

    /// <summary>
    /// PDA字段显示属性
    /// </summary>
    public List<PdaCellAttributes> CellAttributes = new List<PdaCellAttributes>();

    /// <summary>
    /// 统计信息
    /// </summary>
    public PdaStockCountSummaryShow Summary = new PdaStockCountSummaryShow();
}

public class PdaStockCountSummaryShow
{
    /// <summary>
    /// 盘点单总数量
    /// </summary>
    public decimal Qty { get; set; }
    /// <summary>
    /// 盘点单已盘数量
    /// </summary>
    public decimal CheckQty { get; set; }

    /// <summary>
    /// 盘盈数量
    /// </summary>
    public decimal ProfitQty { get; set; }

    /// <summary>
    /// 盘亏数量
    /// </summary>
    public decimal LoseQty { get; set; }

    /// <summary>
    /// 本次扫描数量
    /// </summary>
    public decimal ScanQty { get; set; }
}