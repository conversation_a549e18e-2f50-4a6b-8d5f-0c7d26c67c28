﻿using System.Text;

namespace Neuz.License.WinForm;

public partial class MainForm : Form
{
    public MainForm()
    {
        InitializeComponent();
    }

    protected override void OnLoad(EventArgs e)
    {
        base.OnLoad(e);

        btnCreate.Click += BtnCreate_Click;
        rbLicenseTypeTrial.CheckedChanged += RbLicenseTypeTrial_CheckedChanged;
        btnOpen.Click += BtnOpen_Click;
        btnSave.Click += BtnSave_Click;

        rbLicenseTypeTrial.Checked = true;
    }

    private void BtnSave_Click(object? sender, EventArgs e)
    {
        var licenseStr = txtLicense.Text;
        if (string.IsNullOrEmpty(licenseStr))
        {
            MessageBox.Show(@"请先生成授权");
            return;
        }

        try
        {
            //处理可能的换行、非法字符
            var rBuilder = new StringBuilder(txtCustomerName.Text);
            rBuilder.Replace("\r", "");
            rBuilder.Replace("\n", "");
            foreach (char rInvalidChar in Path.GetInvalidFileNameChars())
                rBuilder.Replace(rInvalidChar.ToString(), "_");
            var customerName = rBuilder.ToString();
            saveFileDialog1.FileName = $"({customerName}){DateTime.Now:yyyyMMddHHmmss}.key";
            if (saveFileDialog1.ShowDialog() != DialogResult.OK) return;

            var fileName = saveFileDialog1.FileName;
            File.WriteAllText(fileName, licenseStr);
            MessageBox.Show(@"生成成功");
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private void BtnOpen_Click(object? sender, EventArgs e)
    {
        if (openFileDialog1.ShowDialog() != DialogResult.OK) return;

        var fileName = openFileDialog1.FileName;
        var fileContent = File.ReadAllText(fileName);
        txtPrivateKey.Text = fileContent;
    }

    private void RbLicenseTypeTrial_CheckedChanged(object? sender, EventArgs e)
    {
        var nowDate = DateTime.Now.Date;
        if (rbLicenseTypeTrial.Checked)
        {
            label7.Visible = false;
            dtServiceEndDate.Visible = false;

            dtBeginDate.Value = nowDate;
            dtEndDate.Value = nowDate.AddDays(90);

            numMaxConnectCount.Value = 2;
        }
        else
        {
            label7.Visible = true;
            dtServiceEndDate.Visible = true;

            dtBeginDate.Value = dtBeginDate.MinDate;
            dtEndDate.Value = dtEndDate.MaxDate;

            numMaxConnectCount.Value = 10;

            dtServiceEndDate.Value = nowDate.AddYears(1);
        }
    }

    private void BtnCreate_Click(object? sender, EventArgs e)
    {
        var errMsgs = new List<string>();
        if (string.IsNullOrWhiteSpace(txtPrivateKey.Text)) errMsgs.Add("私钥字符串不能为空");
        if (string.IsNullOrWhiteSpace(txtCustomerName.Text)) errMsgs.Add("使用主体不能为空");
        if (string.IsNullOrWhiteSpace(txtServerKey.Text)) errMsgs.Add("服务器序列号不能为空");

        if (dtEndDate.Value < dtBeginDate.Value) errMsgs.Add("结束日期不能小于开始日期");

        if (rbLicenseTypeTrial.Checked)
        {
            if (numMaxConnectCount.Value == 0) errMsgs.Add("试用授权最大连接数不能为0");
            if ((dtEndDate.Value - dtBeginDate.Value).TotalDays > 90) errMsgs.Add("试用授权授权日期范围不能超过90天");
        }

        if (errMsgs.Count > 0)
        {
            MessageBox.Show(string.Join("\r\n", errMsgs));
            return;
        }

        var licenseInfo = new LicenseInfo
        {
            CreateTime = DateTime.Now,
            CustomerName = txtCustomerName.Text,
            BeginDate = dtBeginDate.Value,
            EndDate = dtEndDate.Value,
            MaxConnectCount = (int)numMaxConnectCount.Value,
            ServerKey = txtServerKey.Text,
            Description = txtDescription.Text,
        };
        if (rbLicenseTypeTrial.Checked) licenseInfo.LicenseType = LicenseType.Trial;
        if (rbLicenseTypeOfficial.Checked)
        {
            licenseInfo.LicenseType = LicenseType.Official;
            licenseInfo.ServiceEndDate = dtServiceEndDate.Value;
        }

        try
        {
            txtLicense.Text = LicenseInfo.Generate(txtPrivateKey.Text, licenseInfo);
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }
}