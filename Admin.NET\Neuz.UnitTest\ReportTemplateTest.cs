﻿using Furion;
using Furion.DependencyInjection;
using Neuz.Application;
using Stimulsoft.Drawing;
using Xunit;
using Xunit.Abstractions;

namespace Neuz.UnitTest
{
    public class ReportTemplateTest
    {
        private readonly ITestOutputHelper _output;

        public ReportTemplateTest(ITestOutputHelper tempOutput)
        {
            _output = tempOutput;
        }

        [Fact(DisplayName = "打印测试")]
        public void PrintTest()
        {
            Scoped.Create((_, s) =>
            {
                // 2023.1.1，默认引擎是 GraphicsEngine.ImageSharp，Print() 时会抛 Object reference not set to an instance of an object
                Graphics.GraphicsEngine = GraphicsEngine.ImageSharp;
                // 打印
                var reportTemplateService = App.GetService<SysReportTemplateService>(s.ServiceProvider);
                var templateId = 12921711517381;
                var report = reportTemplateService.GetReport(templateId, typeof(BarBarcodeService).FullName, new List<string>() { "12921709402053" }).GetAwaiter().GetResult();
                report.Render(false);
                report.Print(false);
            });
        }
    }
}