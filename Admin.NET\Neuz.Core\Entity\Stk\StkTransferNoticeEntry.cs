﻿namespace Neuz.Core.Entity;

/// <summary>
/// 调拨通知单明细
/// </summary>
[SugarTable(null, "调拨通知单明细")]
[SugarIndex("index_{table}_SBSI", nameof(SrcBillKey), OrderByType.Asc, nameof(SrcBillId), OrderByType.Asc)]
[SugarIndex("index_{table}_SB", nameof(SrcBillNo), OrderByType.Asc)]
public class StkTransferNoticeEntry : EsEntryEntityBase
{
    /// <summary>
    /// 明细业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "明细业务状态")]
    public StkTransferNoticeEntryStatus EntryStatus { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [MaterialSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 调出库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调出库区Id")]
    public long? SrcWhAreaId { get; set; }

    /// <summary>
    /// 调出库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea SrcWhArea { get; set; }

    /// <summary>
    /// 调出库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调出库位Id")]
    public long? SrcWhLocId { get; set; }

    /// <summary>
    /// 调出库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc SrcWhLoc { get; set; }

    /// <summary>
    /// 调出容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调出容器Id")]
    public long? SrcContainerId { get; set; }

    /// <summary>
    /// 调出容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer SrcContainer { get; set; }

    /// <summary>
    /// 调入库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调入库区Id")]
    public long? DestWhAreaId { get; set; }

    /// <summary>
    /// 调入库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea DestWhArea { get; set; }

    /// <summary>
    /// 调入库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调入库位Id")]
    public long? DestWhLocId { get; set; }

    /// <summary>
    /// 调入库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc DestWhLoc { get; set; }

    /// <summary>
    /// 调入容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "调入容器Id")]
    public long? DestContainerId { get; set; }

    /// <summary>
    /// 调入容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer DestContainer { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 分配数量
    /// </summary>
    [SugarColumn(ColumnDescription = "分配数量")]
    public decimal AllocateQty { get; set; }

    /// <summary>
    /// 已调拨数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已调拨数量")]
    public decimal TransferQty { get; set; }

    /// <summary>
    /// 剩余调拨数量
    /// </summary>
    [SugarColumn(ColumnDescription = "剩余调拨数量")]
    public decimal RemainTransferQty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 辅助属性值Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助属性值Id")]
    public long? AuxPropValueId { get; set; }

    /// <summary>
    /// 辅助属性值
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxPropValueId))]
    [CustomSerializeFields(true, nameof(BdAuxPropValue.StorageValue))]
    public BdAuxPropValue AuxPropValue { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的毛重
    /// </remarks>
    [SugarColumn(ColumnDescription = "毛重")]
    public decimal GrossWeight { get; set; }

    /// <summary>
    /// 包装体积
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的包装体积
    /// </remarks>
    [SugarColumn(ColumnDescription = "包装体积")]
    public decimal PackingVolume { get; set; }

    /// <summary>
    /// 箱数
    /// </summary>
    /// <remarks>
    /// 默认计算规则：[Ceiling]向上取整(数量 / 物料的标准装箱量)
    /// </remarks>
    [SugarColumn(ColumnDescription = "箱数")]
    public int PackingQty { get; set; }

    /// <summary>
    /// 来源单据标识
    /// </summary>
    /// <remarks>
    /// 标识为单据的实体名称
    /// </remarks>
    [SugarColumn(ColumnDescription = "来源单据标识", Length = 200)]
    public string? SrcBillKey { get; set; }

    /// <summary>
    /// 来源单据编号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据编号", Length = 80)]
    public string? SrcBillNo { get; set; }

    /// <summary>
    /// 来源单据分录序号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录序号")]
    public int? SrcBillEntrySeq { get; set; }

    /// <summary>
    /// 来源单据Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据Id")]
    public long? SrcBillId { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id")]
    public long? SrcBillEntryId { get; set; }

    /// <summary>
    /// 明细备注
    /// </summary>
    [SugarColumn(ColumnDescription = "明细备注", Length = 255)]
    public string? EntryMemo { get; set; }

    /// <summary>
    /// 辅助数量
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助数量")]
    public decimal AuxQty { get; set; }
    
    /// <summary>
    /// 辅助单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "辅助单位Id")]
    public long? AuxUnitId { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AuxUnitId))]
    [CustomSerializeFields]
    public BdUnit AuxUnit { get; set; }
}