﻿namespace Neuz.Application.Pda.Enum;

/// <summary>
/// 使用仓库优先类型
/// </summary>
public enum FirstStockType
{
    /// <summary>
    /// 以条码上的仓库优先
    /// </summary>
    [Description("以条码上的仓库优先")] Barcode,

    /// <summary>
    /// 以PDA选择的仓库优先
    /// </summary>
    [Description("以选择的仓库优先")] Select,

    /// <summary>
    /// 不带仓库 [注: 选择NoStock,会导致条码或表头选择的仓库,都不会带到扫描的条码上,要后面特殊处理]
    /// </summary>
    [Description("不带仓库")] NoStock
}