﻿using Furion.Localization;
using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 仓储分配策略服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkAllocatePolicy", Order = 100)]
public class StkAllocatePolicyService : BaseBdService<StkAllocatePolicy, LookupInput, LookupOutput>, IDynamicApiController, ITransient
{
    private readonly StkInventoryService _inventoryService;

    private readonly IQueryDefine _queryDefine;

    /// <summary>
    /// 仓储分配策略服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkAllocatePolicyService(IServiceProvider serviceProvider, SqlSugarRepository<StkAllocatePolicy> rep) : base(serviceProvider, rep)
    {
        _inventoryService = serviceProvider.GetService<StkInventoryService>();
        _queryDefine = new StkAllocateBillQueryDefine();
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理实体名称的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "EntityName");
        var entityService = ServiceProvider.GetService<SysEntityService>();
        var entityList = entityService.ListAsync().GetAwaiter().GetResult();
        billTypeColumn.Options = entityList.Select(u => new SelectOption { Value = u.EntityName, Title = $"[{u.EntityName}]{u.Description}" }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "Number", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Name", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EntityName", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "Number",
            "Name",
            "EntityName",
            "Description",
            "IsForbid",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
        ];
    }

    protected override void OnBeforeAdd(StkAllocatePolicy entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkAllocatePolicy entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkAllocatePolicy entity)
    {
    }

    /// <summary>
    /// 获取实体字段名列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("getEntityFieldList")]
    public Task<List<Dictionary<string, string>>> GetEntityFieldList([FromQuery] string entityName)
    {
        if (!SqlSugarExtension.EntityTypeNames.TryGetValue(entityName, out var typeName))
            return Task.FromResult(new List<Dictionary<string, string>>());

        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, Type.GetType(typeName));

        return Task.FromResult(
            columnInfos
                .Select(u => new Dictionary<string, string> { ["FieldName"] = u.Value.FieldName, ["Title"] = u.Value.Title })
                .ToList()
        );
    }

    /// <summary>
    /// 获取库存实体字段名列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("getInvEntityFieldList")]
    public Task<List<Dictionary<string, string>>> GetInvEntityFieldList()
    {
        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, typeof(StkInventory));

        return Task.FromResult(
            columnInfos
                .Select(u => new Dictionary<string, string> { ["FieldName"] = u.Value.FieldName, ["Title"] = u.Value.Title })
                .ToList()
        );
    }

    /// <summary>
    /// 获取库位实体字段名列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("getWhLocEntityFieldList")]
    public Task<List<Dictionary<string, string>>> GetWhLocEntityFieldList()
    {
        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, typeof(BdWhLoc));

        return Task.FromResult(
            columnInfos
                .Select(u => new Dictionary<string, string> { ["FieldName"] = u.Value.FieldName, ["Title"] = u.Value.Title })
                .ToList()
        );
    }

    /// <summary>
    /// 分配
    /// </summary>
    /// <param name="number">分配策略编码</param>
    /// <param name="billId">需要分配的单据的Id</param>
    /// <param name="allocateDefine">分配信息定义</param>
    [NonAction]
    public List<StkAllocateResult> Allocate(string number, long billId, EntityAllocateDefine allocateDefine)
    {
        var stkAllocatePolicy = Rep.AsQueryable().IncludeNavCol()
            // IncludeNavCol 不会包含以下导航属性，需要手工指定
            .Includes(u => u.Entries, p => p.AllocateRule, r => r.BillFilterEntries)
            .Includes(u => u.Entries, p => p.AllocateRule, r => r.InvFilterEntries)
            .Includes(u => u.Entries, p => p.AllocateRule, r => r.InvOrderEntries)
            .Includes(u => u.Entries, p => p.AllocateRule, r => r.WhLocFilterEntries)
            .Includes(u => u.Entries, p => p.AllocateRule, r => r.WhLocOrderEntries)
            .First(u => u.Number == number && u.IsForbid == false);
        if (stkAllocatePolicy == null)
            throw Oops.Bah(StkErrorCode.StkAllocatePolicy1001, number);

        return Allocate(stkAllocatePolicy, billId, allocateDefine);
    }

    /// <summary>
    /// 分配
    /// </summary>
    /// <param name="stkAllocatePolicy">分配策略实体，传入的实体需要包含导航属性</param>
    /// <param name="billId">需要分配的单据的Id</param>
    /// <param name="allocateDefine">分配信息定义</param>
    [NonAction]
    public List<StkAllocateResult> Allocate(StkAllocatePolicy stkAllocatePolicy, long billId, EntityAllocateDefine allocateDefine)
    {
        // 参数校验
        ValidationParameter(allocateDefine);

        // 获取需要分配的单据 table
        var billTable = GetBillTable(stkAllocatePolicy, billId, allocateDefine);

        // 开启事务，如有外部事务，内部事务用外部事务
        using var uow = Rep.RootContext.CreateContext(Rep.Context.Ado.IsNoTran());

        var resultList = new List<StkAllocateResult>();

        // 找出未禁用的规则，并排序
        foreach (var ruleEntity in stkAllocatePolicy.Entries?
                     .Where(u => !(u.AllocateRule?.IsForbid ?? true))
                     .OrderBy(u => u.Seq).Select(u => u.AllocateRule) ?? new List<StkAllocateRule>())
        {
            // 单据查询条件
            var billConditionStr = ruleEntity.BillFilterEntries?
                .Where(u => u.IsEnable)
                .Select(u => new QueryCondition(u.FieldName, Enum.Parse<DataQueryOp>(u.Op, true), u.ConstValue)).ToList()
                .BuildDataTableSelectStr() ?? "";

            // 根据规则条件查找当前规则可以处理的单据行
            var handleBillRows = billTable.Select(billConditionStr);
            foreach (var billRow in handleBillRows)
            {
                // 只开启了执行分配库位预入库
                if (!allocateDefine.IsLockInv && allocateDefine.IsPreIn)
                {
                    var remainHandleQty = billRow.Field<decimal>("_RemainHandleQty_");
                    var list = AllocatePreIn(stkAllocatePolicy.EntityName, allocateDefine, billRow, ruleEntity, remainHandleQty, null);

                    // 将余下的剩余可处理数量设置到DataRow中
                    billRow["_RemainHandleQty_"] = remainHandleQty - list.Sum(u => u.AllocateQty);
                    resultList.AddRange(list);
                }
                else if (allocateDefine.IsLockInv)
                {
                    // 库存变更集合
                    var innerChanges = new List<StkInvChange>();

                    // 剩余可处理数量
                    var remainHandleQty = billRow.Field<decimal>("_RemainHandleQty_");

                    // 如果行的剩余可处理数量为0，跳过
                    if (remainHandleQty <= 0) continue;

                    // 获取满足查询条件的库存集合（此库存集合仅用于计算锁定库存，不更新到数据库）
                    var invList = GetInventoryList(billRow, ruleEntity, allocateDefine);

                    foreach (var inv in invList)
                    {
                        if (inv.AvailableQty <= 0) continue;

                        // 需要处理的锁定数量
                        var handleLockQty = Math.Min(remainHandleQty, inv.AvailableQty);

                        if (!allocateDefine.IsPreIn) // 没有开启执行分配库位预入库
                        {
                            inv.LockQty += handleLockQty;
                            inv.AvailableQty -= handleLockQty;
                            remainHandleQty -= handleLockQty;

                            // 添加到库存变更列表
                            innerChanges.Add(CreateLockStkInvChange(stkAllocatePolicy.EntityName, allocateDefine, inv, billRow, handleLockQty));

                            // 添加到待返回的处理结果集合中
                            resultList.Add(new StkAllocateResult
                            {
                                BillId = billRow.Field<long>(allocateDefine.IdField),
                                BillNo = billRow.Field<string>(allocateDefine.BillNoField),
                                BillEntryId = string.IsNullOrEmpty(allocateDefine.EntryIdField) ? null : billRow.Field<long>(allocateDefine.EntryIdField),
                                BillEntrySeq = string.IsNullOrEmpty(allocateDefine.EntrySeqField) ? null : billRow.Field<int>(allocateDefine.EntrySeqField),
                                LockInventoryId = inv.Id,
                                PreInInventoryId = null,
                                AllocateQty = handleLockQty,
                                IsAllowLocReplace = ruleEntity.IsAllowLocReplace,
                                IsAllowBatchNoReplace = ruleEntity.IsAllowBatchNoReplace,
                                UsedRuleNumber = ruleEntity.Number,
                                UsedRuleName = ruleEntity.Name,
                            });
                        }
                        else // 开启执行分配库位预入库
                        {
                            var list = AllocatePreIn(stkAllocatePolicy.EntityName, allocateDefine, billRow, ruleEntity, handleLockQty, inv);
                            foreach (var item in list)
                            {
                                var qty = item.AllocateQty; // 分配的预入库数量

                                inv.LockQty += qty;
                                inv.AvailableQty -= qty;
                                remainHandleQty -= qty;

                                // 添加到库存变更列表
                                innerChanges.Add(CreateLockStkInvChange(stkAllocatePolicy.EntityName, allocateDefine, inv, billRow, qty));
                                // 补回锁定库存Id
                                item.LockInventoryId = inv.Id;
                            }

                            resultList.AddRange(list);
                        }

                        // 将余下的剩余可处理数量设置到DataRow中
                        billRow["_RemainHandleQty_"] = remainHandleQty;

                        if (remainHandleQty == 0) break;
                    }

                    // 提交库存变更
                    _inventoryService.UpdateInventory(innerChanges, "分配");
                }
            }

            // 如果没有剩余可处理数量>0的数据，退出循环
            if (billTable.Select("_RemainHandleQty_ > 0").Length == 0) break;
        }

        // 提交事务
        uow.Commit();

        // 返回处理结果集合
        return resultList;
    }

    /// <summary>
    /// 分配库位预入库
    /// </summary>
    /// <param name="entityName"></param>
    /// <param name="allocateDefine"></param>
    /// <param name="billRow"></param>
    /// <param name="ruleEntity"></param>
    /// <param name="handleQty"></param>
    /// <param name="inv"></param>
    /// <returns></returns>
    private List<StkAllocateResult> AllocatePreIn(string entityName, EntityAllocateDefine allocateDefine, DataRow billRow, StkAllocateRule ruleEntity,
        decimal handleQty, StkInventory inv)
    {
        var resultList = new List<StkAllocateResult>();

        // 如果行的剩余可处理数量为0，返回
        if (handleQty <= 0) return resultList;

        // 剩余可处理数量
        var remainHandleQty = handleQty;

        // 获取库位库存数量列表
        var whLocList = GetWhLocList(billRow, ruleEntity, allocateDefine);
        foreach (var whLoc in whLocList)
        {
            // 如果库位有限制且已满，跳过
            if (whLoc.QtyCapacity > 0 && whLoc.QtyCapacity <= whLoc.UsedQtyCapacity) continue;

            // 需要处理的预入库数量
            var handlePreInQty = whLoc.QtyCapacity > 0 ? Math.Min(remainHandleQty, whLoc.QtyCapacity - whLoc.UsedQtyCapacity) : remainHandleQty;

            whLoc.UsedQtyCapacity += handlePreInQty;
            remainHandleQty -= handlePreInQty;

            var stkInvChange = !allocateDefine.IsLockInv && allocateDefine.IsPreIn
                ? CreatePreInStkInvChange(entityName, allocateDefine, billRow, whLoc.Id, handlePreInQty)
                : CreatePreInStkInvChangeWithInv(entityName, allocateDefine, billRow, whLoc.Id, handlePreInQty, inv);
            // 提交库存变更，并返回库存Id
            var invId = _inventoryService.UpdateInventory(stkInvChange, "分配");

            // 添加到待返回的处理结果集合中
            resultList.Add(new StkAllocateResult
            {
                BillId = billRow.Field<long>(allocateDefine.IdField),
                BillNo = billRow.Field<string>(allocateDefine.BillNoField),
                BillEntryId = string.IsNullOrEmpty(allocateDefine.EntryIdField) ? null : billRow.Field<long>(allocateDefine.EntryIdField),
                BillEntrySeq = string.IsNullOrEmpty(allocateDefine.EntrySeqField) ? null : billRow.Field<int>(allocateDefine.EntrySeqField),
                LockInventoryId = null,
                PreInInventoryId = invId,
                AllocateQty = handlePreInQty,
                IsAllowLocReplace = ruleEntity.IsAllowLocReplace,
                IsAllowBatchNoReplace = ruleEntity.IsAllowBatchNoReplace,
                UsedRuleNumber = ruleEntity.Number,
                UsedRuleName = ruleEntity.Name
            });

            if (remainHandleQty == 0) break;
        }

        return resultList;
    }

    private static StkInvChange CreateLockStkInvChange(string entityName, EntityAllocateDefine allocateDefine, StkInventory inv, DataRow billRow, decimal handleQty)
    {
        return new StkInvChange
        {
            InvLockLogType = allocateDefine.InvLockLogType,
            MaterialId = inv.MaterialId,
            BatchNo = inv.BatchNo,
            ProduceDate = inv.ProduceDate,
            ExpiryDate = inv.ExpiryDate,
            UnitId = inv.UnitId,
            WhLocId = inv.WhLocId,
            SourceWhLocId = null,
            ContainerId = inv.ContainerId,
            OwnerId = inv.OwnerId,
            AuxPropValueId = inv.AuxPropValueId,
            RelBillKey = entityName,
            RelBillId = billRow.Field<long>(allocateDefine.IdField),
            RelBillNo = billRow.Field<string>(allocateDefine.BillNoField),
            RelBillType = billRow.Field<string>(allocateDefine.BillTypeField),
            RelBillEntryId = string.IsNullOrEmpty(allocateDefine.EntryIdField) ? null : billRow.Field<long>(allocateDefine.EntryIdField),
            RelSeq = string.IsNullOrEmpty(allocateDefine.EntrySeqField) ? null : billRow.Field<int>(allocateDefine.EntrySeqField),
            RelSourceBillNo = null,
            RelTaskId = allocateDefine.RelTaskId,
            LockQty = handleQty,
            InventoryId = inv.Id, // 指定库存Id
        };
    }

    private static StkInvChange CreatePreInStkInvChange(string entityName, EntityAllocateDefine allocateDefine, DataRow billRow, long whLocId, decimal handleQty)
    {
        return new StkInvChange
        {
            InvPreInLogType = allocateDefine.InvPreInLogType,
            MaterialId = billRow.Field<long>(allocateDefine.MaterialIdField),
            BatchNo = string.IsNullOrEmpty(allocateDefine.BatchNoField) ? null : billRow.Field<string>(allocateDefine.BatchNoField),
            ProduceDate = string.IsNullOrEmpty(allocateDefine.ProduceDateField) ? null : billRow.Field<DateTime?>(allocateDefine.ProduceDateField),
            ExpiryDate = string.IsNullOrEmpty(allocateDefine.ExpiryDateField) ? null : billRow.Field<DateTime?>(allocateDefine.ExpiryDateField),
            UnitId = billRow.Field<long>(allocateDefine.UnitIdField),
            WhLocId = whLocId,
            SourceWhLocId = null,
            ContainerId = string.IsNullOrEmpty(allocateDefine.ContainerIdField) ? null : billRow.Field<long?>(allocateDefine.ContainerIdField),
            OwnerId = billRow.Field<long>(allocateDefine.OwnerIdField),
            AuxPropValueId = string.IsNullOrEmpty(allocateDefine.AuxPropValueIdField) ? null : billRow.Field<long?>(allocateDefine.AuxPropValueIdField),
            RelBillKey = entityName,
            RelBillId = billRow.Field<long>(allocateDefine.IdField),
            RelBillNo = billRow.Field<string>(allocateDefine.BillNoField),
            RelBillType = billRow.Field<string>(allocateDefine.BillTypeField),
            RelBillEntryId = string.IsNullOrEmpty(allocateDefine.EntryIdField) ? null : billRow.Field<long>(allocateDefine.EntryIdField),
            RelSeq = string.IsNullOrEmpty(allocateDefine.EntrySeqField) ? null : billRow.Field<int>(allocateDefine.EntrySeqField),
            RelSourceBillNo = null,
            RelTaskId = allocateDefine.RelTaskId,
            PreInQty = handleQty,
        };
    }

    private static StkInvChange CreatePreInStkInvChangeWithInv(string entityName, EntityAllocateDefine allocateDefine, DataRow billRow, long whLocId, decimal handleQty,
        StkInventory inv)
    {
        return new StkInvChange
        {
            InvPreInLogType = allocateDefine.InvPreInLogType,
            MaterialId = inv.MaterialId,
            BatchNo = inv.BatchNo,
            ProduceDate = inv.ProduceDate,
            ExpiryDate = inv.ExpiryDate,
            UnitId = inv.UnitId,
            WhLocId = whLocId,
            SourceWhLocId = null,
            ContainerId = inv.ContainerId,
            OwnerId = inv.OwnerId,
            AuxPropValueId = inv.AuxPropValueId,
            RelBillKey = entityName,
            RelBillId = billRow.Field<long>(allocateDefine.IdField),
            RelBillNo = billRow.Field<string>(allocateDefine.BillNoField),
            RelBillType = billRow.Field<string>(allocateDefine.BillTypeField),
            RelBillEntryId = string.IsNullOrEmpty(allocateDefine.EntryIdField) ? null : billRow.Field<long>(allocateDefine.EntryIdField),
            RelSeq = string.IsNullOrEmpty(allocateDefine.EntrySeqField) ? null : billRow.Field<int>(allocateDefine.EntrySeqField),
            RelSourceBillNo = null,
            RelTaskId = allocateDefine.RelTaskId,
            PreInQty = handleQty,
        };
    }

    /// <summary>
    /// 校验实体分配信息定义参数
    /// </summary>
    private void ValidationParameter(EntityAllocateDefine allocateDefine)
    {
        var errMsgs = new List<string>();

        if (allocateDefine.IsLockInv && allocateDefine.InvLockLogType == null)
            errMsgs.Add(L.Text["当 {0} 为 true，需要设置 {1}", nameof(EntityAllocateDefine.IsLockInv), nameof(EntityAllocateDefine.InvLockLogType)]);
        if (allocateDefine.IsPreIn && allocateDefine.InvPreInLogType == null)
            errMsgs.Add(L.Text["当 {0} 为 true，需要设置 {1}", nameof(EntityAllocateDefine.IsPreIn), nameof(EntityAllocateDefine.InvPreInLogType)]);

        if (allocateDefine.GetBillExtraSelectFields == null)
            errMsgs.Add(L.Text["{0} 需要指定", nameof(allocateDefine.GetBillExtraSelectFields)]);
        if (allocateDefine.GetCanAllocateQty == null)
            errMsgs.Add(L.Text["{0} 需要指定", nameof(allocateDefine.GetCanAllocateQty)]);

        if (!allocateDefine.IsLockInv && allocateDefine.IsPreIn)
        {
            if (string.IsNullOrEmpty(allocateDefine.UnitIdField))
                errMsgs.Add(L.Text["当 {0} 为 false， {1} 为 true，需要设置 {2}", nameof(EntityAllocateDefine.IsLockInv), nameof(EntityAllocateDefine.IsPreIn),
                    nameof(EntityAllocateDefine.UnitIdField)]);
            if (string.IsNullOrEmpty(allocateDefine.OwnerIdField))
                errMsgs.Add(L.Text["当 {0} 为 false， {1} 为 true，需要设置 {2}", nameof(EntityAllocateDefine.IsLockInv), nameof(EntityAllocateDefine.IsPreIn),
                    nameof(EntityAllocateDefine.OwnerIdField)]);
        }

        if (errMsgs.Count > 0)
            throw new ArgumentException(string.Join(", ", errMsgs));
    }

    /// <summary>
    /// 获取需要分配的单据 table
    /// </summary>
    /// <param name="stkAllocatePolicy">分配策略实体</param>
    /// <param name="billId">需要分配的单据的Id</param>
    /// <param name="allocateDefine">分配信息定义</param>
    /// <returns></returns>
    private DataTable GetBillTable(StkAllocatePolicy stkAllocatePolicy, long billId, EntityAllocateDefine allocateDefine)
    {
        if (!SqlSugarExtension.EntityTypeNames.TryGetValue(stkAllocatePolicy.EntityName, out var typeName))
            throw Oops.Bah(StkErrorCode.StkAllocatePolicy1002, stkAllocatePolicy.EntityName);

        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, Type.GetType(typeName));

        // 查询字段集合
        var selectFieldNames = new List<string>
        {
            allocateDefine.IdField,
            allocateDefine.BillNoField,
            allocateDefine.BillTypeField,
            allocateDefine.MaterialIdField,
            allocateDefine.WarehouseIdField,
        };

        // 添加扩展的查询字段
        selectFieldNames.AddRange(allocateDefine.GetBillExtraSelectFields);

        // 如果分录主键Id字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.EntryIdField)) selectFieldNames.Add(allocateDefine.EntryIdField);
        // 如果分录序号字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.EntrySeqField)) selectFieldNames.Add(allocateDefine.EntrySeqField);

        // 如果批号字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.BatchNoField)) selectFieldNames.Add(allocateDefine.BatchNoField);
        // 如果生产日期字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.ProduceDateField)) selectFieldNames.Add(allocateDefine.ProduceDateField);
        // 如果有效期至字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.ExpiryDateField)) selectFieldNames.Add(allocateDefine.ExpiryDateField);
        // 如果容器Id字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.ContainerIdField)) selectFieldNames.Add(allocateDefine.ContainerIdField);
        // 如果单位Id字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.UnitIdField)) selectFieldNames.Add(allocateDefine.UnitIdField);
        // 如果货主Id字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.OwnerIdField)) selectFieldNames.Add(allocateDefine.OwnerIdField);
        // 如果辅助属性值Id字段名不为空
        if (!string.IsNullOrWhiteSpace(allocateDefine.AuxPropValueIdField)) selectFieldNames.Add(allocateDefine.AuxPropValueIdField);

        // 加上配置的库存条件所用到的目标字段
        selectFieldNames.AddRange(stkAllocatePolicy.Entries?
            .SelectMany(u => u.AllocateRule?.InvFilterEntries?
                .Where(p => p.IsEnable && !string.IsNullOrWhiteSpace(p.TargetValueFieldName))
                .Select(p => p.TargetValueFieldName) ?? new List<string>()) ?? new List<string>());

        // 加上配置的库位条件所用到的目标字段
        selectFieldNames.AddRange(stkAllocatePolicy.Entries?
            .SelectMany(u => u.AllocateRule?.WhLocFilterEntries?
                .Where(p => p.IsEnable && !string.IsNullOrWhiteSpace(p.TargetValueFieldName))
                .Select(p => p.TargetValueFieldName) ?? new List<string>()) ?? new List<string>());

        // 查询字段去重复
        selectFieldNames = selectFieldNames.Distinct().ToList();

        // 查询条件集合
        var queryConditions = new List<QueryCondition> { new(allocateDefine.IdField, DataQueryOp.Equals, billId) };

        // 构建查询
        var query = Rep.Context.Queryable<object>()
            .Where(columnInfos, queryConditions)
            .Select(columnInfos, selectFieldNames)
            .AddTenantFilter(columnInfos); // 增加租户过滤条件
        // TODO: 增加仓库、库区、货主数据隔离？

        // 执行查询，返回 table
        var table = query.ToDataTable();

        // 添加一列，剩余可处理数量，并赋值
        table.Columns.Add(new DataColumn("_RemainHandleQty_", typeof(decimal)));
        foreach (var row in table.AsEnumerable())
        {
            row["_RemainHandleQty_"] = allocateDefine.GetCanAllocateQty(row);
        }

        return table;
    }

    /// <summary>
    /// 获取满足查询条件的库存集合
    /// </summary>
    /// <param name="billRow">单据行</param>
    /// <param name="stkAllocateRule">规则实体</param>
    /// <param name="allocateDefine">分配信息定义</param>
    /// <returns></returns>
    private List<StkInventory> GetInventoryList(DataRow billRow, StkAllocateRule stkAllocateRule, EntityAllocateDefine allocateDefine)
    {
        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, typeof(StkInventory));

        // 查询字段，只查主表的字段
        var selectFields = columnInfos.Values.Where(u => u.TableInfo.IsRootTable).Select(u => u.FieldName).ToList();

        // 查询条件集合
        var conditions = new List<QueryCondition>
        {
            new(nameof(StkInventory.WarehouseId), DataQueryOp.Equals, billRow.Field<long>(allocateDefine.WarehouseIdField)), // 仓库Id
            new(nameof(StkInventory.MaterialId), DataQueryOp.Equals, billRow.Field<long>(allocateDefine.MaterialIdField)), // 物料Id
            new(nameof(StkInventory.AvailableQty), DataQueryOp.GreaterThan, 0), // 可用数 > 0
        };
        // TODO: 增加仓库、库区、货主数据隔离？

        // 加上配置的库存查询条件
        if (stkAllocateRule.InvFilterEntries != null)
            foreach (var invFilterEntry in stkAllocateRule.InvFilterEntries.Where(u => u.IsEnable))
            {
                if (!string.IsNullOrEmpty(invFilterEntry.ConstValue))
                    conditions.Add(
                        new QueryCondition(invFilterEntry.FieldName, Enum.Parse<DataQueryOp>(invFilterEntry.Op, true), invFilterEntry.ConstValue, invFilterEntry.OrGroup));
                else if (!string.IsNullOrEmpty(invFilterEntry.TargetValueFieldName))
                    conditions.Add(new QueryCondition(invFilterEntry.FieldName, Enum.Parse<DataQueryOp>(invFilterEntry.Op, true), billRow[invFilterEntry.TargetValueFieldName],
                        invFilterEntry.OrGroup));
                // else 不添加
            }

        // 排序集合
        var orderBys = new List<QueryOrderBy>();
        if (stkAllocateRule.InvOrderEntries != null)
            orderBys.AddRange(stkAllocateRule.InvOrderEntries.Select(orderEntry => new QueryOrderBy(orderEntry.FieldName, Enum.Parse<QuerySortType>(orderEntry.SortType, true))));

        // 查询并返回库存集合
        return Rep.Context.Queryable<object>()
            .Where(columnInfos, conditions)
            .Select(columnInfos, selectFields)
            .AddTenantFilter(columnInfos) // 增加租户过滤条件
            .OrderBy(columnInfos, orderBys)
            .ToList() // 从数据库查询数据
            .Select(u => u.Adapt<StkInventory>()) // 映射到实体
            .ToList();
    }

    /// <summary>
    /// 获取满足查询条件的库位集合
    /// </summary>
    /// <param name="billRow">单据行</param>
    /// <param name="stkAllocateRule">规则实体</param>
    /// <param name="allocateDefine">分配信息定义</param>
    /// <returns></returns>
    private List<BdWhLocInvDto> GetWhLocList(DataRow billRow, StkAllocateRule stkAllocateRule, EntityAllocateDefine allocateDefine)
    {
        var columnInfos = _queryDefine.GetEntityTypeColumnInfos(Rep.Context, typeof(BdWhLoc));

        // 查询字段，只查主表的字段
        var selectFields = columnInfos.Values.Where(u => u.TableInfo.IsRootTable).Select(u => u.FieldName).ToList();

        // 查询条件集合
        var conditions = new List<QueryCondition>
        {
            new(nameof(BdWhLoc.WarehouseId), DataQueryOp.Equals, billRow.Field<long>(allocateDefine.WarehouseIdField)), // 仓库Id
        };
        // TODO: 增加仓库、库区、货主数据隔离？

        // 加上配置的库存查询条件
        if (stkAllocateRule.WhLocFilterEntries != null)
            foreach (var whLocFilterEntry in stkAllocateRule.WhLocFilterEntries.Where(u => u.IsEnable))
            {
                if (!string.IsNullOrEmpty(whLocFilterEntry.ConstValue))
                    conditions.Add(
                        new QueryCondition(whLocFilterEntry.FieldName, Enum.Parse<DataQueryOp>(whLocFilterEntry.Op, true), whLocFilterEntry.ConstValue, whLocFilterEntry.OrGroup));
                else if (!string.IsNullOrEmpty(whLocFilterEntry.TargetValueFieldName))
                    conditions.Add(new QueryCondition(whLocFilterEntry.FieldName, Enum.Parse<DataQueryOp>(whLocFilterEntry.Op, true),
                        billRow[whLocFilterEntry.TargetValueFieldName],
                        whLocFilterEntry.OrGroup));
                // else 不添加
            }

        // 排序集合
        var orderBys = new List<QueryOrderBy>();
        if (stkAllocateRule.WhLocOrderEntries != null)
            orderBys.AddRange(stkAllocateRule.WhLocOrderEntries.Select(orderEntry => new QueryOrderBy(orderEntry.FieldName, Enum.Parse<QuerySortType>(orderEntry.SortType, true))));

        // 查询库位集合
        var whLocList = Rep.Context.Queryable<object>()
            .Where(columnInfos, conditions)
            .Select(columnInfos, selectFields)
            .AddTenantFilter(columnInfos) // 增加租户过滤条件
            .OrderBy(columnInfos, orderBys)
            .Take(50) // 最多返回50条数据
            .ToList() // 从数据库查询数据
            .Select(u => u.Adapt<BdWhLocInvDto>()) // 映射到实体
            .ToList();

        // 查询库位库存数量集合
        var invQtyList = Rep.Context.Queryable<StkInventory>()
            .Where(u => whLocList.Select(p => p.Id).Contains(u.WhLocId))
            .GroupBy(u => new { u.WhLocId })
            .Select(u => new { u.WhLocId, Qty = SqlFunc.AggregateSum(u.Qty), PreInQty = SqlFunc.AggregateSum(u.PreInQty) })
            .ToList();

        // 填充已使用数量容量
        whLocList.ForEach(u =>
        {
            var invQty = invQtyList.FirstOrDefault(p => p.WhLocId == u.Id);
            u.UsedQtyCapacity = (invQty?.Qty ?? 0) + (invQty?.PreInQty ?? 0);
        });

        return whLocList;
    }

    /// <summary>
    /// 库位库存数量信息
    /// </summary>
    private class BdWhLocInvDto : BdWhLoc
    {
        /// <summary>
        /// 已使用数量容量
        /// </summary>
        public decimal UsedQtyCapacity { get; set; }
    }
}