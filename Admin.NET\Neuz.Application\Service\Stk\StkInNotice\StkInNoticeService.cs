﻿using Neuz.Application.Model;
using SqlSugar;

namespace Neuz.Application;

/// <summary>
/// 入库通知单服务
/// </summary>
[ApiDescriptionSettings("默认业务分组", Name = "StkInNotice", Order = 100)]
public class StkInNoticeService : StkBaseBillService<StkInNotice>, IDynamicApiController, ITransient
{
    /// <summary>
    /// 实体名称
    /// </summary>
    protected string EntityName => nameof(StkInNotice);

    /// <summary>
    /// 入库通知单服务
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="rep"></param>
    public StkInNoticeService(IServiceProvider serviceProvider, SqlSugarRepository<StkInNotice> rep) : base(serviceProvider, rep)
    {
    }

    protected override List<ListColumn> GetCustomQueryListColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        var listColumns = base.GetCustomQueryListColumns(columnInfos);

        // 处理单据类型的列显示
        var billTypeColumn = listColumns.First(u => u.FieldName == "BillType");
        var stkBillTypeService = ServiceProvider.GetService<StkBillTypeService>();
        var billTypeList = stkBillTypeService.GetBillType(EntityName).GetAwaiter().GetResult();
        billTypeColumn.Options = billTypeList.Select(u => new SelectOption { Value = u.Number, Title = u.Name }).ToList();

        return listColumns;
    }

    protected override List<LiteSearchColumn> GetCustomQueryDefaultSearchColumns(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            new LiteSearchColumn { FieldName = "BillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "EsBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DocumentStatus", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Status", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "Date", Op = DataQueryOp.Between, InputCtrl = InputCtrlType.DateRange },
            new LiteSearchColumn { FieldName = "BillType", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
            new LiteSearchColumn { FieldName = "WarehouseNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "WarehouseName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "DepartmentName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "SupplierName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "CustomerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_MaterialName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_BatchNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_WhAreaName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerNumber", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_OwnerName", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "Entries_SrcBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "OrderBillNo", Op = DataQueryOp.Like, InputCtrl = InputCtrlType.Input },
            new LiteSearchColumn { FieldName = "PushFlag", Op = DataQueryOp.Equals, InputCtrl = InputCtrlType.Select },
        ];
    }

    protected override List<string> GetCustomQueryDefaultFieldNames(IDictionary<string, QueryColumnInfo> columnInfos)
    {
        return
        [
            "BillNo",
            "DocumentStatus",
            "Date",
            "BillType",
            "Status",
            "WarehouseNumber",
            "WarehouseName",
            "DepartmentNumber",
            "DepartmentName",
            "SupplierNumber",
            "SupplierName",
            "CustomerNumber",
            "CustomerName",
            "OrderBillNo",
            "PushFlag",
            "EsBillNo",
            "Entries_Seq",
            "Entries_EntryStatus",
            "Entries_MaterialNumber",
            "Entries_MaterialName",
            "Entries_BatchNo",
            "Entries_ProduceDate",
            "Entries_ExpiryDate",
            "Entries_WhAreaNumber",
            "Entries_WhAreaName",
            "Entries_Qty",
            "Entries_ReceiveQty",
            "Entries_RemainReceiveQty",
            "Entries_InQty",
            "Entries_RemainInQty",
            "Entries_ReturnQty",
            "Entries_UnitNumber",
            "Entries_UnitName",
            "Entries_OwnerNumber",
            "Entries_OwnerName",
            "Entries_AuxPropValueNumber",
            "Entries_AuxPropValueName",
            "Entries_GrossWeight",
            "Entries_PackingVolume",
            "Entries_PackingQty",
            "Entries_Price",
            "Entries_TotalPrice",
            "Entries_SrcBillNo",
            "Entries_SrcBillEntrySeq",
            "Entries_EntryMemo",
            "CreateTime",
            "CreateUserName",
            "UpdateTime",
            "UpdateUserName",
            "IsCancel",
            "CancelTime",
            "CancelUserName",
            "Entries_MadeQty",
        ];
    }

    protected override void BeforeCustomQueryPage(IDictionary<string, QueryColumnInfo> columnInfos, CustomPageInput input, ISugarQueryable<object> query)
    {
        query.AddWarehouseFilter(columnInfos, ServiceProvider, "WarehouseId"); // 仓库权限
        query.AddWhAreaFilter(columnInfos, ServiceProvider, "Entries_WhAreaId"); // 库区权限
        query.AddOwnerFilter(columnInfos, ServiceProvider, "Entries_OwnerId"); // 货主权限
        base.BeforeCustomQueryPage(columnInfos, input, query);
    }

    protected override void OnBeforeAdd(StkInNotice entity)
    {
        base.OnBeforeAdd(entity);
        BeforeSaveHandle(entity);
    }

    protected override void OnBeforeUpdate(StkInNotice entity)
    {
        base.OnBeforeUpdate(entity);
        BeforeSaveHandle(entity);
    }

    private void BeforeSaveHandle(StkInNotice entity)
    {
        // 判断仓库是否已填
        if (entity.WarehouseId == 0) throw Oops.Bah(StkErrorCode.Stk1023);

        // 判断单据类型是否存在
        if (!Rep.Context.Queryable<StkBillType>().Any(u => u.EntityName == EntityName && u.Number == entity.BillType))
            throw Oops.Bah(StkErrorCode.Stk1024, entity.BillType);

        if (entity.Entries == null) return;

        // 填充实体辅助属性值，并保存
        AuxPropValueService.FillAndSaveValue(entity.Entries, "MaterialId", "AuxPropValueId", "AuxPropValue", false);

        var materialIds = entity.Entries.Select(u => u.MaterialId).Distinct().ToList();
        var materials = Rep.Change<BdMaterial>().AsQueryable().IncludeNavCol().Where(u => materialIds.Contains(u.Id)).ToList();

        foreach (var entry in entity.Entries)
        {
            // 重新计算剩余数量
            entry.RemainReceiveQty = entry.Qty - entry.ReceiveQty;
            entry.RemainInQty = entry.Qty - entry.InQty;

            // 判断物料是否已填
            if (entry.MaterialId == 0) throw Oops.Bah(StkErrorCode.Stk1020);
            // 判断单位是否已填
            if (entry.UnitId == 0) throw Oops.Bah(StkErrorCode.Stk1021);
            // 判断货主是否已填
            if (entry.OwnerId == 0) throw Oops.Bah(StkErrorCode.Stk1022);
            // 判断数量是否小于等于0
            if (entry.Qty <= 0) throw Oops.Bah(StkErrorCode.Stk1026);

            var materialInfo = materials.First(u => u.Id == entry.MaterialId);
            // 批号判断
            if (!materialInfo.IsBatchManage && !string.IsNullOrEmpty(entry.BatchNo)) throw Oops.Bah(StkErrorCode.Stk1002, materialInfo.Number);

            // 保质期判断
            if (!materialInfo.IsKfPeriod && entry.ProduceDate != null) throw Oops.Bah(StkErrorCode.Stk1027, materialInfo.Number);
            if (!materialInfo.IsKfPeriod && entry.ExpiryDate != null) throw Oops.Bah(StkErrorCode.Stk1028, materialInfo.Number);
        }
    }

    /// <summary>
    /// 获取当前单据的所有来源单据标识
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<string> GetSrcBillKeys(StkInNotice entity)
    {
        return entity.Entries.Where(u => !string.IsNullOrWhiteSpace(u.SrcBillKey)).Select(u => u.SrcBillKey).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据指定来源单据标识的所有来源单据Id
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="srcBillKey"></param>
    /// <returns></returns>
    private List<long> GetSrcBillIds(StkInNotice entity, string srcBillKey)
    {
        return entity.Entries.Where(u => u.SrcBillKey == srcBillKey && u.SrcBillId != null).Select(u => u.SrcBillId.Value).Distinct().ToList();
    }

    /// <summary>
    /// 获取当前单据所有来源任务Id
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private List<long> GetSrcTaskIds(StkInNotice entity)
    {
        return new List<long>();
    }

    protected override void OnAfterAudit(StkInNotice entity)
    {
        base.OnAfterAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    protected override void OnBeforeUnAudit(StkInNotice entity)
    {
        base.OnBeforeUnAudit(entity);

        if (entity.PushFlag == PushFlag.Success)
            throw Oops.Bah(StkErrorCode.Stk1019, entity.BillNo);
        if (entity.Status != StkInNoticeStatus.UnHandle)
            throw Oops.Bah(StkErrorCode.StkInNotice1001, entity.BillNo);

        // TODO: 入库通知单反审核，当下游单据创建了单据，但未审核时，无法根据业务状态或者回写的数量来判断是否能够反审核
    }

    protected override void OnAfterUnAudit(StkInNotice entity)
    {
        base.OnAfterUnAudit(entity);

        // 上游单据刷新状态
        SrcBillRefreshStatus(entity, GetSrcBillKeys, GetSrcBillIds, GetSrcTaskIds);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="billNos">单据编号集合</param>
    [HttpPost("refreshStatus")]
    public void RefreshStatus(List<string> billNos)
    {
        var ids = Rep.Context.Queryable<StkInNotice>().Where(u => billNos.Contains(u.BillNo)).Select(u => u.Id).ToList();
        RefreshStatus(ids);
    }

    /// <summary>
    /// 刷新单据状态
    /// </summary>
    /// <param name="ids"></param>
    [NonAction]
    public void RefreshStatus(List<long> ids)
    {
        // TODO: 手动完结状态需要跳过处理

        var srcBillKey = EntityName;

        // 查询
        var entities = Rep.Context.Queryable<StkInNotice>().Includes(u => u.Entries).Where(u => ids.Contains(u.Id)).ToList();

        foreach (var entity in entities)
        {
            // 收货单明细集合
            var stkReceiveEntryList = Rep.Context.Queryable<StkReceiveEntry>()
                .LeftJoin<StkReceive>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve && te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id)
                .ToList();

            // 入库单明细集合
            var stkInStockEntryList = Rep.Context.Queryable<StkInStockEntry>()
                .LeftJoin<StkInStock>((te, t) => te.Id == t.Id)
                .Where((te, t) => t.DocumentStatus == DocumentStatus.Approve &&
                                  ((te.SrcBillKey == srcBillKey && te.SrcBillId == entity.Id /*上游为入库通知单，则为直接入库*/) ||
                                   (te.SrcBillKey == nameof(StkReceive) && stkReceiveEntryList.Select(u => u.Id).Contains(te.SrcBillId.Value)) /*上游为收货单*/)
                )
                .ToList();

            foreach (var entry in entity.Entries)
            {
                // 实际收货数量
                var realReceiveQty = stkReceiveEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.Qty - u.ReturnQty);
                // 实际入库数量
                var realInQty = stkInStockEntryList
                    .Where(u => (u.SrcBillKey == srcBillKey && u.SrcBillEntryId == entry.EntryId /*上游为入库通知单，则为直接入库*/) ||
                                (u.SrcBillKey == nameof(StkReceive) &&
                                 stkReceiveEntryList.Where(p => p.SrcBillEntryId == entry.EntryId).Select(p => p.EntryId).Contains(u.SrcBillEntryId ?? 0) /*上游为收货单*/)
                    )
                    .Sum(u => u.Qty - u.ReturnQty);
                // 已退货数量（收货后退货 + 上架后退货）
                var returnQty = stkReceiveEntryList.Where(u => u.SrcBillEntryId == entry.EntryId).Sum(u => u.ReturnQty);

                entry.ReturnQty = returnQty;
                entry.ReceiveQty = realReceiveQty;
                entry.RemainReceiveQty = entry.Qty - entry.ReceiveQty < 0 ? 0 : entry.Qty - entry.ReceiveQty;

                entry.InQty = realInQty;
                entry.RemainInQty = entry.Qty - entry.InQty < 0 ? 0 : entry.Qty - entry.InQty;

                if (entry.InQty >= entry.Qty)
                    entry.EntryStatus = StkInNoticeEntryStatus.Finish;
                else if (entry.ReceiveQty > 0 || entry.InQty > 0)
                    entry.EntryStatus = StkInNoticeEntryStatus.Handling;
                else
                    entry.EntryStatus = StkInNoticeEntryStatus.UnHandle;
            }

            if (entity.Entries.All(u => u.EntryStatus == StkInNoticeEntryStatus.Finish))
                entity.Status = StkInNoticeStatus.Finish;
            else if (entity.Entries.Any(u => u.EntryStatus is StkInNoticeEntryStatus.Handling or StkInNoticeEntryStatus.Finish))
                entity.Status = StkInNoticeStatus.Handling;
            else
                entity.Status = StkInNoticeStatus.UnHandle;

            // 保存明细
            Rep.Context.Updateable(entity.Entries).ExecuteCommand();
            // 保存单据头
            Rep.Context.Updateable(entity).ExecuteCommand();
        }
    }
}