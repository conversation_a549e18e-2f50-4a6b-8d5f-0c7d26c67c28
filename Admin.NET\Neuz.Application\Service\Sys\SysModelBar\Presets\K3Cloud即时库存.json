{"$schema": "http://barModelSchema.json", "modelServiceName": "K3CloudBarModelService", "modelParams": {"billTypeofIReportData": "", "billDataMaterialNumberFieldName": "MaterialNumber", "billDataQtyFieldName": "Qty", "isStrictControlMadeQty": false, "perBarcodeQtyFieldName": "", "defaultPerBarcodeQty": 0}, "modelSchema": {"billSearchColumns": [{"fieldName": "FMaterialId.FNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FMaterialId.FName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FLot.FNumber", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FProduceDate", "title": "生产日期", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "FExpiryDate", "title": "有效期至", "inputCtrl": "DateRange", "op": "Between"}, {"fieldName": "FStockId.FNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockId.FName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockLocId.FNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockLocId.FName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FAuxPropId.FNumber", "title": "辅助属性编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FAuxPropId.FName", "title": "辅助属性名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockOrgId.FNumber", "title": "库存组织编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "FStockOrgId.FName", "title": "库存组织名称", "inputCtrl": "Input", "op": "Like"}], "billListColumns": [{"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "sortable": true}, {"fieldName": "MaterialSpec", "title": "规格型号"}, {"fieldName": "Qty", "title": "库存量"}, {"fieldName": "UnitNumber", "title": "库存主单位编码"}, {"fieldName": "UnitName", "title": "库存主单位名称"}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "_ExpUnit_", "title": "保质期单位"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "sortable": true}, {"fieldName": "StockName", "title": "仓库名称", "sortable": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "sortable": true}, {"fieldName": "StockLocName", "title": "仓位名称", "sortable": true}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "FStockOrgIdNumber", "title": "库存组织编码"}, {"fieldName": "FStockOrgIdName", "title": "库存组织名称"}], "barcodeSearchColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "Status", "title": "条码状态", "inputCtrl": "Select", "op": "Equals", "optionType": "BarcodeStatus"}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateUserName", "title": "创建者姓名", "inputCtrl": "Input", "op": "Like"}, {"fieldName": "CreateTime", "title": "创建时间", "inputCtrl": "DateRange", "op": "Between"}], "barcodeListColumns": [{"fieldName": "Id", "title": "主键Id", "isHide": true}, {"fieldName": "Barcode", "title": "条码", "width": 200, "sortable": true}, {"fieldName": "BarcodeType", "title": "条码类型", "width": 140, "optionType": "BarcodeType"}, {"fieldName": "Status", "title": "条码状态", "optionType": "BarcodeStatus"}, {"fieldName": "MaterialNumber", "title": "物料编码", "sortable": true}, {"fieldName": "MaterialName", "title": "物料名称", "width": 200, "sortable": true}, {"fieldName": "MaterialShortNumber", "title": "物料短编码"}, {"fieldName": "MaterialSpec", "title": "规格型号", "width": 200}, {"fieldName": "BatchNo", "title": "批号", "sortable": true}, {"fieldName": "Qty", "title": "数量"}, {"fieldName": "UnitNumber", "title": "单位编码"}, {"fieldName": "UnitName", "title": "单位名称"}, {"fieldName": "IsBatchManage", "title": "启用批号管理", "format": "Boolean"}, {"fieldName": "IsSnManage", "title": "启用序列号管理", "format": "Boolean"}, {"fieldName": "ExpPeriod", "title": "保质期"}, {"fieldName": "ExpUnit", "title": "保质期单位", "optionType": "ExpUnit"}, {"fieldName": "ProduceDate", "title": "生产日期", "format": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "format": "Date"}, {"fieldName": "AuxPropNumber", "title": "辅助属性编码"}, {"fieldName": "AuxPropName", "title": "辅助属性名称"}, {"fieldName": "StockNumber", "title": "仓库编码"}, {"fieldName": "StockName", "title": "仓库名称"}, {"fieldName": "StockLocNumber", "title": "仓位编码"}, {"fieldName": "StockLocName", "title": "仓位名称"}, {"fieldName": "IsOnceInStock", "title": "是否曾经入库", "format": "Boolean"}, {"fieldName": "IsInStock", "title": "是否在库", "format": "Boolean"}, {"fieldName": "LastPrintTime", "title": "最后打印时间", "width": 140, "format": "DateTime"}, {"fieldName": "PrintedQty", "title": "已打印数量"}, {"fieldName": "LastPrintUserName", "title": "最后打印者名称"}, {"fieldName": "CreateTime", "title": "创建时间", "width": 140, "format": "DateTime"}, {"fieldName": "UpdateTime", "title": "更新时间", "width": 140, "format": "DateTime"}, {"fieldName": "CreateUserName", "title": "创建者名称"}, {"fieldName": "UpdateUserName", "title": "修改者名称"}], "barcodeEditColumns": [{"fieldName": "Barcode", "title": "条码", "inputCtrl": "Input"}, {"fieldName": "BarcodeType", "title": "条码类型", "inputCtrl": "Select", "optionType": "BarcodeType"}, {"fieldName": "MaterialNumber", "title": "物料编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialName", "title": "物料名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "MaterialSpec", "title": "规格型号", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "BatchNo", "title": "批号", "inputCtrl": "Input"}, {"fieldName": "Qty", "title": "数量", "inputCtrl": "InputNumber"}, {"fieldName": "UnitNumber", "title": "单位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "UnitName", "title": "单位名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpPeriod", "title": "保质期", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "ExpUnit", "title": "保质期单位", "inputCtrl": "Input", "optionType": "ExpUnit", "readOnly": true}, {"fieldName": "ProduceDate", "title": "生产日期", "inputCtrl": "Date"}, {"fieldName": "ExpiryDate", "title": "有效期至", "inputCtrl": "Date"}, {"fieldName": "StockNumber", "title": "仓库编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockName", "title": "仓库名称", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocNumber", "title": "仓位编码", "inputCtrl": "Input", "readOnly": true}, {"fieldName": "StockLocName", "title": "仓位名称", "inputCtrl": "Input", "readOnly": true}]}, "billDataQuery": {"type": "K3Cloud", "idFieldName": "FID", "entryIdFieldName": "", "content": "{\r\n    \"$schema\": \"http://k3CloudDataQuerySchema.json\",\r\n    \"formId\": \"STK_Inventory\",\r\n    \"fieldKeys\": [\r\n        \"FID AS _id\",\r\n        \"FMaterialId.FMasterId AS MaterialId\",\r\n        \"FMaterialId.FNumber AS MaterialNumber\",\r\n        \"FMaterialId.FName AS MaterialName\",\r\n        \"FMaterialId.FSpecification AS MaterialSpec\",\r\n        \"FQty AS Qty\",\r\n        \"FStockUnitId AS UnitId\",\r\n        \"FStockUnitId.FNumber AS UnitNumber\",\r\n        \"FStockUnitId.FName AS UnitName\",\r\n        \"FLot.FNumber AS BatchNo\",\r\n        \"FMaterialId.FIsBatchManage AS IsBatchManage\",\r\n        \"FMaterialId.FIsKFPeriod AS IsKfPeriod\",\r\n        \"FMaterialId.FIsSNManage AS IsSnManage\",\r\n        \"FMaterialId.FExpPeriod AS ExpPeriod\",\r\n        \"FMaterialId.FExpUnit AS _ExpUnit_\",\r\n        \"FProduceDate AS ProduceDate\",\r\n        \"FExpiryDate AS ExpiryDate\",\r\n        \"FStockId AS StockId\",\r\n        \"FStockId.FNumber AS StockNumber\",\r\n        \"FStockId.FName AS StockName\",\r\n        \"FStockLocId AS StockLocId\",\r\n        \"FStockLocId.FNumber AS StockLocNumber\",\r\n        \"FStockLocId.FName AS StockLocName\",\r\n        \"FAuxPropId AS AuxPropId\",\r\n        \"FAuxPropId.FNumber AS AuxPropNumber\",\r\n        \"FAuxPropId.FName AS AuxPropName\",\r\n        \"FStockOrgId\",\r\n        \"FStockOrgId.FNumber AS FStockOrgIdNumber\",\r\n        \"FStockOrgId.FName AS FStockOrgIdName\",\r\n        \"FBaseQty\",\r\n        \"FBaseUnitId\",\r\n        \"FBaseUnitId.FNumber AS FBaseUnitIdNumber\",\r\n        \"FBaseUnitId.FName AS FBaseUnitIdName\"\r\n    ],\r\n    \"filters\": [\r\n        {\r\n            \"fieldKey\": \"FBaseQty\",\r\n            \"op\": \"GreaterThan\",\r\n            \"value\": 0\r\n        }\r\n    ],\r\n    \"customFilter\": \"\"\r\n}"}}