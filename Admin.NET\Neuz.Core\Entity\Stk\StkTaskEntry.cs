﻿namespace Neuz.Core.Entity;

/// <summary>
/// 库存任务明细
/// </summary>
[SugarTable(null, "库存任务明细")]
public class StkTaskEntry : EntryEntityBase
{
    /// <summary>
    /// 明细业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "明细业务状态")]
    public StkTaskEntryStatus EntryStatus { get; set; }

    /// <summary>
    /// 物料Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物料Id")]
    public long MaterialId { get; set; }

    /// <summary>
    /// 物料
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(MaterialId))]
    [CustomSerializeFields]
    public BdMaterial Material { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnDescription = "批号", Length = 100)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 批号档案
    /// </summary>
    [Navigate(NavigateType.Dynamic, "[{m:\"BatchNo\",c:\"BatchNo\"},{m:\"MaterialId\",c:\"MaterialId\"}]")]
    [CustomSerializeFields(false, "Id", "BatchNo")]
    public BdBatchFile BatchFile { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnDescription = "生产日期")]
    public DateTime? ProduceDate { get; set; }

    /// <summary>
    /// 有效期至
    /// </summary>
    [SugarColumn(ColumnDescription = "有效期至")]
    public DateTime? ExpiryDate { get; set; }

    /// <summary>
    /// 来源库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源库区Id")]
    public long? SrcWhAreaId { get; set; }

    /// <summary>
    /// 来源库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea SrcWhArea { get; set; }

    /// <summary>
    /// 来源库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源库位Id")]
    public long? SrcWhLocId { get; set; }

    /// <summary>
    /// 来源库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc SrcWhLoc { get; set; }

    /// <summary>
    /// 来源容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源容器Id")]
    public long? SrcContainerId { get; set; }

    /// <summary>
    /// 来源容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SrcContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer SrcContainer { get; set; }

    /// <summary>
    /// 目标库区Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标库区Id")]
    public long? DestWhAreaId { get; set; }

    /// <summary>
    /// 目标库区
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhAreaId))]
    [CustomSerializeFields]
    public BdWhArea DestWhArea { get; set; }

    /// <summary>
    /// 目标库位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标库位Id")]
    public long? DestWhLocId { get; set; }

    /// <summary>
    /// 目标库位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestWhLocId))]
    [CustomSerializeFields]
    public BdWhLoc DestWhLoc { get; set; }

    /// <summary>
    /// 目标容器Id
    /// </summary>
    [SugarColumn(ColumnDescription = "目标容器Id")]
    public long? DestContainerId { get; set; }

    /// <summary>
    /// 目标容器
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DestContainerId))]
    [CustomSerializeFields(false, "Id", "Number")]
    public BdContainer DestContainer { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnDescription = "数量")]
    public decimal Qty { get; set; }

    /// <summary>
    /// 已执行数量
    /// </summary>
    [SugarColumn(ColumnDescription = "已执行数量")]
    public decimal ExecQty { get; set; }

    /// <summary>
    /// 剩余执行数量
    /// </summary>
    [SugarColumn(ColumnDescription = "剩余执行数量")]
    public decimal RemainExecQty { get; set; }

    /// <summary>
    /// 单位Id
    /// </summary>
    [SugarColumn(ColumnDescription = "单位Id")]
    public long UnitId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UnitId))]
    [CustomSerializeFields]
    public BdUnit Unit { get; set; }

    /// <summary>
    /// 货主Id
    /// </summary>
    [SugarColumn(ColumnDescription = "货主Id")]
    public long OwnerId { get; set; }

    /// <summary>
    /// 货主
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OwnerId))]
    [CustomSerializeFields]
    public BdOwner Owner { get; set; }

    /// <summary>
    /// 毛重
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的毛重
    /// </remarks>
    [SugarColumn(ColumnDescription = "毛重")]
    public decimal GrossWeight { get; set; }

    /// <summary>
    /// 包装体积
    /// </summary>
    /// <remarks>
    /// 默认计算规则：数量 * 物料的包装体积
    /// </remarks>
    [SugarColumn(ColumnDescription = "包装体积")]
    public decimal PackingVolume { get; set; }

    /// <summary>
    /// 箱数
    /// </summary>
    /// <remarks>
    /// 默认计算规则：[Ceiling]向上取整(数量 / 物料的标准装箱量)
    /// </remarks>
    [SugarColumn(ColumnDescription = "箱数")]
    public int PackingQty { get; set; }

    /// <summary>
    /// 来源单据分录序号
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录序号")]
    public int SrcBillEntrySeq { get; set; }

    /// <summary>
    /// 来源单据分录Id
    /// </summary>
    [SugarColumn(ColumnDescription = "来源单据分录Id")]
    public long SrcBillEntryId { get; set; }

    /// <summary>
    /// 是否允许更换库位
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许更换库位")]
    public bool IsAllowLocReplace { get; set; }

    /// <summary>
    /// 是否允许更换批号
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许更换批号")]
    public bool IsAllowBatchNoReplace { get; set; }

    /// <summary>
    /// 关联锁定的库存Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联锁定的库存Id")]
    public long? RelLockInvId { get; set; }

    /// <summary>
    /// 关联预入库的库存Id
    /// </summary>
    [SugarColumn(ColumnDescription = "关联预入库的库存Id")]
    public long? RelPreInInvId { get; set; }

    /// <summary>
    /// 手动关闭时间
    /// </summary>
    [SugarColumn(ColumnDescription = "手动关闭时间")]
    public DateTime? ManualCloseTime { get; set; }

    /// <summary>
    /// 手动关闭操作人Id
    /// </summary>
    [SugarColumn(ColumnDescription = "手动关闭操作人Id")]
    public long? ManualCloseUserId { get; set; }

    /// <summary>
    /// 手动关闭操作人姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "手动关闭操作人姓名", Length = 50)]
    public string? ManualCloseUserName { get; set; }

    /// <summary>
    /// 明细备注
    /// </summary>
    [SugarColumn(ColumnDescription = "明细备注", Length = 255)]
    public string? EntryMemo { get; set; }

    /// <summary>
    /// 使用的分配规则编码
    /// </summary>
    [SugarColumn(ColumnDescription = "使用的分配规则编码", Length = 80)]
    public string? UsedRuleNumber { get; set; }

    /// <summary>
    /// 使用的分配规则名称
    /// </summary>
    [SugarColumn(ColumnDescription = "使用的分配规则名称", Length = 255)]
    public string? UsedRuleName { get; set; }

    /// <summary>
    /// 锁定数量
    /// </summary>
    [SugarColumn(ColumnDescription = "锁定数量")]
    public decimal LockQty { get; set; }
    
    /// <summary>
    /// 被释放锁定数量
    /// </summary>
    [SugarColumn(ColumnDescription = "被释放锁定数量")]
    public decimal ReleaseLockQty { get; set; }

    /// <summary>
    /// 是否推送退货通知单
    /// </summary>
    [SugarColumn(ColumnDescription = "是否推送")]
    public int IsPush { get; set; }

    /// <summary>
    /// 推送的退货通知单明细ID
    /// </summary>
    [SugarColumn(ColumnDescription = "退货通知单明细ID", Length = 60)]
    public string? MrAppEntryIds { get; set; }

    /// <summary>
    /// 推送的退货通知单号
    /// </summary>

    [SugarColumn(ColumnDescription = "MrAppBillNo", Length = 60)]
    public string? MrAppBillNo { get; set; }

}