using Neuz.Application.Adapter.K3Cloud;
using Neuz.Application.Adapter.K3Cloud.ApiClient.Param;
using Neuz.Application.ExternalSystem.Dto;
using SqlSugar;

namespace Neuz.Application.ExternalSystem.K3Cloud.Push.Bill;

/// <summary>
/// K3Cloud PUR_MRAPP2 收料单推送服务（通过批次查询收料单，根据供应商分组）
/// </summary>
/// <remarks>
/// 金蝶FormId: PUR_MRAPP2
/// </remarks>
[Injection(Named = "K3Cloud:PurMrapp2InStock")]
public class K3CloudPURMRAPP2InStockPushService : K3CloudBasePushService<StkInStockPushData>
{
    public K3CloudPURMRAPP2InStockPushService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider) :
        base(scopeFactory, serviceProvider)
    {
    }

    protected override BasePushHandle<StkInStockPushData> GetPushQuery()
    {
        return new InnerPushQuery(ServiceProvider, Rep.Context);
    }

    protected override string RuleId => "";

    protected override string SourceFormId => ""; // 无源单

    protected override bool IsSumQtyToBeforePush => false; // 项目需求，不能汇总，数据需要按单据的行和金蝶生成的单据行一一匹配

    protected override async Task AfterSetPushObject(EsSyncPushSetting pushSetting, StkInStockPushData localObject,
        Dictionary<string, object> pushObject)
    {
        await base.AfterSetPushObject(pushSetting, localObject, pushObject);

        // 添加供应商信息（从收货单获取）
        if (localObject.Bill_Supplier != null)
        {
            pushObject["FSupplierId.FNumber"] = localObject.Bill_Supplier.EsNumber ?? localObject.Bill_Supplier.Number;
            pushObject["F_PBGK_Supplier"] = localObject.Bill_Supplier.Name;
        }

        // 添加通知单外部编号（从收货单明细获取）
        if (localObject.ReceiveEntry != null && !string.IsNullOrEmpty(localObject.ReceiveEntry.NoticeEsBillNo))
        {
            pushObject["F_PBGK_NoticeEsBillNo"] = localObject.ReceiveEntry.NoticeEsBillNo;
        }

        pushObject["FDate"] = DateTime.Now;
        pushObject["F_PBGK_PdaUser"] = localObject.Bill.CreateUserName;
        
        // 根据单据类型设置不同的字段
        switch (localObject.Bill.BillType)
        {
            case "SCRKD":
                pushObject["F_PBGK_BillType"] = "生产入库";
                break;
            case "CGRKD":
                pushObject["F_PBGK_BillType"] = "采购入库";
                break;
            default:
                pushObject["F_PBGK_BillType"] = localObject.Bill.BillType;
                break;
        }

        // 设置明细字段
        pushObject["[FBillEntry].FMaterialId.FNumber"] = localObject.Entry_Material.EsNumber ?? localObject.Entry_Material.Number;
        pushObject["[FBillEntry].FQty"] = localObject.Entry.Qty;
        pushObject["[FBillEntry].FUnitID.FNumber"] = localObject.Entry_Unit.EsNumber ?? localObject.Entry_Unit.Number;
        pushObject["[FBillEntry].FLot.FNumber"] = localObject.Entry.BatchNo;
        pushObject["[FBillEntry].F_PBGK_kc7Seq"] = localObject.Entry.Seq;

        pushObject["[FBillEntry].FProduceDate"] = localObject.Entry.ProduceDate;
        pushObject["[FBillEntry].FExpiryDate"] = localObject.Entry.ExpiryDate;

        // 设置源单信息（如果有收货单）
        if (localObject.Receive != null)
        {
            pushObject["[FBillEntry]._SourceBillId_"] = localObject.Receive.EsId;
            pushObject["[FBillEntry]._SourceBillEntryId_"] = localObject.ReceiveEntry?.EsEntryId;
        }
    }

    /// <summary>
    /// 推送查询
    /// </summary>
    private class InnerPushQuery : StkInStockPushHandle
    {
        /// <summary>
        /// K3Cloud 接口
        /// </summary>
        protected K3CloudInterface K3CloudInterface { get; }

        public InnerPushQuery(IServiceProvider serviceProvider, ISqlSugarClient context) : base(serviceProvider,
            context)
        {
            K3CloudInterface = serviceProvider.GetService<K3CloudInterface>();
        }

        protected override List<string> QueryBillTypes => new() { "SCRKD", "CGRKD" };

        /// <summary>
        /// 重写查询方法，通过批次查询收货单并根据供应商分组
        /// </summary>
        public override async Task<List<StkInStockPushData>> QueryLocalObject(DateTime? queryBeginTime = null, DateTime? queryEndTime = null, IList<string> queryBillNos = null)
        {
            // 先获取基础的入库单数据
            var baseResult = await base.QueryLocalObject(queryBeginTime, queryEndTime, queryBillNos);

            // 通过批次查询收货单并获取供应商信息
            var enhancedResult = new List<StkInStockPushData>();

            foreach (var inStockData in baseResult)
            {
                // 通过批次和物料查询收货单
                var receiveInfo = await QueryReceiveByBatch(inStockData.Entry.MaterialId, inStockData.Entry.BatchNo);

                // 创建增强的推送数据，包含收货单的供应商信息
                var enhancedData = new StkInStockPushData
                {
                    InNotice = inStockData.InNotice,
                    InNoticeEntry = inStockData.InNoticeEntry,
                    Receive = receiveInfo?.Receive,
                    ReceiveEntry = receiveInfo?.ReceiveEntry,
                    BillId = inStockData.BillId,
                    BillNo = inStockData.BillNo,
                    CreateUser = inStockData.CreateUser,
                    Bill = inStockData.Bill,
                    Bill_Warehouse = inStockData.Bill_Warehouse,
                    Bill_Department = inStockData.Bill_Department,
                    Entry = inStockData.Entry,
                    Entry_Material = inStockData.Entry_Material,
                    Entry_BatchFile = inStockData.Entry_BatchFile,
                    Entry_SrcWhArea = inStockData.Entry_SrcWhArea,
                    Entry_SrcWhLoc = inStockData.Entry_SrcWhLoc,
                    Entry_DestWhArea = inStockData.Entry_DestWhArea,
                    Entry_DestWhLoc = inStockData.Entry_DestWhLoc,
                    Entry_DestContainer = inStockData.Entry_DestContainer,
                    Entry_Unit = inStockData.Entry_Unit,
                    Entry_Owner = inStockData.Entry_Owner,
                    // 添加收货单的供应商信息
                    Bill_Supplier = receiveInfo?.Receive?.Supplier,
                    Bill_Customer = receiveInfo?.Receive?.Customer
                };

                enhancedResult.Add(enhancedData);
            }

            return enhancedResult;
        }

        /// <summary>
        /// 通过批次和物料查询收货单信息
        /// </summary>
        private async Task<ReceiveInfo> QueryReceiveByBatch(long materialId, string batchNo)
        {
            if (string.IsNullOrEmpty(batchNo))
                return null;

            // 查询收货单明细，通过批次和物料匹配
            var receiveEntry = await Context.Queryable<StkReceiveEntry>()
                .Where(e => e.MaterialId == materialId && e.BatchNo == batchNo)
                .OrderByDescending(e => e.CreateTime) // 取最新的收货记录
                .FirstAsync();

            if (receiveEntry == null)
                return null;

            // 查询对应的收货单，包含供应商信息
            var receive = await Context.Queryable<StkReceive>()
                .Includes(s => s.Supplier)
                .Includes(s => s.Customer)
                .Where(s => s.Id == receiveEntry.Id) // receiveEntry.Id 是关联到收货单主表的外键
                .FirstAsync();

            return new ReceiveInfo
            {
                Receive = receive,
                ReceiveEntry = receiveEntry
            };
        }

        /// <summary>
        /// 收货单信息
        /// </summary>
        private class ReceiveInfo
        {
            public StkReceive Receive { get; set; }
            public StkReceiveEntry ReceiveEntry { get; set; }
        }

        public override async Task UpdateFlag(List<StkInStockPushData> localObjects, EsSyncPushResult esSyncPushResult,
            bool isByLastPushTime)
        {
            await base.UpdateFlag(localObjects, esSyncPushResult, isByLastPushTime);

            if (!esSyncPushResult.IsSuccess) return;

            // 项目需求，提交成功后，重新查询一次生成的单据，获取相关信息进行回写
            var firstLocalObject = localObjects.First();

            var client = K3CloudInterface.GetK3CloudClient();
            var billList = await client.QueryBillData(new QueryBillParam
            {
                FormId = "PUR_MRAPP2",
                FieldKeys = new List<string>
                {
                    "FID",
                    "FBillNo",
                    "FBillEntry_FEntryID AS FEntryID",
                    "FBillEntry_FSeq AS FEntrySeq",
                },
                Filters = new List<QueryFilter> { new("FBillNo", QueryType.Equals, esSyncPushResult.TargetBillNo) }
            });

            // 入库单
            var stkInStock = await Context.Queryable<StkInStock>().Includes(u => u.Entries)
                .FirstAsync(u => u.Id == firstLocalObject.Bill.Id);
            stkInStock.EsId = esSyncPushResult.TargetId;
            stkInStock.EsBillNo = esSyncPushResult.TargetBillNo;
            // 保存入库单变更
            await Context.Updateable(stkInStock).ExecuteCommandAsync();

            foreach (var entry in stkInStock.Entries)
            {
                var record = billList.FirstOrDefault(u => Convert.ToInt32(u["FEntrySeq"]) == entry.Seq);
                if (record == null)
                    continue;

                entry.EsEntryId = record["FEntryID"] + "";
            }

            // 保存入库单明细变更
            await Context.Updateable(stkInStock.Entries).ExecuteCommandAsync();
        }
    }
}
