﻿namespace Neuz.Core.Enum;

/// <summary>
/// 条码类型
/// </summary>
public enum BarcodeType
{
    /// <summary>
    /// 标准(一物一码)
    /// </summary>
    [Description("标准(一物一码)"), Theme("primary")]
    Standard = 0,

    /// <summary>
    /// 可扣减(一物一码)
    /// </summary>
    [Description("可扣减(一物一码)"), Theme("success")]
    Deduct = 1,

    /// <summary>
    /// 容器(同物一码)
    /// </summary>
    /// <remarks></remarks>
    [Description("容器(同物一码)"), Theme("warning")]
    Container = 2,
}