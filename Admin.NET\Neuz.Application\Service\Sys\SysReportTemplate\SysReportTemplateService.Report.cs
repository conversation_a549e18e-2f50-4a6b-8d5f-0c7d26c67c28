﻿using Stimulsoft.Report;
using Stimulsoft.Report.Dictionary;

namespace Neuz.Application;

public partial class SysReportTemplateService
{
    /// <summary>
    /// 将 Id 集合放入缓存，返回一个缓存标识，缓存有效时间2小时
    /// </summary>
    /// <returns></returns>
    [HttpPost("exchangeToCacheId")]
    public Task<string> ExchangeToCacheId(StringIdsInput input)
    {
        var cacheId = YitIdHelper.NextId() + "";
        CacheService.Set(CacheConst.ExchangeCacheId + cacheId, input.Ids, TimeSpan.FromHours(2));
        return Task.FromResult(cacheId);
    }

    /// <summary>
    /// 删除缓存标识
    /// </summary>
    /// <returns></returns>
    [HttpPost("deleteCacheId")]
    public Task DeleteCacheId(string cacheId)
    {
        CacheService.Remove(CacheConst.ExchangeCacheId + cacheId);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 根据缓存标识从缓存中返回 Id 集合
    /// </summary>
    /// <returns></returns>
    [HttpPost("exchangeFromCacheId")]
    public Task<List<string>> ExchangeFromCacheId(string cacheId)
    {
        return Task.FromResult(CacheService.Get<List<string>>(CacheConst.ExchangeCacheId + cacheId));
    }

    /// <summary>
    /// 获取报表
    /// </summary>
    /// <param name="templateId">报表模板Id</param>
    /// <param name="typeofIReportData">实现 <see cref="IReportData"/> 或 <see cref="IReportDataSet"/> 接口的类型名称（命名空间.类名, 程序集名）</param>
    /// <param name="ids"><see cref="string"/> 主键集合输入参数</param>
    /// <returns></returns>
    [NonAction]
    public async Task<StiReport> GetReport(long templateId, string typeofIReportData, List<string> ids)
    {
        var reportTemplate = await GetAsync(new IdInput { Id = templateId });
        if (reportTemplate == null)
            throw Oops.Bah(SysErrorCode.SysReportTemplate1001, templateId);

        if (string.IsNullOrEmpty(typeofIReportData))
            throw Oops.Bah(SysErrorCode.SysReportTemplate1002);

        var reportDataType = Type.GetType(typeofIReportData);
        if (reportDataType == null)
            throw Oops.Bah(SysErrorCode.SysReportTemplate1003, typeofIReportData);
        if (!typeof(IReportData).IsAssignableFrom(reportDataType) && !typeof(IReportDataSet).IsAssignableFrom(reportDataType))
            throw Oops.Bah(SysErrorCode.SysReportTemplate1004, typeofIReportData);
        var reportDataService = ServiceProvider.GetService(reportDataType);
        if (reportDataService is not IReportData && reportDataService is not IReportDataSet)
            throw Oops.Bah(SysErrorCode.SysReportTemplate1005, typeofIReportData);

        // 加载报表
        var report = StiReport.CreateNewReport();
        if (!string.IsNullOrWhiteSpace(reportTemplate.Json))
            report.LoadFromJson(reportTemplate.Json);
        report.ReportFile = $"[{reportTemplate.Number}]{reportTemplate.Name}"; // 会显示在网页标题中，设置后同时会跳过保存时要求输入保存名的提示框

        if (reportDataService is IReportData s1)
        {
            // 清空字典后重新注册 reportData，业务对象的 Guid 会改变，清空前记录旧 Guid
            var oldBusinessObject = report.Dictionary.BusinessObjects["业务数据"];
            IDictionary<string, object> oldBusinessObjectGuid = null;
            if (oldBusinessObject != null)
            {
                oldBusinessObjectGuid = GetBusinessObjectGuid(oldBusinessObject);
                // 移除现有业务对象
                report.Dictionary.BusinessObjects.Remove(oldBusinessObject);
            }

            // 查询数据源
            var reportData = s1.GetReportData(reportTemplate, new StringIdsInput { Ids = ids });
            var reportDataColAlias = s1.GetReportDataColAlias();

            if (reportData != null)
            {
                // 注册数据
                report.RegBusinessObject("业务数据", reportData);
                report.Dictionary.SynchronizeBusinessObjects(ReportTemplateConst.NestedCheckMaxLevel);

                // 数据别名处理
                if (reportDataColAlias != null)
                    HandleReportDictionaryColAlias(reportDataColAlias, report.Dictionary.BusinessObjects, "业务数据");

                // 还原 Guid
                if (oldBusinessObjectGuid != null)
                    RestoreBusinessObjectGuid(report.Dictionary.BusinessObjects["业务数据"], oldBusinessObjectGuid);

                // 添加引用程序集
                // 可能在 2023.2.2 版本中，StiReport 会自动处理，当前的 2023.1.1 还是需要自行处理
                if (reportData.Count > 0)
                {
                    var dataType = reportData.First().GetType();
                    var refAssemblies = new List<string>(report.ReferencedAssemblies);
                    refAssemblies.AddRange(GetReportDataAssemblies(dataType, 0));
                    report.ReferencedAssemblies = refAssemblies.Distinct().ToArray();
                }
            }
        }

        if (reportDataService is IReportDataSet s2)
        {
            // 移除现有数据源
            var oldDataSet = report.Dictionary.DataSources["业务数据"];
            if (oldDataSet != null)
                report.Dictionary.DataSources.Remove(oldDataSet);

            // 查询数据源
            var dataSet = s2.GetReportDataSet(reportTemplate, new StringIdsInput { Ids = ids });
            if (dataSet != null)
            {
                dataSet.DataSetName = "业务数据";

                // 注册数据
                await report.RegDataAsync(dataSet);
                // 同步字典
                await report.Dictionary.SynchronizeAsync();
            }
        }

        // 添加当前用户名变量
        if (report.Dictionary.Variables.Contains("CurUserName"))
            report.Dictionary.Variables.Remove("CurUserName");
        report.Dictionary.Variables.Add(new StiVariable("", "CurUserName", "当前用户名", typeof(string), CurUserName, true));

        return report;
    }

    /// <summary>
    /// 获取报表数据类型所在的程序集
    /// </summary>
    /// <param name="dataType">报表数据类型</param>
    /// <param name="nestedLevel">当前所在嵌套层级</param>
    /// <returns></returns>
    protected List<string> GetReportDataAssemblies(Type dataType, int nestedLevel)
    {
        var list = new List<string> { dataType.Assembly.ManifestModule.Name };

        if (nestedLevel > ReportTemplateConst.NestedCheckMaxLevel)
            return list;

        foreach (var prop in dataType.GetProperties())
        {
            var propertyType = prop.PropertyType.GenericTypeArguments.Length > 0
                ? prop.PropertyType.GenericTypeArguments[0]
                : prop.PropertyType;
            list.AddRange(GetReportDataAssemblies(propertyType, nestedLevel + 1));
        }

        return list.Distinct().ToList();
    }

    /// <summary>
    /// 处理报表字典数据源中数据列别名
    /// </summary>
    /// <param name="reportDataColAlias">报表数据字段别名字典</param>
    /// <param name="businessObjects"><see cref="StiDictionary"/> 中的 BusinessObjects</param>
    /// <param name="businessObjectName">业务对象名称</param>
    private void HandleReportDictionaryColAlias(IDictionary<string, object> reportDataColAlias, StiBusinessObjectsCollection businessObjects, string businessObjectName)
    {
        var businessObject = businessObjects[businessObjectName];
        if (businessObject == null)
            return;

        foreach (var key in reportDataColAlias.Keys)
        {
            var value = reportDataColAlias[key];
            if (value is IDictionary<string, object> v)
                HandleReportDictionaryColAlias(v, businessObject.BusinessObjects, key);
            else
            {
                var col = businessObject.Columns[key];
                if (col != null)
                    col.Alias = value + "";
            }
        }
    }

    /// <summary>
    /// 获取报表业务对象Guid
    /// </summary>
    /// <returns></returns>
    private IDictionary<string, object> GetBusinessObjectGuid(StiBusinessObject businessObject)
    {
        var dic = new Dictionary<string, object>
        {
            ["Guid"] = businessObject.Guid
        };

        if (businessObject.BusinessObjects == null) return dic;

        foreach (StiBusinessObject childBusinessObject in businessObject.BusinessObjects)
            dic[childBusinessObject.Name] = GetBusinessObjectGuid(childBusinessObject);

        return dic;
    }

    /// <summary>
    /// 将当前 StiBusinessObject 的 Guid 按 oldBusinessObjectGuid 进行还原
    /// </summary>
    /// <param name="businessObject"></param>
    /// <param name="oldBusinessObjectGuid"></param>
    private void RestoreBusinessObjectGuid(StiBusinessObject businessObject, IDictionary<string, object> oldBusinessObjectGuid)
    {
        if (oldBusinessObjectGuid.ContainsKey("Guid"))
            businessObject.Guid = oldBusinessObjectGuid["Guid"] + "";

        if (businessObject.BusinessObjects == null) return;

        foreach (StiBusinessObject childBusinessObject in businessObject.BusinessObjects)
            if (oldBusinessObjectGuid.ContainsKey(childBusinessObject.Name))
                RestoreBusinessObjectGuid(childBusinessObject, (IDictionary<string, object>)oldBusinessObjectGuid[childBusinessObject.Name]);
    }

    /// <summary>
    /// 导出 Pdf
    /// </summary>
    [HttpPost("exportToPdf")]
    public async Task<IActionResult> ExportToPdf(SysReportTemplateExportPdfInput input)
    {
        var ms = new MemoryStream();
        var report = await GetReport(input.TemplateId, input.TypeofIReportData, input.Ids);
        await report.RenderAsync();
        await report.ExportDocumentAsync(StiExportFormat.Pdf, ms);
        ms.Seek(0, SeekOrigin.Begin);
        return new FileStreamResult(ms, "application/octet-stream") { FileDownloadName = $"{report.ReportFile}_{DateTime.Now:yyyyMMddHHmmss}.pdf" };
    }
}