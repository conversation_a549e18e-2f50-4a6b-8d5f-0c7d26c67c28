﻿namespace Neuz.Core.Enum;

/// <summary>
/// 批号状态
/// </summary>
public enum BdBatchStatus
{
    /// <summary>
    /// 待检
    /// </summary>
    [Description("待检"), Theme("warning")]
    Inspection = 0,

    /// <summary>
    /// 合格
    /// </summary>
    [Description("合格"), Theme("success")]
    Qualified = 1,

    /// <summary>
    /// 不合格
    /// </summary>
    [Description("不合格"), Theme("danger")]
    UnQualified = 2,
}