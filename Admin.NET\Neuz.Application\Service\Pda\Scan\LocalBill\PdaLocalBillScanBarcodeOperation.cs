﻿using Neuz.Application.Pda.Contract;
using Neuz.Application.Pda.LocalBill;
using Neuz.Application.Pda.Proxy;

namespace Neuz.Application.Pda.Scan.LocalBill;

/// <summary>
/// 本地单据条码扫描
/// </summary>
public class PdaLocalBillScanBarcodeOperation : PdaScanBarcodeOperationBase
{
    public PdaLocalBillScanBarcodeOperation(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <inheritdoc/>
    public override void Operation(PdaLocalBillScanBarcodeArgs args)
    {
        var dataCacheService = App.GetService<PdaDataCacheService>(ServiceProvider);
        var billData = (PdaLocalBillData)dataCacheService.GetBillData(args.Key, args.TranId);
        var billModel = (IPdaLocalBillModel)dataCacheService.GetPdaModel(billData.ModelKey);
        var barcodeService = App.GetService<BdBarcodeService>(ServiceProvider);
        var barcode = barcodeService.GetBarcodeAsync(args.BarcodeString).GetAwaiter().GetResult();
        if (barcode == null) return;
        if (billData.BarcodeList.Exists(b => b.Barcode.Id == barcode.Id))
            throw Oops.Bah(PdaErrorCode.Pda1015, barcode.Barcode);
        if (billModel.Config.BillParams.IsBarcodeOpTypeInOutLimit)
        {
            // 检查条码状态
            var pdaLocalBillCheckBarcodeStatusService = App.GetService<PdaLocalBillCheckStatusService>(ServiceProvider);
            pdaLocalBillCheckBarcodeStatusService.CheckBarcodeStatus(args.TranId, args, barcode);
        }

        if (billModel.Config.BillParams.IsDeductShowModifyQty && !args.IsRepeat)
        {
            var barcodeExtras = GetMatchBarcodeExtras(args.TranId, barcode, billModel);

            //可扣减条码
            PdaRestfulCode restfulCode = PdaRestfulCode.P105;
            //如果返回只有一个条码,并且为一物一码
            PdaExtrasRestfulResult<PdaBdBarcodeOperationExtras> result = new PdaExtrasRestfulResult<PdaBdBarcodeOperationExtras>
            {
                Code = (int)restfulCode,
                Message = null,
                Data = barcodeExtras,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            UnifyContext.Fill(result);

            billData.ExtendArgs["loadedComponent"] = "bdBarcodeModifyQty";
        }
        else
        {
            var scanQty = barcode.Qty;
            if (args.Ext != null && args.Ext.Contains("qty")) scanQty = Convert.ToDecimal(args.Ext["qty"]);
            // 扫描条码
            billModel.ScanBarcode(args.TranId, barcode, scanQty);
        }

        args.Barcodes.Add(barcode);
        args.IsResult = true;
    }

    /// <summary>
    /// 检查是否需要匹配源单数量
    /// </summary>
    /// <param name="tranId"></param>
    /// <param name="barcode"></param>
    /// <param name="billModel"></param>
    /// <returns></returns>
    public PdaBdBarcodeOperationExtras GetMatchBarcodeExtras(long tranId, BdBarcode barcode, IPdaLocalBillModel billModel)
    {
        var barcodeExtras = barcode.Adapt<PdaBdBarcodeOperationExtras>();
        barcodeExtras.ExecQty = barcodeExtras.Qty;
        if (!billModel.Config.BillParams.IsDeductMatchSourceQty) return barcodeExtras;
        // 如果需要匹配源单数量
        var info = billModel.CheckMatchingQty(tranId, new PdaLocalBillBarcode(barcode, barcode.Qty));
        barcodeExtras.ExecQty = info.Infos.Sum(r => r.Qty);
        return barcodeExtras;
    }
}