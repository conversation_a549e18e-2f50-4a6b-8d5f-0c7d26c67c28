# Changelog

All notable changes to this project will be documented in this file. See [conventional commits](https://www.conventionalcommits.org/) for commit guidelines.

---
## [unreleased]

### Documentation

- **(CHANGELOG.md)** update - ([0bbfc89](https://github.com/yiyungent/PluginCore/commit/0bbfc8955b7f6338db2125c78ec250e9eeeadcce)) - github-actions[bot]
- **(CHANGELOG.md)** update - ([4f6b47b](https://github.com/yiyungent/PluginCore/commit/4f6b47b3f86bfce4a8f660166837a7322c568d78)) - github-actions[bot]

### Miscellaneous Chores

- **(cliff.toml)** add - ([5614ef0](https://github.com/yiyungent/PluginCore/commit/5614ef024d644349095e19a0016bb23d989b0c90)) - yiyun

---
## [PluginCore.IPlugins-v0.9.1](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.9.0..PluginCore.IPlugins-v0.9.1) - 2023-12-30

### Features

- **(src/**/*.cs)** //  License: Apache-2.0 -> //  License: GNU LGPLv3 - ([57366d3](https://github.com/yiyungent/PluginCore/commit/57366d3e2afdb8e20e94851aa8a09f1ee61b6d7e)) - yiyun
- **(src/**/*.cs)** //  Project: https://moeci.com/PluginCore -> //  Project: https://yiyungent.github.io/PluginCore - ([7420480](https://github.com/yiyungent/PluginCore/commit/742048065978c1b8597fab3d52f011db4247fbda)) - yiyun
- **(src/plugincore.iplugins/constants.cs)** add - ([1278d0f](https://github.com/yiyungent/PluginCore/commit/1278d0f4acaa201869e0eb014156e14c6575cd00)) - yiyun
- **(src/plugincore.iplugins/constants.cs)** add: AspNetCoreAuthenticationScheme - ([697fe42](https://github.com/yiyungent/PluginCore/commit/697fe422408eec364075689c60aa9771113e1bd2)) - yiyun
- **(src/plugincore.iplugins/constants.cs)** add: AspNetCoreLanguageCookieName = "language" - ([e3f1196](https://github.com/yiyungent/PluginCore/commit/e3f119655739a510a6804101c4e5d7067719ff86)) - yiyun
- **(src/plugincore.iplugins/constants.cs)** add: AspNetCoreLanguageKey = "PluginCore.Admin.Language" - ([b50fa81](https://github.com/yiyungent/PluginCore/commit/b50fa81fb9efa87ae8048ab1925d3f79ec7c869c)) - yiyun

### Miscellaneous Chores

- **(src/plugincore.iplugins/plugincore.iplugins.csproj)** 0.9.0 -> 0.9.1 - ([d842de1](https://github.com/yiyungent/PluginCore/commit/d842de15552e19f7ba8f75e5ef89c68713ef31a5)) - yiyun

---
## [PluginCore.IPlugins-v0.9.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.AspNetCore-v1.0.2..PluginCore.IPlugins-v0.9.0) - 2023-02-15

### Features

- **(plugincore.aspnetcore,plugincore.iplugins,plugincore)** 仅保留已启用/已禁用 状态, IPlugin新方法 - ([e843a5b](https://github.com/yiyungent/PluginCore/commit/e843a5ba9fad4e88290c09bb3282b730c44c5a06)) - yiyun

### Build

- **(src/plugincore.iplugins/plugincore.iplugins.csproj)** <Version>0.9.0</Version> - ([4f07e99](https://github.com/yiyungent/PluginCore/commit/4f07e99d176421853e276c2a83e84433592f5112)) - yiyun

---
## [PluginCore.AspNetCore-v1.0.2](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.8.0..PluginCore.AspNetCore-v1.0.2) - 2022-04-19

### Style

- add: copyright: *.cs - ([9643dce](https://github.com/yiyungent/PluginCore/commit/9643dce112861a440d63306cb555accbed3d5111)) - yiyun

---
## [PluginCore.IPlugins-v0.8.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.7.0..PluginCore.IPlugins-v0.8.0) - 2022-04-16

### Miscellaneous Chores

- **(plugincore.iplugins.csproj)** <Version>0.8.0</Version> - ([853e638](https://github.com/yiyungent/PluginCore/commit/853e63850940aeecc0492bb12da54c548321e408)) - yiyun

### Refactoring

- 1.提取出 PluginCore.AspNetCore,PluginCore.IPlugins.AspNetCore 2.提取出更多接口,可自由替换 - ([fffd8d9](https://github.com/yiyungent/PluginCore/commit/fffd8d91c23fd6e4a4d09cbf91975beb3cf7acf0)) - yiyun

---
## [PluginCore.IPlugins-v0.7.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.6.1..PluginCore.IPlugins-v0.7.0) - 2022-02-09

### Features

- **(helloworldplugin.cs,iwidgetplugin.cs,plugincore)** add: Plugin Widget - ([0f010e9](https://github.com/yiyungent/PluginCore/commit/0f010e9cb9b11c4ccda51c40656dc5fd82a16a01)) - yiyun

### Miscellaneous Chores

- **(plugincore.iplugins.csproj)** 0.7.0 - ([87eda42](https://github.com/yiyungent/PluginCore/commit/87eda427bae83181559de92abaa8241f6e94199a)) - yiyun

---
## [PluginCore.IPlugins-v0.6.1](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.6.0..PluginCore.IPlugins-v0.6.1) - 2021-09-01

### Features

- **(pluginsettingsmodelfactory.cs,plugincore.iplugins.csproj)** remove: Newtonsoft.Json - ([84db1d3](https://github.com/yiyungent/PluginCore/commit/84db1d3f2bf9bae71320883b4c92f7e0f565bf15)) - yiyun

### Miscellaneous Chores

- **(plugincore.iplugins.csproj)** 0.6.1 - ([97e88ed](https://github.com/yiyungent/PluginCore/commit/97e88edeacd7b2526f5899db67d66165eb3f4dc9)) - yiyun

---
## [PluginCore.IPlugins-v0.6.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.5.0..PluginCore.IPlugins-v0.6.0) - 2021-08-21

### Features

- **(testtimejobplugin,plugincore.iplugins,plugincore)** timeJobPlugin 相关 - ([55d4f4c](https://github.com/yiyungent/PluginCore/commit/55d4f4ca7ddd9738216b9434ad1c30ef75f06471)) - yiyun

### Miscellaneous Chores

- **(plugincore.iplugins.csproj)** 0.6.0 - ([402abb3](https://github.com/yiyungent/PluginCore/commit/402abb38d25c8677b671e8e4ac3aa3f08fb33f51)) - yiyun

---
## [PluginCore.IPlugins-v0.5.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.4.0..PluginCore.IPlugins-v0.5.0) - 2021-08-21

### Features

- **(plugins,plugincore.iplugins,plugincore)** add: order, add: PluginApplicationBuilderManager - ([5e4a5f4](https://github.com/yiyungent/PluginCore/commit/5e4a5f46a4eb3aaca5d978fc1e695d0849e11e5c)) - yiyun

### Miscellaneous Chores

- **(plugincore.iplugins.csproj)** 0.5.0 - ([60eaa08](https://github.com/yiyungent/PluginCore/commit/60eaa08c68e46668d9a6d83b2b7664c6843fadd3)) - yiyun

---
## [PluginCore.IPlugins-v0.4.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.2.0..PluginCore.IPlugins-v0.4.0) - 2021-08-20

### Features

- **(istartupplugin.cs,istartupxplugin.cs)** 添加注释 - ([d4519b5](https://github.com/yiyungent/PluginCore/commit/d4519b54e9df931c6e75d9ca59742edc5f3185ac)) - yiyun
- **(plugincore)** pluginContentFilterMiddleware, IContentFilterPlugin - ([2597e9c](https://github.com/yiyungent/PluginCore/commit/2597e9c054bde134f9f250071347990be59e8d37)) - yiyun
- **(plugincore,/plugincore.iplugins)** pluginHttpEndFilter - ([c0cd458](https://github.com/yiyungent/PluginCore/commit/c0cd4581df72cdb9f4f678a531e7f04980c9695d)) - yiyun
- **(plugincore,plugincore.iplugins,helloworldplugin)** iStartupXPlugin: 运行时 Configure(app) - ([0d18a6f](https://github.com/yiyungent/PluginCore/commit/0d18a6f9949faa1e92f1d20da35689e8e153bac1)) - yiyun
- **(plugincore.iplugins)** iStartupPlugin.cs, PluginCore.IPlugins.csproj - ([4459fbe](https://github.com/yiyungent/PluginCore/commit/4459fbe5e2cbe369519b7010a7b7d6d4600738cf)) - yiyun
- **(plugincore.iplugins.csproj)** 0.3.0 - ([c8000be](https://github.com/yiyungent/PluginCore/commit/c8000bec4800826afa5db37edfb095a945231591)) - yiyun
- 生成注释xml: PluginCore.IPlugins,PluginCore - ([5878148](https://github.com/yiyungent/PluginCore/commit/5878148244344f412e75fe9446824dd99ca2de47)) - yiyun

### Miscellaneous Chores

- **(plugincore.iplugins)** 注释 - ([4fe65ce](https://github.com/yiyungent/PluginCore/commit/4fe65ce4e731e1a67d35f2c202239f062fe45adc)) - yiyun
- **(plugincore.iplugins.csproj)** 0.4.0 - ([a6e8851](https://github.com/yiyungent/PluginCore/commit/a6e8851b75dabb8ca68d8e14124a1332e7c13ad7)) - yiyun

---
## [PluginCore.IPlugins-v0.2.0](https://github.com/yiyungent/PluginCore/compare/PluginCore.IPlugins-v0.1.0..PluginCore.IPlugins-v0.2.0) - 2021-08-10

### Features

- **(plugincore.iplugins.csproj)** 0.2.0 - ([d7fb02f](https://github.com/yiyungent/PluginCore/commit/d7fb02fe481e1b2d20a7f7b34f0fa50e95240059)) - yiyun
- plugin 支持加载插件 wwwroot 文件夹下的 html前端等 - ([273f9a4](https://github.com/yiyungent/PluginCore/commit/273f9a44c8727675f60d364fcf59a373958b3575)) - yiyun

---
## [PluginCore.IPlugins-v0.1.0] - 2021-08-08

### Features

- pluginCore.IPlugins, plugins: HelloWorldPlugin - ([1e81de2](https://github.com/yiyungent/PluginCore/commit/1e81de2107394f527a94ec5d4c2ae6853d2d5526)) - yiyun
- pluginCore, plugins/HelloWorldPlugin - ([5141afd](https://github.com/yiyungent/PluginCore/commit/5141afded8feba94af581d6132fccb87aafa516c)) - yiyun
- nuget config, v0.1.0 - ([fffc419](https://github.com/yiyungent/PluginCore/commit/fffc419480481b632340eb4e42a0b608c5fff144)) - yiyun

<!-- generated by git-cliff -->
