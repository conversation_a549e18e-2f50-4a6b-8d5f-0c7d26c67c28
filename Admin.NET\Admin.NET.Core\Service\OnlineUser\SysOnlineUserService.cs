// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Furion.Localization;
using Microsoft.AspNetCore.SignalR;
using Org.BouncyCastle.Tls;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;

namespace Admin.NET.Core.Service;

/// <summary>
/// 系统在线用户服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 300)]
public class SysOnlineUserService : IDynamicApiController, ITransient
{
    //private readonly SqlSugarRepository<SysOnlineUser> _sysOnlineUerRep;
    private readonly SqlSugarRepository<SysUser> _sysUserRep;
    private readonly SysConfigService _sysConfigService;
    private readonly IHubContext<OnlineUserHub, IOnlineUserHub> _onlineUserHubContext;
    private readonly UserManager _userManager;

    //在线用户集合
    private static readonly List<SysOnlineUser> OnlineUsers = new();

    public SysOnlineUserService( //SqlSugarRepository<SysOnlineUser> sysOnlineUerRep,
        SqlSugarRepository<SysUser> sysUserRep,
        SysConfigService sysConfigService,
        IHubContext<OnlineUserHub, IOnlineUserHub> onlineUserHubContext,
        UserManager userManager)
    {
        //_sysOnlineUerRep = sysOnlineUerRep;
        _sysUserRep = sysUserRep;
        _sysConfigService = sysConfigService;
        _onlineUserHubContext = onlineUserHubContext;
        _userManager = userManager;
    }

    /// <summary>
    /// 获取在线用户分页列表 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取在线用户分页列表")]
    public Task<SqlSugarPagedList<SysOnlineUser>> Page(PageOnlineUserInput input)
    {
        //return await _sysOnlineUerRep.AsQueryable()
        //    .WhereIF(!string.IsNullOrWhiteSpace(input.UserName), u => u.UserName.Contains(input.UserName))
        //    .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), u => u.RealName.Contains(input.RealName))
        //    .ToPagedListAsync(input.Page, input.PageSize);

        lock (OnlineUsers)
        {
            return Task.FromResult(
                OnlineUsers
                    .WhereIF(!_userManager.SuperAdmin, u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.UserName), u => u.UserName.Contains(input.UserName))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), u => u.RealName.Contains(input.RealName))
                    .ToPagedList(input.Page, input.PageSize)
            );
        }
    }

    /// <summary>
    /// 强制下线 🔖
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    [NonValidation]
    [DisplayName("强制下线")]
    public async Task ForceOffline(SysOnlineUser user)
    {
        await _onlineUserHubContext.Clients.Client(user.ConnectionId).ForceOffline(L.Text["强制下线"]);
        //await _sysOnlineUerRep.DeleteAsync(user);

        lock (OnlineUsers)
        {
            var existOnlineUser = OnlineUsers.FirstOrDefault(u => u.Id == user.Id);
            if (existOnlineUser != null)
                OnlineUsers.Remove(existOnlineUser);
        }
    }

    /// <summary>
    /// 发布站内消息
    /// </summary>
    /// <param name="notice"></param>
    /// <param name="userIds"></param>
    /// <returns></returns>
    [NonAction]
    public async Task PublicNotice(SysNotice notice, List<long> userIds)
    {
        //var userList = await _sysOnlineUerRep.GetListAsync(u => userIds.Contains(u.UserId));

        List<SysOnlineUser> userList;
        lock (OnlineUsers)
        {
            userList = OnlineUsers.Where(m => userIds.Contains(m.UserId)).ToList();
        }

        if (!userList.Any()) return;

        foreach (var item in userList)
        {
            await _onlineUserHubContext.Clients.Client(item.ConnectionId).PublicNotice(notice);
        }
    }

    /// <summary>
    /// 单用户登录
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task SingleLogin(long userId)
    {
        if (await _sysConfigService.GetConfigValue<bool>(CommonConst.SysSingleLogin))
        {
            List<SysOnlineUser> users;
            lock (OnlineUsers)
            {
                users = OnlineUsers.Where(u => u.UserId == userId).ToList();
            }

            foreach (var user in users)
            {
                await ForceOffline(user);
            }
        }
    }

    /// <summary>
    /// 通过用户ID踢掉在线用户
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task ForceOffline(long userId)
    {
        List<SysOnlineUser> users;
        lock (OnlineUsers)
        {
            users = OnlineUsers.Where(u => u.UserId == userId).ToList();
        }

        foreach (var user in users)
        {
            await ForceOffline(user);
        }
    }

    /// <summary>
    /// Pda单用户登录
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(false)]
    public async Task PdaSingleLogin(long userId, string curConnectionId = "")
    {
        //var user = await _sysOnlineUerRep.GetFirstAsync(u => u.UserId == userId && u.LoginDeviceType == LoginDeviceType.Pda);
        //if (user == null) return;

        List<SysOnlineUser> users;
        lock (OnlineUsers)
        {
            users = OnlineUsers.Where(u => u.UserId == userId && u.LoginDeviceType == LoginDeviceType.Pda && u.ConnectionId != curConnectionId).ToList();
        }

        if (users.Count == 0) return;

        foreach (var user in users)
        {
            await ForceOffline(user);
        }
    }

    /// <summary>
    /// 获取租户在线用户数（排除供应商用户）
    /// </summary>
    /// <param name="tenantId">租户Id</param>
    /// <returns></returns>
    [NonAction]
    public async Task<int> GetTenantConnCount(long tenantId)
    {
        //return await _sysOnlineUerRep.AsQueryable().Filter("", true).CountAsync(u => u.TenantId == tenantId);

        var onlineUsers = (await GetTenantOnlineUsers(tenantId));
        var onlineUserIds = onlineUsers.Select(u => u.UserId).Distinct().ToList();
        // 排除供应商用户
        var supplierUserIds = await _sysUserRep.AsQueryable().Where(u => onlineUserIds.Contains(u.Id) && u.AccountType == AccountTypeEnum.Supplier).Select(u => u.Id).ToListAsync();
        return onlineUsers.Count(u => !supplierUserIds.Contains(u.UserId));
    }

    /// <summary>
    /// 获取租户在线用户列表
    /// </summary>
    /// <param name="tenantId">租户Id</param>
    /// <returns></returns>
    [NonAction]
    public Task<List<SysOnlineUser>> GetTenantOnlineUsers(long tenantId)
    {
        lock (OnlineUsers)
        {
            return Task.FromResult(OnlineUsers.Where(u => u.TenantId == tenantId).ToList());
        }
    }

    /// <summary>
    /// 获取用户是否Pda在线
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <returns></returns>
    [NonAction]
    public bool GetUserPdaOnline(long userId)
    {
        lock (OnlineUsers)
        {
            return OnlineUsers.Count(u => u.UserId == userId && u.LoginDeviceType == LoginDeviceType.Pda) > 0;
        }
    }

    /// <summary>
    /// 增加在线用户
    /// </summary>
    /// <param name="user"></param>
    [NonAction]
    public void Add(SysOnlineUser user)
    {
        lock (OnlineUsers)
        {
            OnlineUsers.Add(user);
        }
    }

    /// <summary>
    /// 根据连接Id获取
    /// </summary>
    /// <param name="connectionId">连接Id</param>
    [NonAction]
    public SysOnlineUser GetByConnectId(string connectionId)
    {
        lock (OnlineUsers)
        {
            var user = OnlineUsers.FirstOrDefault(u => u.ConnectionId == connectionId);
            return user;
        }
    }

    /// <summary>
    /// 删除在线用户
    /// </summary>
    /// <param name="id"></param>
    [NonAction]
    public void Delete(long id)
    {
        lock (OnlineUsers)
        {
            var user = OnlineUsers.FirstOrDefault(u => u.Id == id);
            OnlineUsers.Remove(user);
        }
    }
}